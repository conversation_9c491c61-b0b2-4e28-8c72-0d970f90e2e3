<!-- 输入框与popup模态框组合 -->
<template>
    <div>
        <van-field readonly v-model="props.value" :name="props.name" :label="props.label"
            :label-align="props.labelAlign" :label-width="props.labelWidth" :placeholder="props.placeholder"
            :required="props.required" :rules="props.requiredRule" :right-icon="props.rightIcon" @click="showPopupfn" />
        <van-popup v-model:show="showPicker" :position="props.position" round :teleport="to">
            <div class="search text-center mb-20px mt-30px px-30px" v-if="props.filterSearch">
                <div class="text-[#666] text-28px mb-20px">{{ '请选择' + props.label }}</div>
                <van-field v-model="searchText" class="search__input"
                    :placeholder="'请搜索' + props.label + '关键词'"></van-field>
            </div>
            <van-picker :columns="columns" @confirm="onComfirm" @cancel="showPicker = false"
                :columns-field-names="props.columnsFieldNames" :show-toolbar="props.showToolbar">
            </van-picker>
        </van-popup>

    </div>
</template>

<script setup lang="ts">
const props = defineProps({
    value: {
        required: true,
        type: String,
    },
    name: {
        required: true,
        type: String,
    },
    label: {
        required: true,
        type: String,
    },
    placeholder: {
        type: String,
        default: '请选择',
    },
    requiredRule: {
        type: Object,
        default: () => [{ required: false }]
    },
    labelWidth: {
        type: String,
        default: '100%',
    },
    labelAlign: {
        type: String,
        default: 'left',
    },
    required: {
        type: Boolean,
        default: false
    },
    disabled: {
        type: Boolean,
        default: false
    },
    rightIcon: {
        type: String,
        default: 'arrow-down'
    },
    position: {
        type: String,
        default: 'bottom',
    },
    columns: {
        type: Array,
        default() {
            return [];
        },
        required: true
    },
    columnsFieldNames: {
        type: Object,
        default() {
            return {
                text: 'label',
                value: 'value',
                children: 'children'
            }
        }
    },
    showToolbar: {
        type: Boolean,
        default: true
    },
    filterSearch: {
        type: Boolean,
        default: false
    },
    to: {
        type: HTMLElement || null,
    }

})
const columns = computed(() => {
    if (searchText.value) {
        return formatArr(props.columns)
    }
    else return props.columns
})
const emit = defineEmits(['onConfirm'])
const showPicker = ref(false)
const searchText = ref('')
const showPopupfn = () => {
    if (props.disabled) return
    showPicker.value = true
}
const onComfirm = ({ selectedOptions }) => {
    try {
        emit('onConfirm', selectedOptions)
        showPicker.value = false
    }
    catch (e) {
        console.error(e)
    }
}
const formatArr = (arr: Recordable) => {
    return arr.filter((item: Recordable) => {
        if (item.children) {
            item.children = formatArr(item.children)
        }
        return item.label.includes(searchText.value)
    })
}
</script>

<style></style>