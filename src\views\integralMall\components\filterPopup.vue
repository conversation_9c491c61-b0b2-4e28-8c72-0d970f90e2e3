<template>
    <van-popup v-model:show="props.show" position="right" @click-overlay="close">
        <div class="selects h-100vh w-60vw overflow-scroll text-#333 p-30px box-border flex flex-col">
            <div class="flex-1 overflow-scroll">
                <div class="exchange-values">
                    <div class="text-32px font-medium">兑换值</div>
                    <div class="flex items-center input-values mt-20px">
                        <van-field placeholder="最低分" type="number" v-model="userMinIntegral" clearable />
                        <div class="line w-64px h-1px bg-#CCC mx-14px"></div>
                        <van-field placeholder="最高分" type="number" v-model="userMaxIntegral" clearable />
                    </div>

                </div>
                <div class="filter-types mt-30px">
                    <div class="text-32px font-medium">筛选</div>
                    <div class="flex flex-wrap">
                        <div class="bg-[#F2F0F1] text-26px leading-none whitespace-nowrap
                        px-22px py-12px rounded-20px mt-20px mr-20px"
                            :class="{ 'active-type': userSearchType === item.value }" @click="handleType(item.value)"
                            v-for="item, index in goodsType" :key="index">
                            {{ item.label }}
                        </div>
                    </div>

                </div>
            </div>
            <div class="controll-end flex justify-around mt-20px mb-120px">
                <div @click="comfirm"
                    class="text-26px leading-none whitespace-nowrap w-40% text-center py-12px rounded-20px active-type">
                    确认</div>
                <div @click="reset"
                    class="text-26px leading-none whitespace-nowrap w-40% text-center py-12px rounded-20px bg-[#F2F0F1] ">
                    重置
                </div>
            </div>
        </div>
    </van-popup>
</template>
<script lang="ts" setup>
import { showToast } from 'vant';

const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    filterSearch: {
        type: Object,
        default() {
            return {}
        },
    },
})
watch(() => props.show, (val) => {
    if (!val) return
    userMinIntegral.value = props.filterSearch?.userMinIntegral || ''
    userMaxIntegral.value = props.filterSearch?.userMaxIntegral || ''
    userSearchType.value = props.filterSearch?.userSearchType === false ? false : true
})
const emit = defineEmits(['update:show', 'confirm'])
const userMinIntegral = ref('') //最小分值
const userMaxIntegral = ref('') //最大分值

const userSearchType = ref(true)
const goodsType = ref([
    {
        label: '全部商品',
        value: true
    },
    {
        label: '我能兑换',
        value: false
    },
])

const sortType = ref([
    {
        label: '默认排序',
        value: '1'
    },
    {
        label: '默认排序',
        value: '2'
    },
])

const handleType = (value: boolean) => {
    userSearchType.value = value
}
const close = () => {
    emit('update:show', false)
}
const reset = () => {
    emit('update:show', false)
    emit('confirm', {
        userMinIntegral: '',
        userMaxIntegral: '',
        userSearchType: true,
    })
}
const comfirm = () => {
    if (!userMaxIntegral.value && userMinIntegral.value) return showToast('请输入最大分值')
    if (userMaxIntegral.value && !userMinIntegral.value) return showToast('请输入最小分值')

    if (Number(userMinIntegral.value) > Number(userMaxIntegral.value)) {
        showToast('最大分值不能小于最小分值')
        return
    }
    emit('update:show', false)
    emit('confirm', {
        userMinIntegral: userMinIntegral.value,
        userMaxIntegral: userMaxIntegral.value,
        userSearchType: userSearchType.value,
    })
}

</script>
<style lang="scss" scoped>
.selects {
    .input-values {
        :deep(.van-cell) {
            background-color: #F2F0F1;
            border-radius: 23px;
            padding: 11px 22px;
        }
    }

    .active-type {
        border: 2px solid #FF4344;
        color: #FF4344;
        background-color: #fff;
    }
}

.exchange-values {
    :deep(.van-cell__value) {
        font-size: 26px;
    }
}
</style>