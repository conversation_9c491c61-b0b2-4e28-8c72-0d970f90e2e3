<template>
    <div class="check-info bg-[#F6F7F8] h-fit min-h-screen">
        <van-skeleton title :row="10" :loading="loading">
            <div class="banner relative z-1">
                <img loading="lazy" :src="statusObj[status].banner" class="w-full">
                <div class="text-line absolute top-20% left-8%">
                    <img loading="lazy" :src="statusObj[status].title" class="w-260px mb-10px">
                    <div class="text-#fff text-30px">{{ statusObj[status].text }}</div>
                </div>
            </div>
            <div class="form-content mx-30px bg-#fff -mt-100px rounded-20px relative z-2 mb-20px">
                <div class="infos px-30px py-35px" v-if="infos">
                    <div class="flex items-center flex-wrap py-15px" v-for="item, index in infoColumn" :key="index">
                        <div class="label text-30px text-[#808080] mr-20px w-fit">{{ item.label }}: </div>
                        <div class="content" :class="{ 'w-full pt-15px': item.type === 'img' }">
                            <template v-if="item.type === 'img'">
                                <div v-if="infos[item.props]" class="flex flex-wrap">
                                    <img loading="lazy" v-previewImg="judgeStaticUrl(img)"
                                        v-for="img, index in infos[item.props]" :src="judgeStaticUrl(img)" :key="index"
                                        class="mr-10px object-cover w-fit h-fit max-w-246px max-h-300px rounded-12px" />
                                </div>
                                <div v-else class="text-30px text-[#333]">-</div>
                            </template>
                            <div class="text-30px text-[#333]" v-else>
                                {{ infos[item.props] ? infos[item.props] : '-' }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="resubmit_btn relative m-auto w-55% my-45px" @click="handleSubmit"
                v-if="infos?.auditState === 'refuse'">
                <img src="@/assets/public/butto.png" class="w-full block" />
                <div class="absolute left-50% top-50% -translate-50% leading-none
                text-34px text-[#fff] font-medium">重新提交</div>
            </div>
        </van-skeleton>
    </div>
</template>
<script lang="ts" setup>
import waitBanner from "@/assets/merchants/status/wait_banner.png";
import waitTitle from "@/assets/merchants/status/wait_title.png";
import failBanner from "@/assets/merchants/status/fail_banner.png";
import failTitle from "@/assets/merchants/status/fail_title.png";
import { applyRecord } from '@/api/merchants';
import { judgeStaticUrl } from '@/utils/utils'
import { useDictionary } from '@/store/modules/dictionary';
import { categoryList } from '@/api/merchants'
const router = useRouter()
const dictionnary = useDictionary()
const loading = ref(true)

const statusObj = {
    'wait': {
        banner: waitBanner,
        title: waitTitle,
        text: '请耐心等待~'
    },
    'refuse': {
        banner: failBanner,
        title: failTitle,
        text: ''
    }
}
const status = ref('wait')
const infos = ref({})
const belongAreaArr = computed(() => dictionnary.getDictionaryOpt?.['regionCode'])
const infoColumn = reactive(
    [
        {
            label: '营业名称',
            props: 'companyName'
        },
        {
            label: '商家类型',
            props: 'Type'
        },
        {
            label: '营业时间',
            props: 'businessTime'
        },
        {
            label: '商家地址',
            props: 'address'
        },
        {
            label: '所属区县',
            props: 'areaCodeName'
        },
        {
            label: '联系人',
            props: 'contractName'
        },
        {
            label: '联系电话',
            props: 'contractPhone'
        },
        {
            label: '商家介绍',
            props: 'introduce'
        },
        {
            label: '商家封面图',
            props: 'companyIconList',
            type: 'img'
        },

        {
            label: '商家身份证',
            props: 'identityImglist',
            type: 'img'
        },
        {
            label: '营业执照',
            props: 'licenseImgList',
            type: 'img'
        },
        {
            label: '资质证明',
            props: 'qualificationImg',
            type: 'img'
        },
        {
            label: '开户许可证',
            props: 'openingImg',
            type: 'img'
        },
        {
            label: '宣传图',
            props: 'publicityImg',
            type: 'img'
        },
        {
            label: '统一社会信息代码',
            props: 'labourUnionCode'
        },
        {
            label: '提交时间',
            props: 'createTime'
        },
    ]
)

const typeColumn = ref([])
const getTypeList = async () => {
    const res = await categoryList({
        pageSize: 0,
        pageNum: 1
    })
    if (res.data) typeColumn.value = res.data.map((item: any) => {
        return {
            label: item.typeName,
            value: item.autoId,
        }
    })
}

const getDetails = () => {
    applyRecord({
        pageNum: 1,
        pageSize: 0
    }).then(res => {
        if (res.data) {
            res.data.businessTime = `${res.data.openTime} - ${res.data.closeTime}`
            res.data.licenseImgList = res.data.licenseImg ? res.data.licenseImg.split(',') : []
            res.data.companyIconList = res.data.companyIcon ? res.data.companyIcon.split(',') : []
            res.data.qualificationImg = res.data.qualificationImg ? res.data.qualificationImg.split(',') : []
            res.data.publicityImg = res.data.publicityImg ? res.data.publicityImg.split(',') : []
            res.data.openingImg = res.data.openingImg ? res.data.openingImg.split(',') : []
            res.data.identityImglist = [res.data.identityImgFront, res.data.identityImgBack]
            res.data.Type = typeColumn.value.find((item: any) => item.value === res.data.typeId)?.label || ''

            status.value = res.data.auditState
            infos.value = res.data
            loading.value = false
            if (res.data.areaCode) {
                infos.value.areaCodeName = belongAreaArr.value.find((item: any) => item.value === res.data.areaCode)?.label || ''
            }
        }
    }).catch((err) => {
        console.log(err);
        loading.value = false

    })
}
const handleSubmit = () => {
    router.push({
        path: '/merchants/form'
    })
}
onBeforeMount(
    async () => {
        await getTypeList()
        getDetails()
    }
)

</script>
<style scoped lang="scss">
.form-content {
    min-height: calc(100vh - 250px);
}
</style>