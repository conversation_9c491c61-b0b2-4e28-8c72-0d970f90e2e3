<template>
  <div class="echart-box" style="width:100%;height:100%">
    <div ref="echartRef" class="w-full h-full"></div>
  </div>
</template>

<script lang="ts" setup>
// 饼图
import { useECharts } from '@/utils/useECharts'
import { Ref, ref, watch, onMounted } from 'vue'
import * as echarts from 'echarts'
import { LabelBg, getVwSize } from '../../data'

const props = defineProps({
  dataSource: {
    type: Object,
    default: {
      areaNameList: [
        '顺庆区',
        '高坪区',
        '嘉陵区',
        '阆中市',
        '南部县',
        '西充县',
        '仪陇县',
        '营山县',
        '蓬安县',
      ],
      total: [],
      available: [],
      used: []
    },
  },
})

const echartRef = ref<HTMLDivElement | null>(null)
const { setOptions } = useECharts(echartRef as Ref<HTMLDivElement>)

const initChart = () => {
  let data = { x: [], y1: <any[]>[], y2: <any[]>[], y3: <any[]>[] }
  let maxNumTotal = 0, maxNumAvail = 0, maxNumUsed = 0
  data.x = props.dataSource?.areaNameList
  let isFormated = false
  if (props.dataSource?.total) {
    maxNumTotal = Math.max(...props.dataSource.total)
    data.y1 = props.dataSource.total
  }
  if (props.dataSource?.available) {
    maxNumAvail = Math.max(...props.dataSource.available)
    data.y2 = props.dataSource.available
  }
  if (props.dataSource.used) {
    maxNumUsed = Math.max(...props.dataSource.used)
    data.y3 = props.dataSource.used
  }
  if ([maxNumTotal, maxNumAvail, maxNumUsed].some(item => item >= 10000)) {
    isFormated = true
    data.y1 = data.y1.map((item: any) => parseFloat((item / 10000).toFixed(2)))
    data.y2 = data.y2.map((item: any) => parseFloat((item / 10000).toFixed(2)))
    data.y3 = data.y3.map((item: any) => parseFloat((item / 10000).toFixed(2)))
  }

  // 绘制图表
  var option = {
    // tooltip: {
    //   show: true,
    //   trigger: 'axis',
    //   formatter: (params) => {
    //     return ''
    //   },
    //   axisPointer: {
    //     type: 'shadow',
    //   }
    // },
    grid: {
      top: '15%',
      left: '0%',
      right: '0%',
      bottom: '0%',
      containLabel: true,
    },
    legend: {
      show: true,
      icon: 'circle',
      orient: 'horizontal',
      top: '0%',
      right: '0%',
      itemWidth: getVwSize(20),
      itemHeight: getVwSize(20),
      itemGap: getVwSize(60),
      textStyle: {
        color: '#666666',
        fontSize: getVwSize(24),
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        label: {
          show: true,
          backgroundColor: 'rgba(141, 226, 218, .8)',
          color: '#fff',
          borderColor: 'rgba(0,0,0,0)',
          shadowColor: 'rgba(0,0,0,0)',
          shadowOffsetY: 0,
          fontSize: getVwSize(22)
        },
        type: 'line',
        lineStyle: {
          color: 'rgba(141, 226, 218, .2)',
          width: getVwSize(28),
          type: 'solid'
        }
      },
      backgroundColor: '#fff',
      textStyle: {
        fontSize: getVwSize(22)
      },
      rounded: getVwSize(40),
      padding: [getVwSize(10), getVwSize(10)],
      extraCssText: 'box-shadow: 1px 0 2px 0 rgba(163,163,163,0.2)',
      formatter: function (params: any) {
        let result = params[0].name + '<br/>'
        params.forEach((item: any) => {
          let value = item.value
          if (item.seriesIndex === 0) value = props.dataSource?.total[item.dataIndex];
          else if (item.seriesIndex === 1) value = props.dataSource?.available[item.dataIndex];
          else if (item.seriesIndex === 2) value = props.dataSource?.used[item.dataIndex];

          result += item.marker + item.seriesName + ': ' + value + '<br/>'
        })
        return result
      }
    },

    xAxis: [
      {
        type: 'category',
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitArea: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        boundaryGap: true,
        axisLabel: {
          show: true,
          interval: 0,
          rotate: 45,
          fontSize: getVwSize(24),
          lineHeight: getVwSize(30),
          margin: getVwSize(8),
          color: '#999',
        },
        data: data.x,
      },
    ],
    yAxis: [
      {
        position: 'left',  // 确保轴在左
        name: `数量${isFormated ? '(万分)' : '(分)'}`,
        nameTextStyle: {
          color: '#999',
          fontSize: getVwSize(22),
        },
        type: 'value',
        minInterval: 1,
        splitLine: {
          show: true,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#E6E6E6',
          },
        },
        axisLabel: {
          textStyle: {
            color: '#999',
            fontSize: getVwSize(22),
          },
        },
        axisTick: {
          show: false,
        },
        min: 0,  // 取数据最小值
        max: 'dataMax'   // 取数据最大值
      },
    ],
    series: [
      {
        name: '累计积分',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        showSymbol: false,
        z: 3,
        // label: {
        //   show: true,
        //   position: 'top',
        //   lineHeight: 40,
        //   distance: -5,
        //   backgroundColor: {
        //     image: LabelBg // 设置背景图
        //   },
        //   formatter: (params: any) => {
        //     return `{c|${params.value}w}`;
        //   },
        //   rich: {
        //     c: {
        //       width: 70,
        //       height: 35,
        //       color: '#fff',
        //       fontSize: getVwSize(24),
        //       verticalAlign: 'top', // 文本垂直对齐方式
        //       align: 'center', // 文本对齐方式
        //       padding: [0, 0, 0, 0], // 设置内边距
        //     }
        //   }
        // },

        lineStyle: {
          width: getVwSize(6),
          shadowColor: 'rgba(98, 42, 220, 0.1)',
          shadowBlur: getVwSize(6),
          shadowOffsetY: getVwSize(6)
        },
        itemStyle: {
          normal: {
            color: '#8DE2DA',
          },
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [{
            offset: 0,
            color: '#fff'
          },
          {
            offset: 1,
            color: 'rgba(141, 226, 218, 0.1)'
          }
          ])
        },
        data: data.y1,

      },
      {
        name: '可用积分',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        showSymbol: false,
        z: 1,
        lineStyle: {
          width: getVwSize(6),
          shadowColor: 'rgba(66, 163, 255, 0.1)',
          shadowBlur: getVwSize(6),
          shadowOffsetY: getVwSize(6)
        },
        itemStyle: {
          normal: {
            color: '#42A3FF',
          },
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [{
            offset: 0,
            color: '#fff'
          },
          {
            offset: 1,
            color: '#D8ECFF'
          }
          ])
        },
        data: data.y2,
      },
      {
        name: '消耗积分',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        showSymbol: false,
        z: 1,
        lineStyle: {
          width: getVwSize(6),
          shadowColor: 'rgba(255, 204, 123, 0.27)',
          shadowBlur: getVwSize(2),
          shadowOffsetY: getVwSize(8)
        },
        itemStyle: {
          normal: {
            color: '#FFCC7B',
          },
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [{
            offset: 0,
            color: '#fff'
          },
          {
            offset: 1,
            color: 'rgba(255, 204, 123, 0.01)'
          }
          ])
        },
        data: data.y3,
      },
    ],
  }

  setOptions(option)
}

onMounted(() => {
  initChart()
})

// 监听dataSource
watch(
  () => props.dataSource,
  (newValue, oldValue) => {
    initChart()
  },
  { deep: true, immediate: true }
)
</script>
<style scoped lang="scss">
.echart-box {}
</style>
