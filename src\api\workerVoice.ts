import { h5Http } from '@/utils/http/axios';
import { BasicResponse } from '.';

enum API {
  findList = '/selectEmployeeMessageList',
  getPersonalList = '/getPersonalList',
  view = '/selectById',
  saveOrUpdate = '/saveOrUpdateByDTO',
  del = '/deleteById',
}

function getApi(url?: string) {
  if (!url) {
    return '/question';
  }
  return '/question' + url;
}

// 公共列表
export const selectEmployeeMessageList = (params: Recordable) => {
  return h5Http.get<BasicResponse>(
    { url: getApi(API.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

// 个人列表
export const getPersonalList = (params: Recordable) => {
  return h5Http.get<BasicResponse>(
    { url: getApi(API.getPersonalList), params },
    {
      isTransformResponse: false,
    }
  );
};

// 新增修改
export const saveOrUpdate = (params: Recordable) => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(API.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// view
export const view = (params: Recordable) => {
  return h5Http.get<Recordable>(
    {
      url: getApi(API.view),
      params,
    },
    {
      isTransformResponse: true,
    }
  );
};

// 删除
export const deleteLine = (params: Recordable | string) => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(API.del),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const questionTypeList = (params: Recordable) => {
  return h5Http.get<Recordable[]>(
    {
      url: '/questionType/findVoList',
      params,
    },
    {
      isTransformResponse: true,
    }
  );
};
