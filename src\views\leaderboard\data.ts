export { default as IconUser } from '@/assets/leaderBoard/index/icon_user.png';
export { default as IconUnion } from '@/assets/leaderBoard/index/icon_union.png';
export { default as IconMenber } from '@/assets/leaderBoard/index/icon_menber.png';
export { default as IconOfficer } from '@/assets/leaderBoard/index/icon_officer.png';
export { default as IconTurn } from '@/assets/leaderBoard/index/icon_turn.png';
export { default as IconRZ } from '@/assets/leaderBoard/index/icon_rz.png';
export { default as LabelBg } from '@/assets/leaderBoard/ph/label_bg.png';

// 动态计算echarts中文字大小 baseFontSize:基础字体大小
export const getVwSize = (baseFontSize:number) => {
  if (typeof baseFontSize !== 'number' || baseFontSize <= 0) return 12;
  if (typeof window === 'undefined') return baseFontSize;

    const viewportWidth = window.innerWidth; // 获取视口宽度
    // 计算字体大小为视口宽度的百分比
    const result = (baseFontSize / 750) * viewportWidth; // 计算结果

    return isNaN(result) ? baseFontSize : result; 
}
// 计算dataZoom默认初始赋值
export const dataZoomIndexSet = (dataX:any[],gap:number=5) => {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const nowdate = `${year}-${month}-${day}`
    let currentIndex = 0
    let startValue = 0
    let endValue = gap

    if (dataX?.length) currentIndex = dataX.findIndex((item: any) => item === nowdate)

    if (currentIndex >= 0 && dataX?.length > gap) {
      startValue = currentIndex > gap ? currentIndex - gap : currentIndex
      endValue = currentIndex > gap ? currentIndex : currentIndex + gap
    }
    return {
        startValue,
        endValue
    }
}