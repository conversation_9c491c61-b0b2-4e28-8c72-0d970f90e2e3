<template>
    <div class="mall bg-[#F9F9F9] h-fit min-h-full ">
        <div class="mall_header relative z-1">
            <img loading="lazy" src="@/assets/integralMall/banner.png" class="w-full">
            <div class="integral_text absolute top-35% left-25px right-25px -translate-y-50% flex justify-around">
                <div class="text-[#fff] text-center" v-for="item, index in integralNav" :key="index">
                    <div class="text-28px">{{ item.title }}</div>
                    <div class="num text-52px mb-24px mt-10px">{{ mallInfo[item.props] }}</div>
                    <div @click="toPage(item.path, { type: index })"
                        class="btn flex justify-center items-center bg-[#E3F1FF] text-28px text-[#3190f3] py-10px px-24px rounded-30px">
                        <img loading="lazy" :src="item.icon" class="w-26px mr-10px">
                        <span>{{ item.btnText }}</span>
                    </div>
                </div>
            </div>
            <div class="text-[#fff] text-26px integral_rule absolute  leading-none flex items-center
            right-0 px-10px h-50px top-30%  -translate-y-50%"
                @click="toPage('/integralMall/integralDetails', { type: '1' })">
                兑换记录
            </div>
        </div>
        <!-- 签到 -->
        <div class="sign_canlendar relative z-2 -mt-120px px-22px pr-26px mb-20px">
            <!-- 签到内容展示区域 -->
            <div class="sigin_centent">
                <div class="header relative z-1">
                    <img loading="lazy" src="@/assets/integralMall/sign_bg.png" class="w-full">
                    <div
                        class="sign_text ml-149px mr-30px h-100px flex items-center mt-10px absolute top-0 left-0 right-0 bottom-0">
                        <div class="text flex-1 text-[#fff] text-30px">本月已连续签到<span class="text-40px"> {{ signDays }}
                            </span>天</div>
                        <div class="record_btn text-[#0E84FF] text-24px bg-[#fff] rounded-22px px-18px py-11px leading-none"
                            @click="toActivityRecord()">中奖记录
                        </div>
                    </div>
                </div>
                <div class="calendar relative z-2 bg-#fff -mt-100px rounded-18px pt-10px pb-20px ml-16px mr-9px">
                    <div class="controll px-95px flex justify-center mb-28px mt-20px">
                        <img loading="lazy" src="@/assets/integralMall/icon_left.png" class="w-16px"
                            @click="changeDate('pre')" />
                        <span class="text-28px flex-1 text-center">{{ nowDate }}</span>
                        <img loading="lazy" src="@/assets/integralMall/icon_left.png" class="w-16px rotate-180"
                            @click="changeDate('last')" />
                    </div>
                    <div class="week_days  px-35px">
                        <div class="weeKs flex justify-center">
                            <div class="items text-center w-70px text-[#A1A1A1] text-28px"
                                :class="{ 'mr-18px': (index + 1) % 7 !== 0 }" v-for="item, index in weeks" :key="index">
                                {{ item }}
                            </div>
                        </div>
                        <div class="days flex justify-center flex-wrap">
                            <div class="items text-center" :class="{ 'mr-18px': (index + 1) % 7 !== 0 }"
                                v-for="item, index in weekDayArray" :key="index" @click="signCoins(item)">
                                <div class="day bg-[#EBEDF8] text-[#333] rounded-10px
                                 mt-17px w-70px h-100px flex justify-center items-center flex-col"
                                    :class="{ 'active_sign': signMap.hasOwnProperty(item.day) }">
                                    <img loading="lazy" src="@/assets/integralMall/coins.png" class="w-38px" />
                                    <div class="text-24px  whitespace-nowrap">
                                        <!-- {{
                                            signMap.hasOwnProperty(item.day) || item.day >= currentDate ?
                                                item.day.substring(item.day.length -
                                                    2, item.day.length) + '日' : '补'
                                        }} -->
                                        {{ item.day.substring(item.day.length - 2, item.day.length) }}
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <!-- <div
                        class="sign_btn mx-126px h-72px rounded-36px text-28px text-[#fff] flex justify-center items-center mt-30px">
                        立即签到
                    </div> -->
                    <div class="collapse m-auto text-center mt-10px" @click="expandMore">
                        <img loading="lazy" src="@/assets/integralMall/icon_left.png" class="w-16px"
                            :class="calendarType === 'month' ? 'rotate-90' : '-rotate-90'" />
                    </div>
                </div>
            </div>
        </div>
        <!-- end -->
        <div class="">
            <!-- 积分活动&积分夺宝 -->
            <div class="intergal_nav flex px-30px">
                <div class="activity_nav flex-1 relative mr-12px" @click="toPage('/integralMall/activity')">
                    <img loading="lazy" src="@/assets/integralMall/integral_activity_bg.png" class="w-full">
                    <div class="absolute left-32px right-32px top-28px box-border">
                        <img loading="lazy" src="@/assets/integralMall/integral_activity_text.png" class="w-120px">
                        <div class="des text-[#C99557] text-24px mt-8px mb-12px">活动专区</div>
                        <div class="btn text-[#fff] rounded-20px text-20px w-117px h-41px text-center leading-41px">参与活动
                        </div>
                    </div>
                </div>
                <div class="pressure_nav flex-1 relative" @click="toPage('/integralMall/treasureHunt')">
                    <img loading="lazy" src="@/assets/integralMall/integral_pressure_bg.png" class="w-full">
                    <div class="absolute left-32px right-32px top-28px box-border">
                        <img loading="lazy" src="@/assets/integralMall/integral_pressure_text.png" class="w-120px">
                        <div class="des text-[#B75656] text-24px mt-8px mb-12px">免费得好礼</div>
                        <div class="btn text-[#fff] rounded-20px text-20px w-117px h-41px text-center leading-41px">立即参与
                        </div>
                    </div>
                </div>
            </div>

            <div class="product-list">
                <div class="header my-20px flex items-center px-30px">
                    <div class="search flex rounded-28px items-center flex-1">
                        <div class="ml-24px">
                            <img loading="lazy" src="@/assets/integralMall/search_icon.png" class="w-30px" />
                        </div>
                        <div class="ml-10px flex-1 h-56px search-input">
                            <van-field v-model="combinationSearch" placeholder="请搜索您想要的商户/商品" clearable
                                @clear="searchChange">
                            </van-field>
                        </div>
                        <div class="text-26px text-[#FFF] w-101px h-56px bg-[#FF4344] box-border
                            rounded-28px flex items-center justify-center ml-24px leading-0" @click="searchChange">
                            搜索
                        </div>
                    </div>
                    <div class="filter-icon flex items-center ml-15px" @click="filterShow = true">
                        <img loading="lazy" src="@/assets/integralMall/filter_icon.png" class="w-30px" />
                        <div class="text-26px text-[#333] ml-6px">筛选</div>
                    </div>
                </div>
                <!-- tab选择 -->
                <div class="tab-list h-80px bg-[#F9F9F9] sticky top-0 z-99">
                    <van-tabs v-model:active="tabActive" title-active-color="#333" title-inactive-color="#333"
                        background="transparent" @change="searchChange">
                        <van-tab v-for="item, index in typeTabList" :name="item.value" :title="item.label"
                            :key="index"></van-tab>
                    </van-tabs>
                </div>
                <!-- end -->
                <div class="px-30px">
                    <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
                        <div class="flex flex-wrap justify-between">
                            <div v-for="(item, index) in list" :key="index"
                                class="w-336px mt-20px box-border overflow-hidden">
                                <goodsListCell :content="item"></goodsListCell>
                            </div>
                        </div>
                    </refreshList>
                </div>

            </div>
        </div>
        <!-- 补签确认弹窗 -->
        <confirmPopop v-model:show="showConfirm" tips="是否确认补签？" @confirm="confirmSign"></confirmPopop>
        <!-- 打卡惊喜弹窗 -->
        <surprisePopup v-model:show="supriseShow" :surpiseType="supriseType">
            <!-- 积分 -->
            <div class="text-[#E71E03] text-40px font-medium flex items-center tracking-wide"
                v-if="supriseType === 'integral'">
                <span>恭喜获得</span>
                <!-- 分值 -->
                <span class="text-50px">{{ supriseObj?.prizeContent }}</span>积分
            </div>
            <!-- 商品/优惠券/红包 -->
            <div class="flex flex-col justify-center mt-60px h-50%" v-else>
                <div class="text-center">
                    <div class="text-32px text-#333 tracking-wide 
                    text-ellipsis line-clamp-2 whitespace-nowrap text-wrap">{{ supriseObj?.prizeName }}
                    </div>
                    <div class="text-28px text-#666 mt-10px tracking-wide 
                        text-ellipsis line-clamp-2 whitespace-nowrap text-wrap" v-if="supriseObj?.prizeContent">
                        {{ supriseObj?.prizeContent }}
                    </div>
                </div>
                <div class="text-[#E71E03] text-40px font-bold mt-40px text-center tracking-wide">恭喜您中奖啦~</div>
            </div>
        </surprisePopup>
        <!-- 筛选弹窗 -->
        <filterPopup v-model:show="filterShow" :filterSearch="filterSearch" @confirm="confirmFilter"></filterPopup>
        <!-- 气泡效果 -->
        <div class="fixed left-50% top-50% -translate-50% z-100">
            <waterIntergral v-model:show="showBubble" :score="signScore" bigSize="150px" midSize="62px" smallSize="50px"
                scorefontSize="40px"></waterIntergral>
        </div>
    </div>
</template>
<script setup lang="ts">
defineOptions({
    name: 'IntegralMall'
})
import detailIcon from '@/assets/integralMall/icon_point_detail.png'
import taskIcon from '@/assets/integralMall/icon_task.png'
import { getNowFormatDate, getWeekDates } from '@/utils/date'
import goodsListCell from './components/goodsListCell.vue';
import refreshList from '@/components/refreshList/index.vue';
import { showToast } from 'vant';
const waterIntergral = defineAsyncComponent(() => import('@/components/Bubble/waterIntergral.vue'))
const confirmPopop = defineAsyncComponent(() => import('@/components/Popup/confirmPopop.vue'));
const filterPopup = defineAsyncComponent(() => import('./components/filterPopup.vue'));
import surprisePopup from './components/surprisePopup.vue';
import {
    userSign, userSignList, myIntegralNum,
    integralGoodsList, userSignLottery, integralLotteryDetail
} from "@/api/mall/integral"
import { useUserStore } from '@/store/modules/user';
import dayjs from 'dayjs'
import { useDictionary } from '@/store/modules/dictionary';
const dictionary = useDictionary()
onMounted(() => {
    initalPage() //初始化
    loadMoreData()
})

const useStore = useUserStore();
const userInfo = computed(() => useStore.userInfo)
const router = useRouter()
const initalPage = async () => {
    getIntegralNum() //获取积分统计
    getSignActivtyDetail()
    // 获得当前日期
    nowDate.value = getNowFormatDate('', '', calendarType.value)
    weekDayArray.value = getWeekDates(nowDate.value)
    await getSigninfos()
    // 未签到
    if (!signMap.value.hasOwnProperty(nowDate.value)) {
        controllSign(nowDate.value)
    }
}

// 积分统计
const integralNav = [
    {
        title: "累计积分",
        props: 'summaryIntegral',
        path: "/integralMall/integralDetails",
        btnText: '积分明细',
        icon: detailIcon
    },
    {
        title: "可用积分",
        props: 'userIntegral',
        path: "/integralMall/integralTask",
        btnText: '积分任务',
        icon: taskIcon
    }
]
const mallInfo = ref<any>({})

// 获取统计积分 
const getIntegralNum = async () => {
    const { code, data } = await myIntegralNum({
        userId: userInfo.value?.userId
    })
    if (code === 200) mallInfo.value = data
}
// end

// 签到
const showBubble = ref(false)//积分水泡
const signScore = ref(0) //积分值

const nowDate = ref('') //当前选择日期
const currentDate = ref(dayjs(new Date()).format('YYYY-MM-DD')) //当日日期
const weeks = ref([
    '一', '二', '三', '四', '五', '六', '日'
]) //周列表
const weekDayArray = ref<any[]>([]) //周列表
const signMap = ref<any>({}) //签到map
const signDays = ref(0) //连续签到天数
const calendarType = ref('date')

// 补签弹窗
const showConfirm = ref(false)//确认弹窗
const supriseShow = ref(false)//惊喜弹窗
const supriseType = ref('integral') // 积分/礼品 goods
const targetDate = ref('') //当前点击日期
const supriseObj = ref<any>({})
// 获取签到记录
const getSigninfos = async () => {
    const { code, data } = await userSignList({
        beginDate: weekDayArray.value[0]?.day,
        endDate: weekDayArray.value[weekDayArray.value.length - 1]?.day,
        userId: userInfo.value?.userId,
    })
    if (code === 200) {
        signMap.value = data.signRecordArray
        signDays.value = data.signDays
    }
}
// 调用签到接口
const controllSign = async (signDate: string) => {
    const { code, data, message } = await userSign({
        signDate
    })
    if (code === 200) {
        const { statefulFlowState, score } = data
        if (statefulFlowState === true) {
            showToast({ type: 'success', message: '签到成功', duration: 1500 })
            // 活动id存在才去调用抽奖
            if (activityId.value) signSuprise()
        }
        else if (statefulFlowState === false) showToast({ type: 'success', message: '补签成功', duration: 1500 })
        else return
        signMap.value[signDate] = data

        // 积分气泡动画
        if (score) {
            signScore.value = score
            setTimeout(() => { showBubble.value = true }, 1500)
        }
        // END
        getSigninfos()
        getIntegralNum()
    }
    else showToast({ type: 'fail', message })

}
// 当日签到抽奖
const signSuprise = async () => {
    const { code, data } = await userSignLottery()
    if (code === 200) {
        supriseObj.value = data
        if (data?.prizeType === '2') supriseType.value = 'integral'
        else supriseType.value = 'goods'
        if (data?.prizeType !== '1') supriseShow.value = true
    }
}
// 获取签到活动详情
const activityId = ref('') //抽奖活动id
const getSignActivtyDetail = async () => {
    const { code, data } = await integralLotteryDetail({
        activityMode: 'signLottery'
    })
    if (code === 200) {
        activityId.value = data?.activityId
    }
}

// 更改日期
const changeDate = (type: string) => {
    nowDate.value = getNowFormatDate(nowDate.value, type, calendarType.value)
    weekDayArray.value = getWeekDates(nowDate.value, calendarType.value)
    getSigninfos()
}
const expandMore = () => {
    if (calendarType.value === 'date') calendarType.value = 'month'
    else if (calendarType.value === 'month') calendarType.value = 'date'
    changeDate('')
}
// 确认补签
const confirmSign = () => {
    showConfirm.value = false
    controllSign(targetDate.value)
}

// 点击日期事件
const signCoins = (item: any) => {
    if (signMap.value.hasOwnProperty(item.day)) return showToast({ type: 'fail', message: '已签到' })

    // 补签逻辑 -暂时取消
    if (item.day < currentDate.value) {
        // showConfirm.value = true
        // targetDate.value = item.day
    }
    else if (item.day === currentDate.value) {
        controllSign(item.day)
    }
    else showToast({ message: '亲，无法提前签到，请在当天签到' })
}
// end


// 商品列表
// tab标签列表
const typeTabList = computed(() => {
    return [
        {
            label: '热门推荐',
            value: '-1',
        },
        ...dictionary.getDictionaryOpt?.['integralProductColumn']
    ]
})
const tabActive = ref('-1')
const combinationSearch = ref('') //搜索条件

const filterShow = ref(false)//筛选弹窗
const list = ref<any>([]);
const pageNum = ref(1);
const loadMoreRef = ref<any>(null)
const filterSearch = ref({})
const confirmFilter = (val: any) => {
    filterSearch.value = val
    if (loadMoreRef.value) loadMoreRef.value.resetStatus();
    onRefreshList()
}
const searchChange = () => {
    if (loadMoreRef.value) loadMoreRef.value.resetStatus();
    onRefreshList()
}
const loadMoreData = async () => {
    const params: any = {
        pageNum: pageNum.value,
        pageSize: 10,
        systemQueryType: 'h5',
        integralProductColumn: tabActive.value,
        userSearchType: true,
        combinationSearch: combinationSearch.value,
        birthdayQueryFlag: false,
        ...filterSearch.value
    }
    if (tabActive.value !== '-1') {
        params.orderBy = 'create_time'
        params.sortType = 'desc'
    }
    const { code, data, total } = await integralGoodsList(params)
    if (pageNum.value === 1) list.value = []
    if (code === 200) {
        if (pageNum.value === 1) list.value = data
        else list.value = [...list.value, ...data]
    }
    //重置刷新状态及 判断是否加载完成
    if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(list.value.length, total)
    }
}
// 刷新
const onRefreshList = () => {
    pageNum.value = 1
    loadMoreData()
}
// 加载更多
const onLoadMore = () => {
    pageNum.value++
    loadMoreData()
}

// 路由跳转
const toPage = (path: string, query: any = {}) => {
    router.push({
        path,
        query: {
            ...query,
            // index:1
        }
    })
}
const toActivityRecord = () => {
    router.push({
        path: '/activityHome/lotteryRecord',
        query: {
            activityId: activityId.value,
        }
    })
}
</script>
<style lang="scss" scoped>
.integral_rule {
    background: linear-gradient(90deg, #5A9AF6 0%, #0066FF 100%);
    border-radius: 30px 0px 0px 30px;
    border: 1px solid #fff;
    border-right: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.active_sign {
    background-color: #5AA4FF;
    color: #fff;
}

.sign_btn {
    background: linear-gradient(179deg, #1C8BFF 0%, #7EB2FF 100%);
}

.activity_nav .btn {
    background: linear-gradient(85deg, #F0A24A 0%, #FF6407 100%);
}

.pressure_nav .btn {
    background: linear-gradient(85deg, #FB8463 0%, #FF3F43 100%);
}

.card_shadow {
    box-shadow: 0px 3px 11px 0px rgba(0, 45, 92, 0.1);
}

.goodsCard {
    box-shadow: 0px 2px 40px 0px rgba(157, 0, 0, 0.20);
}

.calendar {
    box-shadow: 0px 2px 8px 0px rgba(0, 45, 92, 0.18);
}

.product-list {
    .header {
        .search {
            border: 2px solid #FF4344;

            :deep(.van-cell) {
                padding: 0;
                height: 100%;
                background-color: transparent;
            }

            :deep(.van-field__body) {
                height: 100%;
                font-size: 24px;
            }

            --van-field-clear-icon-size: 25px;
        }
    }

    .tab-list {
        :deep(.van-tabs--line .van-tabs__wrap) {
            height: 80px;
        }

        :deep(.van-tab) {
            line-height: 1;
            height: 100%;
            font-size: 30px;
        }

        :deep(.van-tab--active) {
            font-size: 32px;
        }

        :deep(.van-tabs__nav--line) {
            height: 100%;
            padding-bottom: 15px;
        }

        :deep(.van-tabs__line) {
            background-color: #FF4344;
            width: 40px;
            height: 6px;
            bottom: 15px;
            border-radius: 3px
        }

        // :deep(.van-tab__text) {
        //     font-size: 28px;
        // }
    }
}
</style>