<template>
    <van-popup :show="showPop" round class="popup1">
        <div class="text-center text-32px text-#333">{{ titleName }}</div>
        <div class="text-input mt-15px">
            <van-field required v-model="introduce" label="" rows="3" type="textarea" label-width="0"
                :placeholder="placeHolder" :maxlength="200"></van-field>
        </div>
        <div class="btn_ok rounded-40px w-65% py-20px m-auto text-center text-#fff mt-50px text-28px" @click="submit()">
            确定</div>
        <img loading="lazy" src="@/assets/public/close.png" alt=""
            class="fixed -bottom-15 w-60px h-60px left-1/2 -translate-x-1/2" @click="closePopup">
    </van-popup>

</template>
<script lang="ts" setup>
const emit = defineEmits(['submitContent', 'closePopup']);
const introduce = ref(null);
const props = defineProps({
    showPop: {
        type: Boolean,
        default: false
    },
    titleName: {
        type: String,
        default: ''
    },
    placeHolder: {
        type: String,
        default: '请输入'
    }
})
//确定
function submit() {
    emit('submitContent', introduce.value);
    console.log(introduce.value);

    // setTimeout(()=>{
    //     introduce.value="";
    // },500)
}
//关闭
function closePopup() {
    emit('closePopup')
}
</script>
<style lang="scss" scoped>
.van-overlay {
    background: rgba(0, 0, 0, 0.5)
}

.popup1 {
    width: 85%;
    height: 500px;
    padding: 50px 40px;
    box-sizing: border-box;
    top: 40%;
    overflow-y: visible;
}

.btn_ok {
    background: linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%);
}

.text-input {
    :deep(.van-cell) {
        padding: 0;
    }

    :deep(.van-field__control) {
        background: #F6F7F8;
        border-radius: 10px;
        padding: 20px 35px;
        box-sizing: border-box;
        font-size: 28px;
    }

    :deep(.van-field__body) {
        margin-top: 15px;
    }
}
</style>