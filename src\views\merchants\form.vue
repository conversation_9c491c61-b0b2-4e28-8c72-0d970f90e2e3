<template>
    <div class="merchants-form bg-#F6F7F8" ref="merchants">
        <div class="banner relative z-1">
            <img loading="lazy" src="@/assets/merchants/form/banner_bg.png" class="w-full h-280px" />
            <img loading="lazy" src="@/assets/merchants/form/banner_title.png"
                class="absolute top-74px left-58px w-315px">
        </div>
        <div class="-mt-100px relative z-2 bg-#fff form-view mx-30px pt-20px pb-160px">
            <van-form ref="formRef" error-message-align="right">
                <van-field v-model="form.companyName" name="companyName" label="商家名称" placeholder="请填写商家名称"
                    :rules="[{ required: true, message: '请填写商家名称' }]" required maxlength="50" />

                <inputSelect :value="typeIdName" name="typeId" label="商家类型" placeholder="请选择商家类型"
                    :requiredRule="[{ required: true, message: '请选择商家类型' }]" :columns="typeIdCloumn"
                    labelWidth="fit-content" @onConfirm="(val) => onConfirmSelect(val, 'typeId')" required readonly
                    right-icon="arrow" :to="merchants" />

                <van-field v-model="form.closeTime" name="closeTime" label="营业时间" placeholder="请选择营业时间" required
                    readonly right-icon="arrow">
                    <template #input>
                        <div class="flex justify-end w-full field-chidren">
                            <van-field v-model="form.openTime" name="openTime"
                                :rules="[{ required: true, message: '请选择开店时间' }]">
                                <template #input>
                                    <div class="text-#c8c9cc w-full text-center"
                                        :class="{ '!text-#333': form.openTime }" @click="selectTime('openTime')">
                                        {{ form.openTime ? form.openTime : '开店时间' }}
                                    </div>
                                </template>
                            </van-field>
                            <div class="mx-20px text-#c8c9cc">
                                -
                            </div>
                            <van-field v-model="form.closeTime" name="closeTime"
                                :rules="[{ required: true, message: '请选择闭店时间' }]">
                                <template #input>
                                    <div class="text-#c8c9cc w-full text-center"
                                        :class="{ '!text-#333': form.closeTime }" @click="selectTime('closeTime')">
                                        {{ form.closeTime ? form.closeTime : '闭店时间' }}
                                    </div>
                                </template>
                            </van-field>

                        </div>

                    </template>
                </van-field>

                <van-field v-model="form.address" name="address" label="商家地址" placeholder="请选择商家地址"
                    :rules="[{ required: true, message: '请选择商家地址' }]" maxlength="50" required readonly
                    @click="showMapSelect = true">
                    <template #right-icon>
                        <div class="w-26px mt-10px">
                            <img loading="lazy" src="@/assets/public/icon_address_line.png" class="w-100%" />
                        </div>
                    </template>
                </van-field>

                <inputSelect :value="areaCodeName" :columns="belongAreaArr" name="areaCode" label="商家所属区县"
                    class="filter-search-input" labelWidth="fit-content" placeholder="请选择商家所属区县" :filterSearch="true"
                    :requiredRule="[{ required: true, message: '请选择商家所属区县' }]" required readonly right-icon="arrow"
                    @onConfirm="(val) => onConfirmSelect(val, 'areaCode')" :to="merchants" />

                <van-field v-model="form.contractName" name="contractName" label="商家联系人姓名" placeholder="请填写商家联系人姓名"
                    :rules="[{ required: true, message: '请填写商家联系人姓名' }]" required />

                <van-field v-model="form.contractPhone" name="contractPhone" label="商家联系人电话" placeholder="请填写商家联系人电话"
                    :rules="[{ required: true, message: '请填写商家联系人电话' }, ...rules.telRules]" required />

                <van-field v-model="form.identityNumber" name="identityNumber" label="法人身份证号码" required
                    :rules="[{ required: true, message: '请填写法人身份证号码' }, ...rules.idCardRules]"
                    placeholder="请填写法人身份证号码" />

                <div class="label-align-top">

                    <van-field v-model="form.introduce" name="introduce" label="商家简介" placeholder="请输入商家简介"
                        :rules="[{ required: true, message: '请输入商家简介' }]" type="textarea" required
                        error-message-align="left" class="self-textarea" rows="3" maxlength="500" show-word-limit />

                    <van-field v-model="form.companyIcon" name="companyIcon" label="商家封面图" placeholder="请上传商家封面图"
                        :rules="[{ required: true, message: '请上传商家封面图' }]" error-message-align="left" required>
                        <template #input>
                            <van-uploader v-model="companyIconArr" max-count="1" accept="image/*"
                                :after-read="(val) => afterRead(val, 'companyIcon')" reupload class="mt-20px">
                                <div
                                    class="w-160px h-160px bg-#F5F5F5 rounded-12px flex flex-col items-center justify-center">
                                    <img loading="lazy" src="@/assets/public/upload_icon_small.png" class="w-49px" />
                                    <div class="text-#999 text-24px mt-5px">上传</div>
                                </div>
                            </van-uploader>
                        </template>
                    </van-field>
                    <van-field v-model="form.identityImgBack" name="identityImgBack" label="法人身份证"
                        error-message-align="left">
                        <template #input>
                            <div class="flex w-full mt-28px field-chidren">
                                <van-field v-model="form.identityImgFront" name="identityImgFront" required
                                    error-message-align="left" :rules="[{ required: true, message: '请上传法人身份证正面' }]">
                                    <template #input>
                                        <van-uploader v-model="identityImgFrontArr" max-count="1" accept="image/*"
                                            :after-read="(val) => afterRead(val, 'identityImgFront')">
                                            <div>
                                                <img loading="lazy" src="@/assets/merchants/form/idCard_1.png"
                                                    class="w-243px" />
                                            </div>
                                        </van-uploader>
                                    </template>
                                </van-field>
                                <van-field v-model="form.identityImgBack" name="identityImgBack" required
                                    error-message-align="left" :rules="[{ required: true, message: '请上传法人身份证背面' }]">
                                    <template #input>
                                        <van-uploader v-model="identityImgBackArr" max-count="1" accept="image/*"
                                            :after-read="(val) => afterRead(val, 'identityImgBack')">
                                            <div>
                                                <img loading="lazy" src="@/assets/merchants/form/idCard_2.png"
                                                    class="w-243px ml-55px" />
                                            </div>
                                        </van-uploader>
                                    </template>
                                </van-field>

                            </div>
                        </template>
                    </van-field>

                    <van-field v-model="form.licenseImg" name="licenseImg" label="营业执照" error-message-align="left"
                        :rules="[{ required: true, message: '请上传营业执照' }]" required>
                        <template #input>
                            <van-uploader v-model="licenseImgArr" :after-read="(val) => afterRead(val, 'licenseImg')"
                                max-count="1" accept="image/*" class="mt-20px">
                                <div
                                    class="w-160px h-160px bg-#F5F5F5 rounded-12px flex flex-col items-center justify-center">
                                    <img loading="lazy" src="@/assets/public/upload_icon_small.png" class="w-49px" />
                                    <div class="text-#999 text-24px mt-5px">上传</div>
                                </div>
                            </van-uploader>
                        </template>
                    </van-field>
                    <van-field v-model="form.labourUnionCode" name="labourUnionCode" label="统一社会信用代码"
                        placeholder="请输入统一社会信用代码" error-message-align="left" :rules="rules.creditCodeRule" required />

                    <van-field v-model="form.openingImg" name="openingImg" label="开户许可" placeholder="请上传开户许可">
                        <template #input>
                            <van-uploader v-model="openingImgArr" max-count="1" accept="image/*"
                                :after-read="(val) => afterRead(val, 'openingImg')" class="mt-20px">
                                <div
                                    class="w-160px h-160px bg-#F5F5F5 rounded-12px flex flex-col items-center justify-center">
                                    <img loading="lazy" src="@/assets/public/upload_icon_small.png" class="w-49px" />
                                    <div class="text-#999 text-24px mt-5px">上传</div>
                                </div>
                            </van-uploader>
                        </template>
                    </van-field>
                    <van-field v-model="form.qualificationImg" name="qualificationImg" label="资质证明">
                        <template #input>
                            <van-uploader v-model="qualificationImgArr" max-count="3" accept="image/*"
                                :after-read="(val) => afterRead(val, 'qualificationImg')" class="mt-20px">
                                <div
                                    class="w-160px h-160px bg-#F5F5F5 rounded-12px flex flex-col items-center justify-center">
                                    <img loading="lazy" src="@/assets/public/upload_icon_small.png" class="w-49px" />
                                    <div class="text-#999 text-24px mt-5px">上传</div>
                                </div>
                            </van-uploader>
                        </template>
                    </van-field>
                    <van-field v-model="form.publicityImg" name="publicityImg" label="详情宣传图">
                        <template #input>
                            <van-uploader v-model="publicityImgArr" max-count="3" accept="image/*"
                                :after-read="(val) => afterRead(val, 'publicityImg')" class="mt-20px">
                                <div
                                    class="w-160px h-160px bg-#F5F5F5 rounded-12px flex flex-col items-center justify-center">
                                    <img loading="lazy" src="@/assets/public/upload_icon_small.png" class="w-49px" />
                                    <div class="text-#999 text-24px mt-5px">上传</div>
                                </div>
                            </van-uploader>
                        </template>
                    </van-field>
                </div>

            </van-form>
        </div>
        <div class="flex items-center justify-center w-full fixed bottom-0 h-120px bg-#fff z-99 controlbtn safe_area_bottom"
            @click="handleSubmit">
            <div class="text-center relative">
                <img loading="lazy" src="@/assets/public/button.png" class="w-70% mx-auto block" />
                <span class="absolute left-50% top-50% -translate-50% text-#fff text-32px leading-none">提交</span>
            </div>
        </div>
        <submitWaitPopup v-model:show="showPopup" title='提交成功,等待审核' status="wait" message="审核成功将发送短信至您的手机上，请留意短信！"
            btnText="返回首页" :pageTo="toHome">
        </submitWaitPopup>
        <!-- 时间选择器 -->
        <van-popup v-model:show="showTimeSelect" position="bottom">
            <van-time-picker v-model="currentTime" :title="currentType === 'openTime' ? '开店时间' : '闭店时间'"
                :columns-type="['hour', 'minute']" @confirm="confirmTime" @cancel="cancelTime" />
        </van-popup>
        <!-- 地图选择器 -->
        <van-popup v-model:show="showMapSelect" position="bottom">
            <mapView @returnLocation="returnLocation"></mapView>
        </van-popup>
    </div>
</template>
<script lang="ts" setup>
const mapView = defineAsyncComponent(() => import('./components/mapSelect.vue')) //地图选择器组件
const submitWaitPopup = defineAsyncComponent(() => import('@/components/Popup/submitWaitPopup.vue'))
import inputSelect from '@/components/inputSelect/index.vue'
import { uploadFile } from '@/api/public';
import { showDialog, showFailToast, showSuccessToast, showToast } from 'vant'
import { categoryList, applyFormIn } from '@/api/merchants'
import { useUserStore } from '@/store/modules/user';
import { useDictionary } from '@/store/modules/dictionary';
import { telRules, idCardRules } from '@/utils/rulesValidator'
import { applyRecord } from '@/api/merchants';
import { judgeStaticUrl } from '@/utils/utils'
const router = useRouter()
const useStore = useUserStore()
const dictionnary = useDictionary()
const merchants = ref(null)
const form = ref({
    identityType: 'idCard',
    introduce: '',
    companyName: '',
    typeId: '',
    openTime: '',
    closeTime: '',
    addressCoordinate: '',//地址坐标
    address: '',
    contractName: '',
    contractPhone: '',
    areaCode: '',
    companyIcon: '',
    identityImgBack: '',
    identityImgFront: '',
    licenseImg: '',
    companyType: 'merchant',//(商户)   (固定写死)
    nickname: useStore.userInfo?.nickname,
    account: useStore.userInfo?.phone,
    labourUnionCode: '',
    identityNumber: '',
    openingImg: '',
    qualificationImg: '',
    publicityImg: ''
})
const rules = {
    telRules,
    idCardRules,
    creditCodeRule: [
        {
            required: true,
            message: '请输入社会信用代码',
            trigger: 'onBlur'
        },
        {
            validator: (value: string) => {
                return /^[a-zA-Z0-9_]{18}$/.test(value);
            },
            message: "请输入正确格式的社会信用代码",
            trigger: "onBlur"
        }
    ]
}

const typeIdName = ref('')
const typeIdCloumn = ref([])

// 获取商家类型
const getCompanyType = async () => {
    const res = await categoryList({
        pageSize: 0,
        pageNum: 1
    })
    if (res.data) typeIdCloumn.value = res.data.map((item: any) => {
        return {
            label: item.typeName,
            value: item.autoId,
        }
    })
}
// 地址选择
const showMapSelect = ref(false)
const returnLocation = (val: any) => {
    if (val) {
        form.value.address = val?.address || ''
        form.value.addressCoordinate = val?.latitude + ',' + val?.longitude //坐标
    }
    showMapSelect.value = false

}
// end

// 所属区县选择
const belongAreaArr = computed(() => dictionnary.getDictionaryOpt?.['regionCode'])
const areaCodeName = ref('')
// end

// 时间选择器确认
const onConfirmSelect = (val: any, name: string) => {
    switch (name) {
        case 'typeId':
            form.value.typeId = val[0]?.value
            typeIdName.value = val[0]?.label
            break;
        case 'areaCode':
            form.value.areaCode = val[0]?.value
            areaCodeName.value = val[0]?.label
            break;
    }
}
// 时间选择器
const showTimeSelect = ref(false)
const currentTime = ref(['10', '00'])
let currentType = '' //当前选择类型

const selectTime = (type: string) => {
    if (type === 'openTime' && form.value.openTime) currentTime.value = form.value.openTime.split(':')
    else if (type === 'closeTime' && form.value.closeTime) currentTime.value = form.value.closeTime.split(':')
    else {
        //默认时间
        if (type === 'openTime') currentTime.value = ['08', '00']
        else if (type === 'closeTime') currentTime.value = ['20', '00']
    }
    currentType = type
    showTimeSelect.value = true
}
const confirmTime = (val: any) => {
    if (currentType === 'openTime') form.value.openTime = val.selectedValues.join(':')
    else if (currentType === 'closeTime') form.value.closeTime = val.selectedValues.join(':')
    showTimeSelect.value = false
}
const cancelTime = () => {
    currentType = ''
    showTimeSelect.value = false
}
// end 

// end
// 图片上传
const companyIconArr = ref<any>([])
const licenseImgArr = ref<any>([])
const identityImgFrontArr = ref<any>([])
const identityImgBackArr = ref<any>([])
const qualificationImgArr = ref<any>([])//资质证明
const publicityImgArr = ref<any>([])//宣传图
const openingImgArr = ref<any>([])//开户许可证

const afterRead = (file: any, type: any) => {
    // 66:营业执照、资质证明、开户许可证 64:身份证 57:封面图、宣传图
    let filedata = {
        operateType: "", //操作模块类型
        file: file.file,
    }
    switch (type) {
        case 'identityImgFront':
        case 'identityImgBack':
            filedata.operateType = "64"
            break;
        case 'companyIcon':
        case 'publicityImg':
            filedata.operateType = "57"
            break;
        case 'licenseImg':
        case 'qualificationImg':
        case 'openingImg':
            filedata.operateType = "66"
            break;
        default:
            break;
    }

    file.status = "uploading"
    uploadFile(filedata).then((res) => {
        if (res.code == 200 && res.data) {
            file.status = "success";
            file.url = judgeStaticUrl(res.data[0])
            file.originUrl = res.data[0]
            switch (type) {
                case 'identityImgFront':
                    form.value.identityImgFront = res.data[0]
                    break;
                case 'identityImgBack':
                    form.value.identityImgBack = res.data[0]
                    break;
                case 'openingImg':
                    form.value.openingImg = res.data[0]
                    break;
                case 'qualificationImg':
                    form.value.qualificationImg = qualificationImgArr.value.map((item: any) => item.originUrl).join(',')
                    break;
                case 'publicityImg':
                    form.value.publicityImg = publicityImgArr.value.map((item: any) => item.originUrl).join(',')
                    break;
                case 'companyIcon':
                    form.value.companyIcon = res.data[0]
                    break;
                case 'licenseImg':
                    form.value.licenseImg = res.data[0]
                    break;
                default:
                    break;
            }
            showSuccessToast('上传成功')
        } else {
            file.status = "failed";
            showFailToast(res.message);
        }
    })
}
// 表单提交
// 弹窗展示
const showPopup = ref(false)
const formRef = ref<any>(null)
const handleSubmit = () => {
    formRef.value
        .validate()
        .then(async () => {
            if (!form.value.addressCoordinate) {
                showToast('请在地图上选择地址信息')
            }
            const res = await applyFormIn({
                ...form.value,
                openTime: form.value.openTime + ':00',
                closeTime: form.value.closeTime + ':00'
            })
            if (res.code == 200) {
                showPopup.value = true
            } else {
                showDialog({
                    title: '提示',
                    message: res.message,
                    confirmButtonText: '确定'
                })
            }
        })
}
const toHome = () => {
    router.replace({
        path: '/home'
    })
}

const getRecordForm = () => {
    applyRecord({
        pageNum: 1,
        pageSize: 0
    }).then(res => {
        if (res.data) {
            form.value = res.data
            if (form.value.hasOwnProperty('autoId')) delete form.value.autoId
            if (form.value.typeId) typeIdName.value = typeIdCloumn.value.find((item: any) => item.value == form.value.typeId)?.label
            if (form.value.areaCode) areaCodeName.value = belongAreaArr.value.find((item: any) => item.value == form.value.areaCode)?.label

            if (form.value.openTime) form.value.openTime = form.value.openTime.slice(0, 5)
            if (form.value.closeTime) form.value.closeTime = form.value.closeTime.slice(0, 5)

            companyIconArr.value = form.value.companyIcon?.split(',').map((item: string) => {
                return {
                    url: judgeStaticUrl(item),
                    objectUrl: judgeStaticUrl(item),
                    originUrl: item
                }
            })
            licenseImgArr.value = form.value.licenseImg?.split(',').map((item: string) => {
                return {
                    url: judgeStaticUrl(item),
                    objectUrl: judgeStaticUrl(item),
                    originUrl: item
                }
            })
            identityImgFrontArr.value = form.value.identityImgFront?.split(',').map((item: string) => {
                return {
                    url: judgeStaticUrl(item),
                    objectUrl: judgeStaticUrl(item),
                    originUrl: item
                }
            })
            identityImgBackArr.value = form.value.identityImgBack?.split(',').map((item: string) => {
                return {
                    url: judgeStaticUrl(item),
                    objectUrl: judgeStaticUrl(item),
                    originUrl: item
                }
            })
            qualificationImgArr.value = form.value.qualificationImg?.split(',').map((item: string) => {
                return {
                    url: judgeStaticUrl(item),
                    objectUrl: judgeStaticUrl(item),
                    originUrl: item
                }
            })
            publicityImgArr.value = form.value.publicityImg?.split(',').map((item: string) => {
                return {
                    url: judgeStaticUrl(item),
                    objectUrl: judgeStaticUrl(item),
                    originUrl: item
                }
            })
            openingImgArr.value = form.value.openingImg?.split(',').map((item: string) => {
                return {
                    url: judgeStaticUrl(item),
                    objectUrl: judgeStaticUrl(item),
                    originUrl: item
                }
            })
        }
    })
}

onBeforeMount(async () => {
    await getCompanyType()
    getRecordForm()
})
</script>
<style scoped lang="scss">
.merchants-form {
    .form-view {
        border-radius: 20px 20px 0 0;

        :deep(.van-field__label) {
            width: fit-content;
            font-size: 30px;
        }

        :deep(.van-cell) {
            padding-top: 26px;
            padding-bottom: 26px;

            :after {
                border: none !important;
            }
        }

        :deep(.van-field__control) {
            text-align: right;
            font-size: 28px;
        }

        .label-align-top {
            :deep(.van-field__label) {
                width: 100%;
            }

            :deep(.van-field__control) {
                text-align: left;
                margin-top: 20px;
            }

            .tags {
                .tag {
                    border: 1px solid #D8D8D8;
                }
            }
        }

        :deep(.van-field__right-icon) {
            color: #c8c9cc;

            .van-icon {
                font-size: 28px;
            }
        }
    }

    .controlbtn {
        box-shadow: 0px -1px 10px rgba(0, 0, 0, 0.05);
    }

    .filter-search-input {
        :deep(.search__input) {
            background-color: #F7F8FA;
            border-radius: 10px;

            .van-field__control {
                text-align: left;
                font-size: 28px;
            }
        }
    }

    .self-textarea {
        :deep(.van-field__control) {
            background-color: #F5F5F5;
            border-radius: 12px;
            padding: 10px;
        }
    }

    .field-chidren {
        :deep(.van-cell) {
            padding: 0;

            .van-field__control {
                text-align: center;
            }
        }
    }

    :deep(.van-uploader__preview-image) {
        width: fit-content;
        height: fit-content;
        max-width: 246px;
        max-height: 300px;
    }
}
</style>
