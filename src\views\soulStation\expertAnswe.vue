<template>
  <div :class="$style.chat" class="w-full relative h-[100vh] overflow-hidden">
    <div class="w-full h-[80px] bg-[#9fcaff1a] text-28px text-[#65AAFF] flex justify-start items-center pl-[36px]">
      温馨提示：目前您正在一对一咨询中</div>

    <div class="fixed bottom-0 h-[120px] shadow-[4px_-3px_4px_0_#F6F7F8] w-full">
      <van-search v-model="searchVal" placeholder="有问题可以向我提问哦" left-icon="" class="rounded-[33px]">
        <template #right-icon>
          <div
            class="bg-[#5AA4FF] rounded-[24px] px-[22px] py-[9px] flex justify-center items-center shadow-[1px_3px_4px_0px_#4992f833]"
            @click="toSendMessage">
            <img loading="lazy" :src="pushIcon" class="w-[36px] h-[29px]" />
          </div>
        </template>
      </van-search>
    </div>

    <div class="w-full h-[calc(100%-80px-120px)] relative overflow-y-auto" ref="scrollBox">
      <div class="p-45px">
        <refreshList @onRefreshList="onLoadMore" ref="loadMoreRef" :finishedText="''" :slotEmpty="true">
          <div v-for="(item, index) of Data.listData" :key="index" class="text-28px">
            <div class="text-#999 text-center py-30px">{{ fixDateTime(item.createTime) }}</div>
            <div v-if="item.type == 'reply'" class="flex items-center justify-end">
              <div class="bg-#EFF6FF py-20px px-24px box-border max-w-85% rounded-10px">{{
                item.content
              }}</div>
              <img loading="lazy" src="@/assets/public/head_default.png" alt="" class="w-66px h-66px ml-20px" />
            </div>
            <div v-if="item.type == 'consult'" class="flex items-center justify-start">
              <img loading="lazy" src="@/assets/public/head_default.png" alt="" class="w-66px h-66px mr-20px" />
              <div class="bg-#F6F7F8 py-20px px-24px box-border max-w-85% rounded-10px">{{
                item.content
              }}</div>
            </div>
          </div>
        </refreshList>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import pushIcon from '@/assets/soulStation/push.png';
import { selectDialogueList, sendMessage } from '@/api/soulStation';
import utils from '@/utils/utils';
import defaultImg from '@/assets/public/head_default.png';
import dayjs from 'dayjs';
import refreshList from '@/components/refreshList/index.vue';
import { useScroll, useWindowScroll } from '@vueuse/core';
import { showFailToast } from 'vant';
const route = useRoute();
const listData = ref([]);
const searchVal = ref();
function fixDateTime(time: string | number | dayjs.Dayjs | Date | null | undefined) {
  const day = dayjs(dayjs().format('YYYY-MM-DD')).diff(dayjs(time).format('YYYY-MM-DD'), 'day');
  switch (day) {
    case 0:
      return '今天' + dayjs(time).format('HH:mm');
    case 1:
      return '昨天' + dayjs(time).format('HH:mm');
    default:
      return dayjs(time).format('YYYY-MM-DD HH:mm');
  }
}
//发送
function toSendMessage() {
  sendMessage({
    psychologicalExpertId: route.query.psychologicalExpertId,
    psychologicalUserId: route.query.psychologicalUserId,
    content: unref(searchVal),
  }).then(res => {
    if (res.code == 200) {
      Data.value.listData.push({ type: 'reply', content: searchVal.value, createTime: new Date() });
      searchVal.value = '';
      setTimeout(() => {
        scrollToEnd();
      }, 300);
    } else {
      showFailToast(res.message)
    }
  });
}
const lastScrollHeight = ref(null);
const scrollBox = ref<HTMLElement | null>(null);
// const {  y } = useWindowScroll()

const { y } = useScroll(scrollBox);

const displayY = computed({
  get() {
    return y.value;
  },
  set(val) {
    y.value = val;
  },
});

const scrollHeight = ref(null);
//消息自动滚到
function scrollToEnd() {
  displayY.value = scrollBox.value.scrollHeight;
}
const Data = ref({
  pageNum: 1,
  listData: [],
});
const isMore = ref(true);

// 加载更多
const onLoadMore = () => {
  if (loadMoreRef.value && !isMore.value) {
    loadMoreRef.value.onLoadSuc(Data.value.listData.length, Data.value.listData.length);
    return;
  }
  Data.value.pageNum++;
  getList();
};
//搜索列表
const loadMoreRef = ref(null);

//获取列表
function getList() {
  selectDialogueList({
    pageNum: Data.value.pageNum,
    pageSize: 6,
    psychologicalExpertId: route.query.psychologicalExpertId,
    psychologicalUserId: route.query.psychologicalUserId,
  }).then(res => {
    if (res.code == 200) {
      isMore.value = res.hasNextPage;
      res.data.forEach((item: Recordable) => {
        item.avatar = utils.judgeStaticUrl(item.avatar) || defaultImg;
      });
      if (Data.value.pageNum === 1) Data.value.listData = [];
      Data.value.listData = Data.value.listData.concat(res.data);
      Data.value.listData.sort(utils.compare('createTime', 'positive'));
      setTimeout(() => {
        scrollToEnd();
      }, 300);

      //重置刷新状态及 判断是否加载完成
      if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(Data.value.listData.length, res.total);
      }
    }
  });
}
onMounted(async () => {
  await nextTick();
  if (route.query.psychologicalExpertId) {
    unref(scrollBox) && getList();
  }
});
</script>

<style lang="less" module>
.chat {
  :global {
    .van-search {
      &__content {
        border-radius: 33px;
      }
    }
  }
}
</style>
