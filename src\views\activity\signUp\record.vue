<template>
  <div class="w-full h-full">
    <div class="relative w-full">
      <div class="absolute left-60px top-70px">
        <img loading="lazy" alt="" class="h-43px mb-10px" :src="BtnStr[record?.state]?.icon" />
        <div class="!text-[#fff] !text-28px">{{ BtnStr[record?.state]?.text }}</div>
      </div>
      <img loading="lazy" alt="" class="w-full" :src="BtnStr[record?.state]?.bg" />
    </div>
    <div class="px-30px pb-120px">
      <Title title="报名信息" />
      <div class="px-10px text-29px text-[#666] mt-10px">
        <div>
          <div v-if="record && record.answerRecord" class="flex leading-45px mb-15px"
            v-for="(item, index) in record.answerRecord" :key="index">
            <div class="flex-shrink-0 min-w-125px " style="text-align-last: justify">{{ item.title }}</div>：
            <div class="text-[#333]">{{ item.content }}</div>
          </div>
          <!--    没有报名表单      -->
          <div v-else>
            <div class="flex leading-45px mb-15px">
              <div class="flex-shrink-0 min-w-125px " style="text-align-last: justify">姓名</div>：
              <div class="text-[#333]">{{ record.userName }}</div>
            </div>
            <div class="flex leading-45px mb-15px">
              <div class="flex-shrink-0 min-w-125px " style="text-align-last: justify">所属工会</div>：
              <div class="text-[#333]">{{ record.companyName ?? '-' }}</div>
            </div>
          </div>
          <div class="flex leading-45px mb-20px items-center">
            <div class="flex-shrink-0 min-w-125px text-justify">提交时间</div>：
            <div class="text-[#333] leading-40px">{{ record.createTime }}</div>
          </div>
          <div class="flex leading-45px mb-20px items-center" v-if="record.state !== 'wait' && record.auditTime">
            <div class="flex-shrink-0 min-w-125px text-justify">审核时间</div>：
            <div class="text-[#333] leading-40px">{{ record.auditTime }}</div>
          </div>

          <div class="flex leading-45px mb-20px items-center" v-if="record.state === 'refuse' && record.auditOpinion">
            <div class="flex-shrink-0 min-w-125px text-justify">失败原因</div>：
            <div class="text-[#E14026] leading-40px">{{ record.auditOpinion }}</div>
          </div>
          <div class="flex leading-45px mb-20px items-center" v-if="record.signInFlag == 'y'">
            <div class="flex-shrink-0 min-w-125px text-justify">签到状态</div>：
            <div class="text-[#333] leading-40px">已签到</div>
          </div>
          <div class="flex leading-45px mb-20px items-center" v-if="record.signInFlag == 'y'">
            <div class="flex-shrink-0 min-w-125px text-justify">签到时间</div>：
            <div class="text-[#333] leading-40px">{{ record.signInTime }}</div>
          </div>

        </div>
      </div>
    </div>
    <div class="flex justify-center w-full" v-if="record.state === 'refuse'">
      <FixedBottom class="w-100/100" name="重新提交" @click="toSignUp" />
    </div>

  </div>
</template>
<script setup>
import passMsg from '@/assets/activity/signUp/pass-msg.png'
import passBg from '@/assets/activity/signUp/pass.jpg'
import waitMsg from '@/assets/activity/signUp/wait-msg.png'
import waitBg from '@/assets/activity/signUp/wait.jpg'
import refuseMsg from '@/assets/activity/signUp/refuse-msg.png'
import refuseBg from '@/assets/activity/signUp/refuse.jpg'
import { useUserStore } from "@/store/modules/user";
import { applicationRecord, signUp } from "@/api/activity";
import { showConfirmDialog, showToast } from "vant";
import Title from '@/components/Title/vitalityTitle.vue';
import FixedBottom from "@/components/Button/fixedBottom.vue";
import router from "@/router";

const useStore = useUserStore();
const route = useRoute()
const record = ref({})

// 计算属性
const activityDetail = computed(() => useStore.activityDetail || {});

const BtnStr = {
  wait: {
    text: '请耐心等待~',
    icon: waitMsg,
    bg: waitBg,
  },
  pass: { text: '', icon: passMsg, bg: passBg },
  refuse: {
    text: '请重新提交报名信息',
    icon: refuseMsg,
    bg: refuseBg,
  },
};

const toSignUp = async () => {
  if (activityDetail.value.signUpInfo.writeFlag !== 'y') {
    const platform = sessionStorage.getItem('platform')
    const { code } = await signUp({
      autoId: record.value.autoId,
      activityId: activityDetail.value.activityId,
      platform,
    })
    if (code === 200) {
      await getSignUpRecord()
      await showConfirmDialog({
        title: '温馨提示',
        message: '报名信息提交成功~',
        confirmButtonText: '我知道了',
      })
    }
    return
  }

  router.push({
    path: '/activityHome/signUpActivity/form',
    query: {
      autoId: record.value.autoId
    }
  })
}

const getSignUpRecord = async () => {
  const { code, data, message } = await applicationRecord({
    activityId: route.query.activityId || sessionStorage.getItem('activityId'),
  });
  if (code) {
    record.value = data;

  } else {
    showToast(message)
  }
}
onMounted(() => {
  getSignUpRecord()
})

</script>
<style scoped lang="scss"></style>
