<template>
    <div class="news_detail flex flex-col h-[100vh] max-w-100% bg-#F6F7F8">
        <van-skeleton title :row="10" :loading="loading">
            <div class="content flex-1 py-50px box-border">
                <div class="news_title px-50px text-36px text-[#2D2D2D] font-bold">
                    {{ detailInfo?.newsTitle }}
                </div>
                <div class="px-50px text-24px text-#333 mt-36px">
                    <div class="flex justify-between  pb-18px items-center" style="border-bottom: 1px solid #999;">
                        <div>{{ detailInfo?.publishTime ? dayjs(detailInfo?.publishTime).format('YYYY-MM-DD HH:mm') :
                            '--' }}
                        </div>
                        <div class="text-#999" v-if="detailInfo?.newsSource"> {{ detailInfo?.newsSource || '--' }}</div>
                    </div>
                </div>
                <div class="rich_text mt-20px px-50px text-[#333] min-h-350px" v-html="detailInfo?.newsDetailsContent"
                    v-if="!detailInfo?.whetherExternalLink">
                </div>
                <nestedWindow :url="detailInfo?.externalLinkAddress" v-else />
            </div>
        </van-skeleton>

    </div>
</template>

<script lang="ts" setup>
import nestedWindow from "@/views/public/nestedWindow.vue"
import { showFailToast } from "vant";
import { previewGetNewsDetails } from "@/api/news/index";
import { useRoute } from "vue-router";
const route = useRoute();
import dayjs from "dayjs";
const detailInfo = ref(null);
const loading = ref(true);//页面加载
//新闻详情
const getDetail = async () => {
    const res = await previewGetNewsDetails({ newsInfoId: route.query.newsId, platformType: 30 })
    if (res.data) {
        detailInfo.value = res.data;
        // 2025-1-8 外部链接在新闻详情页嵌套展示
        // if (detailInfo.value?.whetherExternalLink) {
        //     window.location.href = detailInfo.value?.externalLinkAddress
        // }
    } else {
        showFailToast(res.data)
    }
    loading.value = false
}

onMounted(() => {
    getDetail();
})
</script>

<style scoped lang="scss">
.rich_text {
    width: 100%;

    :deep(img, image) {
        max-width: 100% !important;
        height: 100% !important;
    }
}

.news_detail {
    box-shadow: 0px 0px 21px 0px rgba(195, 194, 194, 0.31);
    overflow-x: hidden;

    image,
    img {
        max-width: 100% !important;
        max-height: 100% !important;
    }

    .rich_text {

        // 处理富文本样式
        :deep(P) {
            max-width: 100%;

            img {
                max-width: 100%;
            }
        }

        &::after {
            content: "";
            display: table;
            clear: both;
        }
    }

    .input {
        :deep(.van-cell) {
            background-color: transparent;
        }

        :deep(.van-field__control) {
            font-size: 26px;
        }

        .active {
            background-color: #5ca5ff;
            color: #fff;
        }
    }


}

.recomend {
    .refresh {
        border: 1px solid #5CA5FF;
    }
}

.header {
    .title {
        position: relative;
        padding-left: 10px;
    }

    .title::before {
        width: 5px;
        height: 30px;
        background-color: #5CA5FF;
        border-radius: 3px;
        content: "";
        display: block;
        margin-right: 10px;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
    }
}

.border_top_20px {
    border-top: 20px solid #F2F2F2;
}

.border_bottom_1px {
    border-bottom: 1px solid #F2F2F2;
}

:deep(.van-skeleton) {
    background-color: #fff !important;
    height: 100%;
}
</style>
