<template>
    <div>
        <van-popup :show="showPop">
            <div class="detail-num-box flex flex-col items-center pt-160px ">
                <div class="logo w-184px h-184px mb-10px flex items-center justify-center bg-#FFE5F1 rounded-15px">
                    <img loading="lazy" class="w-160px h-160px rounded-15px"
                        :src="utils.judgeStaticUrl(props.detailObj.avatar, true)" alt="">
                </div>
                <div class="flex items-center"><span class="text-31px text-#444444">{{ props.detailObj.nickname
                        }}</span>
                    <span class="ml-10px mt-3px flex items-center justify-center" :class="detailObj.gender"><img
                            loading="lazy" class="w-19px h-19px" :src="props.detailObj.gender == 'male' ? male : female"
                            alt=""></span>
                </div>
                <span class="leading-50px text-31px text-#444 mt-40px">
                    <p class="my-0 text-center">对方向您发来了TA的</p>
                    <p class="my-0 text-center">联系方式</p>
                </span>
                <div @click="utils.getTel({ number: detailObj.phone })"
                    class="mt-30px  w-523px h-100px bg-#fff rounded-20px border-solid border-1px border-#5AA4FF flex items-center justify-center">
                    <span class="text-30px text-#333">请联系我：<span class="text-40px text-#0C79F4 font-bold">{{
                            detailObj.phone }}</span></span>
                </div>
                <div class="flex justify-around mt-60px">

                    <span style="background: linear-gradient(90deg,  #FAA8AB, #FF7097); " @click="close()"
                        class="ml-24px flex items-center justify-center w-345px h-72px bg-#f3f3f3 rounded-39px text-29px text-#fff">好的，感谢</span>

                </div>
            </div>
        </van-popup>
    </div>
</template>

<script lang="ts" setup>
import male from '@/assets/friendShip/male_icon_w.png'
import female from '@/assets/friendShip/female_icon_w.png'
import utils from '@/utils/utils';
const show = ref(false)
const emit = defineEmits(['closePopup']);
const props = defineProps({
    showPop: {
        type: Boolean,
        default: false
    },
    detailObj: {
        type: Object,
        default: {
            avatar: '',
            nickname: '',
            gender: '',

        }
    },
})
function close() {
    // show.value = false;
    emit('closePopup', false)
}
</script>

<style lang="scss" scoped>
.van-popup {
    background: transparent;

    .female {
        width: 30px;
        height: 30px;
        background: linear-gradient(90deg, #FAA8AB, #FF7097);
        border-radius: 50%;
    }

    .male {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: linear-gradient(90deg, #9FC9FF, #4196FF);
    }
}

.detail-num-box {
    width: 625px;
    height: 860px;
    background: url('@/assets/friendship/contact_bg.png') no-repeat;
    background-size: 100% 100%;
    background-position: center center;
}
</style>