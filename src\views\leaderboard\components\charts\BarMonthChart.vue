<template>
  <div class="echart-box" style="width:100%;height:100%">
    <div ref="echartRef" class="w-full h-full"></div>
  </div>
</template>

<script lang="ts" setup>
// 饼图
import { useECharts } from '@/utils/useECharts'
import { Ref, ref, watch, onMounted, inject } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  dataSource: {
    type: Object,
    default: {
      x: [
        '7月',
        '8月',
        '9月',
        '10月',
        '11月',
        '12月',
      ],
      y: ['1200', '1400', '1008', '1411', '1026', '1200', ],
    },
  }, //color
})

const echartRef = ref<HTMLDivElement | null>(null)
const { setOptions } = useECharts(echartRef as Ref<HTMLDivElement>)

const initChart = () => {
  var e = document.body.clientWidth

  let data = { x: [], y: [], y2: [] }
  data.x = props.dataSource?.x
  data.y = props.dataSource?.y

  // 绘制图表
  var option = {
    tooltip: {
      show: true,
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      }
    },
    grid: {
      top: '20%',
      left: '0%',
      right: '0%',
      bottom: '0%',
      containLabel: true,
    },
    legend: {
      show: true,
      icon: 'square',
      orient: 'horizontal',
      top: '0%',
      right: '0%',
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 30,
      textStyle: {
        color: '#666666',
        fontSize: 12,
      },
    },
    xAxis: [
      {
        type: 'category',
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitArea: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        boundaryGap: true,
        axisLabel: {
          show: true,
          interval: 0,
          // rotate: 30,
          fontSize: 12,
          lineHeight: 15,
          margin: 10,
          color: '#999',
          // hideOverlap: true,
        },
        data: data.x,
      },
    ],
    yAxis: [
      {
        name: '访问量(万)',
        nameTextStyle: {
          color: '#999',
        },
        type: 'value',
        min: 0,
        minInterval: 1,
        splitLine: {
          show: true,
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#E6E6E6',
          },
        },
        axisLabel: {
          // margin: 20 / 1920 * e,
          textStyle: {
            color: '#999',
          },
        },
        axisTick: {
          show: false,
        },
      },
      {
        name: '参与人数(个)',
        nameTextStyle: {
          color: '#999',
          fontSize: 12,
        },
        type: 'value',
        min: 0,
        minInterval: 1,
        splitLine: {
          show: true,
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#E6E6E6',
          },
        },
        axisLabel: {
          // margin: 20 / 1920 * e,
          textStyle: {
            color: '#999',
          },
        },
        axisTick: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: '参与人数',
        type: 'bar',
        barWidth: 10,
        yAxisIndex: 1,
        label: {
          show: false,
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [{
            offset: 0,
            color: '#FFD35D'

          },
          {
            offset: 1,
            color: '#FFB017'
          }
          ]),
          borderRadius: 8
        },
        data: data.y,
      },
      {
        name: '访问量',
        type: 'bar',
        barWidth: 10,
        label: {
          show: false,
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [{
            offset: 0,
            color: '#61E6A7'
          },
          {
            offset: 1,
            color: '#1BBF71'
          }
          ]),
          borderRadius: 8
        },
        data: data.y,
      },
    ],
  }

  setOptions(option)
}

onMounted(() => {
  initChart()
})

// 监听dataSource
watch(
  () => props.dataSource,
  (newValue, oldValue) => {
    // console.log('watch', newValue, oldValue);
    initChart()
  },
  { deep: true, immediate: true }
)
</script>
<style scoped lang="scss">
.echart-box {}
</style>
