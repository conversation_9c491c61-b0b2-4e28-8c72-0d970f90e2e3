import { KeepAlive } from 'vue';

export default [
    {
        path: '/friendship',
        name: 'Friendship',
        component: () => import('@/views/friendship/index.vue'),
        meta: {
            title: '单身联谊',
            isShowTabBar: false,
            isBack:true,
            keepAlive:true,
            updatePath:['/home','/friendship/Notice']
        }
    },
    {
        path: '/friendship/detail',
        name: 'FriendshipDetail',
        component: () => import('@/views/friendship/detail.vue'),
        meta: {
            title: '单身联谊-查看详情',
            isShowTabBar: false,
            isBack:true,
            keepAlive:true,
            updatePath:['/friendship']
        }
    },
    {
        path: '/friendship/Notice',
        name: 'FriendshipNotice',
        component: () => import('@/views/friendship/myInformation/notice.vue'),
        meta: { 
            title: '单身联谊-消息通知',
            isShowTabBar: false,
            isBack:true
        }
    },
    {
        path: '/friendship/myFollowList',
        name: 'MyFollowList',
        component: () => import('@/views/friendship/myInformation/myFollowList.vue'),
        meta: { 
            title: '单身联谊-我的关注',
            isShowTabBar: false,
            isBack:true
        }
    },
    {
        path: '/friendship/MyApply',
        name: 'MyApply',
        component: () => import('@/views/friendship/myInformation/myApply.vue'),
        meta: { 
            title: '单身联谊-我的申请',
            isShowTabBar: false,
            isBack:true
        }
    },
    {
        path: '/friendship/activity',
        name: 'FriendshipActivity',
        component: () => import('@/views/friendship/activity/index.vue'),
        meta: {
            title: '联谊活动',
            isShowTabBar: false,
            isBack:true,
            keepAlive:true,
            updatePath:['/home','/friendship']
        }
    },
    {
        path: '/friendship/activity/applyDetail',
        name: 'FriendshipActivityApplyDetail',
        component: () => import('@/views/friendship/activity/applyDetail.vue'),
        meta: {
            title: '联谊活动报名详情',
            isShowTabBar: false,
            isBack:true
        }
    },
    {
        path:'/friendship/myProfile',
        name:'FriendshipMyProfile',
        component: () => import('@/views/friendship/profile/my.vue'),
        meta: { 
            title: '个人资料',
            isShowTabBar: false,
            isBack:true,
            keepAlive:true,
            updatePath:['/friendship']
        }
    },
    {
        path:'/friendship/personFillIn',
        name:'FriendshipPersonFillIn',
        component: () => import('@/views/friendship/profile/fillin.vue'),
        meta: { 
            title: '个人资料填写',
            isShowTabBar: false,
            isBack:true
        }
    },
    {
        path: '/friendship/blackList',
        name: 'FriendshipBlackList',
        component: () => import('@/views/friendship/blackList.vue'),
        meta: { 
            title: '单身联谊黑名单',
            isShowTabBar: false,
            isBack:true
        }
    },
    {
        path:'/friendship/square',
        name:'FriendshipSquare',
        component: () => import('@/views/friendship/square/index.vue'),
        meta: { 
            title: '广场互动',
            isShowTabBar: false,
            isBack:true,
            keepAlive:true,
            updatePath:['/friendship']
        },
    },
    {
        path:'/friendship/pushDynamic',
        name:'FriendshipPushDynamic',
        component: () => import('@/views/friendship/profile/pushDynamic.vue'),
        meta: { 
            title: '发布动态',
            isShowTabBar: false,
            isBack:true
        }
    },
    // 动态详情
    {
        path:'/friendship/dynamicDetail',
        name:'FriendshipDynamicDetail',
        component: () => import('@/views/friendship/dnamicDetail.vue'),
        meta: { 
            title: '动态详情',
            isShowTabBar: false,
            isBack:true
        }
    },
    {
        path:'/friendship/personEdit',
        name:'FriendshipPersonEdit',
        component: () => import('@/views/friendship/profile/editInfo.vue'),
        meta: { 
            title: '个人资料编辑',
            isShowTabBar: false,
            isBack:true
        }
    },
    {
        path:'/friendship/editLog',
        name:'FriendshipEditLog',
        component: () => import('@/views/friendship/myInformation/myEditLog.vue'),
        meta: { 
            title: '修改记录',
            isShowTabBar: false,
            isBack:true
        }
    },
]