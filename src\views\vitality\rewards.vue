<template>
  <div class="relative bg-#f5f5f5 min-h-100vh">
    <img loading="lazy" src="@/assets/vitality/jljf_bg.png" alt="" class="w-100% h-360px absolute lrft-0 top-0" />
    <div class="relative px-30px box-border">
      <div class="text-#7A9199 text-28px ml-50px mt-52px mb-20px">我的奖励积分:</div>
      <div style="background: rgba(255, 255, 255, 0.4)"
        class="rounded-33px border-1px border-solid font-bold border-#FFFFFF inline-flex px-46px py-10px box-border ml-50px text-50px text-#FFA200">
        888
      </div>
      <div class="bg-#fff rounded-16px px-16px py-30px box-border mt-43px mb-26px flex items-center justify-between">
        <Title title="积分明细" />
        <div class="h-44px bg-#F0F0F0 rounded-22px px-24px box-border text-28px text-#999 flex items-center"
          @click="Data.showPicker = true">
          2024年 <img loading="lazy" src="@/assets/public/grey_sjx.png" alt="" class="w-14px h-9px ml-10px">
        </div>
      </div>
      <div>
        <div class="bg-#fff rounded-16px p-29px box-border mb-19px flex items-center" v-for="(item, index) in 5"
          :key="index">
          <div class="flex-1">
            <div class="text-30px text-#333 font-medium">低碳环保宣传活动</div>
            <div class="text-#999 text-24px mt-20px">2024-05-06 10:00:00</div>
          </div>
          <div class="flex items-end text-#FE5552">
            <div class="text-40px">+5</div>
            <div class="text-24px ml-8px mb-6px">积分</div>
          </div>
        </div>
      </div>
    </div>
    <van-popup v-model:show="Data.showPicker" position="bottom">
      <van-picker :columns="Data.columns" @confirm="onConfirm" @cancel="Data.showPicker = false" />
    </van-popup>
  </div>
</template>
<script lang="ts" setup>
import Title from "@/components/Title/vitalityTitle.vue/";
const Data = ref({
  showPicker: false,
  columns: [
    { values: ['1', '2', '3'] }
  ],
})
function onConfirm(value: any, index: number) {
  console.log('onConfirm', value, index);
}
</script>
<style lang="scss" scoped></style>