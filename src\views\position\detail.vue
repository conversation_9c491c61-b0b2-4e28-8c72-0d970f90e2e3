<template>
  <div :class="$style.detail">
    <van-swipe class="h-[419px]" :autoplay="3000">

      <van-swipe-item v-for="image in images" :key="image">
        <img loading="lazy" :src="image" v-previewImg="image" class="h-full w-full object-cover" />
      </van-swipe-item>

      <template #indicator="{ active, total }">
        <div class="custom-indicator px-[10px] py-1 flex items-center" v-if="total > 0">
          <img loading="lazy" :src="iconImage" class="w-[25px] h-[22px] mr-1" />
          <span>{{ active + 1 }}/{{ total }}</span>
        </div>
      </template>
    </van-swipe>

    <!-- 详情 -->
    <DetailTop />
    <!-- 列表 -->
    <DetailCenter />

    <!-- 点赞分享扫码 -->
    <DetailBottom />

    <van-back-top :class="$style.custom" />
  </div>
</template>

<script lang="ts" setup>
import iconImage from '@/assets/position/icon-image.png';
import DetailTop from './components/DetailTop.vue';
import DetailBottom from './components/DetailBottom.vue';
import DetailCenter from './components/DetailCenter.vue';
import { view } from '@/api/position';
import { isEmpty, map, split } from 'lodash-es';
import { exchangeImg } from './utils';
import defaultPosition from '@/assets/position/default-position.png';

const route = useRoute();

const detailRecord = ref<Recordable>({});

const images = computed(() =>
  isEmpty(unref(detailRecord).positionImages)
    ? [defaultPosition]
    : map(split(unref(detailRecord).positionImages, ','), v => exchangeImg(v))
);

provide('detailRecord', detailRecord);

onMounted(async () => {
  detailRecord.value = await view(route.query);
});
</script>

<style lang="less" module>
.detail {
  :global {
    background: #f6f7f8;
    overflow-x: hidden;

    * {
      box-sizing: border-box;
    }

    .custom-indicator {
      position: absolute;
      transform: translateX(600px);
      bottom: 130px;
      font-size: 24px;
      color: #fff;
      border-radius: 19px;
      background: rgba(0, 0, 0, 0.5);
    }

    .default-shadow {
      &:active {
        box-shadow: 0 0 10px 0px #5ba5ff;
      }
    }
  }
}

.custom {
  :global {
    width: 80px;
    font-size: 14px;
    text-align: center;
    left: 35px;
    bottom: 140px;
  }
}
</style>
