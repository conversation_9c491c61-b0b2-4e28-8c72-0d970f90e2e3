/* rulesValidator 是 vant表单验证规则 */

//手机号码验证
const telRules = [{
        required: true,
        message: "请输入联系电话",
        trigger: "onBlur"
    },
    {
        // 自定义校验规则
        validator: (value:string)=> {
            // return /^(0|86|17951)?(13[0-9]|15[012356789]|166|17[3678]|18[0-9]|14[57])[0-9]{8}$/.test(value);
            return /^(0|86|17951)?(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/.test(value);
        },
        message: "请输入正确格式",
        trigger: "onBlur"
    }
];
//密码验证
const pwdRules = [{
        required: true,
        message: "密码不能为空",
        trigger: "onBlur"
    },
    {
        // 自定义校验规则
        validator: (value:string)=> {
            // return /^(0|86|17951)?(13[0-9]|15[012356789]|166|17[3678]|18[0-9]|14[57])[0-9]{8}$/.test(value);
            return /^[a-zA-Z0-9]{8}$/.test(value);
        },
        message: "请输入正确格式的密码",
        trigger: "onBlur"
    }
];

//邮箱验证
const emailRules = [{
        required: true,
        message: "邮箱不能为空",
        trigger: "onBlur"
    },
    {
        // 自定义校验规则
        validator: (value:string)=> {
            return /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(value);
        },
        message: "请输入正确格式的邮箱",
        trigger: "onBlur"
    }
];
//邮政编码
const postCodeRules = [{
        required: true,
        message: "邮政编码不能为空",
        trigger: "onBlur"
    },
    {
        // 自定义校验规则
        validator: (value:string)=> {
            return /^[1-9][0-9]{5}$/.test(value);
        },
        message: "请输入正确的邮政编码",
        trigger: "onBlur"
    }
];

//验证码验证
const codeRules = [{
    required: true,
    message: '验证码不能为空',
    trigger: 'onBlur'
}]

//身份证验证
const idCardRules = [{
        required: true,
        message: '请输入身份证号码',
        trigger: 'onBlur'
    },
    {
        // 自定义校验规则
        validator: (value:string)=> {
            return /^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}([0-9]|x|X)$/.test(value);
        },
        message: "请输入正确格式的身份证",
        trigger: "onBlur"
    }
]

//姓名验证
const nameRules = [{
    required: true,
    message: '姓名不能为空',
    trigger: 'onBlur'
}];

//与本人关系验证
const relationRules = [{
    required: true,
    message: '关系不能为空',
    trigger: 'onBlur'
}];


//日期验证
const dateRules = [{
    required: true,
    message: '日期不能为空',
    trigger: 'onBlur'
}];

//区域验证
const areaRules = [{
    required: true,
    message: '区域不能为空',
    trigger: 'onBlur'
}];

//地址验证
const addrRules = [{
    required: true,
    message: '地址不能为空',
    trigger: 'onBlur'
}];

//类型验证
const typeRules = [{
    required: true,
    message: '类型不能为空',
    trigger: 'onBlur'
}];

//选项验证
const optionRules = [{
    required: true,
    message: '类型不能为空',
    trigger: 'onBlur'
}];

//备注验证
const remarksRules = [{
    required: true,
    message: '备注不能为空',
    trigger: 'onBlur'
}];
//选择
const selRules = [{
    required: true,
    message: '请选择',
    trigger: 'onBlur'
}];

//位置验证
const locationRules = [{
    required: true,
    message: '位置不能为空',
    trigger: 'onBlur'
}];

//标题验证
const titleRules = [{
    required: true,
    message: '标题不能为空',
    trigger: 'onBlur'
}];


//描述验证
const describeRules = [{
    required: true,
    message: '描述不能为空',
    trigger: 'onBlur'
}];

//工会验证
const unionRules = [{
    required: true,
    message: '工会不能为空',
    trigger: 'onBlur'
}];

//图片上传验证
const uploadImgRules = [{
    required: true,
    message: '图片不能为空',
    trigger: 'onBlur'
}];

//建议验证
const suggestRules = [{
    required: true,
    message: '建议不能为空',
    trigger: 'onBlur'
}];
//通用  必填项，没有其他验证，只判断是否填入   传入提示文字前缀
export function generalRules(text:string) {
    return [{
        required: true,
        message: `${text}不能为空`,
        trigger: 'onBlur'
    }]
}
//性别验证
const sexRules = [{
    required: true,
    message: '请选择性别',
    trigger: 'onBlur'
}];
export {
    telRules,
    emailRules,
    postCodeRules,
    nameRules,
    codeRules,
    selRules,
    relationRules,
    dateRules,
    idCardRules,
    areaRules,
    addrRules,
    typeRules,
    remarksRules,
    optionRules,
    locationRules,
    titleRules,
    describeRules,
    unionRules,
    uploadImgRules,
    suggestRules,
    sexRules,
    pwdRules
}