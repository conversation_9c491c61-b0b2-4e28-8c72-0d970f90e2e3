<template>
  <div class="my-groups-page bg-#f6f7f8">
    <div class="tab-box">
      <van-tabs v-model:active="Data.tab.active" sticky color="#5AA4FF" title-active-color="#5AA4FF"
        title-inactive-color="#333333" line-width="30" @click-tab="onClickTab">
        <van-tab :title="item.name" v-for="(item, index) in Data.tab.nav" :key="index"
          title-class="tab-title"></van-tab>
      </van-tabs>
    </div>
    <div class="px-[40px] bg-[#fff] box-border two-tab-box">
      <van-tabs v-model:active="Data.tab.nav[Data.tab.active].twoTabActive" sticky type="card"
        title-active-color="#5AA4FF" title-inactive-color="#333333" @click-tab="onClickTwoTab"
        v-if="Data.twoTab && Data.twoTab.length">
        <van-tab :title="item.label" v-for="(item, index) in Data.twoTab" :key="index"></van-tab>
      </van-tabs>
    </div>
    <div class="mt-30px px-30px box-border">
      <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
        <interestGroupList :data="Data.groupList" :showBtn="false" @details="toDetail"
          v-if="Data.groupList && Data.groupList.length">
          <template #bContent="{ item }">
            <div class="bg-#f6f7f8 rounded-10px px-30px py-20px box-border text-28px text-#666 mt-[22px]"
              v-if="(Data.twoTab[Data.tab.nav[Data.tab.active].twoTabActive].value == 'refuse' && item.refuseQuestion) || (Data.twoTab[Data.tab.nav[Data.tab.active].twoTabActive].value == 'refuse' && item.auditRemarks)">
              <span class="text-#999">拒绝理由：</span>{{
                Data.tab.active == 0 ? item.refuseQuestion ? item.refuseQuestion : '无' :
                  Data.tab.active == 1 ? item.auditRemarks ? item.auditRemarks : '无' : ''
              }}
            </div>
          </template>
          <template #dataPart="{ item }">
            <div class="flex items-center my-[20px]">
              <div class="flex items-center text-[#666] text-[26px]">
                <img loading="lazy" src="@/assets/interest/icon_num.png" alt="" class="w-26px h-26px mr-10px" />
                成员：{{ item.joinSum }}/{{ item.maxPeople }}
              </div>
            </div>
          </template>
        </interestGroupList>
      </refreshList>
    </div>
  </div>
</template>
<script lang="ts" setup>
import interestGroupList from '@/components/List/interestGroupList.vue';
import { findMyGroupVoList, findMyGroupCreateVoList } from '@/api/digitalSchools/group';
import refreshList from '@/components/refreshList/index.vue';
import router from '@/router';
import { useDictionary } from '@/store/modules/dictionary';
import { judgeStaticUrl } from '@/utils/utils';
const dictionary = useDictionary();
const Data = ref({
  tab: {
    active: 0,
    nav: [
      { name: '加入的小组', code: 'my', api: findMyGroupVoList, params: 'pass', twoTabActive: 0 },
      { name: '创建的小组', code: 'apply', api: findMyGroupCreateVoList, twoTabActive: 0 },
    ],
  },
  twoTab: [],
  groupList: [],
  pageNum: 1,
});
const loadMoreRef = ref('');
onMounted(() => {
  Data.value.twoTab =
    dictionary.getDictionaryOpt?.['groupStudyStatus'] &&
    dictionary.getDictionaryOpt?.['groupStudyStatus'];
  Data.value.twoTab.forEach(item => {
    if (item.value == 'wait') {
      item.label = '申请中';
    } else if (item.value == 'refuse') {
      item.label = '未通过';
    } else if (item.value == 'pass') {
      item.label = '已通过';
    }
  });
  getGroupList();
});
//学习小组列表
function getGroupList() {
  let activeObj = Data.value.tab.nav[Data.value.tab.active];
  activeObj['api']({
    pageNum: Data.value.pageNum,
    pageSize: 10,
    auditStatus: Data.value.twoTab[activeObj.twoTabActive].value,
  }).then(res => {
    if (res.code == 200) {
      if (Data.value.pageNum == 1) {
        Data.value.groupList = res.data || [];
      } else {
        Data.value.groupList = Data.value.groupList.concat(res.data);
      }
      Data.value.groupList.forEach(item => {
        item.logo = judgeStaticUrl(item.groupCover);
      });
      //重置刷新状态及 判断是否加载完成
      if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(Data.value.groupList.length, res.total);
      }
    }
  });
}

// 刷新
const onRefreshList = () => {
  Data.value.pageNum = 1;
  getGroupList();
};
// 加载更多
const onLoadMore = () => {
  Data.value.pageNum++;
  getGroupList();
};

/**
 * 点击状态标签页的处理函数
 *
 * @param item 被点击的标签页对象
 */
function onClickTab(item: any) {
  Data.value.tab.active = item.name;
  Data.value.pageNum = 1;
  if (loadMoreRef.value) loadMoreRef.value.resetStatus();
  onRefreshList();
}
/**
 * 点击类型标签页时触发的事件处理函数
 *
 * @param item 标签页项对象
 */
function onClickTwoTab(item: any) {
  let activeObj = Data.value.tab.nav[Data.value.tab.active];
  Data.value.twoTab[activeObj.twoTabActive].twoTabActive = item.name;
  // Data.value.twoTab.active = item.name;
  Data.value.pageNum = 1;
  if (loadMoreRef.value) loadMoreRef.value.resetStatus();
  onRefreshList();
}
function toDetail(item: any) {
  let active = Data.value.tab.nav[Data.value.tab.active].twoTabActive;
  if (Data.value.twoTab[active].value == 'pass') {
    router.push({
      path: '/digitalSchool/studyGroup/groupDetails',
      query: {
        groupBizId: item.groupBizId,
        groupId: item.groupId,
      },
    });
  }
}
</script>
<style lang="scss" scoped>
.my-groups-page {
  background: #f6f7f8;
  min-height: 100vh;
  padding-bottom: 30px;
  box-sizing: border-box;

  .tab-box {
    :deep(.tab-title) {
      font-weight: 400;
      font-size: 32px;
      color: #333333;
    }

    :deep(.van-tabs--line .van-tabs__wrap) {
      height: 76px;
    }

    :deep(.van-tab) {
      line-height: 1;
      height: 100%;
      font-size: 30px;
    }

    :deep(.van-tabs__nav--line) {
      height: 100%;
    }

    :deep(.van-tab--active) {
      font-weight: 400;
      font-size: 32px;
      color: #5aa4ff;
    }
  }

  .two-tab-box {
    width: calc(100% - 80px);
    margin: 20px auto 30px;
    padding: 10px;

    :deep(.van-tab) {
      line-height: 1;
      height: 100%;
      font-size: 28px;
    }

    :deep(.van-tabs__nav--line) {
      height: 100%;
    }

    :deep(.van-tab--active) {
      background-color: #fff;
    }

    :deep(.van-tabs__nav--card) {
      border: none;
      height: 46px;
    }

    :deep(.van-tab--card) {
      border-right: none;
    }

    :deep(.van-tab--card):nth-child(1) {
      position: relative;

      &::after {
        content: '';
        width: 1px;
        height: 29px;
        background: #e5e5e5;
        position: absolute;
        right: 0;
      }
    }

    :deep(.van-tab--card):nth-child(2) {
      position: relative;

      &::after {
        content: '';
        width: 1px;
        height: 29px;
        background: #e5e5e5;
        position: absolute;
        right: 0;
      }
    }

    :deep(.tab-title) {
      font-weight: 400;
      font-size: 28px;
      color: #333333;
    }

    :deep(.van-tab--active) {
      font-weight: 400;
      font-size: 30px;
      color: #5aa4ff;
    }
  }
}
</style>
