export default [
    {
      path: '/workerCraftsman',
      name: 'workerCraftsman',
      component: () => import('@/views/workerCraftsman/index.vue'),
      meta: {
        title: '南充工匠',
        isShowTabBar: false,
        isBack: true,
        keepAlive:true,
        updatePath:['/home']
      },
    },
    {
      // 劳模栏目通用页面
      path: '/workerCraftsman/subject/:type',
      name: 'wokerSubject',
      component: () => import('@/views/workerCraftsman/subject/index.vue'),
      meta: { 
        title: '',
        isShowTabBar: false,
        isBack: true,
      },
    },
    {
      // 劳模申报流程页面
      path: '/workerCraftsman/subject/applyFlow',
      name: 'wokerApplyFlow',
      component: () => import('@/views/workerCraftsman/subject/applyFlow.vue'),
      meta: { 
        title: '',
        isShowTabBar: false,
        isBack: true,
      },
    },
    {
      // 劳模介绍详情页
      path: '/workerCraftsman/subject/wokerDetail',
      name: 'wokerDetail',
      component: () => import('@/views/workerCraftsman/subject/workerDetail.vue'),
      meta: { 
        title: '劳模详情',
        isShowTabBar: false,
        isBack: true,
      },
    },
    // 劳模工匠认证表单
    {
      path: '/workerCraftsman/auth/form',
      name: 'wokerAuthForm',
      component: () => import('@/views/workerCraftsman/auth/form.vue'),
      meta: { 
        title: '劳模工匠认证',
        isShowTabBar: false,
        isBack: true,
      },
    },
     // 劳模认证详情
    {
      path: '/workerCraftsman/auth/info',
      name: 'wokerAuthInfo',
      component: () => import('@/views/workerCraftsman/auth/info.vue'),
      meta: { 
        title: '劳模工匠认证',
        isShowTabBar: false,
        isBack: true,
      },
    },
    // 劳模认证审核结果
    {
      path: '/workerCraftsman/auth/status',
      name: 'wokerAuthStatus',
      component: () => import('@/views/workerCraftsman/auth/status.vue'),
      meta: { 
        title: '认证结果',
        isShowTabBar: false,
        isBack: true,
      },
    }
  ];