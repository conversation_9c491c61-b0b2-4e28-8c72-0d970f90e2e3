<template>
  <div class="w-full py-30px px-30px bg-[#F9F9F9]">
    <div v-if="couponRecord && couponRecord.autoId" class="bg-[#fff]  px-30px pb-50px rounded-36px"
      style="box-shadow: 0px 3px 10px 0px rgba(119,151,203,0.11);">
      <div class="flex pt-90px pb-50px flex-col text-[#333] items-center justify-center">
        <div class="w-450px text-[38px] text-center">
          {{ info.couponName }}
        </div>
        <div class="text-#333333 font-500 text-28px truncate my-8px">
          {{ info?.couponType === 'noLimit' ? '无门槛优惠券' :
            `满${Number(info?.amountLimit)}减${Number(info?.discountAmount)}元` }}
          {{
            info?.couponType === 'discount' && info?.discountAmount ? `，最高减${Number(info?.discountAmount)}元` : ''
          }}
        </div>
        <div class="w-280px h-280px my-30px relative">
          <img loading="lazy" v-if="couponRecord.state === 'n' && couponRecord.codeBase64"
            :src="couponRecord.codeBase64" alt="" class="w-full h-full" />
          <img loading="lazy" src="@/assets/activity/used.png" alt="" v-else class="w-full h-full">
          <div v-if="!couponRecord.codeBase64"
            class="bg-#F1F1F1 text-26px rounded-6px py-6px px-14px absolute top-1/2 left-1/2 -translate-1/2">{{
              couponRecord.stateMsg }}</div>
        </div>

        <!-- <div v-if="couponRecord.state === 'n' && couponRecord.codeBase64" class="text-[35px]">券码:{{
          couponRecord.shortRecordId || '--' }}</div> -->
      </div>
      <van-divider dashed></van-divider>
      <div class="leading-60px">
        <div class="flex justify-between">
          <div class="text-30px text-[#333]">状态</div>
          <div class="text-28px" :style="`color:${couponRecord.stateColor ?? ''}`">{{ couponRecord.stateMsg ?? '-' }}
          </div>
        </div>
        <div class="flex justify-between">
          <div class="text-30px text-[#333]">用户</div>
          <div class="text-28px text-[#666]">{{ couponRecord?.phone }}</div>
        </div>
        <div class="flex justify-between">
          <div class="text-30px text-[#333]">领取日期</div>
          <div class="text-28px text-[#666]">{{ dayjs(couponRecord.createTime).format('YYYY-MM-DD HH:mm') }}</div>
        </div>
        <div>
          <div class="text-30px text-[#333]">兑换有效期</div>
          <div class="text-28px text-[#666]">{{ couponRecord.useStartTime }}至{{ couponRecord.useEndTime }}</div>
        </div>
        <div v-if="info?.couponDesc">
          <div class="text-30px text-[#333]">使用说明</div>
          <div class="text-28px text-[#666]">
            {{ info?.couponDesc }}
          </div>
        </div>
        <div v-if="merchantList && merchantList.length">
          <div class="text-30px text-[#333]">适用商家</div>
          <div v-for="item, index in merchantList" :key="index" class="store flex mt-22px bg-#fff p-14px rounded-10px"
            @click="toAddress(item)">
            <img loading="lazy" :src="utils.judgeStaticUrl(item.companyIcon)"
              class="w-110px h-110px rounded-14px object-cover" />
            <div class="left-info ml-20px flex-1">
              <div class="text-30px">
                {{ item.companyName }}
                <van-icon name="arrow" class="text-[30px] text-#B3B3B3 ml-10px" />
              </div>
              <div class="text-#999999 text-28px leading-40px">
                <img loading="lazy" src="@/assets/public/icon_address-gray.png" class="w-22px h-26px" />
                <span class="ml-12px ">{{ item.address }}</span>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { getMyCouponDetail } from "@/api/activity";
import { showToast, showConfirmDialog } from "vant";
import dayjs from "dayjs";
import utils from '@/utils/utils'
const route = useRoute()
const couponRecord = ref({})
const info = ref({})
const merchantList = ref([]);
const refreshTicketState = ref<any>(null);
//刷新状态
let isPolling = false;
let pollInterval = 10000; // 默认轮询间隔时间（毫秒）
let maxWaitTime = 5 * 60 * 1000; //最大等待时间（毫秒）
let waitTime = 0; //当前等待时间（毫秒）

const getCouponRecord = async () => {
  if (isPolling) return;
  isPolling = true;
  let isWait = true;//是否开始轮询
  try {
    const { code, message, data: { couponInfo, merchantInfos, ...record } } = await getMyCouponDetail({
      couponId: route.query.couponId ?? undefined,
      recordId: route.query.recordId ?? undefined,
    })
    if (code === 200) {
      if (dayjs().format('YYYY-MM-DD') > record.useEndTime && record.state === 'n') {
        record.stateColor = '#FF4344'
        record.stateMsg = '已过期'
      } else if (record.state === 'y') {
        record.stateColor = '#46B15A'
        record.stateMsg = '已核销';
        isWait = false;
      } else {
        record.stateColor = '#FFA025'
        record.stateMsg = '未核销'
      }
      couponRecord.value = record
      info.value = couponInfo
      merchantList.value = merchantInfos
    } else {
      showToast(message)
      isWait = false;
    }
  }
  catch (e) {
    isWait = false;
  }
  finally {
    clearTimeout(refreshTicketState.value)
    if (isWait && waitTime < maxWaitTime) {
      refreshTicketState.value = setTimeout(() => {
        waitTime += pollInterval;
        getCouponRecord()
      }, pollInterval)
    } else {
      waitTime = 0;
    }
    isPolling = false;
  }
}


// function refreshState() {
//   refreshTicketState.value = setInterval(() => {
//     getCouponRecord()
//   }, pollInterval)
// }
const toAddress = (item) => {
  if (!item?.addressCoordinate) return showToast('暂未商家地址信息')
  showConfirmDialog({
    message: '确定开启导航吗？',
  }).then(() => {
    utils.mapNav({
      win: route.fullPath,
      lon: item?.addressCoordinate?.split(',')[0] || '',
      lat: item?.addressCoordinate?.split(',')[1] || '',
      name: item?.address,
    })
  })
}

onMounted(() => {
  getCouponRecord()
  // // 监听用户活动
  // window.addEventListener('touchstart', () => {
  //   pollInterval = 10000; // 活跃时高频轮询
  // });
  // window.addEventListener('touchmove', () => {
  //   pollInterval = 10000; // 活跃时高频轮询
  // });
  // // 监听用户非活动状态
  // window.addEventListener('touchend', () => {
  //   pollInterval = 20000; // 非活跃时低频轮询
  // });
  // window.addEventListener('touchcancel', () => {
  //   pollInterval = 20000; // 非活跃时低频轮询
  // });
  // refreshState()
})
onBeforeUnmount(() => {
  // clearInterval(refreshTicketState.value)
})
</script>
<style scoped lang="scss">
.store {
  border-bottom: 1px solid #e7e7e7;

  &:last-child {
    border-bottom: none;
  }
}
</style>
