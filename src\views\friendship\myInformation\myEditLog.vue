<script lang="ts" setup>
import { getSingleRecord } from '@/api/friendship/profile'

import refreshList from '@/components/refreshList/index.vue';
import applyPopup from '../components/applyPopup.vue';

import router from '@/router';
import { showToast } from 'vant';
const active = ref(0)
const ListObj = ref({
    pageIndex: 1,
    List: [],
    isLoad: false,
    loading: false,
    finished: false,
    finishedText: '',
    RecordCount: 0
})
const getList = () => {
 
    getEditLogList()

}
const btnClick = ref(false)
const loadMoreRef = ref(null)
// 申请和收到的申请
const getEditLogList = () => {
    getSingleRecord({
        pageSize: 10,
        pageNum: ListObj.value.pageIndex++,
        // dataType:'update',
    }).then(res => {
        ListObj.value.loading = false
        ListObj.value.isLoad = false
        ListObj.value.pageIndex == 1 ? ListObj.value.List = res.data
            : ListObj.value.List = [...ListObj.value.List, ...res.data]
        if (loadMoreRef.value) {
            loadMoreRef.value.onLoadSuc(ListObj.value.length, res.total);
        }
    }).catch(err => {

    })
}
// 加载更多
const onLoadMore = () => {
    ListObj.value.pageIndex++;
    getList();
};
const popshow = ref(false)
const detailObj = ref({})
const showPop = (item)=>{
    if(item.authorizeStatus =='wait'){
        detailObj.value = item
        popshow.value = true
    }
   
}


// 刷新
const onRefresh = () => {
    ListObj.value.List = []
    ListObj.value.pageIndex = 1
    getList()
}
// 打开联系方式弹窗
const showApplyPop = ref(false)
const popupType = ref('error')
const popupMsg = ref('')
const popuTitle=ref('')
function showPopFn(item:any){
    switch (item.auditStatus){
        case 'pass':
        popupType.value = 'editSuccess'
            popupMsg.value = '点击我的资料 查看最新详情 '
            popuTitle.value = '恭喜!审核通过'
            showApplyPop.value = true
            break
            case 'wait':
            popupType.value = 'wait'
            popupMsg.value = '约1~3个工作日 请耐心等候'
            popuTitle.value = '审核中... '
            showApplyPop.value = true
            break
            case 'refuse':
            popupType.value = 'checkError'
            popupMsg.value = item.auditRemarks
            popuTitle.value = '审核失败'
            showApplyPop.value = true
            break
    }
   
}
//详情
function closeApplyPopup() {
    showApplyPop.value = false
}
onMounted(() => {
    getList();
})
</script>
<template>
    <div class="follow-list">
        <div class="list-box">
            <refreshList key="relist" @onRefreshList="onRefresh" @onLoadMore="onLoadMore" ref="loadMoreRef">
                <div class="follow-item" v-for="(item, index) in ListObj.List" :key="index" >
                    <div class="live-info flex justify-between items-start">
                        <div class="info-left flex">
                        
                            <div class="info-text">
                                <div class="name text-28px text-#333 font-500 mb-15px">修改了个人资料</div>
                                <div class="text-24px text-#B3B3B3">{{item.updateTime}}</div>
                            </div>
                        </div>
                


                        <span  class="detail-btn" :class="item.auditStatus =='pass'?'text-#4D9DFF':
                        item.auditStatus=='wait'?'text-#FF7841 ':item.auditStatus=='refuse'?'text-#FB2A2A':''" @click="showPopFn(item)" >
                        {{ item.auditStatus =='pass'?'审核成功':
                        item.auditStatus=='wait'?'审核中 ':item.auditStatus=='refuse'?'审核失败':'' }}</span>

                    </div>
                </div>
            </refreshList>

        </div>
        <applyPopup :showPop="showApplyPop" :type="popupType" :title="popuTitle" :msg="popupMsg" @closePopup="closeApplyPopup">
        </applyPopup>
        
    </div>
</template>
<style lang="scss" scoped>
// padding-bottom: 20px;
.van-popup {
    background: transparent;
    .female{
        width: 30px;
height: 30px;
background: linear-gradient(90deg,  #FAA8AB, #FF7097);
border-radius: 50%;
    }
    .male{
        width: 30px;
height: 30px;
border-radius: 50%;
        background: linear-gradient(90deg, #9FC9FF, #4196FF);
    }
}
.detail-num-box{
    width: 625px;
height: 806px;
background: url('@/assets/friendship/contact_bg.png') no-repeat;
    background-size: 100% 100%;
    background-position: center center;
}
.detail-box{
    width: 630px;
    height: 730px;  
    background: url('@/assets/friendship/apply_file_popup.png') no-repeat;
    background-size: 100% 100%;
    background-position: center center;

}
.border-bottom{
    border-bottom: 1px solid;
}
.detail-btn{
    width: 128px;
height: 50px;
background: #FFFFFF;
border-radius: 24px;
border: 2px solid ;
font-weight: 400;
font-size: 24px;
display: flex;
align-items: center;
justify-content: center;
margin-top: 10px;
}
:deep(.nav-box) {
    .van-tabs__wrap {
        height: 98px;

        .van-tab {
            font-weight: 400;
            font-size: 28px;
            color: #8D9099;
        }

        .van-tab--active {
            font-weight: 500;
            font-size: 32px;
            color: #404455;
        }

        .van-tabs__line {
            width: 40px;
            height: 6px;
            background: linear-gradient(86deg, #C7E0FF 0%, #5AA4FF 100%);
            border-radius: 3px;
            bottom: 40px;
        }
    }
}
.list-box {
    border-top: 12px solid #F5F5F5;
    box-sizing: border-box;
    padding: 0px 31px 20px;

    .follow-item {
        padding: 43px 0 27px;
        border-bottom: 1px solid #EBEBEB;

        .live-info {
            width: 100%;

            .follow-state {
                width: 162px;
                height: 48px;
                border-radius: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 400;
                font-size: 24px;

                &.follow {
                    background: #F4698D;
                    color: #FFF;
                }

                &.followed {
                    border: 2px solid #999999;
                    border: 2px solid #F4698D;
                    color: #999999;
                }
            }


            .info-left {

                .info-text {
                    display: flex;
                    flex-flow: column;

                    .sex-time {
                        .sex {
                            font-weight: 400;
                            font-size: 24px;
                            color: #FFFFFF;
                            width: 85px;
                            height: 36px;

                            border-radius: 18px;
                            display: flex;
                            align-items: center;
                            justify-content: center;

                            >img {
                                width: 19px;
                                margin-right: 7px;
                            }

                            &.female {
                                background: linear-gradient(90deg, #FAA8AB, #FF7097);
                            }

                            &.male {
                                background: linear-gradient(90deg, #9FC9FF, #4196FF);
                            }
                        }
                    }
                }
            }

        }
    }
}
</style>