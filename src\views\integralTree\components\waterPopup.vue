<template>
    <!-- 兑换弹窗 -->
    <van-popup v-model:show="props.show" position="center" round class="popup box-border" @click-overlay="close">
        <div class="text-center relative box-border mx-auto w-60%">
            <img loading="lazy" src="@/assets/tree/water_popup.png" class="w-full">
            <div class="text_content absolute top-53% bottom-0 left-0 right-0 flex flex-col">
                <div class="flex-1">
                    <div class="title text-[#3E95D5] font-medium text-1.5vh">
                        {{ `${props.waterUseIntegral}积分兑换${props.waterDropletNum}g水滴` }}
                    </div>
                    <div class="tips_text text-[#999] text-1.2vh mt-1% ">
                        {{ `每日仅限兑换${props.waterDailyCount}g水滴` }}
                    </div>
                    <div class="step_num mt-4%">
                        <van-stepper v-model="exchangeValue" integer min="1" :max="maxNum" input-width="38px"
                            button-size="24px" />
                    </div>
                </div>
                <div class="my-5%" @click.stop="confirm">
                    <img loading="lazy" src="@/assets/tree/water_btn.png" class="w-60%" />
                </div>
            </div>
        </div>
    </van-popup>
</template>
<script lang="ts" setup>
import { dailyExchange } from '@/api/integralTree'
import { showLoadingToast, closeToast, showToast } from 'vant';
const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    activityId: {
        type: String,
        default: '',
        required: true
    },
    currentNum: {
        type: Number,
        default: 10 //今日兑换次数
    },
    waterDropletNum: {
        type: Number,
        default: 10,//每次可以兑换水滴量
    },
    waterUseIntegral: {
        type: Number,
        default: 10,//每次兑换水滴消耗积分
    },
    waterDailyCount: {
        type: Number,
        default: 10,//每日兑换水滴次数
    }
});
const maxNum = computed(() => {
    return props.waterDailyCount - props.currentNum;
})
const exchangeValue = ref(1);
const emit = defineEmits(['update:show', 'refresh']);
let handleClick = false
// 确认执行操作
const confirm = async () => {
    if(handleClick) return;
    handleClick = true
    showLoadingToast({
        message: '水滴兑换中,请耐心等待...',
        forbidClick: true,
        duration: 0
    })
    const { data, code, message } = await dailyExchange({
        platform: "app",
        exchangeType: "water",
        exchangeCount: exchangeValue.value,
        activityId: props.activityId
    });
    closeToast()
    if (code === 200) {
        showToast('兑换成功')
        setTimeout(() => {
            emit('update:show', false);
            emit('refresh', {
                type: 'exchange',
                data
            });
        }, 1000)

    } else {
        showToast(message)
        setTimeout(() => {
            emit('update:show', false);
        }, 2000)
    }
    handleClick = false;
};
// 关闭弹窗
const close = () => {
    emit('update:show', false);
}
</script>

<style scoped lang="scss">
.van-popup {
    background: transparent;
}
</style>
