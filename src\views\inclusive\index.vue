<template>
    <div class="inclusive w-full">
        <div class="p-20px w-full box-border">
            <Search @on-search="searchVal" placeholder="请输入搜索活动关键词" :showBtn="false" />
            <van-swipe class="my-swipe w-full h-230px mt-12px" :autoplay="3000" indicator-color="white">
                <van-swipe-item v-for="(item, index) of Data.incluBanner" class="w-full h-full rounded-15px">
                    <img loading="lazy" :src="item.src" alt="" class="w-full h-full rounded-15px" />
                </van-swipe-item>
                <!-- <img loading="lazy" :src="bannerImg" alt="" v-if="!Data.speicalList?.length"> -->
            </van-swipe>
            <div class="nav-box h-150px flex justify-around text-28px text-#333 mt-15px" @click="navTo">
                <div v-for="(item, index) of Data.nav" :key="index" class="flex flex-col justify-center items-center">
                    <img loading="lazy" :src="item.src" alt="" class="w-94px h-94px mb-8px" />
                    {{ item.name }}
                </div>
            </div>
            <div class="flex justify-between mt-15px">
                <div class="w-48% h-180px" @click="navTo">
                    <img loading="lazy" src="@/assets/inclusive/inclusive_banner.png" alt=""
                        class="w-full h-full rounded-20px">
                </div>
                <div class="w-48% h-180px" @click="toPage('/integralMall')">
                    <img loading="lazy" src="@/assets/inclusive/integral.png" alt="" class="w-full h-full rounded-20px">
                </div>
            </div>
        </div>
        <div class="store bg-#fff">
            <div class="title-box">
                <div class="flex items-center p-26px">
                    <img loading="lazy" src="@/assets/inclusive/inclusive_icon.png" alt="" class="w-48px mr-10px">
                    <img loading="lazy" src="@/assets/inclusive/inclusive_title.png" alt="" class="w-128px">
                </div>
                <div class="act-content relative">
                    <!-- tab切换 -->
                    <div class="z-10000 text-30px flex justify-around text-#525252 h-70px">
                        <div v-for="(item, i) in Data.tabName" :key="i"
                            :class="Data.tabIndex == i ? 'text-#5AA4FF' : ''" @click="activityTabBtn(i, item)">
                            <div>
                                {{ Data.tabName[i]?.currentName ? Data.tabName[i]?.currentName : item.name }}
                                <van-icon name="arrow-down"></van-icon>
                            </div>
                        </div>
                    </div>
                    <!-- tab切换下拉 -->
                    <div class="dialog-activity" v-show="Data.isShow">
                        <van-popup :show="Data.isShow" round position="top" @click-overlay="Data.isShow = false">
                            <div class="p-24px box-border">
                                <div class="flex items-start flex-wrap  box-border ">
                                    <div class="dropdown_menu_item box-border mb-15px px-20px py-6px rounded-25px bg-#f7f7f7 text-#4c4c4c mr-15px text-28px"
                                        :class="Data.tabName[Data.tabIndex].index == index ? ' !bg-#A1CBFF !text-#fFF' : ''"
                                        v-for="(item, index) in Data.tabName[Data.tabIndex].children" :key="index"
                                        @click="dropdownMenuBtn(index, item)">
                                        {{ item.label }}
                                    </div>
                                </div>
                            </div>
                        </van-popup>
                    </div>
                    <div class="list-container px-26px pb-130px">
                        <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore"
                            ref="loadMoreRef" class="w-full">
                            <div class="flex justify-between w-full flex-wrap">
                                <div v-for="(item, index) of Data.activityList" :key="index"
                                    class="bg-#FFF rounded-20px list-items mb-22px w-48% overflow-hidden"
                                    @click="toDetail(item)">
                                    <div class="h-160px w-full relative">
                                        <img loading="lazy"
                                            :src="item.appCover ? judgeStaticUrl(item.appCover) : defaultCover" alt=""
                                            class="w-full h-full appCover">
                                    </div>
                                    <div class="p-20px box-border w-full">
                                        <div class="truncate w-100% text-30px">{{ item.activityName }}</div>
                                        <div class="text-#999999 text-24px py-12px pb-5px">{{ item.activityStartTime }}
                                            - {{
                                                item.activityEndTime }}
                                        </div>
                                        <div class="text-24px text-#ccc pb-12px">
                                            <van-icon name="location" class="mr-12px" />{{ item.companyName }}
                                        </div>
                                        <div class="flex justify-between items-center text-24px text-#ccc">
                                            <div class="rounded-10px text-24px border-1px border-solid px-6px py-5px leading-none"
                                                :class="item.progressMsg == '未开始' ? 'text-#FBAA46  border-#FBAA46' : item.progressMsg == '进行中' ? 'text-#5AA4FF  border-#5AA4FF' : 'text-#999999  border-#999999'">
                                                {{ item.progressMsg }}
                                            </div>

                                            <div class="text-26px">
                                                <van-icon name="friends" class="mr-12px" />{{ item.readCount
                                                }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </refreshList>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
defineOptions({
    name: 'inclusive'
});

import Search from "@/components/Search/index.vue";
import banner1 from '@/assets/inclusive/banner1.png';
import grain from '@/assets/inclusive/icon_lyfs.png';
import home from '@/assets/inclusive/icon_jjry.png';
import specialty from '@/assets/inclusive/icon_tczq.png';
import season from '@/assets/inclusive/icon_slsx.png';
import refreshList from '@/components/refreshList/index.vue';
import { showToast } from "vant";
import defaultCover from '@/assets/activity/tu.png'
import { activityInfoList } from "@/api/activity.ts";
import { useDictionary } from "@/store/modules/dictionary";
import { useRoute } from "vue-router";
import { judgeStaticUrl } from '@/utils/utils';
import { toDetail } from "@/hooks/useValidator";

const router = useRouter();
const Data = ref({
    searchVal: null,
    incluBanner: [{ src: banner1 }],
    nav: [
        { name: '粮油副食', src: grain },
        { name: '居家日用', src: home },
        { name: '特产专区', src: specialty },
        { name: '时令上新', src: season }
    ],
    list: [],
    tabIndex: 0,
    tabName: [
        {
            id: 0,
            index: 0,
            name: "区域",
            //由areaCode改成companyId
            code: 'companyId',
            children: [
                { label: "全部", unionId: "" },
            ],
            currentName: ''
        },
        {
            id: 1,
            index: 0,
            name: "状态",
            code: 'progress',
            children: [
                { label: "全部", value: "" },
                { label: "进行中", value: 2 },
                { label: "已结束", value: 3 },
                { label: "未开始", value: 1 }
            ],
            currentName: ''
        },
        {
            id: 2,
            index: 0,
            name: "类型",
            code: 'activityType',
            children: [
                { namlabele: "全部", type: 0, code: '' }
            ],
            currentName: ''
        }
    ],
    isShow: false,
    pageNum: 1,
    params: {},
    activityCategory: 'inclusive',
    activityList: []

})
//搜索
function searchVal(val) {
    Data.value.searchVal = val;
    Data.value.pageNum = 1;
    getActList();
}
//页面跳转
function toPage(path) {
    router.push(path)
}
const navTo = () => {
    showToast("敬请期待~")
}

const route = useRoute();
const dictionary = useDictionary()
/*全部/区域/状态 tab切换   */
function activityTabBtn(index, item) {
    // tab切换 下划线滑动
    if (Data.value.tabIndex == index) {
        Data.value.isShow = !Data.value.isShow;
    } else {
        Data.value.isShow = true;
    }
    Data.value.tabIndex = index;
}
/* 区域/状态 tab下拉的选项 */
function dropdownMenuBtn(index, item) {
    Data.value.tabName[Data.value.tabIndex].index = index
    Data.value.tabName[Data.value.tabIndex].currentName = item.label === '全部' ? '' : item.label


    Data.value.isShow = false;
    Data.value.tabName.forEach(t => {
        const { code, children, index } = t
        Data.value.params[code] = children[index].value
    })
    Data.value.pageNum = 1;
    getActList();
}
//获取活动列表
const loadMoreRef: any = ref(null)
function getActList() {
    activityInfoList({
        ...Data.value.params,
        activityCategory: Data.value.activityCategory,
        pageSize: 10,
        pageNum: unref(Data).pageNum,
        activityName: Data.value.searchVal
    }).then(res => {
        if (res.code == 200) {
            if (Data.value.pageNum === 1) {
                Data.value.activityList = [];
            }
            res.data = res.data?.map(t => {
                const { activityStartTime, activityEndTime } = t
                t.activityStartTime = activityStartTime?.split(' ')[0] ?? ''
                t.activityEndTime = activityEndTime?.split(' ')[0] ?? ''
                return t
            })
            Data.value.activityList = Data.value.activityList.concat(res.data);
            //重置刷新状态及 判断是否加载完成
            if (loadMoreRef.value) {
                loadMoreRef.value.onLoadSuc(Data.value.activityList.length, res.total);
            }
        }

    })
}
// 刷新
const onRefreshList = () => {
    Data.value.tabName.forEach(t => {
        t.index = 0
        t.currentName = ''
    })
    Data.value.tabIndex = 0
    Data.value.params = {}
    Data.value.pageNum = 1;
    getActList();
};
// 加载更多
const onLoadMore = () => {
    Data.value.pageNum++;
    getActList();
};
onMounted(() => {
    const areaNames = dictionary.dictionaryOBJmap?.['regionCode']?.map((t: any) => {
        const { dictName, remark } = t
        return { value: remark, label: dictName }
    })

    Data.value.tabName[0].children = [{ label: "全部", value: '' }]
    if (areaNames && areaNames.length) Data.value.tabName[0].children = [{ label: "全部", value: '' }, ...areaNames]

    const activityTypes = dictionary.getDictionaryOpt?.['inclusiveActivityType'] || []
    Data.value.tabName[2].children = [{ label: "全部", value: '' }]
    if (activityTypes && activityTypes.length) Data.value.tabName[2].children = [{ label: "全部", value: '' }, ...activityTypes]

    // 区县跳转，默认回显区县活动类型筛选条件
    if (route.query?.companyId) {
        Data.value.tabName[0].index = Data.value.tabName[0].children?.findIndex((t: any) => t.value === route.query.companyId) ?? 0
        Data.value.tabName[0].currentName = Data.value.tabName[0].children?.[Data.value.tabName[0].index]?.label ?? ''
        Data.value.params[Data.value.tabName[0].code] = route.query.companyId
    }

    getActList();
})


</script>
<style lang="scss" scoped>
.inclusive {
    background: url('@/assets/inclusive/inclusive.png'), #F6F7F8;
    background-size: 100%, 100%;
    background-repeat: no-repeat;

    :deep(.van-swipe__indicator) {
        width: 14px !important;
        height: 4px !important;
        background: rgba(255, 67, 68, 0.5);
        border-radius: 2px;
    }

    :deep(.van-swipe__indicator--active) {
        background-color: rgba(255, 67, 68, 1) !important;
    }

    .title-box {
        background: url('@/assets/inclusive/inclusive_shadow.png') no-repeat;
        background-size: 100%, auto;
        border-radius: 20px 20px 0px 0px;
    }

    .act-content {
        min-height: 30vh;

        .dialog-activity {

            /* 活动主页 tab切换 下拉的弹窗  */
            .van-popup {
                position: absolute;
                top: 50px;
                left: 0;
                right: 0;
                background-color: #fff;
            }

            .van-overlay {
                background: rgba($color: #000000, $alpha: 0.3);
                top: 50px;
                bottom: 0;
                left: 0;
                right: 0;
                position: absolute;
            }

            .dropdown_menu_box {
                border-radius: 0px 0px 12px 12px;

                .dropdown_menu_item {}
            }

        }

        .list-items {
            box-shadow: 0px 3px 10px 0px rgba(128, 153, 162, 0.1);

            .act_status_box {
                >div {

                    background-position: center center;
                    width: 119px;
                    height: 44px;
                    line-height: 44px;
                    /*box-shadow: 3px 1px 1px 0px #f04128;*/
                    /*border-radius: 28px 0px;*/
                    text-align: center;
                }

            }
        }
    }
}
</style>