<template>
  <div class="absolute left-0 top-0 right-0 bottom-0 overflow-hidden">
    <van-swipe :loop="false" vertical :show-indicators="false" @change="onChange" class="w-full h-full"
      :touchable="touchable" ref="vanSwipeRef">
      <van-swipe-item v-for="(item, index) in videoList" :key="item.shortVideoId || item.autoId" class="z-index-9">
        <div @click.stop="onVideoClick(item, index)" z-index="1" class="z-index-1 swiperItemVideo w-full h-full">
          <!--  controlslist="nodownload nofullscreen noremoteplayback noplaybackrate" -->
          <video class="w-full h-full bg-#333 z-index-0" :id="'videoPlayer_' + item.index" :key="index" :autoplay="true"
            :controls="true" controlslist="nodownload  noremoteplayback noplaybackrate" disablePictureInPicture
            :height="height" :width="width" :poster="item.dataType == 'LIVEID'
              ? utils.judgeStaticUrl(item.liveCover)
              : utils.judgeStaticUrl(item.pictures)
              " :src="item.dataType == 'LIVEID'
                ? item.isOpenLive == 'hasEnd'
                  ? item.livePlayReturn
                  : item.livePlayBack
                : utils.judgeStaticUrl(item.linkUrl)
                " :muted="false" :loop="true" preload="preload" language="zh-CN" x5-video-player-type="h5"
            :x5-video-player-fullscreen="false" x5-video-player-style="position:fixed;z-index:0;" playsinline
            webkit-playsinline x5-video-orientation="portraint" playsinlin z-index="0"></video>
        </div>
        <div class="absolute left-0 right-0 bottom-160px p-2 text-#f5f5f5 text-28px">
          <div class="flex items-center object-cover" v-if="item.dataType !== 'LIVEID'" @click="onUserClick(item)">
            <img loading="lazy" src="@/assets/public/head_default.png" class="avatar w-56px h-56px" />
            <span class="ml-2">{{ item.userName || item?.createUser }}</span>
          </div>
          <div class="mt-2 max-h-27 overflow-y-auto" @touchstart="touchable = false" @touchend="touchable = true">
            <span class="text-#5ba5ff mr-14px"> {{ item.dataType == 'LIVEID' ? item.liveIntroduction : item.title
            }}</span>
            <span v-if="item.describes">视频描述:&nbsp;{{ item.describes }}</span>
          </div>
        </div>

        <div class="absolute left-0 right-0 bottom-0 bg-[rgba(0,0,0,0.5)] text-[#F5f5f5] 
          p-2 flex items-center functionalarea text-28px">
          <van-field v-if="item.dataType !== 'LIVEID'" v-model="sms" center left-icon="edit" clearable placeholder="评论"
            readonly @click="commentClick(item, index)">
            <template #button>
              <van-button size="small" type="primary">
                发送
              </van-button>
            </template>
          </van-field>
          <span class="flex-1"></span>
          <div class="funcbut text-28px">
            <van-icon name="good-job-o" size="25" :badge="item.likeVolume || ''" @click="likeClick(item, index)"
              :color="item.whetherLike ? '#ed4809' : ''" />
            <div>点赞</div>
          </div>
          <div class="funcbut" v-if="item.dataType !== 'LIVEID'">
            <van-icon name="comment-o" size="25" :badge="item.commentsVolume || ''"
              @click="commentClick(item, index)" />
            <div>评论</div>
          </div>
          <div class="funcbut">
            <van-icon name="star-o" size="25" :badge="item.collectVolume || ''" @click="startClick(item, index)"
              :color="item.whetherCollect ? '#f2a808' : ''" />
            <div>收藏</div>
          </div>
          <div class="funcbut">
            <van-icon name="share-o" size="25" :badge="item.sharVolume || ''" @click="shareClick(item, index)" />
            <div>分享</div>
          </div>
        </div>
      </van-swipe-item>
    </van-swipe>

    <playComment v-model:showBottomPopup="showBottomPopup" :currentVideo="currentVideo" />

    <!-- 气泡样式 -->
    <div class="fixed bottom-140px z-100" :style="`left:${bubbleX}%`">
      <waterIntergral v-model:show="showBubble" :score="scoreNum"></waterIntergral>
    </div>

  </div>
</template>
<script setup>
import Hls from 'hls.js';
import {
  getShortPublicVideos,
  getMyShortVideos,
  getMyFavoritesVideos,
  getVideoDetails,
  getLoveDetails,
} from '@/api/video/index';
import { likeOperate, collectOperate, shareOperate } from '@/api/public';
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import { showToast, showNotify, showConfirmDialog } from 'vant';
import playComment from '@/views/video/components/playComment.vue';
import { useVideoStore } from '@/store/modules/video';
import { useRoute, useRouter } from 'vue-router';
import { useUserStore } from '@/store/modules/user';
import { showSuccessToast, showFailToast } from 'vant';
import utils from '@/utils/utils';
const waterIntergral = defineAsyncComponent(() => import('@/components/Bubble/waterIntergral.vue'))
const useStore = useUserStore();
const route = useRoute();
const router = useRouter();
const videoStore = useVideoStore();
const touchable = ref(true);
const sms = ref('');
const vanSwipeRef = ref(null);

/**
 * 短视频列表
 */
const videoList = ref([]);
let videoDataList = [];

const beingPlayed = ref(null);
const currentIndex = ref(0);
const playing = ref(true);
const width = computed(() => window.innerWidth);
const height = computed(() => window.innerHeight);
const showBottomPopup = ref(false);

/**
 * 切换视频
 */
async function onChange(index) {
  await getVideoCount(index);

  beingPlayed.value && beingPlayed.value?.pause();
  let item = videoList.value[index];

  /**
   * 获取更多视频数据
   */
  nextTick(() => {
    if (index >= videoList.value?.length - 1 || (index <= 0 && item.index > 0)) {
      setPlayLength(item.index);
    } else {
      onVideoPlay('videoPlayer_' + item.index, index);
      currentIndex.value = item.index;
    }
  });
}
/**
 * 获取视频信息
 */
async function getVideoCount(i) {
  let item = videoList.value[i];
  let reqfun = getVideoDetails;
  if (item.dataType == 'LIVEID') {
    reqfun = getLoveDetails;
  }
  const { code, data } = await reqfun({
    shortVideoId: item.sourceBizId || item.shortVideoId,
    liveBizId: item.sourceBizId,
  });
  if (code === 200) {
    videoList.value[i] = { ...item, ...data };
  }
}
/**
 * 播放|暂停
 */
const onVideoClick = (item, index) => {
  // if (beingPlayed.value && playing.value) {
  //   beingPlayed.value.pause();
  //   playing.value = false;
  //   showToast('已暂停播放');
  // } else {
  //   beingPlayed.value.play();
  //   playing.value = true;
  //   showToast('继续播放');
  // }
};

// 气泡提示框参数设置
const bubbleX = ref(50);//50 62 75 85
const showBubble = ref(false);
const scoreNum = ref(0);
let isReq = false;

/**
 * 点赞|取消点赞
 */
const likeClick = (item, index) => {
  utils.isLoginDialog((isLogin) => {
    if (isLogin) {
      if (isReq) return;
      isReq = true;

      likeOperate({ sourceId: item.sourceBizId || item.shortVideoId }).then(res => {
        isReq = false;
        if (res.code == 200) {
          if (!res.data?.statefulFlowState) {
            showToast('已取消点赞');
            item.likeVolume -= 1;
          } else {
            item.likeVolume += 1;
            showToast('已点赞成功');
          }
          item.whetherLike = res.data?.statefulFlowState || false;
          if (res.data?.score) {
            scoreNum.value = res.data.score
            bubbleX.value = 50
            showBubble.value = true;
          }
        }
      })
        .catch(() => {
          isReq = false;
        })
    }
  });

};

/**
 * 收藏|取消收藏
 */
const startClick = (item, index) => {
  utils.isLoginDialog((isLogin) => {
    if (isLogin) {
      if (isReq) return;
      isReq = true;
      collectOperate({ sourceId: item.sourceBizId || item.shortVideoId }).then(res => {
        isReq = false;
        if (res.code == 200) {
          if (!res.data?.statefulFlowState) {
            item.collectVolume -= 1;
            showToast('已取消收藏');
          } else {
            item.collectVolume += 1;
            showToast('已添加收藏');
          }
          item.whetherCollect = res.data?.statefulFlowState || false;

          if (res.data?.score) {
            scoreNum.value = res.data.score
            bubbleX.value = 75
            showBubble.value = true;
          }
        }
      })
        .catch(() => {
          isReq = false;
        })
    }
  })

};
/**
 * 查看评论
 */
const currentVideo = ref({});
const commentClick = (item, index) => {
  showBottomPopup.value = true;
  currentVideo.value = item;
};
/**
 * 分享
 */
const shareClick = (item, index) => {
  if (utils.isApp()) {
    // utils.newsAction({ title: item.title, dataId: item.shortVideoId }, 'share');
    utils.SharedWorker(item?.title, window.location.href, (isCan) => {
      if (isCan) getShare()
    })
  } else {
    showToast('请在川工之家app分享');
  }
};
const getShare = () => {
  if (isReq) return;
  isReq = true;
  shareOperate({
    sourceId:
      videoDataList[currentIndex.value].sourceBizId ||
      videoDataList[currentIndex.value].shortVideoId,
  }).then(res => {
    isReq = false;
    if (res.code == 200) {
      showSuccessToast('分享成功');
      videoDataList[currentIndex.value].sharVolume += 1;
      if (res.data?.score) {
        scoreNum.value = res.data.score
        bubbleX.value = 85
        showBubble.value = true;
      }
    }
  })
    .catch(() => {
      isReq = false;
    })
};
/**
 * 去用户主页
 */
const onUserClick = item => {
  router.push({
    path: '/video-personalCenter',
    query: { userId: item.userId, nickname: item.userName },
  });
};

/**
 * @Author: liaohong
 * 列表来源说明
 * 1、根据不同进入的状态进行获取视频列表
 * entertype  1：视频专区列表 2：用户作品（存在多个列表因用户不同） 3：我的收藏
 * userId     用户id
 * listnumber  列表序号
 * 2、页面刷新后通过对应分类获取视频列表
 */

/**
 * 视频列表
 */
let pageNum = 1;
let params = {};
let pageHlsHasNextPage = false;
let pageTotal = 0;
const getList = async () => {
  let requestMethod = getShortPublicVideos;

  let reqParams = {
    ...videoStore.getParams, //条件参数
    pageNum: pageNum,
    pageSize: 20,
  };
  if (route.query.entertype == 2) {
    requestMethod = getMyShortVideos;
    reqParams = {
      pageNum,
      pageSize: 10,
      userId: useStore.getUserInfo?.userId, //发布人员id
    };
  }
  if (route.query.entertype == 3) {
    requestMethod = getMyFavoritesVideos;
    reqParams = {
      pageNum: pageNum,
      pageSize: 10,
    };
  }
  const { code, data, total, hasNextPage } = await requestMethod(reqParams);
  pageHlsHasNextPage = hasNextPage;
  pageTotal = total;
  if (!hasNextPage && videoDataList.length == total) {
    showNotify({ type: 'danger', message: '没有更多了...' });
    showToast('到底了！');
  }
  if (code === 200 && videoDataList.length != total) {
    videoDataList = [...videoDataList, ...data];
    videoDataList?.forEach((item, index) => {
      item.index = index;
    });
  }
};

watch([() => pageHlsHasNextPage, () => pageTotal], ([nval1, nval2]) => {
  console.log(nval1, nval2);
});

onMounted(async () => {
  let list = [];
  window.h5NewsShareReturn = getShare;
  if (route.query.entertype == 1) {
    list = videoStore.getZoneList;
  } else if (route.query.entertype == 2) {
    list = videoStore.getPersonalWorks[route.query.userId];
  } else {
    list = videoStore.getCollectionList;
  }
  if (list.length <= 0) {
    await getList();
  } else videoDataList = list;
  videoDataList?.forEach((item, index) => {
    item.index = index;
  });
  let index = route.query?.listnumber || 0;

  setPlayLength(index);

  if (index == 0) {
    await getVideoCount(index);
  }

  currentIndex.value = index;
  nextTick(() => {
    stopAllVideo('videoPlayer_' + index, index);
  });
});

/**
 * 设置播放数组长度数量
 */
async function setPlayLength(index) {
  let listNum = 0;
  let staNum = 0;
  let endNum = 5;
  if (index <= 2) {
    listNum = +index;
  } else if (index >= videoDataList.length - 3) {
    staNum = videoDataList.length - 5;
    endNum = videoDataList.length;
    listNum = 5 + index - endNum;
  } else {
    staNum = index - 2;
    endNum = index + 3;
    listNum = 2;
  }
  videoList.value = videoDataList.slice(staNum, endNum);

  vanSwipeRef.value?.swipeTo(listNum, { immediate: true });

  if (videoDataList.length - 3 <= index) {
    pageNum = videoDataList.length / 10 + 1;
    await getList();
  }
}

/**
 * 视频播放
 */
let pageHls = null;
function onVideoPlay(videoId, index) {
  let item = videoList.value[index];
  if (pageHls) {
    pageHls.stopLoad();
    pageHls.destroy();
  }
  beingPlayed.value = document.getElementById(videoId);
  if (item.dataType == 'LIVEID' && item.isOpenLive !== 'hasEnd') {
    if (Hls.isSupported()) {
      pageHls = new Hls();
      pageHls.loadSource(beingPlayed.value.src);
      pageHls.attachMedia(beingPlayed.value);
      pageHls.on(Hls.Events.MANIFEST_PARSED, () => {
        beingPlayed.value?.play();
      });
    } else if (beingPlayed.value.canPlayType('application/vnd.apple.mpegURL')) {
      beingPlayed.value.addEventListener('loadedmetadata', () => {
        beingPlayed.value?.play();
      });
    }
  } else {
    beingPlayed.value && beingPlayed.value?.play();
  }
}

/**
 * 停止所有视频播放
 */
function stopAllVideo(playId = '', index = 0) {
  const videos = document.querySelectorAll('video');
  for (const video of videos) {
    if (video && video.id) {
      video.pause();
    }
    if (video && video.id === playId && index == 0) {
      video.play();
    }
  }
}

onBeforeUnmount(() => {
  // 页面卸载时暂停所有视频播放
  stopAllVideo();
  if (pageHls) {
    pageHls.stopLoad();
    pageHls.destroy();
  }
});
</script>
<style scoped lang="scss">
.functionalarea {
  :deep(.van-cell) {
    background-color: #383838;
    color: #575757;
    font-size: 28px;

    &::after {
      display: none;
    }

    .van-field__left-icon .van-icon,
    .van-field__right-icon .van-icon {
      color: #575757;
      font-size: 28px;
    }

    input::placeholder {
      color: #575757;
    }

    --van-cell-vertical-padding: 5px;
    --van-cell-horizontal-padding: 20px;
    border-radius: 40px;
    --van-button-small-height: 50px;
    --van-button-small-font-size: 26px;
    --van-button-border-width: 0px;
    --van-button-small-padding: 0 20px;
    --van-button-primary-background: #575757;
    --van-button-radius: 28px;
    width: 300px;

    .van-button {
      top: -5px;
    }
  }

  .funcbut {
    position: relative;
    text-align: center;
    width: 90px;
    font-size: 22px;
    --van-badge-background: #5ca5ff;
    --van-badge-border-width: 0px;
    --van-badge-font-size: 20px;

    :deep(.van-badge--top-right) {
      top: 10px;
    }
  }
}

:deep(.van-share-sheet__cancel) {
  background-color: #f5f5f5;
}

:deep(.van-popup) {
  background-color: #f5f5f5;
}

:deep(.van-share-sheet__cancel:before) {
  background-color: #f5f5f5;
}

.swiperItemVideo {
  video::-webkit-media-controls-play-button {
    display: none;
  }

  video::-webkit-media-controls-timeline {
    /* display: none; */
    padding-bottom: 120px;
  }

  video::-webkit-media-controls-current-time-display {
    display: none;
  }

  video::-webkit-media-controls-time-remaining-display {
    display: none;
  }

  video::-webkit-media-controls-mute-button {
    display: none;
  }

  video::-webkit-media-controls-fullscreen-button {
    // display: none;
  }

  video::-webkit-media-controls-toggle-closed-captions-button {
    display: none;
  }

  video::-webkit-media-controls-volume-slider {
    display: none;
  }

  /* video::-webkit-media-controls-enclosure {
    display: none;
  } */
  video::-webkit-media-controls-playback-speed-button {
    display: none;
  }

  video::-webkit-media-controls:not(.audio-only) [pseudo='-webkit-media-controls-panel'] [pseudo='-internal-media-controls-overflow-button'] {
    display: none !important;
  }
}
</style>
