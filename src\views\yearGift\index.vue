<template>
  <div class="yearGift min-h-full w-full flex flex-col">
    <div class="nav-banner py-20px h-fit">
      <img loading="lazy" src="@/assets/yearGift/banner_title.png" class="w-45% ml-84px" />
      <div class="navs flex items-end justify-between px-27px mt-100px mb-40px">
        <div class="bg-cover bg-center h-272px flex-1 box-border px-21px pt-34px"
          :class="{ 'mr-25px': index < navTabs.length - 1 }" v-for="item, index in navTabs" :key="index"
          :style="{ backgroundImage: `url(${item.bgUrl})` }" @click="toPage(item)">
          <div :class="item.color" class="text-27px"> {{ item.name }}</div>
          <div :class="item.color" class="text-30px leading-none mt-12px font-500"> {{ item.des }}</div>
          <div :class="item.btnColor" class="text-#fff mt-12px
                    py-8px px-14px rounded-18px text-20px text-center inline-block leading-none">
            {{ item.btnText }}</div>
        </div>
      </div>
    </div>
    <div class="activities flex-1">
      <div class="bg-#fff flex-1 p-30px box-border mt-[-40px] rounded-20px">
        <div class="tab-box mb-36px">
          <van-tabs v-model:active="Data.tab.active" sticky type="card" color="#F2F2F2" title-inactive-color="#666666"
            title-active-color="#5AA4FF" line-width="30" @click-tab="onClickTab">
            <van-tab :title="item.name" v-for="(item, index) in Data.tab.nav" :key="index"
              title-class="tab-title"></van-tab>
          </van-tabs>
        </div>
        <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
          <activityList :data="Data.list" @toDetails="(item) => toDetail(item)">
            <template #subContent="{ item }">
              <div class="text-#666 text-26px pb-12px flex items-center"><van-icon name="underway"
                  class="mr-12px text-[#CCCCCC]" size="14" />
                {{ item.activityStartTime }} - {{ item.activityEndTime }}
              </div>
              <div class="flex justify-between text-24px text-#ccc">
                <div><van-icon name="location" class="mr-12px" size="14" />{{ item.companyName }}</div>
                <div><van-icon name="friends" class="mr-12px" size="14" />{{ item.readCount }}</div>
              </div>
            </template>
            <template #status="{ item }">
              <div class="text-24px text-#fff px-15px py-8px box-border rounded-ss-16px rounded-br-16px"
                :style="{ background: dealStatus(item.progress, 'bgColor') }">
                {{ dealStatus(item.progress, "text") }}
              </div>
            </template>
          </activityList>
        </refreshList>
      </div>
    </div>

    <!-- 积分领取弹窗 -->
    <van-popup v-model:show="isshowDialog" @click-overlay="isshowDialog = false" position="center"
      class="flex items-center">
      <div class="relative w-100%">
        <img loading="lazy" src="@/assets/yearGift/integral_popup.png" class="w-full" />
        <div class="text-content absolute left-18% right-15% top-20% text-#e7300e text-center">
          <div class="text-46px font-500">工会一点心意</div>
          <div class="text-30px mt-10px">祝你生日快乐</div>
          <div class="inline-block mt-20px bg-#fff rounded-20px text-center px-25px py-10px">
            <img loading="lazy" src="@/assets/yearGift/integral_coins.png" class="w-117px" />
            <div class="text-34px flex items-baseline">
              <span class="font-bold text-40px">
                {{ integralObj?.score }}
              </span>
              积分
            </div>
          </div>
          <div class="w-60% mx-auto mt-30px" @click="receive">
            <img loading="lazy" src="@/assets/yearGift/receive_btn.png" class="w-full" />
          </div>
        </div>
      </div>
    </van-popup>
    <confirmPopop v-model:show="show" :showCancel="false" confirmText="我知道了" @confirm="show = false"
      confirmColor="#ff6684" :tips="contentTips" />
  </div>
</template>
<script lang="ts" setup>
import dayjs from "dayjs";

defineOptions({
  name: 'yearGift'
})
import navbg1 from '@/assets/yearGift/nav_item1.png'
import navbg2 from '@/assets/yearGift/nav_item2.png'
import navbg3 from '@/assets/yearGift/nav_item3.png'
import { getBirthdayIntegral, receiveBirthdayIntegral, judgeBirthday, judgeUserCanIntegral } from '@/api/yearGift';
import { showDialog } from 'vant'
import activityList from "@/components/activity/list.vue";
import refreshList from "@/components/refreshList/index.vue";
import { activityInfoList } from "@/api/activity";
import { toDetail } from "@/hooks/useValidator";
import { showToast } from "vant";
import confirmPopop from '@/components/Popup/confirmPopop.vue';

onMounted(() => {
  initalPage()
})

const navTabs = [
  {
    name: '生日惊喜',
    des: '专享优惠券',
    btnText: '立即领取',
    btnColor: 'blue-liner',
    bgUrl: navbg1,
    color: 'text-#4278f4',
    path: '/yearGift/benefits'
  },
  {
    name: '生日狂欢',
    des: '好礼等你拿',
    btnText: '立即兑换',
    btnColor: 'pink-liner',
    bgUrl: navbg2,
    color: 'text-#8b0120',
    path: '/integralMall/birthdayGoods'
  },
  {
    name: '生日特权',
    des: '积分大放送',
    btnText: '立即领取',
    btnColor: 'yellow-liner',
    bgUrl: navbg3,
    color: 'text-#7d491c',
    path: ''//弹窗
  }
]
const router = useRouter()
const isshowDialog = ref(false)
const integralObj = ref<any>(null)
const isBirthDay = ref(false)
const getIsBirthDay = () => {
  judgeBirthday().then((res: any) => {
    // 生日才请求
    if (res.code === 200) isBirthDay.value = res.data
    if (isBirthDay.value) getIntegralScore()
  })
}
const initalPage = () => {
  getIsBirthDay()
  getActList()
}
const getIntegralScore = async () => {
  const { code, data } = await getBirthdayIntegral()
  if (code == 200) {
    integralObj.value = data
  }
}
const show = ref(false)
const contentTips = ref('')
const toPage = async (item: any) => {
  // 需要判断当前人是不是生日
  if (!isBirthDay.value) {
    contentTips.value = '抱歉，今天不是您的生日哦，请生日当天来参与活动。'
    show.value = true
    return
  }
  if (item.path == '') {
    // 先判断是否领取过
    const { data, message } = await judgeIsReceive()
    if (!data) {
      contentTips.value = message
      show.value = true
      return
    }

    if (integralObj.value) {
      isshowDialog.value = true
      return
    }
  }
  router.push(item.path)
}
// 判断是否可以领取
const judgeIsReceive = async () => {
  const res = await judgeUserCanIntegral()
  return res
}

// 积分领取
const receive = async () => {
  const { code, message } = await receiveBirthdayIntegral()
  if (code === 200) {
    isshowDialog.value = false
    showToast('恭喜您，领取积分成功！')
  }
  else {
    showDialog({
      title: '温馨提示',
      message: message || '积分领取失败',
      confirmButtonText: '知道了',
    })
  }
}

// 活动
const Data = ref({
  tab: {
    active: 0,
    nav: [
      { name: "全部", code: "" },
      {
        name: "进行中",
        code: "2",
        bgColor: "linear-gradient(90deg, #FD633F, #FE952E)",
      },
      {
        name: "未开始",
        code: "1",
        bgColor: "linear-gradient(90deg, #2FB095, #55D1AC)",
      },
      {
        name: "已结束",
        code: "3",
        bgColor: "linear-gradient(90deg, #999999, #CCCCCC)",
      },
    ],
  },
  list: [],
  pageNum: 1,
});


/**
 * 点击状态标签页的处理函数
 *
 * @param item 被点击的标签页对象
 */
function onClickTab(item: any) {
  Data.value.tab.active = item.name;
  Data.value.pageNum = 1;
  getActList();
}
function dealStatus(status: string, type: string) {
  if (type === "text")
    return Data.value.tab.nav.find((el: any) => el.code === status)?.name;
  if (type === "bgColor")
    return Data.value.tab.nav.find((el: any) => el.code === status)?.bgColor;
}
function toJump(path: string) {
  if (!path) return;
  router.push({ path });
}
//活动列表
const loadMoreRef = ref<any>(null);
// 刷新
const onRefreshList = () => {
  Data.value.pageNum = 1;
  getActList();
};
// 加载更多
const onLoadMore = () => {
  Data.value.pageNum++;
  getActList();
};
async function getActList() {
  let res = await activityInfoList({
    activityCategory: "birthday",
    pageSize: 10,
    pageNum: Data.value.pageNum,
    progress: Data.value.tab.nav[Data.value.tab.active].code,
  });
  if (res.code == 200) {
    if (Data.value.pageNum === 1) Data.value.list = [];
    res.data = res.data?.map(t => {
      const { activityStartTime, activityEndTime } = t
      t.activityStartTime = dayjs(activityStartTime).format('YYYY.MM.DD') ?? '';
      t.activityEndTime = dayjs(activityEndTime).format('YYYY.MM.DD') ?? '';
      return t
    })
    Data.value.list = Data.value.list.concat(res.data);
    //重置刷新状态及 判断是否加载完成
    if (loadMoreRef.value) {
      loadMoreRef.value.onLoadSuc(Data.value.list.length, res.total);
    }
  }
}

</script>
<style scoped lang="scss">
.yearGift {
  .nav-banner {
    ;
    background-image: url('@/assets/yearGift/banner.png');
    background-size: 100% auto;
    background-color: #fef2f4;
    background-repeat: no-repeat;
  }
}

.van-popup {
  width: 100vw;
  height: 100vh;
  background: transparent;
}

.tab-box {
  :deep(.van-tabs__nav--card) {
    border: none !important;
    margin: 0 !important;
  }

  :deep(.van-tab--card) {
    border-right: none !important;
    border-radius: 44px;
    // background-color: #f2f2f2;
    color: #666;
    margin-right: 44px;
  }

  :deep(.van-tab--card):last-child {
    margin-right: 0;
  }

  :deep(.van-tab--active) {
    background-color: #5aa3ff !important;
    font-weight: 500;
    font-size: 28px;
    color: #fff !important;
  }

  :deep(.tab-title) {
    font-weight: 400;
    font-size: 28px;
    color: #666;
  }

}

.blue-liner {
  background: linear-gradient(to bottom, #3e76f4, #88aaf8);
}

.pink-liner {
  background: linear-gradient(to bottom, #f58770, #ff6684);
}

.yellow-liner {
  background: linear-gradient(to bottom, #fda754, #ffcd60);
}
</style>
