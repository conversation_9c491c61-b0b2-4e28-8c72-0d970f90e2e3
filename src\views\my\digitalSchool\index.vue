<template>
    <div class="my-groups-page bg-#f6f7f8">
        <div class="tab-box h-80px bg-white sticky top-0 z-99">
            <van-tabs v-model:active="Data.tabActive" sticky color="#5AA4FF" title-active-color="#5AA4FF"
                title-inactive-color="#333333" line-width="30" @click-tab="onClickTab"
                v-if="Data.tab && Data.tab.length">
                <van-tab :title="item.label" v-for="(item, index) in Data.tab" :key="index"
                    title-class="tab-title"></van-tab>
            </van-tabs>
        </div>
        <div class="px-[40px] bg-[#fff] box-border two-tab-box ">
            <van-tabs v-model:active="Data.twoTabActive" sticky type="card" title-active-color="#5AA4FF"
                title-inactive-color="#333333" @click-tab="onClickTwoTab" v-if="Data.twoTab && Data.twoTab.length">
                <van-tab :title="item.name" v-for="(item, index) in Data.twoTab" :key="index"></van-tab>
            </van-tabs>
        </div>
        <div class="mt-30px px-30px box-border">
            <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
                <div v-if="Data.twoTabActive == 0">
                    <div class="bg-#fff p-28px rounded-20px mb-20px" v-for="(item, index) in Data.list" :key="index">
                        <div class="flex mb-24px">
                            <img loading="lazy" src="@/assets/public/head_default.png" alt=""
                                class="w-76px h-76px topic-icon">
                            <div class="topic-right pl-33px flex-1">
                                <div class="flex justify-between items-center">
                                    <div class="text-32px text-#333 font-500">
                                        {{ item.addUserName }}
                                    </div>
                                    <div class="text-24px text-#666 font-400">
                                        {{ dayjs(item.createTime).format("YYYY-MM-DD HH:mm") }}
                                    </div>
                                </div>
                                <div class="text-#333 text-24px mt26px">申请加入：<span class="text-#666">{{ item.groupName
                                }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="bg-#f2f2f2 rounded-10px px28px py16px text-#333 text-24px"
                            v-if="item.addCause || item.auditRemarks">
                            <div v-if="item.addCause">
                                加入原因：<span class="text-#666">{{ item.addCause }}</span>
                            </div>
                            <div :class="item.addCause ? 'mt24px' : ''"
                                v-if="Data.tab[Data.tabActive].value == 'refuse' && item.auditRemarks">
                                拒绝原因：<span class="text-#FF4344">{{ item.auditRemarks }}</span>
                            </div>
                        </div>
                        <div class="flex justify-end bottom_btn mt52px" v-if="Data.tab[Data.tabActive].value == 'wait'">
                            <div class="mr33px" @click.stop="clickRefuse(item)">
                                拒绝
                            </div>
                            <div class="" @click.stop="clickAgree(item)">
                                同意
                            </div>
                        </div>
                    </div>
                </div>
                <div v-if="Data.twoTabActive == 1">
                    <shareList :data="Data.list" :type="Data.tab[Data.tabActive].value == 'refuse' ? 'examine' : ''"
                        @detail="toDetail">
                        <template #centerContent="{ item }">
                            <div class="text-#333 text-24px">
                                发布小组：<span class="text-#666">{{ item.groupName }}</span>
                            </div>
                        </template>
                        <template #bottomContent="{ item }" v-if="Data.tab[Data.tabActive].value == 'wait'">
                            <div class="flex justify-end bottom_btn mt52px">
                                <div class="mr33px" @click.stop="clickRefuse(item)">
                                    拒绝
                                </div>
                                <div class="" @click.stop="clickAgree(item)">
                                    通过
                                </div>
                            </div>
                        </template>
                        <template #bottomContent="{ item }" v-if="Data.tab[Data.tabActive] == 'refuse'">
                            <div class="bg-#f6f7f8 rounded-10px px-30px py-20px box-border text-28px  mt-[22px]"
                                v-if="item.auditRemarks"><span class="text-#999">拒绝理由：</span> <span
                                    class="text-#ff4344">{{ item.auditRemarks
                                    }}</span></div>
                        </template>
                    </shareList>
                </div>
            </refreshList>

        </div>
        <van-popup :show="Data.showPop" class="popup1" round>
            <van-form @submit="submit" class="flex flex-col items-center">
                <div class="text-center text-32px text-#333 mb-40px">拒绝原因</div>
                <div class="text-input w-full">
                    <van-field v-model="Data.formData.auditRemarks" label="" rows="3" type="textarea"
                        label-width="0"></van-field>
                </div>
                <van-button type="primary"
                    class="btn w-65/100 mt-40px mx-auto text-34px text-#fff py-23px text-center border-none rounded-40px"
                    native-type="submit"
                    style="background: linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%);">提交</van-button>
                <img loading="lazy" src="@/assets/public/close.png" alt=""
                    class="fixed -bottom-15 w-60px h-60px left-1/2 -translate-x-1/2" @click="Data.showPop = false">
            </van-form>

        </van-popup>

    </div>
</template>
<script lang="ts" setup>
defineOptions({
    name: 'myDigitalSchool'
})
import refreshList from "@/components/refreshList/index.vue"
import router from "@/router";
import { useDictionary } from "@/store/modules/dictionary";
import utils from "@/utils/utils";
import { shareFindAuditListH5, shareAuditH5, groupFindAuditListH5, auditInsertGroups } from "@/api/digitalSchools/index"
import shareList from '@/views/digitalSchool/components/shareList.vue';
import dayjs from "dayjs";
import { showSuccessToast, showFailToast } from "vant";
const dictionary = useDictionary()
const Data = ref({
    tabObj: {
        "wait": "待审核",
        "refuse": "未通过",
        "pass": "已通过",
    },
    tabActive: 0,
    twoTabActive: 0,
    tab: [],
    twoTab: [
        { name: "加入人员", code: "group", listApi: groupFindAuditListH5, auditApi: auditInsertGroups },
        { name: "心得分享", code: "share", listApi: shareFindAuditListH5, auditApi: shareAuditH5 }
    ],
    list: [],
    pageNum: 1,
    showPop: false,
    formData: {
        auditStatus: "",//参考字典groupStudyStatus，pass我加入的，wait我申请的，refuse未通过的
        auditRemarks: "",  //拒绝必填
        autoId: ""
    }
});
const loadMoreRef = ref('')
onMounted(() => {
    Data.value.tab = dictionary.getDictionaryOpt?.['groupStudyStatus'] && dictionary.getDictionaryOpt?.['groupStudyStatus'];
    Data.value.tab.forEach(item => {
        item.label = Data.value.tabObj[item.value]
    })
    getList()
})
// 拒绝
function clickRefuse(item) {
    Data.value.showPop = true
    Data.value.formData.autoId = item.autoId
    Data.value.formData.auditStatus = "refuse"
}
function clickAgree(item) {
    Data.value.formData.autoId = item.autoId
    Data.value.formData.auditStatus = "pass"
    submit()

}
function submit() {
    let activeObj = Data.value.twoTab[Data.value.twoTabActive]
    activeObj['auditApi'](Data.value.formData).then(res => {
        if (res.code == 200) {
            showSuccessToast("提交成功");
            onRefreshList()
        } else {
            showFailToast(res.message)
        }
        Data.value.showPop = false

        for (const key in Data.value.formData) {
            Data.value.formData[key] = ""
        }
    })
}

//列表
function getList() {
    let activeObj = Data.value.twoTab[Data.value.twoTabActive]
    activeObj['listApi']({
        pageNum: Data.value.pageNum,
        pageSize: 10,
        auditStatus: Data.value.tab[Data.value.tabActive].value
    }).then(res => {
        if (res.code == 200) {
            if (Data.value.pageNum == 1) {
                Data.value.list = res.data || []
            } else {
                Data.value.list = Data.value.list.concat(res.data);
            }
            //重置刷新状态及 判断是否加载完成
            if (loadMoreRef.value) {
                loadMoreRef.value.onLoadSuc(Data.value.list.length, res.total);
            }
        }
    })
}

// 刷新
const onRefreshList = () => {
    Data.value.pageNum = 1;
    getList();
};
// 加载更多
const onLoadMore = () => {
    Data.value.pageNum++;
    getList();
};

/**
 * 点击状态标签页的处理函数
 *
 * @param item 被点击的标签页对象
 */
function onClickTab(item: any) {
    Data.value.tabActive = item.name;
    Data.value.pageNum = 1;
    if (loadMoreRef.value) loadMoreRef.value.resetStatus();
    onRefreshList();
}
/**
 * 点击类型标签页时触发的事件处理函数
 *
 * @param item 标签页项对象
 */
function onClickTwoTab(item: any) {
    Data.value.twoTabActive = item.name
    Data.value.pageNum = 1;
    if (loadMoreRef.value) loadMoreRef.value.resetStatus();
    onRefreshList();
}
function toDetail(item: any) {
    const { autoId, auditRemarks = '', describes = '', userName, createTime, file = '', auditStatus, groupName } = item
    router.push({
        path: "/my/digitalSchoolDetail",
        query: {
            info: JSON.stringify({
                autoId, auditRemarks, describes, userName, createTime, file, auditStatus, groupName
            })
        }
    });
}

import useRefreshFun from '@/hooks/app.ts';
const changePage = () => {
    onRefreshList()
}
// 定义刷新属性
const { refresh } = useRefreshFun()
refresh([
    { name: 'changePage', funsName: [changePage] }
])

</script>
<style lang="scss" scoped>
.my-groups-page {
    background: #f6f7f8;
    min-height: 100vh;
    padding-bottom: 30px;
    box-sizing: border-box;

    .tab-box {
        :deep(.tab-title) {
            font-weight: 400;
            font-size: 32px;
            color: #333333;
        }

        :deep(.van-tab--active) {
            font-weight: 400;
            font-size: 32px;
            color: #5aa4ff;
        }

        :deep(.van-tab) {
            line-height: 1;
            height: 100%;
        }

        :deep(.van-tabs__line) {
            background: linear-gradient(86deg, #5aa4ff 0%, #c7e0ff 100%);
            border-radius: 3px;
            height: 6px;
            width: 54px;
        }

        :deep(.van-tabs--line .van-tabs__wrap) {
            height: 80px;
        }

        :deep(.van-tabs__nav--line) {
            height: 100%;
            padding-bottom: 15px;
        }
    }

    .two-tab-box {
        width: calc(100% - 80px);
        margin: 20px auto 30px;
        padding: 10px;

        :deep(.van-tab--active) {
            background-color: #fff;
        }

        :deep(.van-tabs__nav--card) {
            border: none;
        }

        :deep(.van-tab--card) {
            border-right: none;
        }

        :deep(.van-tab--card):nth-child(1) {
            position: relative;

            &::after {
                content: "";
                width: 1px;
                height: 29px;
                background: #e5e5e5;
                position: absolute;
                right: 0;
            }
        }

        :deep(.tab-title) {
            font-weight: 400;
            font-size: 28px;
            color: #333333;

        }

        :deep(.van-tab--active) {
            font-weight: 400;
            font-size: 28px;
            color: #5aa4ff;
        }


        :deep(.van-tab) {
            line-height: 1;
            height: 100%;
            font-size: 26px;
        }
    }

    .van-overlay {
        background: rgba(0, 0, 0, 0.5);
    }

    .popup1 {
        width: 85vw;
        min-height: 500px;
        padding: 50px 40px;
        box-sizing: border-box;
        top: 40%;
        overflow-y: visible;
    }

    .text-input {
        :deep(.van-cell) {
            padding: 0;
        }

        :deep(.van-field__control) {
            background: #f6f7f8;
            border-radius: 10px;
            padding: 20px 35px;
            box-sizing: border-box;
        }
    }

    .bottom_btn {
        >div {
            width: 184px;
            height: 68px;
            border-radius: 34px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 400;
            font-size: 32px;

            &:nth-child(1) {
                background-color: #fff;
                border: 1px solid #5CA5FF;
                color: #5CA5FF;
            }

            &:nth-child(2) {
                background: linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%);
                color: #FFFFFF;
            }
        }
    }
}
</style>