<template>
  <div class="video_container bg-[#F6F7F8] min-h-full w-full box-border h-fit">
    <div class="tab_header flex bg-[#fff] h-110px items-center sticky top-0 z-99">
      <div v-for="(item, index) in tabList" :key="index" class="flex-1 text-center" @click="changeTab(item)">
        <img loading="lazy" :src="item.activeImg" v-if="item.value === tabActive" class="w-182px" />
        <span v-else class="text-[#666] text-32px">
          {{ item.title }}
        </span>
      </div>
    </div>
    <!-- 个人信息 -->
    <myinfo v-show="tabActive === '2'" :userId="useStore.getUserInfo?.userId"
      :nickname="useStore.getUserInfo?.nickname" />
    <!-- 区域查询  v-model="companyClassicIds" -->
    <filterPop v-show="tabActive === '1'" @success="filterList" />
    <!-- 列表 -->
    <div class="list w-full pb-150px h-fit box-border" :class="{ '!bg-[#fff] personal  h-50vh': tabActive === '2' }">
      <div class="personal_tab z-99 sticky top-110px" v-show="tabActive === '2'">
        <van-tabs v-model:active="videoType" @change="changeVideoTab">
          <van-tab v-for="(item, index) in profileType" :title="item.title" class="text-34px" :key="index"></van-tab>
        </van-tabs>
      </div>
      <refreshList ref="loadMoreRef" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore">
        <div class="flex flex-wrap  p-30px justify-between">
          <messageCell v-if="tabActive === '2' && videoType === 2" :content="item" v-for="item in list"
            :key="item.autoId" />
          <commentCell v-if="tabActive === '2' && videoType === 3" :content="item" v-for="item in list"
            :key="item.autoId" @reply="onReply" @deleteContent="deleteContent" />
          <videoListCell v-if="(tabActive === '2' && videoType !== 2 && videoType !== 3) || tabActive === '1'"
            :content="item" v-for="(item, index) in list" :key="item.sourceBizId || item.autoId"
            @click="onVideoClick(index)" :showDelete="tabActive === '2' && videoType === 0" @deleteItem="deleteVideo">
          </videoListCell>
        </div>
      </refreshList>
    </div>

    <!-- 底部操作 -->
    <div
      class="fix_btn fixed bottom-0px mx-auto w-full flex justify-center items-center bg-#fff h-110px safe_area_bottom">
      <!-- <div class="honor text-#fff text-34px relative w-260px h-78px flex items-center justify-center" @click="toHonor">
        <img loading="lazy" src="@/assets/video/icon_honor.png" class="w-28px mr-18px" />
        <span>获奖信息</span>
      </div> -->

      <div class="draft button relative w-300px h-78px flex items-center justify-center" @click="showSubmit">
        <img loading="lazy" src="@/assets/video/add_icon.png" class="w-28px mr-18px" />
        <div class="text-34px text-#fff">我要投稿</div>
      </div>
    </div>
    <confirmPopup v-model:show="showConfirm" />

    <!-- 评论窗 -->
    <van-popup v-model:show="showBottomPopup" position="bottom" :style="{ height: '30%', background: '#ffffff' }"
      closeable close-icon="close" round @close="showBottomPopup = false">
      <div class="videoCommentInput w-full px-28px pt-50px box-border">
        <van-field v-model="commentContent" type="textarea" ref="commentRef" :placeholder="placeholder" :rows="1"
          :autosize="{ minHeight: 150 }" :maxlength="150">
          <template #button>
            <van-button size="small" type="primary" :disabled="!commentContent.length" @click="onCommentClick()">
              评论
            </van-button>
          </template>
        </van-field>
      </div>
    </van-popup>
  </div>
</template>
<script lang="ts" setup>
import video_active_icon from '@/assets/video/video_title.png';
import my_active_icon from '@/assets/video/my_video_title.png';
import refreshList from '@/components/refreshList/index.vue';
import videoListCell from '@/components/Cell/videoListCell.vue';
import messageCell from '@/views/video/components/messageCell.vue';
import myinfo from '@/views/video/components/myinfo.vue';
import filterPop from '@/views/video/components/filterPop.vue';
import commentCell from '@/views/video/components/commentCell.vue';
import { useRouter } from 'vue-router';
import utils from '@/utils/utils'

import {
  getShortPublicVideos,
  getMyShortVideos,
  getMyFavoritesVideos,
  getMyLikeAndCollect,
  getReplyCommentList,
  publishComment,
  deleteFunVideo,
  checkVideo, checkUserIdentity
} from '@/api/video/index';

import { showFailToast } from 'vant';

import { useUserStore } from '@/store/modules/user';
import { useVideoStore } from '@/store/modules/video';
const videoStore = useVideoStore();
const useStore = useUserStore();
const router = useRouter();
const companyClassicIds = ref<any>('');

import confirmPopup from '@/views/video/components/confirm_popup.vue';
import { showToast, showConfirmDialog } from 'vant';
onMounted(() => { });
const showConfirm = ref(false);

const tabActive = ref('1');
const tabList = [
  {
    title: '视频专区',
    value: '1',
    activeImg: video_active_icon,
  },
  {
    title: '我的视频',
    value: '2',
    activeImg: my_active_icon,
  },
];

/**
 * 顶部tab切换
 * @param item 顶部tab对象
 */
const changeTab = (item: any) => {

  if (item.value == '2') {
    utils.isLoginDialog((isLogin: boolean) => {
      if (isLogin) {
        tabActive.value = item.value;
        onRefreshList();
      }
    })
  } else {
    tabActive.value = item.value;
    onRefreshList();
  }
  // 切换时-滚动到顶部
  window.scrollTo(0, 0);

};
const shortVideoType = ref<any>('');
function filterList(params) {
  shortVideoType.value = params.shortVideoType;
  companyClassicIds.value = params.areaId;
  onRefreshList();
}
/**
 * 我的视频tab切换
 * @param item 我的视频tab对象
 */
const changeVideoTab = (item: any) => {
  onRefreshList();
};

// 个人作品
const videoType = ref<any>('1');
const profileType = [
  {
    title: '我的作品',
    value: '1',
  },
  {
    title: '收藏视频',
    value: '2',
  },
  {
    title: '赞和收藏',
    value: '3',
  },
  {
    title: '评论',
    value: '4',
  },
];

let pageNum = 1;
const list = ref<any[]>([]);
const loadMoreRef = ref(null);
// 刷新
const onRefreshList = () => {
  pageNum = 1;
  list.value = [];
  getList();
};
// 加载更多
const onLoadMore = () => {
  pageNum++;
  getList();
};

const getList = async () => {
  let params: any = {
    pageNum,
    pageSize: 10,
  };
  let requestMethod = getShortPublicVideos;
  if (tabActive.value !== '1') {
    if (videoType.value === 0) {
      requestMethod = getMyShortVideos;
      params = {
        pageNum,
        pageSize: 10,
        userId: useStore.getUserInfo?.userId, //发布人员id
      };
    }
    if (videoType.value === 1) {
      requestMethod = getMyFavoritesVideos;
    }
    if (videoType.value === 2) {
      requestMethod = getMyLikeAndCollect;
    }
    if (videoType.value === 3) {
      requestMethod = getReplyCommentList;
    }
  } else {
    params = {
      pageNum,
      pageSize: 10,
      areaId: companyClassicIds.value,
      shortVideoType: shortVideoType.value
    };
  }

  const { code, data, total } = await requestMethod(params);
  if (code === 200) {
    list.value = [...list.value, ...data];
  }
  if (loadMoreRef.value) {
    loadMoreRef.value.onLoadSuc(list.value.length, total);
  }
};

function onVideoClick(listnumber: number) {
  let entertype: 1 | 2 | 3 = tabActive.value == '1' ? 1 : videoType.value == 1 ? 3 : 2;
  let userId = useStore.getUserInfo?.userId;

  videoStore.setVideoList(
    unref(list),
    entertype,
    userId,
    entertype == 1
      ? {
        companyClassicIds: companyClassicIds.value,
      }
      : {}
  );
  router.push({
    path: '/video-playback',
    query: { entertype, userId, listnumber },
  });
}

const commentContent = ref('');
const placeholder = ref('请输入评论内容');
const shortComments = ref({});
const showBottomPopup = ref(false);
async function onCommentClick() {
  let { code, data, message } = await publishComment({
    shortVideoId: shortComments.value?.shortVideoId,
    shortVideoCommentsId: shortComments.value?.shortVideoCommentsId,
    content: commentContent.value,
  });
  if (code === 200) {
    commentContent.value = '';
    showToast('回复成功！,请等待审核！');
  } else {
    showToast('回复失败！' + message);
  }
}

function onReply(item) {
  commentContent.value = '';
  placeholder.value = `回复@${item.userName} `;
  shortComments.value = item;
  showBottomPopup.value = true;
}

function deleteContent(item: any) {
  list.value = list.value.filter(e => e.autoId !== item.autoId);
}

function deleteVideo(item: any) {
  showConfirmDialog({
    title: '提示',
    message: '是否要删除视频作品？',
  })
    .then(async () => {
      let { code, message } = await deleteFunVideo({
        shortVideoId: item.shortVideoId,
      });
      if (code == 200) {
        list.value = list.value.filter(e => e.autoId !== item.autoId);
        showToast('删除成功');
      } else {
        showToast('删除失败！' + message);
      }
    })
    .catch(() => {
      showToast('已取消删除');
    });
}

async function showSubmit() {
  utils.isLoginDialog((isLogin: boolean) => {
    if (isLogin) {
      checkUserIdentity({}).then(async res => {
        if (res.data) {
          let { data, message, code } = await checkVideo({});
          if (code == 200) {
            showConfirm.value = true;
          } else {
            showFailToast(message || '作品要求未通过');
          }
        } else {
          showFailToast("仅限南充市认证会员投稿！")
        }
      })
    }
  })
}

const toHonor = () => {
  router.push('/video-cardinfo')
}

</script>
<style lang="scss" scoped>
.video_container {
  background-repeat: no-repeat;
  background-position: 100% auto;

  .list {
    min-height: calc(100vh - 110px);
  }

  .personal {
    border-radius: 30px 30px 0px 0px;
  }

  .personal_tab {
    :deep(.van-tabs--line .van-tabs__wrap) {
      height: 80px;
    }

    :deep(.van-tab) {
      line-height: 1.2;
      height: 100%;
    }

    :deep(.van-tabs__nav--line) {
      height: 100%;
      padding-bottom: 15px;
    }

    :deep(.van-tabs__line) {
      background: linear-gradient(0deg, #a1cbff 0%, #5aa4ff 100%);
      width: 36px;
      height: 6px;
      bottom: 15px;
    }

    :deep(.van-tab__text) {
      font-size: 28px;
    }
  }

  .honor {
    background: linear-gradient(-90deg, #FFCE60 15%, #FFB16B 50%);
    border-radius: 40px 0px 0px 40px;
  }

  .draft {
    background: linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%);
    // border-radius: 0px 40px 40px 0px;
    border-radius: 40px;
  }
}

.videoCommentInput {
  --van-cell-vertical-padding: 5px;

  .van-cell {
    background: #f6f7f8;
    border-radius: 37px;
  }

  --van-button-small-height: 50px;
  --van-button-small-font-size: 26px;
  --van-button-border-width: 0px;
  --van-button-small-padding: 0 20px;
  --van-cell-horizontal-padding: 20px;
  --van-button-radius: 25px;

  .van-button {
    top: -5px;
  }

  :deep(.van-field__body) {
    position: relative;

    .van-field__button {
      position: absolute;
      bottom: 0;
      right: 0;
    }
  }
}

.fix_btn {
  box-shadow: 0 0 7px 0 #d3d3d3;
}
</style>
