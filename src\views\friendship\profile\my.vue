<template>
    <div class="my-profile w-full h-100vh flex flex-col bg-[#F5F5F5]">
        <div class="flex-1 overflow-scroll">
            <div class="profile-header w-full h-520px">
                <div class="swiper-box w-full h-full relative">
                    <van-swipe class="w-full h-full" :autoplay="3000" lazy-render>
                        <van-swipe-item v-for="image, index in myInfo.album" :key="index" @click="previewImages(index)">
                            <img loading="lazy" class="w-full h-full" style="object-fit: cover;" :src="image" />
                        </van-swipe-item>
                        <template #indicator="{ active, total }">
                            <div class="custom-indicator">{{ active + 1 }}/{{ total }}</div>
                        </template>
                    </van-swipe>
                    <div class="icon_box absolute top-20px right-35px">
                        <van-popover v-model:show="showPopover" placement="bottom-end">
                            <div class="pop_list">
                                <div class="pop_item" @click="toPage('/friendship/editLog')">
                                    <span>我的修改记录</span><van-icon name="arrow" />
                                </div>
                            </div>

                            <template #reference>
                                <img loading="lazy" style="filter: drop-shadow(#D76AAC 2px 4px 6px);"
                                    src="@/assets/friendShip/more_icon.png" class="w-33px ml-34px " />
                            </template>
                        </van-popover>
                    </div>

                </div>
            </div>
            <!-- 照片墙 -->
            <!-- <div class="profile-content h-115px flex items-center px-25px mt-30px">
                <div class="left-scroll flex-1 overflow-scroll whitespace-nowrap ">

                    <div class="img w-104px h-104px rounded-20px bg-[#eee] box-border
                     inline-block mr-30px relative" :class="{ 'active-select': currenImageIndex === index }"
                  @click="console.log('1111111111')"    @touchstart="starTouchs" @touchend="endTouchs"  v-for="item, index in myInfo.album" :key="item">
                        <div class="w-full h-full bg-blue rounded-20px bg-cover center"
                            :style="{ backgroundImage: `url(${item})` }"></div>

                        <div @click.stop="console.log('2222222222')" class="delete-icon w-32px h-32px rounded-50% text-#fff text-24px bg-[#FF5353] absolute 
                            right-0px top-0px text-center leading-32px">
                            <van-icon name="cross" />
                        </div>
                    </div>
          
                </div>
                  
                    <van-uploader :multiple="false"  accept="image/*"   :after-read="afterRead">
                <div class="uploader w-104px h-104px bg-#E6E6E6 rounded-20px">
                    1
                    </div>
                    </van-uploader>
                
            </div> -->
            <div class="w-full bg-#fff mt-23px list-centent">
                <van-sticky>
                    <div class="tabs flex h-53px items-end text-30px sticky z-99 bg-[#fff]"
                        :class="currentTab === '2' ? 'left-tab' : 'right-tab'">
                        <div v-for="item, index in tabs" :key="index" class="flex-1 text-center"
                            @click="changeTab(item)">
                            <span class="relative z-10 text-#333"
                                :class="{ '!text-32px': item.value === currentTab }">{{
                                    item.title
                                }}</span>
                        </div>
                    </div>
                </van-sticky>
                <div class="mt-40px px-26px">
                    <div v-show="currentTab === '1'" class="min-h-50vh">
                        <refreshList key="relist" @onRefreshList="onRefreshList" :immeCheck="true"
                            @onLoadMore="onLoadMore" ref="loadMoreRef">
                            <dynamicsCell v-for="item, index in list" :key="index" :detailObj="item" belongTo="my"
                                :singleStatus="myInfo.singleStatus" @handleRefresh="refreshPart(index, true)"
                                @click="toDetail(item, index)">
                            </dynamicsCell>
                        </refreshList>
                    </div>
                    <!-- 个人资料 -->
                    <div class="person-box" v-show="currentTab == '2'">
                        <div class="card">
                            <div class="flex items-center justify-between mb-30px">
                                <span class="flex items-center text-32px text-#444 font-500">{{ myInfo.nickname }}
                                    <span
                                        class="ml-10px mt-3px flex items-center justify-center w-30px h-30px rounded-50%"
                                        :class="myInfo.gender">
                                        <img loading="lazy" class="w-19px h-19px"
                                            :src="myInfo.gender == 'male' ? male : female" alt=""></span>
                                </span>
                                <span class="text-#999 text-26px flex items-center"
                                    @click="toPage('/friendship/personEdit')"><img loading="lazy"
                                        class="w-22px h-24px mr-11px" src="@/assets/friendship/icon_edit.png"
                                        alt="">编辑</span>
                            </div>
                            <div class="text-25px text-#444444 leading-42px pb-20px border-bottom mb-10px">
                                {{ myInfo.biography }}
                            </div>
                            <div class="row-list leading-56px relative">
                                <img loading="lazy" v-show="myInfo.singleStatus == 'y'"
                                    class="absolute w-150px right-0 top-10px"
                                    src="@/assets/friendship/profile/offSingle.png" alt="">
                                <div class="row text-#333333 text-30px"><span
                                        class="text-#999">婚姻情况<span>：</span></span>{{
                                            utils.getDictionaryLabel('maritalStatus', myInfo.maritalStatus) }}
                                </div>
                                <div class="row text-#333333 text-30px"><span
                                        class="text-#999">年龄<span>：</span></span>{{
                                            myInfo.age }}岁
                                </div>
                                <div class="row text-#333333 text-30px"><span
                                        class="text-#999">学历<span>：</span></span>{{
                                            utils.getDictionaryLabel('modelEducation', myInfo.education) }}
                                </div>
                                <div class="row text-#333333 text-30px"><span
                                        class="text-#999">手机号码<span>：</span></span>{{
                                            myInfo.phone
                                        }}
                                </div>
                                <div class="row text-#333333 text-30px"><span
                                        class="text-#999">收入<span>：</span></span>{{
                                            utils.getDictionaryLabel('incomeMonth', myInfo.incomeMonth) }}</div>
                                <div class="row text-#333333 text-30px"><span
                                        class="text-#999">购房情况<span>：</span></span>{{
                                            myInfo.houseStatus == 'y' ? '已购房' : '未购房' }}</div>
                                <div class="row text-#333333 text-30px"><span
                                        class="text-#999">购车情况<span>：</span></span>{{
                                            myInfo.carStatus == 'y' ? '已购车' : '未购车' }}</div>
                                <div class="row text-#333333 text-30px"><span
                                        class="text-#999">户籍<span>：</span></span>{{
                                            myInfo.residence }}
                                </div>
                                <div class="row text-#333333 text-30px"><span
                                        class="text-#999">所属工会<span>：</span></span>{{
                                            myInfo.companyName }}</div>
                                <div class="row text-#333333 text-30px"><span
                                        class="text-#999">注册时间<span>：</span></span>
                                    {{ dayjs(myInfo.createTime).format('YYYY-MM-DD') }}
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="info-title w-full flex items-center justify-between">
                                <span class="text-32px text-#444444 font-500">择偶标准</span>
                            </div>
                            <div class="row-list leading-56px">
                                <div class="row text-#333333 text-30px"><span
                                        class="text-#999">年龄<span>：</span></span>{{
                                            myInfo.singleMateChoose?.lowerAge }}岁-{{ myInfo.singleMateChoose?.upperAge }}岁
                                </div>
                                <div class="row text-#333333 text-30px"><span
                                        class="text-#999">身高<span>：</span></span>{{
                                            myInfo.singleMateChoose?.lowerHeight }}cm-{{ myInfo.singleMateChoose?.upperHeight
                                    }}cm
                                </div>
                                <div class="row text-#333333 text-30px"><span
                                        class="text-#999">学历<span>：</span></span>{{
                                            utils.getDictionaryLabel('modelEducation', myInfo.singleMateChoose?.education ?
                                                myInfo.singleMateChoose?.education : '不限') }}
                                </div>
                                <div class="row text-#333333 text-30px"><span
                                        class="text-#999">收入<span>：</span></span>{{
                                            utils.getDictionaryLabel('incomeMonth',
                                                myInfo.singleMateChoose?.incomeMonth ? myInfo.singleMateChoose?.incomeMonth : '不限')
                                        }}
                                </div>
                                <div class="row text-#333333 text-30px"><span
                                        class="text-#999">购房情况<span>：</span></span>{{
                                            myInfo.singleMateChoose?.houseStatus == 'y' ? '有房' : '不限' }}
                                </div>
                                <div class="row text-#333333 text-30px"><span
                                        class="text-#999">购车情况<span>：</span></span>{{
                                            myInfo.singleMateChoose?.carStatus == 'y' ? '有车' : '不限' }}
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <div class="info-title w-full flex items-center justify-between">
                                <span class="text-32px text-#444444 font-500">兴趣爱好</span>
                                <span :class="showEdit ? 'text-#3D92FF' : 'text-#999'"
                                    class="text-26px flex items-center" @click="editFn()"><img loading="lazy"
                                        class="w-24px h-24px mr-11px" :class="showEdit ? 'mt-5px' : ''"
                                        :src="showEdit ? finish : edit" alt="">{{ showEdit ? '完成'
                                            : '编辑' }}</span>
                            </div>
                            <div class="select-label mb-15px flex items-center flex-wrap">
                                <div class="bg-#E6F2FF text-[#5AA4FF] text-30px py-12px px-28px rounded-12px box-border
                                relative mr-10px" v-for="it, index in labelList" :key="it.autoId">
                                    <span>{{ it.labelName }}</span>
                                    <div @click="removeList(index)" class="w-30px h-30px rounded-50% bg-[#3D92FF] absolute right-0 top-0
                                        text-#fff flex items-center justify-center leading-none" v-show="showEdit">
                                        <div class="w-19px h-4px bg-#fff rounded-2px"></div>
                                    </div>
                                </div>
                                <div class="add-label  text-[#565656] text-30px py-10px px-26px rounded-12px box-border"
                                    v-show="showEdit" @click="addLabel">
                                    + 添加
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

            </div>
        </div>
        <div class="controll-btn h-137px w-full flex items-center justify-between px-30px bg-#fff">
            <div class="unsingle rounded-40px text-#fff text-32px flex items-center justify-center flex-1 h-82px mr-20px"
                @click="offSingle">
                <img loading="lazy" :src="myInfo.singleStatus == 'n' ? unsingle : cxjy" class="w-36px" />
                <span>{{ myInfo.singleStatus == 'n' ? '我已脱单' : '重新交友' }}</span>
            </div>
            <div class="publish rounded-40px text-#fff text-32px 
            flex items-center justify-center flex-1 h-82px" v-show="myInfo.singleStatus == 'n'"
                @click="toPage('/friendship/pushDynamic')">
                <img loading="lazy" src="@/assets/friendShip/profile/icon_publish.png" class="w-36px" />
                <span>发布动态</span>
            </div>
        </div>
        <!-- 标签弹窗 -->
        <labelPopup v-model:show="showLabel" :defaultChecked="defaultLabel" @confirmed="confirmedLabel" />
        <!-- 脱单弹窗 -->
        <van-popup v-model:show="showOffSingle">
            <div class="w-80vw h-800px relative" v-if="myInfo.singleStatus == 'y'">
                <img loading="lazy" class="w-full h-full" src="@/assets/friendShip/profile/pop_offsingle.png" alt="">
                <div class=" btn w-440px h-80px absolute " @click="showOffSingle = false"></div>
            </div>
            <div class="w-80vw h-800px relative onSingle flex flex-col items-center" v-if="myInfo.singleStatus == 'n'">
                <div class="welcome text-36px text-#4A4168 mb-50px">欢迎您回来！</div>
                <div class="text text-30px text-#Fe6594 leading-46px">愿你在寻找真爱的路上 一帆风顺，早日找到那个 与你共度一生的人</div>
                <img loading="lazy" class=" btn w-410px h-100px absolute " @click="showOffSingle = false"
                    src="@/assets/friendShip/profile/btn_onSingle.png" alt="">
            </div>
        </van-popup>

    </div>
</template>
<script setup lang="ts">
defineOptions({
    name: 'FriendshipMyProfile'
})
import { queryMyDetail, getPersonDynamics, updateLabelApi, offSingleApi } from '@/api/friendShip/index'
import { uploadFile } from '@/api/public';
import labelPopup from '../components/labelPopup.vue'
import dynamicsCell from '../components/dynamicsCell.vue'
import refreshList from '@/components/refreshList/index.vue'
import male from '@/assets/friendShip/male_icon_w.png'
import female from '@/assets/friendShip/female_icon_w.png'
import edit from '@/assets/friendship/icon_edit.png'
import finish from '@/assets/friendship/icon_finish.png'
import cxjy from '@/assets/friendship/profile/icon_cxjy.png'
import unsingle from '@/assets/friendShip/profile/icon_unsingle.png'
import utils from '@/utils/utils'
import dayjs from 'dayjs'
import router from '@/router';
import { showToast } from 'vant'
import { useFriendShipStore } from '@/store/modules/friendShip.ts'
import useRefreshFun from '@/hooks/app.ts'
const friendShipStore = useFriendShipStore()
onMounted(() => {
    getMyDetail()
})
const currentTab = ref('2');
const tabs = ref([

    {
        title: '个人信息',
        value: '2',
    },
    {
        title: '我的动态',
        value: '1',
    },
])
const myInfo = ref<any>({})
const showPopover = ref(false)
const images = ref<any>([])
const currenImageIndex = ref(0)
const getMyDetail = () => {
    queryMyDetail().then((res) => {
        if (res.code !== 200) return
        myInfo.value = res.data
        labelList.value = myInfo.value.singleLabelList ? myInfo.value.singleLabelList : []

        if (labelList.value) {
            labelList.value.forEach(element => {
                element.autoId = element.labelId
            });
        }
        if (myInfo.value?.album) {
            myInfo.value.album = myInfo.value.album.split(',').map((item: string) => {
                return utils.judgeStaticUrl(item);
            })
        }
        else myInfo.value.album = []
        if (myInfo.value?.avatar) {
            myInfo.value.avatar = utils.judgeStaticUrl(myInfo.value?.avatar, true)
            myInfo.value.album.unshift(myInfo.value?.avatar)
        }
    })
}
const pageNum = ref(1);
const loadMoreRef = ref()
const list = ref<any>([]);
const onLoadMore = () => {
    pageNum.value++;
    getMyDynamics()
}
const onRefreshList = () => {
    pageNum.value = 1;
    getMyDynamics();
};
const getMyDynamics = async () => {
    const { data, code, total } = await getPersonDynamics({
        pageNum: pageNum.value,
        pageSize: 10,
    })
    if (code === 200) {
        if (pageNum.value === 1) list.value = data;
        else list.value = [...list.value, ...data];
    }
    if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(list.value.length, total);
    }
}
const changeTab = (item: any) => {
    currentTab.value = item.value
};
//详情
function toPage(path, query) {
    router.push({
        path,
        query
    })
}
// 局部刷新
const refreshPart = (index: number, refresh: boolean = false) => {
    let currenPageNum = Math.floor(index / 10) + 1
    if (!refresh) return friendShipStore.setdynamicPageNum(currenPageNum)
    refreshHandle(currenPageNum)
}
const refreshHandle = (currenPageNum: number) => {
    if (currenPageNum > 1) {
        let endIndex = ((currenPageNum - 1) * 10) - 1
        list.value = list.value.slice(0, endIndex)
    }
    pageNum.value = currenPageNum
    getMyDynamics()
}
// 其他页面触发刷新执行事件
const savePageNum = computed(() => friendShipStore.getDynamicsPageNum)
const changePage = () => {
    refreshHandle(savePageNum.value)
}
const { refresh } = useRefreshFun()
refresh([
    { name: 'changePage', funsName: [changePage] }
])
// end

// 显示编辑
const showEdit = ref(false)
const editFn = () => {
    if (showEdit.value) {
        updateLabelApi({
            labelIdList: labelList.value.map((item: any) => item.autoId)
        }).then(res => {
            if (res.code == 200) {
                showToast('修改成功')
                showEdit.value = false
                // getMyDetail()
            }
            else showToast(res.message)
        })
    } else {
        showEdit.value = true
    }
}
const labelList = ref<any>([])//当前标签
const showLabel = ref(false)
const defaultLabel = ref([]) //默认标签
const confirmedLabel = (val: any) => {
    labelList.value = val
    showLabel.value = false
}
const addLabel = () => {
    defaultLabel.value = labelList.value && labelList.value.length ? labelList.value.map((item: any) => item.autoId) : []
    showLabel.value = true
}
const removeList = (index: number) => {
    labelList.value.splice(index, 1)
}
const toDetail = (item: any, index: number) => {
    refreshPart(index)
    // 存储当前动态
    router.push({
        path: '/friendship/dynamicDetail',
        query: {
            postId: item.postId
        }
    })
}
const btnClick = ref(false)
const showOffSingle = ref(false)
const offSingle = () => {
    if (btnClick.value) return
    btnClick.value = true
    offSingleApi().then(res => {
        if (myInfo.value.singleStatus == 'n') {
            showOffSingle.value = true
            myInfo.value.singleStatus = 'y'
        } else {
            showOffSingle.value = true
            myInfo.value.singleStatus = 'n'

            // showToast('人有悲欢离合,月有阴晴圆缺')
        }
        setTimeout(() => {
            btnClick.value = false
        }, 1000)
    }).catch(err => {
        setTimeout(() => { btnClick.value = false }, 1000)

    })
}
// 图片预览
const previewImages = (index: number) => {
    utils.imagePreview(myInfo.value.album, index)
}
</script>
<style scoped lang="scss">
:deep(.van-popover__content) {
    .pop_list {
        width: 250px;
        box-shadow: 0px 1px 25px 2px rgba(88, 88, 88, 0.21);
        border-radius: 16px;
    }

    .pop_item {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        padding: 15px 12px 12px 15px;
        border-bottom: 1px solid #EBEBEB;
        font-weight: 400;
        font-size: 26px;
        color: #333333;
    }
}

:deep(.van-popup) {
    background: transparent;

    .btn {
        left: 50%;
        transform: translate(-50%, -50%);
        bottom: 31px;
    }

    .onSingle {
        background: url('@/assets/friendship/profile/bg_onSingle.png');
        background-size: 100% 100%;
        background-position: center center;
        box-sizing: border-box;
        padding: 280px 100px 0;
    }
}

.profile-header {
    background-size: cover;
    background-position: center;
    background-image: url('@/assets/friendShip/defualt_banner.png');
}

.controll-btn {
    box-shadow: 0px -6px 9px 0px rgba(186, 194, 201, 0.16);

    .unsingle {
        background: linear-gradient(-84deg, #FE779A, #FA9CAB);
    }

    .publish {
        background: linear-gradient(-84deg, #489CFF, #90CCFD);
    }
}

.profile-content {}

.list-centent {
    border-radius: 30px 30px 0 0;
}

.tabs {
    background-size: 100% auto;
    background-position: left -10px;
}

.left-tab {
    background-image: url('@/assets/friendShip/person_active_bj.png');
}

.right-tab {
    background-image: url('@/assets/friendShip/person_profile_bj.png');
}

.active-select {
    border: 1px solid #FF5353;
    padding: 6px;
}

.person-box {
    .tag-box {
        display: flex;
        flex-flow: row wrap;
        margin-top: 20px;
        margin-bottom: -15px;

        .tag {
            background: #EAF1F9;
            border-radius: 12px;
            padding: 12px 14px;
            font-weight: 400;
            font-size: 28px;
            color: #95B3D8;
            margin-right: 17px;
            margin-bottom: 15px;

            &.active {
                background: #5AA4FF;
                color: #FFFFFF;
            }
        }
    }

    .card {
        box-sizing: border-box;
        padding: 27px 26px;

        .border-bottom {
            border-bottom: 1px solid #EBEBEB;
        }

        .female {
            background: linear-gradient(90deg, #FAA8AB, #FF7097);
        }

        .male {
            background: linear-gradient(90deg, #9FC9FF, #4196FF);
        }
    }

    .info-title {
        position: relative;
        box-sizing: border-box;
        padding-left: 20px;
        margin-bottom: 30px;

        &::after {
            content: '';
            width: 7px;
            height: 35px;
            background: #3D92FF;
            border-radius: 3px;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translate(-50%, -50%);

        }
    }
}

.add-label {
    border: 1px solid #565656;
}

.swiper-box {
    .custom-indicator {
        position: absolute;
        right: 30px;
        bottom: 13px;
        padding: 5px 20px;
        font-size: 28px;
        color: #FFFFFF;
        border-radius: 18px;
        background: rgba(0, 0, 0, 0.2);
    }
}
</style>