<template>
  <div class="vieAnswerActivity w-full h-full relative">

    <template v-if="activityDetail.appDetailsCover">
      <img loading="lazy" :src="$store.state.visitPrefix + activityDetail.appDetailsCover" alt="" class="w-full h-full">
    </template>
    <template v-else>
      <div class="absolute  top-30px left-30px flex items-center text-35px text-[#fff]">
        <img loading="lazy" src="@/assets/activity/logo.png" alt="" class="w-60px mr-10px">
        {{ activityDetail?.companyName || '' }}
      </div>
      <img loading="lazy" src="@/assets/activity/bg2.jpg" alt="" class="w-full h-full">
      <div class="absolute top-200px w-full flex justify-center text-[#fff] text-60px px-50px text-center leading-70px">
        {{ activityDetail?.activityName || '投票活动' }}
      </div>
    </template>
    <div class="absolute top-950px w-full flex flex-col items-center">
      <div class="w-320px h-88px btn flex justify-center items-center text-[#fff] text-42px  mb-40px" @click="toVote">
        立即投票
      </div>
      <div class="flex btns w-90/100 justify-around">
        <div class="" @click="toDetail">
          <img loading="lazy" src="@/assets/activity/icon_hdxq.png" alt="">
          活动详情
        </div>
        <div class="" @click="toRankList" v-if="activityDetail.voteInfo?.enableRank === 'y'">
          <img loading="lazy" src="@/assets/activity/icon_phb.png" alt="">
          排行榜
        </div>
        <div @click="toLotteryRecord" v-if="activityDetail.luckDraw === 'y'">
          <img loading="lazy" src="@/assets/activity/icon_zjjl.png" alt="">
          中奖记录
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { activityDetailValidator, defaultValidator } from '@/hooks/useValidator.js';
import useRefreshFun from '@/hooks/app.ts';
export default {
  data() {
    return {
    }
  },
  computed: {
    activityDetail() {
      return this.$store.state.activityDetail || {}
    },
  },
  mounted() {
    refresh()
    // 其他页面触发刷新执行事件
    const { removeIncludesList } = useRefreshFun()
    const refresh = () => {
      //刷新整个页面'
      removeIncludesList('lotteryRecord');
    }
  },
  methods: {
    toVote() {
      if (!activityDetailValidator()) return
      try {
      } catch (error) { }
      this.$router.push('/voteActivity/voteList')
    },
    toDetail() {
      if (!activityDetailValidator()) return
      this.$router.push('/activityDetail')
    },
    toRankList() {
      this.$router.push('/voteActivity/rankList')
    },
    toLotteryRecord() {
      if (!defaultValidator()) return
      this.$router.push('/lotteryRecord')
    },
  },
}
</script>

<style lang="scss" scoped>
.vieAnswerActivity {
  background-color: #9bbefe;

  .btn {
    // background-image: url('@/assets/activity/draw_btn.png');
    background: linear-gradient(0deg, #BF1A05, #E24632);
    border-radius: 44px;
    border: 3px solid rgba(255, 255, 255, 0.5);
  }

  .btns {
    >div {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 208px;
      height: 68px;
      font-size: 29px;
      color: #BF1A05;
      background: #FFFFFF;
      border-radius: 34px;
      border: 1px solid #FFFFFF;
      font-weight: 550;

      img {
        width: 30px;
        margin-right: 5px;
      }
    }
  }
}
</style>
