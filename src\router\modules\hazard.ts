export default [
    {
        path: "/hazard",
        name: "hazardIndex",
        component: () => import("@/views/hazard/index.vue"),
        meta: {
            title: "隐患上报",
            isShowTabBar: false,
            isBack: true,
            keepAlive:true,
            updatePath:['/my']
        }
    },
    {
        path: "/hazard/form",
        name: "hazardForm",
        component: () => import("@/views/hazard/form.vue"),
        meta: {
            title: "上报填写",
            isShowTabBar: false,
            isBack: true,
        }
    },
    {
        path: "/hazard/list",
        name: "hazardList",
        component: () => import("@/views/hazard/list.vue"),
        meta: {
            title: "上报记录",
            isShowTabBar: false,
            isBack: true,
            keepAlive:true,
            updatePath:['/my','/hazard/form']
        }
    },
]