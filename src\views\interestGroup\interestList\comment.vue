<template>
    <div class="comment p-30px">
        <van-form>
            <div class="bg-#fff rounded-20px px-26px py-30px mb-20px">
                <div class="flex">
                    <img loading="lazy"
                        :src="Data.actDetail?.appCover != '' ? useStore.getPrefix + Data.actDetail?.appCover : bannerImg"
                        alt="" class="w-200px h-110px rounded-15px">
                    <div class="text-28px text-#333 pl-35px">{{ Data.actDetail?.activityName }}</div>
                </div>
                <div class="flex justify-center mt-20px">
                    <van-rate v-model="Data.params.ActRate" :size="32" color="#ffd21e" void-icon="star"
                        void-color="#eee" />
                </div>
            </div>
            <div class="bg-#fff rounded-20px px-26px py-30px mb-20px">
                <div class="flex">
                    <img loading="lazy"
                        :src="Data.groupInfo?.logo != '' ? judgeStaticUrl(Data.groupInfo?.logo) : bannerImg" alt=""
                        class="w-88px h-88px rounded-1/2">
                    <div class="text-28px text-#333 pl-35px">{{ Data.groupInfo?.groupName }}</div>
                </div>
                <div class="flex justify-center mt-20px">
                    <van-rate v-model="Data.params.groupRate" :size="32" color="#ffd21e" void-icon="star"
                        void-color="#eee" />
                </div>
            </div>
            <div class="bg-#fff rounded-20px px-26px py-30px mb-20px">
                <div class="text-29px">分享心得体验</div>
                <div class="textarea">
                    <van-field required v-model="Data.params.introduce" label="" type="textarea" label-width="0"
                        placeholder="请输入活动评价"></van-field>
                </div>
            </div>
        </van-form>

        <div class="btn w-65/100 m-auto text-34px text-#fff py-23px text-center fixed bottom-40px left-1/2 -translate-x-1/2"
            @click="submit">确定</div>
    </div>
</template>
<script lang="ts" setup>
import { detailByGroupId, interestComments } from '@/api/interestGroup';
import { getDetails } from '@/api/activity';
import { useRoute } from "vue-router";
import { useUserStore } from '@/store/modules/user';
const useStore = useUserStore();
const route = useRoute();
import bannerImg from '@/assets/interest/banner2.jpg'
import { showFailToast, showSuccessToast } from 'vant';
import router from '@/router';
import { judgeStaticUrl } from '@/utils/utils';
const Data = ref({
    params: {
        groupRate: 0,
        ActRate: 0,
        introduce: '',
        groupInfo: {},
        actDetail: {}
    }
})
//小组详情
function getGroupDetail() {
    detailByGroupId({
        groupId: route.query.groupId
    }).then(res => {
        if (res.code == 200) {
            Data.value.groupInfo = res.data;
        }
    })
}
//获取详情
function getActDetails() {
    getDetails({
        activityId: route.query.activityId
    }).then(res => {
        if (res.code == 200) {
            Data.value.actDetail = res.data;
        }
    })
}
//提交
function submit() {
    interestComments({
        content: Data.value.params.introduce,
        score: Data.value.params.ActRate,//活动打分
        groupScore: Data.value.params.groupRate,//小组打分
        groupId: route.query.groupId,
        sourceId: route.query.activityId,//活动id
        dataSources: "activity", //评论来源 : group:小组  activity：活动
        commentType: "activityComment" //评论类型 （groupComment：小组评价，dynamic：小组动态，activityComment：活动评论，activityDynamic：活动评价 ）
    }).then(res => {
        if (res.code == 200) {
            showSuccessToast("评论成功");
            router.go(-1)
        } else {
            showFailToast(res.message)
        }
    })
}
onMounted(() => {
    getGroupDetail();
    getActDetails();
})
</script>
<style lang="scss">
.comment {
    background: #F6F7F8;
    min-height: 100vh;

    .textarea {
        .van-field {
            padding: 0;
        }

        .van-field__control {
            background: #F6F7F8;
            border-radius: 8px;
            padding: 20px;
        }

        .van-field__body {
            margin-top: 15px;
        }
    }

    .btn {
        background: url("@/assets/public/button.png") no-repeat;
        background-size: 100% 100%;
    }
}
</style>