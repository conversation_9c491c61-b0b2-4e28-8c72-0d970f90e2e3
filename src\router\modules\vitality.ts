export default [
    {
        path: '/vitality',
        name: 'Vitality',
        component: () => import('@/views/vitality/index.vue'),
        meta: {
            title: '活力南充',
            isShowTabBar: false,
            isBack: true,
        },
    },
    {
        path: '/vitality/volunteerService',
        name: 'VolunteerService',
        component: () => import('@/views/vitality/volunteerService/index.vue'),
        meta: {
            title: '志愿者服务',
            isShowTabBar: false,
            isBack: true,
        },
    },
    {
        path: '/vitality/volunteerService/myActivity',
        name: 'VitalityMyAtivity',
        component: () => import('@/views/vitality/volunteerService/myActivity.vue'),
        meta: {
            title: '我的活动',
            isShowTabBar: false,
            isBack: true,
        },
    },
    {
        path: '/vitality/walkActivity',
        name: 'walkActivity',
        component: () => import('@/views/vitality/walkActivity.vue'),
        meta: {
            title: '我的活动',
            isShowTabBar: false,
            isBack: true,
        },
    },

    {
        path: '/vitality/personalInfo',
        name: 'personalInfo',
        component: () => import('@/views/vitality/personalInfo/index.vue'),
        meta: {
            title: '个人信息',
            isShowTabBar: false,
            isBack: true,
        },
    },
    {
        path: '/vitality/personalInfo/info',
        name: 'perInfo',
        component: () => import('@/views/vitality/personalInfo/info.vue'),
        meta: {
            title: '个人信息',
            isShowTabBar: false,
            isBack: true,
        },
    },
    {
        path: '/vitality/rewards',
        name: 'rewards',
        component: () => import('@/views/vitality/rewards.vue'),
        meta: {
            title: '奖励积分',
            isShowTabBar: false,
            isBack: true,
        },
    },
    {
        path: '/vitality/coolSummer',
        name: 'coolSummer',
        component: () => import('@/views/vitality/coolSummer.vue'),
        meta: {
            title: '夏日送清凉',
            isShowTabBar: false,
            isBack: true,
        },
    },
];
