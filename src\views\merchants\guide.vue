<template>
    <div class="guide flex flex-col">
        <div class="txt-title flex flex-col items-center justify-center">
            <img loading="lazy" src="@/assets/merchants/guide/title.png" class="w-60%" />
        </div>
        <div class="guide-pic flex flex-col items-center justify-center">
            <img loading="lazy" src="@/assets/merchants/guide/pic.png" />
        </div>
        <div class="text-center w-40% h-94px rounded-45px mx-auto leading-none
        text-32px text-#fff mt-10 confirm-btn flex items-center justify-center" @click="toSubmit">
           立即入驻
        </div>
    </div>
</template>
<script lang="ts" setup>
const router = useRouter();
const toSubmit = () => {
    router.replace({
        path:'/merchants/form'
    });
}


</script>
<style scoped lang="scss">
.guide {
    width: 100vw;
    min-height: 100vh;
    background-image: url('@/assets/merchants/guide/bg.png');
    background-size: cover;

    .confirm-btn {
        background: linear-gradient(90deg, #4489FF 0%, #15ABFF 100%);
    }

    .txt-title {
        flex: .25;

        img {
            width: calc(100% - 336px);
        }
    }

    .guide-pic {
        img {
            width: calc(100% - 110px);
        }

        flex: .55;
    }
}
</style>