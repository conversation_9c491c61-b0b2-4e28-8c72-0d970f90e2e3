<template>
    <div class="my-message flex flex-col bg-#F6F7F8 w-full min-h-full pt-29px border-box">
        <div class="date h-100px px-25px relative">
            <img loading="lazy" src="@/assets/my/message/border-bg.png" class="w-full" />
            <div class="absolute left-35px right-35px top-5px bottom-0
                flex items-center justify-between text-30px px-20px" @click="handleDatePicker">
                <div class="flex items-center">
                    <img loading="lazy" src="@/assets/my/message/icon_time.png" class="w-50px h-40px mr-28px">
                    <span class="text-#333">{{ currentDate[0] }}.{{ currentDate[1] }}</span>
                </div>
                <div>
                    <van-icon name="arrow-down" class="ml-10px" color="#999"></van-icon>
                </div>
            </div>
        </div>
        <div class="list flex-1 overflow-scroll-y px-30px box-border w-full">
            <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef"
                :slotEmpty="true">
                <div class="message-cell px-30px py-32px bg-#fff rounded-16px relative mb-20px"
                    @click="lookDetail(item)" v-for="item, index in list" :key="index">
                    <div class="no-red w-13px h-13px rounded-50% bg-#FC442F absolute top-30px right-30px"
                        v-if="item.readFlag === 'n'"></div>
                    <div class="text-ellipsis line-clamp-2 text-wrap">
                        <!-- 判断消息类型 -->
                        <img loading="lazy" :src="item.noticeType === 'anno' ? iconNotice :
                            item.noticeType === 'prompt' ? iconTips :
                                item.noticeType === 'reply' ? iconRepaly : iconReq
                            " class="w-40px h-40px mr-10px align-middle" />

                        <div class="text-#333 font-500 text-32px inline text-justify align-middle">
                            {{ item.mesTitle }}
                        </div>
                    </div>
                    <div class="text-#999 text-24px mt-20px text-ellipsis line-clamp-3 whitespace-nowrap text-wrap"
                        v-if="item.sysContent" v-html="item.sysContent">
                    </div>
                    <div class="bottom text-24px text-#999 flex justify-between pt-20px mt-20px">
                        <div class="time leading-none">{{ item.choiceSendTime }}</div>
                        <div class="flex items-center">
                            <span class="leading-none">查看详情</span>
                            <van-icon name="arrow" class="ml-10px leading-none"></van-icon>
                        </div>
                    </div>
                </div>
                <template #noData>
                    <van-empty :image="noMessage" description="暂无消息" :image-size="['60%', 'auto']">
                    </van-empty>
                </template>
            </refreshList>
        </div>
        <van-popup v-model:show="dateSelectShow" position="bottom">
            <!-- 日期选择器 -->
            <van-date-picker v-model="targetDate" title="选择年月" :min-date="minDate" :max-date="maxDate"
                :formatter="formatter" :columns-type="columnsType" @confirm="confirmDate" />
        </van-popup>
    </div>
</template>

<script lang="ts" setup>
defineOptions({
    name: 'myMessage'
})
import refreshList from '@/components/refreshList/index.vue';
import iconNotice from '@/assets/my/message/icon_notice.png';
import iconRepaly from '@/assets/my/message/icon_repaly.png';
import iconReq from '@/assets/my/message/icon_req.png';
import iconTips from '@/assets/my/message/icon_tips.png';
import noMessage from '@/assets/my/message/no_message.png'
import { useUserStore } from '@/store/modules/user';
import { userMessageList, readMessage } from '@/api/message';
const router = useRouter();
const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);
onMounted(() => {
    getList()
})
// 查看
const lookDetail = async (item: any) => {
    // 未读才调接口
    if (item.readFlag === 'n') {
        const { code } = await readMessage({
            autoId: item.autoId,
            userAccount: userInfo.value?.phone
        })
        if (code === 200) item.readFlag = 'y';
    }
    // 存储
    userStore.setCurrentMessageInfo(item)
    router.push({
        path: '/my/messageDetail'
    })
};
// 列表
let pageNum = 1;
const list = ref<any>([]);
const loadMoreRef = ref<any>()
// 刷新
const onRefreshList = () => {
    pageNum = 1;
    getList();
};
// 加载更多
const onLoadMore = () => {
    pageNum++;
    getList();
};

const getList = async () => {
    const { code, data, total } = await userMessageList({
        pageNum,
        pageSize: 10,
        userAccount: userInfo.value?.phone,
        findTime: currentDate.value.join('-')
    })
    if (code === 200) {
        if (pageNum === 1) list.value = data
        else list.value = list.value.concat(data)
    }
    if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(list.value.length, total)
    }
}

// 日期展示
const currentMonth = new Date().getMonth() + 1 < 10 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1
const currentDate = ref([new Date().getFullYear(), currentMonth])
const targetDate = ref<any>()
const columnsType = ['year', 'month']
const minDate = new Date(1949, 0, 1)
// 最大月份当月
const getYear = new Date().getFullYear()
const getMonth = new Date().getMonth()
const maxDate = new Date(getYear, getMonth, 1)

const formatter = (type, option) => {
    if (type === 'year') {
        option.text += '年';
    }
    if (type === 'month') {
        option.text += '月';
    }
    return option;
};
const dateSelectShow = ref(false)
const handleDatePicker = () => {
    targetDate.value = currentDate.value
    dateSelectShow.value = true
}
const confirmDate = ({ selectedValues }) => {
    currentDate.value = selectedValues
    dateSelectShow.value = false
    onRefreshList()
}
</script>

<style lang="scss" scoped>
.my-message {
    background: linear-gradient(180deg, #C5E3FF 0%, #F6F7F8 8%, #F6F7F8 100%);
   
    .list {
        .bottom {
            border-top: 1px solid #ECECEC;
        }
    }
}
</style>