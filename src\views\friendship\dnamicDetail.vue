<template>
    <div class="dnamic-detail flex flex-col">
        <div class="flex-1">
            <div class="px-30px overflow-hidden py-30px">
                <dynamicsCell :singleStatus="myInfos.singleStatus" :detailObj="details" :showComment="false"
                    @handleRefresh="handleRefresh" />
            </div>
            <div class="mt-12px conment-border p-30px">
                <commentList :contentArry="details?.commentVOList?.treeList" :postUserId="details?.userId"
                    :currrentUserId="myInfos?.singleUserId" commentType="detail" @replyed="getReplyed" />
            </div>
        </div>
        <div class="control_btn flex items-end justify-between h-fit
                bg-[#fff] w-100% px-30px py-20px box-border">
            <div class="input flex items-center bg-[#F1F1F1] flex-1 rounded-37px min-h-full  relative">
                <div class="w-39px ml-40px" v-show="!conment">
                    <img loading="lazy" src="@/assets/friendShip/comment_icon.png" class="w-full" />
                </div>
                <van-field v-model="conment" placeholder="写评论" type="textarea" rows="1" autosize
                    :class="{ 'mb-55px': conment }" ref="commentref"></van-field>
            </div>
            <div @click="sendComment"
                class="right-control flex items-center ml-38px w-163px h-82px text-32px rounded-40px flex items-center justify-center leading-none text-#fff">
                发布
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import dynamicsCell from './components/dynamicsCell.vue';
import { getDynamicsList, queryMyDetail, dynamicSendComment } from '@/api/friendShip/index'
import commentList from './components/commentList.vue'
import { showConfirmDialog, showToast } from 'vant';
import useRefreshFun from '@/hooks/app.ts'
const router = useRouter()
const route = useRoute()
onMounted(() => {
    getDetail()
    getMyDetailFn()
})
const details = ref({} as any)
const getDetail = () => {
    getDynamicsList({
        postId: route.query.postId as string,
        queryType: 'all'
    }).then((res: any) => {
        if (res.data?.length) details.value = res.data[0]
        else {
            details.value = {}
            showToast('动态不存在')
            setTimeout(() => {
                router.back()
            }, 1000)
        }
    })
}
const parentCommentId = ref('') // 父评论id
const getReplyed = (data: any) => {
    parentCommentId.value = data?.autoId
    commentref.value?.focus()
}
const commentref = ref(null)
const conment = ref('')
// 发送评论
const sendComment = async () => {
    const { code, message } = await dynamicSendComment({
        postId: details.value.postId,
        content: conment.value,
        parentCommentId: parentCommentId.value
    })
    if (code === 200) {
        showConfirmDialog({
            message: '评论成功！您评论的内容正在审核中,请耐心等待',
            confirmButtonText: '确定'
        })
        conment.value = ''
        getDetail()
        refresh()
    } else {
        showToast(message)
    }
}
const myInfos = ref<any>({})
const getMyDetailFn = () => {
    queryMyDetail().then((res: any) => {
        if (res.data) myInfos.value = res.data
    })
}
const handleRefresh = () => {
    getDetail()
    refresh()
}
// 触发广场刷新
const { addRefreshList } = useRefreshFun();
const refresh = () => {
    addRefreshList({ pageName: 'FriendshipSquare', funsName: ['changePage'] })
    addRefreshList({ pageName: 'FriendshipMyProfile', funsName: ['changePage'] })
    addRefreshList({ pageName: 'FriendshipDetail', funsName: ['changePage'] })
}
// end
</script>
<style scoped lang="scss">
.conment-border {
    border-top: 12px solid #F5F5F5;
}

.control_btn {
    box-shadow: 0px -6px 9px 0px rgba(186, 194, 201, 0.16);

    .input {
        :deep(.van-cell) {
            background-color: transparent;
        }

        :deep(.van-field__control) {
            font-size: 26px;
        }

        .active {
            background-color: #5ca5ff;
            color: #fff;
        }


    }

    .right-control {
        background: linear-gradient(-84deg, #FE769A, #FA9DAB);
    }
}

.conment-border {
    .comment-list:nth-child(2) {
        margin-left: 10px;
    }
}
</style>