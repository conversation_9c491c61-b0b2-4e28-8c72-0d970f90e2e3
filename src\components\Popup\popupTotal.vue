<template>
    <div class="total-popup w-full h-full fixed left-0 top-0 z-9999 " v-if="showTotalPop" @click="closeTotal">
        <div class=" bg-box  pt-180px px-100px pb-50px box-border overflow-hidden relative" ref="popBoxSty">
            <div class=" box-border h-full overflow-hidden">
                <Vue3SeamlessScroll :list="data" hover :step="0.4" :wheel="true" direction="up" class="w-full h-full">
                    <div v-for="(item, index) of data" :key="index" class="flex items-center justify-between  mb-19px">
                        <div class="flex items-center">
                            <img loading="lazy" :src="item.src" alt="" class="w-57px h-57px" />
                            <div class="text-#666666 text-28px pl-35px">{{ item.titName }}</div>
                        </div>
                        <div class="text-#5AA4FF text-30px ml-30px flex justify-end">{{ item.allNumb }}</div>
                    </div>
                </Vue3SeamlessScroll>

            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { Vue3SeamlessScroll } from "vue3-seamless-scroll";
const emit = defineEmits(['closeTotal']);
const props = defineProps({
    showTotalPop: {
        type: Boolean,
        default: false
    },
    data: {
        type: Array,
        default: []
    }
})
function closeTotal() {
    // popBoxSty.value.style.cssText = `height: 0px; width: 20%;top: 5%;left: 10%;`
    emit('closeTotal')
}
const popBoxSty = ref(null)
onMounted(() => {
    if (props.showTotalPop) {
        console.log(popBoxSty.value);

    }

})
watch(
    () => props.showTotalPop,
    (newValue, oldValue) => {
        if (props.showTotalPop) {
            setTimeout(() => {
                popBoxSty.value.style.cssText = `width: 80%;top: 25%;left: 150%;height: 40px;`
            }, 10)
            setTimeout(() => {
                popBoxSty.value.style.cssText = `width: 80%;top: 25%;left: 50%;height: 73.4vw;`
            }, 410)
        }

    },
    { immediate: true }
);
</script>
<style lang="scss" scoped>
.total-popup {
    background-color: rgba(0, 0, 0, 0.5);

    .bg-box {
        background: url('@/assets/home/<USER>') no-repeat;
        background-size: 100% 100%;
        transition: 0.8s;
        transform: translateX(-50%);
        height: 0;
    }
}
</style>