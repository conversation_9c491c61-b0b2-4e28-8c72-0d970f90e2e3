<template>
    <div class="card-info">
        <van-form @submit="onSubmit" class="uploadForm w-full" error-message-align="right">
            <van-cell-group inset>
                <van-field v-model="from.userName" label="开卡人姓名" placeholder="开卡人姓名"
                    :rules="[{ required: true, message: '请填写开卡人姓名' }]" name="userName" required input-align="right" />

                <van-field v-model="from.cardNumber" label="卡号" placeholder="卡号" name="cardNumber"
                    :rules="bankCardRules" required input-align="right"></van-field>
                <van-field v-model="from.issuingBank" label="开户行" :rules="[{ required: true, message: '请填写开户行' }]"
                    placeholder="开户行" name="issuingBank" required input-align="right"></van-field>
            </van-cell-group>
            <div class="my-26px">
                <van-button class="button w-350px h-85px m-auto text-32px" round block type="primary"
                    native-type="submit">
                    保存
                </van-button>
            </div>
        </van-form>
    </div>
</template>
<script lang="ts" setup>
import { getCardInfo, saveCardInfo } from '@/api/video'
import { showFailToast, showSuccessToast } from 'vant';
import { ref } from 'vue'
const from = ref<any>({
    userName: '',
    cardNumber: '',
    issuingBank: ''
})
const bankCardRules = ref([
    {
        require: true,
        message: '请填写卡号',
    },
    {
        validator: (val: any) => val.length >= 16 && val.length <= 19,
        message: '卡号长度不小于16位且不大于19位',
    },

    {
        validator: (val: any) => /^(?!.*[\u4e00-\u9fff]).*$/.test(val),
        message: '卡号格式不正确',
    },
])
onMounted(() => {
    getCardInfo().then(({ data }) => {
        if (data) {
            const { userName, cardNumber, issuingBank, autoId } = data;
            from.value = { userName, cardNumber, issuingBank, autoId };
        }
    })
})
function onSubmit(values: any) {
    saveCardInfo(from.value).then(({ message, code }) => {
        if (code === 200) {
            showSuccessToast('提交成功!');
        } else {
            showFailToast(`提交失败！${message || ''}`);
        }
    })
}
</script>
<style scoped lang="scss">
.uploadForm {
    .button {
        background: url(@/assets/video/add_btn_bg.png) no-repeat center/100% 100%;
        --van-button-border-width: 0;
    }

    :deep(.van-cell-group--inset) {
        margin: 0 !important;
    }

    :deep(.van-field__label) {
        font-size: 32px !important;
    }

    :deep(.van-cell) {
        padding-top: 46px;
        padding-bottom: 46px;
    }

    :deep(.van-field__body) {
        font-size: 30px !important;
    }

    :deep(.van-field__error-message) {
        font-size: 26px !important;
    }
}
</style>