<template>
    <div class="apply_flow px-28px pt-42px w-full">
        <div class="apply_flow_title mx-auto text-[#FFF957] h-80px w-472px  box-border text-32px relative">
            <img loading="lazy" src="@/assets/workerCraftsman/apply_flow_title.png" class="w-full" />
            <span class="absolute top-[50%] left-[50%] -translate-y-[50%]  
                -translate-x-[50%] whitespace-nowrap ml-30px mt-7px">{{
                    route.query.title }}</span>
        </div>
        <div class="apply_flow_content">
            <div class="tips text-[#999] my-39px text-24px">注：单击后可放大缩小流程图</div>
            <div class="flow_img text-center">
                <img loading="lazy" :src="url" @click="previewImage(url)" class="w-full" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { showImagePreview } from 'vant';
import { judgeStaticUrl } from "@/utils/utils";
const route = useRoute();
const url = ref(judgeStaticUrl(route.query.url))
// 初始化页面内容
const previewImage = (url: string) => {
    showImagePreview({
        images: [url],
        closeable: true,
    });
}
</script>

<style scoped lang="scss">
.apply_flow {
    width: 100%;
    height: 100%;
    min-height: 100vh;
    background: url("@/assets/workerCraftsman/apply_flow_bg.jpg"),
        linear-gradient(#fce5d3 0%, #fdf1e3 100%);
    background-size: 100% auto;
    background-position: bottom center;
    box-sizing: border-box;
    background-repeat: no-repeat;
}
</style>