export default [
    {
        path: '/yearGift',
        name: 'yearGift',
        component: () => import('@/views/yearGift/index.vue'),
        meta: {
          title: '一岁一礼',
          isShowTabBar: false,
          isBack: true,
          keepAlive:true
        },
    },
    {
        path: '/yearGift/benefits',
        name: 'yearGiftBenefits',
        component: () => import('@/views/yearGift/benefits.vue'),
        meta: {
          title: '优惠券',
          isShowTabBar: false,
          isBack: true,
          keepAlive:true,
          updatePath:['/yearGift']
        },
    }
]