export default [
  {
    path: '/position',
    name: 'Position',
    component: () => import('@/views/position/index.vue'),
    meta: {
      title: '数字阵地',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/positionDetail',
    name: 'PositionDetail',
    component: () => import('@/views/position/detail.vue'),
    meta: {
      title: '数字阵地详情',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/evaluationOption',
    name: 'EvaluationOption',
    component: () => import('@/views/position/evaluationOption.vue'),
    meta: {
      title: '阵地评价',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/placeDetail',
    name: 'PlaceDetail',
    component: () => import('@/views/position/placeDetail.vue'),
    meta: {
      title: '场所详情',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/vrShow',
    name: 'VRShow',
    component: () => import('@/views/position/vrShow.vue'),
    meta: {
      title: 'VR展示',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/reserve',
    name: 'Reserve',
    component: () => import('@/views/position/reserve.vue'),
    meta: {
      title: '预约',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/scanQRcode',
    name: 'ScanQRcode',
    component: () => import('@/views/position/scanQRcode.vue'),
    meta: {
      title: '扫码签到成功',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/reserveRecord',
    name: 'ReserveRecord',
    component: () => import('@/views/position/reserveRecord.vue'),
    meta: {
      title: '预约记录',
      isShowTabBar: false,
      isBack: true,
      keepAlive:true,
      updatePath:['/my']
    },
  },
  {
    path: '/reserveDetail',
    name: 'ReserveDetail',
    component: () => import('@/views/position/reserveDetail.vue'),
    meta: {
      title: '预约记录详情',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/my/positionAdmin',
    name: 'positionAdmin',
    component: () => import('@/views/my/positionAdmin/positionAdmin.vue'),
    meta: {
      title: '阵地审核',
      isShowTabBar: false,
      isBack: true,
      keepAlive:true,
      updatePath:['/my']
    }
  },
  {
    path: '/my/positionDetail',
    name: 'positionDetail',
    component: () => import('@/views/my/positionAdmin/detail.vue'),
    meta: {
      title: '详情',
      isShowTabBar: false,
      isBack: true,
    }
  },
  {
    path: '/my/positionBlack',
    name: 'positionBlack',
    component: () => import('@/views/my/positionAdmin/blackList.vue'),
    meta: {
      title: '黑名单',
      isShowTabBar: false,
      isBack: true,
    }
  }
];
