import { dataCenterHttp,fileHttp } from '@/utils/http/axios';
import { BasicResponse } from '.';
//登录
export const h5Login = (params) => {
    return dataCenterHttp.get({
      url: '/dataCenterBusiness/h5Login',
      params,
    });
  };
// 获取干部信息
export const getCadreInfo = (params:any) => {
    return dataCenterHttp.get({
      url: '/unionBasicData/cadreFind',
      params,
    });
}
//获取前缀
export const getPrefix = () => {
    return fileHttp.get<Recordable>({
      url: '/minio/getVisitPrefix',
    });
};
//登录
export const accountVerifyCode = (params:any) => {
  return dataCenterHttp.get({
    url: '/unionBasicData/accountVerifyCode',
    params,
  });
}
//获取验证码
export const accountCode = (params:any) => {
  return dataCenterHttp.get({
    url: '/unionBasicData/accountCode',
    params,
  });
}
