// 竞答活动通用方法
import {  showConfirmDialog, showDialog, showToast } from 'vant'
export default {
  data() {
    return {
      show: false,
      isSuccess: false,
      currentTopicIndex: 0,//当前题目
      topicList: [],//题目数据
      startTime: new Date().getTime(),//答题开始时间
      clickState: true, //防止频繁点击
      autoId: null,//首次答题记录,防止中途退出
      rightQuestionNum: 0, //答对数量
      score: 0, //本次答题得分
      isNeedAutoId: false,//是否校验中途退出
      vieAnswerNum: 0,//今日答题次数
      todayShareNum: null,//今日分享次数
      isSubmited: false,//是否提交
    }
  },
  methods: {
    // #region 答题页面
    // 题目选中
    onSelectMixins(item, option) {
      // console.log(item)
      if (item.isSubmited) return
      if (item.optionType === 'radio') {
        item.select = option.optionNo
      } else if (item.optionType === 'select') {
        let selectList = []
        if (item.select && item.select.split(',').includes(option.optionNo)) {
          // 取消选中
          selectList = item.select.split(',').filter(i => i && i != option.optionNo)
        } else {
          // 选中
          item.select = item.select ? item.select + ',' + option.optionNo : option.optionNo
          selectList = item.select.split(',')
        }
        selectList.sort((a, b) => a - b)
        item.select = selectList.join(',')
      }
      this.topicList = [...this.topicList]
    },
    async getTopicListMixins(isNeedAutoId) {
      const res = await this.$api.common.getTopicList({ activityId: this.$store.state.activityId })
      if (res.code === 200) {
        this.topicList = res.data
        this.topicList.forEach(item => {
          item.options?.forEach(i => {
            i.optionNo = i.optionNo + ''
          })
        })
        // 是否需要校验中途退出
        if (isNeedAutoId) {
          this.isNeedAutoId = true
          this.startQuestion()
        }

      }
    },
    // 返回按钮点击
    gobackFnMixins() {
      if (this.autoId) {
        showConfirmDialog({
          title: '温馨提示',
          message: '本次答题未完成,返回将消耗一次答题机会,请确认是否返回?',
        })
          .then(() => {
            this.$router.go(-1)
          })
          .catch(() => { })
      } else {
        this.$router.go(-1)
      }
    },
    // 首次进入生成答题记录
    async startQuestion() {
      const platform = sessionStorage.getItem('platform')
      const params = {
        activityId: this.$store.state.activityId,
        platform,
      }
      const { code, data } = await this.$api.common.vieAnswerSubmit(params)
      if (code === 200) {
        this.autoId = data.autoId
      } else {
        setTimeout(() => {
          this.$router.go(-1)
        }, 2000)
        // setTimeout(() => {
        //   this.$router.replace('/deyang/safetyKnowledgeQuiz')
        // }, 2000)
      }
    },
    async submitMixins(item, index) {
      if (!this.activityValidator()) return
      if (!this.clickState) return
      if (this.isNeedAutoId) {
        if (!this.autoId) {
          showToast({
            message: '暂未获取到题目信息,请稍后重试',
            duration: 2000,
          })
          return
        }
      }
      if (!item.select) {
        showToast({
          message: '请先选择答案',
          duration: 1500,
        })
        return
      }
      item.isSubmited = true

      this.topicList = [...this.topicList]
      if (this.currentTopicIndex === this.topicList.length - 1) {
        // 提交
        const platform = sessionStorage.getItem('platform')
        const params = {
          activityId: this.$store.state.activityId,
          totalTime: (new Date().getTime() - this.startTime) / 1000,
          platform,
          topicAnswers: {},
          autoId: this.isNeedAutoId ? this.autoId : undefined,
        }
        this.rightQuestionNum = 0
        this.score = 0
        this.topicList.forEach(item => {
          // 计算分数
          if (item.answer == item.select) {
            this.rightQuestionNum++
            this.score += Number(item.score)
          }
          params.topicAnswers[item.topicInfoId] = item.select
        })
        this.isSubmited = true
        this.clickState = false
        const res = await this.$api.common.vieAnswerSubmit(params)
        if (res.code === 200) {
          this.isSuccess = res.data
          setTimeout(() => {
            this.show = true
            this.clickState = true
          }, 1000)
        } else {
          setTimeout(() => {
            this.clickState = true
          }, 1000)
        }
      } else {
        // 下一题
        this.clickState = false
        setTimeout(() => {
          this.currentTopicIndex++
          this.clickState = true
        }, 800)
      }
    },
    // #endregion 答题页面
    // #region 答题次数校验
    // 获取答题次数
    async getVieAnswerStateMixins(routePath) {
      let res = await this.$api.common.getVieAnswerState({
        activityId: this.$store.state.activityId,
      })
      if (res.code == 200) {
        this.vieAnswerNum = res.data
        if (this.todayShareNum !== null) {
          this.commonVieAnswer(routePath)
        }
      }
    },
    commonVieAnswer(routePath) {
      // (默认答题次数+额外增加)-已答次数 再判断是否已分享
      const { numberPerDay } = this.activityDetail?.vieAnswerInfo || {}
      // 总次数
      let totalVieAnswerNum = Number(numberPerDay) + Number(this.todayShareNum)
      // 剩余次数
      let remainVieAnswerNum = totalVieAnswerNum - this.vieAnswerNum
      if (remainVieAnswerNum > 0) {
        this.$router.push(routePath)
      } else {
        showToast({
          message: '今日已无答题机会,明日再来吧',
          duration: 2000,
          forbidClick: true
        })
      }
    },
    commonShare() {
      // const { extendCount } = this.activityDetail?.vieAnswerInfo || {}
      if (this.todayShareNum < 1) {
        // 增加分享次数
        this.addExtraCountMixins()
      } else {
        showToast({
          type: 'success',
          message: '分享成功',
          duration: 2000,
        })
        // Dialog({
        //     title: '温馨提示',
        //     message: '分享成功,今日分享获取答题机会已到上限。',
        //     confirmButtonText: '我知道了',
        // })
      }
    },
    // 额外增加次数查询
    async getExtraAddCountMixins(type, routePath) {
      let res = await this.$api.common.getExtraAddCount({
        activityId: this.$store.state.activityId,
      })
      if (res.code == 200) {
        this.todayShareNum = res.data?.today?.share || 0
        if (type === 'question') {
          this.commonVieAnswer(routePath)
        } 
      }
    },
    // 增加答题次数
    async addExtraCountMixins() {
      let res = await this.$api.common.addExtraCount({
        activityId: this.$store.state.activityId,
        sourceCode: 'share',
        targetCode: 'vieAnswer',
      })
      if (res.code === 200) {
        if (this.todayShareNum !== null) {
          this.todayShareNum++
        }
        // Toast({
        //     message: '分享成功,答题机会+1,继续去答题吧',
        //     duration: 2000,
        // })
        showDialog({
          title: '温馨提示',
          message: '分享成功,获得1次额外答题机会,继续去答题吧!',
          confirmButtonText: '我知道了',
        })
      }
    },
   
    // #endregion 答题次数校验
  },
}
