<template>
    <!-- 核销 -->
    <div class="write-off min-h-full bg-[#F9F9F9]">
        <div class="coupon-card bg-#FFF rounded-36px mt-45px mx-27px px-30px py-50px">
            <div class="code pb-90px">
                <div class="goods-title text-38px text-#333 text-center">{{ writeOffInfo.productName }}</div>
                <div class="goods-code w-277px h-277px mt-47px mx-auto">
                    <img loading="lazy" :src="writeOffInfo.productCoverImg" alt="商品图"
                        class="w-277px h-277px object-cover">
                </div>
            </div>
            <div class="infos mt-12px">
                <div class="info flex justify-between mt-40px">
                    <div class="info-title text-30px text-#333">票券来源</div>
                    <div class="info-value text-28px text-#666">积分商城</div>
                </div>
                <div class="info flex justify-between mt-40px">
                    <div class="info-title text-30px text-#333">状态</div>
                    <div class="info-value text-28px text-#666">{{ writeOffInfo.state === 'used' ? '已核销' : '未核销' }}
                    </div>
                </div>
                <div class="info flex justify-between mt-40px">
                    <div class="info-title text-30px text-#333">用户</div>
                    <div class="info-value text-28px text-#666">{{ userInfo.phone ? desPhone(userInfo.phone) : '-' }}
                    </div>
                </div>
                <div class="info flex justify-between mt-40px" v-if="writeOffInfo.usedTime">
                    <div class="info-title text-30px text-#333">核销日期</div>
                    <div class="info-value text-28px text-#666 mt-10px">{{ writeOffInfo.usedTime }}</div>
                </div>
                <div class="info flex justify-between mt-40px" v-if="writeOffInfo.expiredTime">
                    <div class="info-title text-30px text-#333">过期日期</div>
                    <div class="info-value text-28px text-#666 mt-10px">{{ writeOffInfo.expiredTime }}</div>
                </div>


                <!-- <div class="info  mt-40px">
                    <div class="info-title text-30px text-#333">兑换有效期</div>
                    <div class="info-value text-28px text-#666 mt-10px">2024-10-18 08:00至2024-11-20 08:00</div>
                </div> -->
                <div class="use-info">
                    <div class="info flex justify-between mt-40px">
                        <div class="info-title text-30px text-#333">使用说明</div>
                    </div>
                    <div class="mt-25px text-[#666] text-28px leading-46px">
                        1、本兑换券仅限在对应商家使用<br>
                        2、本奖品由工会提供，解释权为南充市总工会
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { useUserStore } from '@/store/modules/user';
import { desPhone } from '@/utils/tool';
import { judgeStaticUrl } from '@/utils/utils';
import { getIntegralOrder } from '@/api/mall/integral'
const userStore = useUserStore()
const route = useRoute()


const writeOffInfo = ref<any>({})
if (route.query?.recordId) {
    getIntegralOrder(route.query?.recordId as string).then((res: any) => {
        writeOffInfo.value = res.data
        writeOffInfo.value.productCoverImg = judgeStaticUrl(writeOffInfo.value?.productCoverImg)
    })
}

const userInfo = computed(() => {
    return userStore.getUserInfo
})
</script>
<style lang="scss" scoped>
.write-off {
    .coupon-card {
        box-shadow: 0px 3px 10px 0px rgba(119, 151, 203, 0.11);

        .code {
            border-bottom: 1px dashed #CCCCCC;
            position: relative;
        }

        .code::after {
            content: "";
            display: block;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #F9F9F9;
            position: absolute;
            bottom: -30px;
            right: -60px;
        }

        .code::before {
            content: "";
            display: block;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #F9F9F9;
            position: absolute;
            bottom: -30px;
            left: -60px;
        }
    }
}
</style>