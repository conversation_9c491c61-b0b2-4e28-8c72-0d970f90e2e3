<template>
    <!-- 兑换肥料弹窗 -->
    <van-popup v-model:show="props.show" position="center" round class="popup box-border" @click-overlay="close">
        <div class="text-center relative box-border mx-auto w-61%" @click="close">
            <img loading="lazy" src="@/assets/tree/fertilizer_popup.png" class="w-full">
            <div class="text_content absolute top-16% bottom-0 left-0 right-0 flex flex-col">
                <div class="flex-1">
                    <div class="title text-[#7C4A20] font-medium text-1.5vh">
                        {{ `${props.fertilizerUseIntegral}积分兑换${props.fertilizerNum}袋化肥` }}
                    </div>
                    <div class="tips_text text-[#666] text-1.2vh mt-1% ">
                        {{ `每日仅限兑换${props.fertilizerDailyCount}次` }}
                    </div>
                </div>
                <div class="my-5%" @click.stop="confirm">
                    <img loading="lazy" src="@/assets/tree/fertilizer_btn.png" class="w-60%" />
                </div>
            </div>
        </div>
    </van-popup>
</template>
<script lang="ts" setup>
import { dailyExchange } from '@/api/integralTree'
import { showLoadingToast, closeToast, showToast } from 'vant';
const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    activityId: {
        type: String,
        default: '',
        required: true
    },
    currentNum: {
        type: Number,
        default: 1 //今日兑换次数
    },
    fertilizerNum: {
        type: Number,
        default: 1 //每次兑换肥料
    },
    fertilizerUseIntegral: {
        type: Number,
        default: 100 //兑换肥料消耗积分
    },
    fertilizerDailyCount: {
        type: Number,
        default: 1 //每日兑换次数
    }
});
const exchangeValue = ref(1);
const emit = defineEmits(['update:show', 'refresh']);
// 确认执行操作

let handleClick = false
const confirm = async () => {
    if(handleClick) return;
    handleClick = true

    showLoadingToast({
        message: '肥料兑换中,请耐心等待...',
        forbidClick: true,
        duration: 0
    })
    const { data, code, message } = await dailyExchange({
        platform: "app",
        exchangeType: "fertilizer",
        exchangeCount: exchangeValue.value,
        activityId: props.activityId
    });
    closeToast()
    if (code === 200) {
        showToast('兑换成功')
        setTimeout(() => {
            emit('update:show', false);
            emit('refresh', {
                type: 'exchange',
                data
            });
        }, 1000)
    } else {
        showToast(message)
        setTimeout(() => {
            emit('update:show', false);
        }, 2000)
    }
    handleClick = false;
};
// 关闭弹窗
const close = () => {
    emit('update:show', false);
}
</script>

<style scoped lang="scss">
.van-popup {
    background: transparent;
}
</style>