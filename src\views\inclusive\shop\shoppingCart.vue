<template>
    <div class="shopping-cart flex flex-col relative z-1">
        <div class="w-full h-260px banner relative z-1"></div>
        <div class="cart-menus -mt-20px bg-#fff relative z-2 flex-1 overflow-scroll-y py-46px pb-140px box-border">
            <div class="header-manager flex items-center justify-between px-30px">
                <div class="title-left text-#333 text-34px">购物车（{{ kindCount || 0 }}）</div>
                <div class="title-right text-#666 text-28px" @click="editFn">{{ isEdit ? '退出管理' : '管理' }}</div>
            </div>
            <div class="goods-list">
                <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
                    <van-skeleton title :row="15" :loading="loading">
                        <div v-for="(item, index) in list" :key="index" class="goods-item py-40px">
                            <div class="store-info flex items-center px-30px mb-40px">
                                <div @click.stop="checkedFn('', item)" class="pb-20px"
                                    v-if="!item.status || item.status !== '已下架' || isEdit">
                                    <img v-show="!item.checked" loading="lazy" src="@/assets/inclusive/shop/uncheck.png"
                                        class="w-30px h-30px block" />
                                    <img v-show="item.checked" loading="lazy" src="@/assets/inclusive/shop/checked.png"
                                        class="w-30px h-30px block" />
                                </div>
                                <div v-else class="w-30px h-30px rounded-50% bg-#e1e1e1 disabledChecked"
                                    @click.stop="disableFn"></div>

                                <div class="flex flex-1 border-bottom pb-20px justify-between ml-26px">
                                    <div class="name-info flex items-center">
                                        <img loading="lazy" src="@/assets/inclusive/shop/icon-store.png"
                                            class="w-30px h-28px block" />
                                        <span class="text-#333 text-30px ml-10px">{{ item.companyName }}</span>
                                        <van-icon name="arrow" class="text-30px text-#999 ml-6px"></van-icon>
                                    </div>
                                    <div class="coupon text-#FF4344 text-30px" @click.stop="lookCoupons">优惠券</div>
                                </div>
                            </div>
                            <!-- 加购商品列表 -->
                            <div class="goods-list-view px-30px" v-for="it, index1 in item.productInfoList"
                                :key="index1">
                                <!-- 选择规格商品 -->
                                <div class="flex justify-between mt-20px" v-for="(info, info_i) of it.priceListInfo"
                                    :key="info_i">
                                    <div class="check-input" @click.stop="checkedFn(item, info)"
                                        v-if="!info.status || info.status !== '已下架' || isEdit">
                                        <img v-show="!info.checked" loading="lazy"
                                            src="@/assets/inclusive/shop/uncheck.png" class="w-30px h-30px block" />
                                        <img v-show="info.checked" loading="lazy"
                                            src="@/assets/inclusive/shop/checked.png" class="w-30px h-30px block" />
                                    </div>
                                    <div v-else class="w-30px h-30px rounded-50% bg-#e1e1e1 disabledChecked"
                                        @click.stop="disableFn"></div>
                                    <div class="flex-1 flex ml-26px border-bottom pb-20px">
                                        <div class="flex flex-1" @click="lookGoodsDetail(info)">
                                            <div class="goods-cover w-140px h-140px rounded-18px relative z-1">
                                                <img :src="judgeStaticUrl(info.productSubImg)" alt=""
                                                    class="object-cover w-full h-full rounded-18px relative z-1"
                                                    :style="{ opacity: info.status === '已下架' ? 0.6 : 1 }" />
                                                <!-- 已下架提示 -->
                                                <div class="down-pic text-#fff absolute flex items-center justify-center
                                                        text-28px w-full py-6px bottom-0px left-0 z-2"
                                                    v-if="info.status === '已下架'">
                                                    商品下架
                                                </div>
                                            </div>
                                            <div class="goods-info ml-26px">
                                                <div class="name text-28px textOverflow">{{ it.productName }}</div>
                                                <div class="specifications text-#999 text-24px leading-2.2em">
                                                    {{ info.productSubName }}</div>
                                                <div class="price text-#FF4344 text-26px font-bold">
                                                    ￥<span class="text-30px">{{ info.nowPrice }}</span></div>
                                            </div>
                                        </div>
                                        <template v-if="info.status !== '已下架'">
                                            <div class="bg-#F6F7F8 h-44px text-#999 text-28px px-10px flex items-center"
                                                @click.stop="showStep(info)" v-if="info.showNum">
                                                x{{ info.currentProductCount }}
                                            </div>
                                            <div class="flex items-center w-28% h-35px" v-else>
                                                <img src="@/assets/inclusive/icon_reduce.png" alt=""
                                                    class="w-35px h-35px"
                                                    @click.stop="handleCount(info, 'decrement', item?.companyId)" />
                                                <div
                                                    class="text-28px ml-20px mr-20px leading-none input-line rounded-10px flex-1">
                                                    <van-field v-model="info.currentProductCount"
                                                        @change="handleCount(info, 'update', item?.companyId)"
                                                        type="number" class="" />
                                                </div>
                                                <img src="@/assets/inclusive/icon_add.png" alt="" class="w-35px h-35px"
                                                    @click.stop="handleCount(info, 'increment', item?.companyId)" />
                                            </div>
                                        </template>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </van-skeleton>
                </refreshList>
            </div>
        </div>
        <div class="settlement h-110px bg-#fff flex justify-end items-center px-20px  bottom-0 w-full z-3 safe_area_bottom fixed"
            :class="{ '!justify-between': isEdit }">
            <div class="flex items-center">
                <div class="" @click="isAllChecked = !isAllChecked">
                    <img v-show="!isAllChecked" loading="lazy" src="@/assets/inclusive/shop/uncheck.png"
                        class="w-28px h-28px block" />
                    <img v-show="isAllChecked" loading="lazy" src="@/assets/inclusive/shop/checked.png"
                        class="w-28px h-28px block" />
                </div>
                <span class="text-#333 text-26px ml-10px">全选</span>
            </div>
            <template v-if="!isEdit">
                <div class="mr-10px flex items-center justify-between flex-1">
                    <div class="flex-1 ml-26px">
                        <div>
                            <span class="text-#666 text-24px">已选{{ checkedArr.length }}件</span>
                            <span class="text-#333 text-24px ml-10px">合计￥<span class="text-38px">{{
                                totalPrice }}</span>
                            </span>
                        </div>
                        <div class="price-detail text-#FF4344 text-24px" @click="lookPriceDetail">
                            共减￥5 | 查看明细<van-icon :name="showPriceDetail ? 'arrow-down' : 'arrow-up'"
                                class="text-24px ml-10px" />
                        </div>
                    </div>
                </div>
                <div class="settlement-btn bg-#FF4344 rounded-40px text-#fff 
                    text-34px w-180px h-68px flex items-center justify-center" @click="createOrderPay">
                    去结算
                </div>
            </template>
            <template v-else>
                <div class="manage-btn flex items-center">
                    <div class="del-btn text-#666 text-center w-152px h-56px rounded-28px flex items-center justify-center mr-30px"
                        @click="delCartFn">
                        删除</div>
                    <div class="remove-collect text-#FF4344 text-28px bg-#FFECEC rounded-28px w-170px h-56px flex items-center justify-center"
                        @click="collectOperateBatch">
                        移入收藏</div>
                </div>
            </template>
        </div>
        <!-- 金额明细 -->
        <div class="price-detail-popup relative z-2" ref="pricedetail">
            <van-popup position="bottom" :teleport="pricedetail" :style="{ height: '50vh' }"
                v-model:show="showPriceDetail">
                <div class="prices-details p-30px">
                    <div class="title text-center text-34px text-#333">金额明细</div>
                    <div class="des text-center text-24px text-#666 mt-20px">实际优惠金额请以下单页为准</div>
                    <div class="goods-list-view flex flex-wrap mt-62px">
                        <div class="goods-item relative w-130px h-130px rounded-20px bg-#C8D6FE mr-20px"
                            v-for="item, index in 2" :key="index">
                            <img loading="lazy" src="@/assets/inclusive/shop/checked.png"
                                class="absolute right-12px top-12px w-30px h-30px" />
                        </div>
                    </div>
                    <div class="cell flex items-center justify-between mt-40px">
                        <div class="label">商品总价</div>
                        <div class="values text-30px text-#333">￥38.3</div>
                    </div>
                    <div class="cell flex items-center justify-between mt-32px">
                        <div class="label">工会普惠券立减</div>
                        <div class="values text-#FF4344 text-30px">￥38.3</div>
                    </div>
                    <div class="cell flex items-center justify-between mt-64px">
                        <div class="label">合集</div>
                        <div class="values text-#FF4344 text-30px">￥38.3</div>
                    </div>
                </div>
            </van-popup>
        </div>

        <!-- 优惠券弹窗 -->
        <couponPopup v-model:show="isShowCoupon" />
    </div>
</template>
<script lang="ts" setup>
import { batchCollectOperate } from '@/api/public'

import { shoppingCartList } from '@/api/mall/inclusive'
import refreshList from '@/components/refreshList/index.vue'
import { judgeStaticUrl } from '@/utils/utils'
import { showLoadingToast, showToast, closeToast } from 'vant'
import useShoppingCart from '@/hooks/shopingCart'
import { useMallStore } from '@/store/modules/mall'
import { cloneDeep } from 'lodash-es';
const couponPopup = defineAsyncComponent(() => import('../components/coupon.vue'))
const { editCart, delCart } = useShoppingCart()
const loading = ref(true)
const isEdit = ref(false)//是否编辑购物车
const editFn = () => {
    isEdit.value = !isEdit.value
    // 不编辑状态时重置已下架勾选
    if (!isEdit.value) {
        list.value.forEach((item: any) => {
            if (item.productInfoList) {
                item.productInfoList.forEach((it: any) => {
                    if (it.priceListInfo) {
                        it.priceListInfo.forEach((info: any) => {
                            if (info.status === '已下架') info.checked = false
                        })
                    }
                })
            }
        })
    }
}
const list = ref<any>([])
// 全选状态
const isAllChecked = computed({
    // 获取全选状态
    get: () => {
        // 判断是否数组及子集是否选中
        if (!list.value.length) return false;
        // 未下架但未勾选|编辑状态未勾选
        if (list.value.some((item: any) => (item.status !== '已下架' && !item.checked) || (isEdit.value && !item.checked))) return false;
        list.value.forEach((item: any) => {
            if (item.productInfoList) {
                item.productInfoList.forEach((it: any) => {
                    // 未下架但未勾选|编辑状态未勾选
                    if (it.priceListInfo.some((info: any) => (info.status !== '已下架' && !info.checked) || (isEdit.value && !info.checked))) return false;
                })
            }
        })
        return true;
    },
    // 设置全选与不全选
    set: (val) => {
        if (!list.value.length) return showToast('暂无数据，请先添加商品到购物车！')
        list.value.forEach((item: any) => {
            // 未下架和编辑状态下可以与全选操作一致
            if (!item.status || item.status !== '已下架' || isEdit.value) item.checked = val
            else item.checked = false
            if (item.productInfoList) {
                item.productInfoList.forEach((it: any) => {
                    it.priceListInfo.forEach((info: any) => {
                        // 未下架和编辑状态下可以与全选操作一致
                        if (!info.status || info.status !== '已下架' || isEdit.value) info.checked = val
                        else info.checked = false
                    })
                })
            }
        })
    }
})
// 选择中数组
const checkedArr = computed(() => {
    let arr: any = []
    // 获取子集中选中的元素
    list.value.forEach((item: any) => {
        if (item.productInfoList) {
            item.productInfoList.forEach((it: any) => {
                if (it.priceListInfo) {
                    it.priceListInfo.forEach((info: any) => {
                        if (info.checked) arr.push(info)
                    })
                }
            })
        }
    })
    return arr
})
// 总金额
const totalPrice = computed(() => {
    return checkedArr.value.reduce((pre: any, cur: any) => (parseFloat(pre) + parseFloat(cur.nowPrice) * cur.currentProductCount).toFixed(2), 0)
})
// const goodsListLength = computed(() => {
//     return list.value.reduce((pre: any, cur: any) => {
//         return pre + (cur.productInfoList?.length || 0)
//     }, 0)
// })
const kindCount = ref(0)//商品数量


// 勾选商品
const checkedFn = (parentItem: any, item: any) => {
    if (item.status === '已下架' && !isEdit.value) return showToast('该商品已下架')
    item.checked = !item.checked //店铺勾选状态
    if (item.productInfoList) {
        item.productInfoList.forEach((it: any) => {
            if (it.priceListInfo) it.priceListInfo.forEach((info: any) => {
                if (!info.status || info.status !== '已下架' || isEdit.value) info.checked = item.checked
            })//商品勾选状态与商铺勾选状态一致
        })
    } else {
        if (parentItem) {
            if (parentItem.productInfoList) {
                parentItem.productInfoList.forEach((it: any) => {
                    if (it.priceListInfo) parentItem.checked = it.priceListInfo.every((info: any) => info.checked)
                })
            }

        }
    }
}
// 不能勾选
const disableFn = () => {
    return showToast('该商品已下架')
}
//点击显示计数器
const showStep = (info: any) => {
    info.showNum = !info.showNum
}
const handleCount = (item: any, type: string, companyId: string) => {
    if (type === 'decrement') {
        if (item.currentProductCount === 1) return
    }
    else if (type === 'increment') {
        if (item.currentProductCount >= item.purchaseLimit) return showToast("当前购买数量已达到最大限制")
    }
    else if (type === 'update') {
        if (item.currentProductCount < 1) {
            item.currentProductCount = 1
            return
        }
        if (item.currentProductCount > item.purchaseLimit) {
            item.currentProductCount = item.purchaseLimit
            return showToast("当前购买数量已达到最大限制")
        }
    }
    const params = {
        companyId,
        productId: item.productId,
        productSubId: item.productSubId,
        amount: type === 'update' ? item.currentProductCount : 1,//增减数量1
        operateType: type//操作类型,递增(increment),递减(decrement),修改(update)
    }

    editCart(params, () => {
        if (type === 'decrement') item.currentProductCount--
        else if (type === 'increment') item.currentProductCount++
    })
}
// end

// 删除商品
const delCartFn = () => {
    if (checkedArr.value.length === 0) return showToast('请选择商品')
    const deleteDataMap: any = {}
    list.value.forEach((item: any) => {
        item.productInfoList.forEach((it: any) => {
            if (it.priceListInfo) it.priceListInfo.forEach((info: any) => {
                if (info.checked) {
                    if (!deleteDataMap[item.companyId]) deleteDataMap[item.companyId] = {}
                    if (!deleteDataMap[item.companyId][info.productId]) deleteDataMap[item.companyId][info.productId] = []
                    deleteDataMap[item.companyId][info.productId].push(info.productSubId)
                }
            })
        })
    })
    delCart(deleteDataMap, (res: any) => {
        onRefreshList()
    })
}
// 移入收藏-未对接
const collectOperateBatch = async () => {
    if (checkedArr.value.length === 0) return showToast('请选择商品')
    showLoadingToast('正在收藏中...')
    const { code, data } = await batchCollectOperate({
        sourceIdList: checkedArr.value.map((item: any) => item.productSubId)
    })
    closeToast()
    if (code === 200) {
        showToast({
            message: '收藏成功',
            type: 'success',
            duration: 1500
        })
    } else {
        showToast(data.message || '收藏失败')
    }
}
const router = useRouter()

// 商品详情
const lookGoodsDetail = (item: any) => {
    const { productId, productSubId, currentProductCount } = item
    router.push({
        path: '/inclusive/goodsDetail',
        query: {
            productId,
            productSubId,
            currentProductCount
        }
    })
}

// 明细
const showPriceDetail = ref(false)
const pricedetail = ref()
const lookPriceDetail = () => {
    showPriceDetail.value = !showPriceDetail.value
}

// 去结算
const mallStore = useMallStore()
const createOrderPay = () => {
    if (checkedArr.value.length === 0) return showToast('请选择商品')
    const confirmList = cloneDeep(toRaw(list.value)).map((item: any) => {
        item.productInfoList = item.productInfoList.map((it: any) => {
            if (it.priceListInfo) it.priceListInfo = it.priceListInfo.filter((info: any) => info.checked)
            return it
        })
        item.productInfoList = item.productInfoList.filter((it: any) => it.priceListInfo.length > 0)

        // 每个商店总金额
        item.priceNum = 0
        item.productInfoList.forEach((it: any) => {
            item.priceNum += parseFloat(it.priceListInfo.reduce((pre: any, cur: any) => (parseFloat(pre) + parseFloat(cur.nowPrice) * cur.currentProductCount).toFixed(2), 0))
        })
        item.priceNum = parseFloat(item.priceNum).toFixed(2)
        // end
        return item
    })

    mallStore.setInclusiveGoodsList(confirmList)
    router.push({
        path: '/inclusive/create',
        query: {
            type: '1',// 1:购物车结算 2.立即支付
        }
    })
}


// 列表加载更多和刷新事件
const loadMoreRef = ref(null)
const getList = async () => {
    const { code, data } = await shoppingCartList()
    if (code === 200) {
        kindCount.value = data?.kindCount
        if (data.companyShopCartList) {
            list.value = data.companyShopCartList.map((item: any) => {
                item.checked = false //选中状态
                item.productInfoList.forEach((item1: any) => {
                    item1.priceListInfo.forEach((it: any) => {
                        it.checked = false
                        it.showNum = true //是否显示数量选择器
                        if (item1.state == "down" || it.saleState == "down") {
                            it.status = "已下架";//商品规格下架状态
                        }
                    })
                    const isAllDown = item1.priceListInfo.every((it: any) => it.status === '已下架')//商品规格是否全部下架
                    if (isAllDown) item1.status = "已下架" //商品是否全部规格下架状态
                })
                const isAllDown = item.productInfoList.every((it: any) => it.status === '已下架')//商品是否全部规格下架状态
                if (isAllDown) item.status = '已下架' //商品是否全部规格下架状态
                return item
            })
        }
    }
    loading.value = false
    if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(list.value.length, list.value.length);
    }
}

/**
 * 刷新列表
 */
const onRefreshList = () => {
    getList()
}
const onLoadMore = () => {
    getList()
};
// end
const isShowCoupon = ref(false)
const lookCoupons = () => {
    isShowCoupon.value = !isShowCoupon.value
}
onMounted(() => {
    getList()
})
</script>
<style scoped lang="scss">
.shopping-cart {
    .banner {
        background: url("@/assets/inclusive/shop/cart_banner.png") no-repeat;
        background-size: cover;
        background-position: center;
    }

    .cart-menus {
        border-radius: 20px 20px 0px 0px;
    }

    .settlement {
        box-shadow: 0px -6px 9px 0px rgba(186, 194, 201, 0.16);
    }

    .manage-btn {
        .del-btn {
            border: 1px solid #CCCCCC;
        }
    }

    .goods-item {
        border-bottom: 20px solid #F6F7F8;
    }

    // .goods-item:last-child {
    //     border-bottom: none;
    // }

    .input-line {
        border: 1px solid #CCC;

        :deep(.van-cell) {
            padding: 0px 10px;
            background: transparent;

            .van-cell__value {
                color: #333;
                font-size: 26px;
            }

            .van-field__control {
                text-align: center;
            }
        }
    }

    .border-bottom {
        border-bottom: 1px solid #ebebeb;
    }

    .goods-item {
        .goods-list-view:last-child {
            .border-bottom {
                border-bottom: none;
            }
        }
    }

    .down-pic {
        background-color: rgba(0, 0, 0, 0.3);
        border-radius: 0 0 18px 18px;

    }
}
</style>