<template>
  <div class="echart-box" style="width:100%;height:100%">
    <div ref="echartRef" class="w-full h-full"></div>
  </div>
</template>

<script lang="ts" setup>

import { useECharts } from '@/utils/useECharts'
import { Ref, ref, watch, onMounted } from 'vue'
import * as echarts from 'echarts'
import { LabelBg, getVwSize } from '../../data'

const props = defineProps({
  dataSource: {
    type: Object,
    default: {
      areaList: [
        '顺庆区',
        '高坪区',
        '嘉陵区',
        '阆中市',
        '南部县',
        '西充县',
        '仪陇县',
        '营山县',
        '蓬安县',
      ],
      behaviorList: [],
      avgList: [],
      pubList: [],
    },
  }, //color
  behaviorName: {
    type: String,
    default: ''
  },
})

const echartRef = ref<HTMLDivElement | null>(null)
const { setOptions } = useECharts(echartRef as Ref<HTMLDivElement>)

const initChart = () => {
  let data = { x: [], y1: [], y2: [], y3: [] }
  let maxNumPub = 0, maxNumBehav = 0, maxNumAvg = 0
  let isFormat = false
  data.x = props.dataSource?.areaList
  if (props.dataSource?.pubList) {
    maxNumPub = Math.max(...props.dataSource.pubList)
  }
  if (props.dataSource?.behaviorList) {
    maxNumBehav = Math.max(...props.dataSource.behaviorList)
  }
  if (props.dataSource?.avgList) {
    maxNumAvg = Math.max(...props.dataSource.avgList)
  }
  if ([maxNumPub, maxNumBehav, maxNumAvg].some(e => e >= 10000)) {
    isFormat = true
    data.y1 = props.dataSource?.pubList.map((item: any) => parseFloat((item / 10000).toFixed(2)))
    data.y2 = props.dataSource?.behaviorList.map((item: any) => parseFloat((item / 10000).toFixed(2)))
    data.y3 = props.dataSource?.avgList.map((item: any) => parseFloat((item / 10000).toFixed(2)))
  } else {
    data.y1 = props.dataSource?.pubList
    data.y2 = props.dataSource?.behaviorList
    data.y3 = props.dataSource?.avgList
  }

  // 绘制图表
  var option = {
    tooltip: {
      show: true,
      trigger: 'axis',
      axisPointer: {
        label: {
          show: true,
          backgroundColor: 'rgba(141, 226, 218, .8)',
          color: '#fff',
          borderColor: 'rgba(0,0,0,0)',
          shadowColor: 'rgba(0,0,0,0)',
          shadowOffsetY: 0,
          fontSize: getVwSize(22)
        },
        type: 'line',
        lineStyle: {
          color: 'rgba(141, 226, 218, .2)',
          width: getVwSize(28),
          type: 'solid'
        }
      },
      backgroundColor: '#fff',
      textStyle: {
        fontSize: getVwSize(22)
      },
      rounded: getVwSize(40),
      padding: [getVwSize(10), getVwSize(10)],
      extraCssText: 'box-shadow: 1px 0 2px 0 rgba(163,163,163,0.2)',
      formatter: function (params: any) {
        let result = params[0].name + '<br/>'
        params.forEach((item: any) => {
          let value = item.value
          if (item.seriesIndex === 0) value = props.dataSource?.pubList[item.dataIndex]
          else if (item.seriesIndex === 1) value = props.dataSource?.behaviorList[item.dataIndex]
          else if (item.seriesIndex === 2) value = props.dataSource?.avgList[item.dataIndex]

          result += item.marker + item.seriesName + ': ' + value + `<br/>`
        })
        return result
      },
    },
    grid: {
      top: '13%',
      left: '0%',
      right: '3%',
      bottom: '0%',
      containLabel: true,
    },
    legend: {
      show: true,
      icon: 'circle',
      orient: 'horizontal',
      top: '0%',
      right: '0%',
      width: '100%',
      itemWidth: getVwSize(20),
      itemHeight: getVwSize(20),
      itemGap: getVwSize(30),
      textStyle: {
        color: '#666666',
        fontSize: getVwSize(24),
      },
    },
    xAxis: [
      {
        type: 'category',
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitArea: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        boundaryGap: true,
        axisLabel: {
          show: true,
          interval: 0,
          rotate: 30,
          fontSize: getVwSize(24),
          lineHeight: getVwSize(30),
          margin: getVwSize(20),
          color: '#999',
          // hideOverlap: true,
        },
        data: data.x,
      },
    ],
    yAxis: [
      {
        name: `${isFormat ? '万' : ''}次`,
        nameTextStyle: {
          color: '#999',
          fontSize: getVwSize(20)
        },
        type: 'value',
        min: 0,
        minInterval: 1,
        splitLine: {
          show: true,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#E6E6E6',
          },
        },
        axisLabel: {
          textStyle: {
            color: '#999',
            fontSize: getVwSize(20)
          },
        },
        axisTick: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: '发布量',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        showSymbol: false,
        z: 3,
        lineStyle: {
          width: getVwSize(6),
          shadowColor: 'rgba(98, 42, 220, 0.1)',
          shadowBlur: getVwSize(6),
          shadowOffsetY: getVwSize(6)
        },
        itemStyle: {
          normal: {
            color: '#8DE2DA',
          },
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [{
            offset: 0,
            color: '#fff'
          },
          {
            offset: 1,
            color: 'rgba(141, 226, 218, 0.1)'
          }
          ])
        },
        data: data.y1,

      },
      {
        name: props.behaviorName,
        type: 'line',
        smooth: true,
        symbol: 'circle',
        showSymbol: false,
        z: 2,
        lineStyle: {
          width: getVwSize(6),
          shadowColor: 'rgba(66, 163, 255, 0.1)',
          shadowBlur: getVwSize(6),
          shadowOffsetY: getVwSize(6)
        },
        itemStyle: {
          normal: {
            color: '#42A3FF',
          },
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [{
            offset: 0,
            color: '#fff'
          },
          {
            offset: 1,
            color: '#D8ECFF'
          }
          ])
        },

        data: data.y2,
      },
      {
        name: `平均${props.behaviorName}`,
        type: 'line',
        smooth: true,
        symbol: 'circle',
        showSymbol: false,
        z: 1,
        lineStyle: {
          width: getVwSize(6),
          shadowColor: 'rgba(255, 204, 123, 0.27)',
          shadowBlur: getVwSize(6),
          shadowOffsetY: getVwSize(6)
        },
        itemStyle: {
          normal: {
            color: '#FFCC7B',
          },
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [{
            offset: 0,
            color: '#fff'
          },
          {
            offset: 1,
            color: 'rgba(255, 204, 123, 0.27)'
          }
          ])
        },
        data: data.y3,
      },
    ],
  }
  setOptions(option)
}

onMounted(() => {
  initChart()
})

// 监听dataSource
watch(
  () => props.dataSource,
  (newValue, oldValue) => {
    initChart()
  },
  { deep: true, immediate: true }
)
</script>
<style scoped lang="scss">
.custom-background {
  background-image: url(@/assets/leaderBoard/ph/label_bg.png) no-repeat;
  background-size: 100% 100%;
  /* 或者你希望的尺寸 */
  background-position: center;
  padding: 10px;
  /* 根据需要调整 */
}
</style>
