<template>
    <div class="signUpActivity-form w-full min-h-full flex flex-col items-center">
        <div class="bg-#fdf2f5 py27px px32px text-28px font-400 text-[#ef799b] w-full">
            请依次填写以下信息，*显示的为必填内容
        </div>
        <div
            class="!rounded-20px overflow-hidden mb-60px w-[calc(100%-60px)] bg-#fff pb-60px mt-27px  pt40px box-border min-h-80vh">
            <img loading="lazy" :src="title" alt="" srcset="" class="title h-45px mx-auto block">
            <van-form ref="formDataRef" class="">
                <template v-for="item in topicInfoList" :key="item.topicInfoId">
                    <!-- 单行输入 -->
                    <van-field v-model="formData[item.topicInfoId]" :name="item.topicContent" :label="item.topicContent"
                        :placeholder="`请输入${item.topicContent}`" :required="item.ifMust === 'y'"
                        :rules="[{ required: item.ifMust === 'y', message: `请输入${item.topicContent}` }]"
                        input-align="left" error-message-align="left" v-if="item.optionType === 'input'" />
                    <!-- 多行输入 -->
                    <van-field v-model="formData[item.topicInfoId]" :name="item.topicContent" :label="item.topicContent"
                        :placeholder="`请输入${item.topicContent}`" :required="item.ifMust === 'y'"
                        :rules="[{ required: item.ifMust === 'y', message: `请输入${item.topicContent}` }]"
                        input-align="left" error-message-align="left" v-if="item.optionType === 'textarea'"
                        label-align="top" type="textarea" rows="4" autosize />
                    <!-- 下拉单选 -->
                    <van-field v-model="formData[item.topicInfoId]" readonly :label="item.topicContent"
                        :placeholder="`请选择${item.topicContent}`" :required="item.ifMust === 'y'"
                        :rules="[{ required: item.ifMust === 'y', message: `请选择${item.topicContent}` }]"
                        input-align="left" error-message-align="left" right-icon="arrow" @click="onSelect(item)"
                        v-if="item.optionType === 'select'" />
                    <!-- 多选框 -->
                    <van-field :label="item.topicContent" :placeholder="`请选择${item.topicContent}`"
                        :rules="[{ required: item.ifMust === 'y', message: `请选择${item.topicContent}` }]"
                        input-align="left" error-message-align="left" v-if="item.optionType === 'checkbox'"
                        :required="item.ifMust === 'y'" class="radioBox">
                        <template #input>
                            <van-checkbox-group v-model="formData[item.topicInfoId]" direction="horizontal">
                                <van-checkbox :name="i.optionContent" shape="square" v-for="i in item.options"
                                    :key="i.topicOptionId" class="mb-12px text-28px flex items-center">
                                    {{ i.optionContent }}
                                </van-checkbox>
                            </van-checkbox-group>
                        </template>
                    </van-field>
                    <!-- 单选框 -->
                    <van-field :label="item.topicContent" :placeholder="`请选择${item.topicContent}`"
                        :rules="[{ required: item.ifMust === 'y', message: `请选择${item.topicContent}` }]"
                        input-align="left" error-message-align="left" v-if="item.optionType === 'radio'"
                        :required="item.ifMust === 'y'" class="radioBox">
                        <template #input>
                            <van-radio-group v-model="formData[item.topicInfoId]" direction="horizontal" shape="dot">
                                <van-radio :name="i.optionContent" v-for="i in item.options" :key="i.topicOptionId">{{
                                    i.optionContent
                                    }}</van-radio>
                            </van-radio-group>
                        </template>
                    </van-field>

                    <!--
                     <van-field v-model="formData[item.topicInfoId]" :name="item.topicContent" :label="item.topicContent"
                        :placeholder="`请输入${item.topicContent}`" :required="item.ifMust === 'y'"
                        :rules="[{ required: item.ifMust === 'y', message: `请输入${item.topicContent}` }]"
                        input-align="left" error-message-align="left" v-if="item.optionType === 'input'" />
                    -->
                    <!-- 日期时间选择 -->
                    <van-field v-model="formData[item.topicInfoId]" readonly :label="item.topicContent"
                        :placeholder="`请选择${item.topicContent}`"
                        :rules="[{ required: item.ifMust === 'y', message: `请选择${item.topicContent}` }]"
                        input-align="left" error-message-align="left" @click="onSelect(item)"
                        v-if="item.optionType === 'datePicker'" :required="item.ifMust === 'y'" right-icon="arrow">
                    </van-field>
                    <!-- 地区选择 -->
                    <!--        <van-field v-model="formData[item.topicInfoId]" is-link readonly label="所在地区" placeholder="请选择所在地区"-->
                    <!--          :rules="[{ required: item.ifMust === 'y', message: '请选择所在地区' }]" input-align="right" error-message-align="right"-->
                    <!--          @click="onSelect(item)" v-if="item.optionType === 'areaPicker'" :required="item.ifMust === 'y'" />-->
                    <!-- 文件上传 -->
                    <!--        <van-field name="uploader" label="文件上传" v-if="item.optionType === 'uploader'"-->
                    <!--          :rules="[{ required: item.ifMust === 'y', message: `请上传${item.topicContent}` }]"-->
                    <!--          :required="item.ifMust === 'y'">-->
                    <!--          <template #input>-->
                    <!--            <Uploader :file-lists="formData[item.topicInfoId]" :multiple="false" accept-type="image/jpeg,image/png"-->
                    <!--              :visitPrefix="$store.state.visitPrefix" :max-size="5" :operateType="2"-->
                    <!--              @success="res => onUploadSuccess(res, item)" @delete="name => onDelete(name, item)">-->
                    <!--            </Uploader>-->
                    <!--          </template>-->
                    <!--        </van-field>-->
                    <!-- 日历 -->
                    <van-field v-model="formData[item.topicInfoId]" is-link readonly :label="item.topicContent"
                        :placeholder="`请选择${item.topicContent}`"
                        :rules="[{ required: item.ifMust === 'y', message: `请选择${item.topicContent}` }]"
                        input-align="left" error-message-align="left" @click="onSelectCalendar(item)"
                        v-if="item.optionType === 'calendar'" :required="item.ifMust === 'y'" />
                </template>
            </van-form>
            <div class="mx27px text-#333 text-30px mt35px">
                <img loading="lazy" :src="checked ? iconYgx : iconWgx" alt="" srcset="" class="w-30px h-auto v-text-top"
                    @click="checked = !checked">
                我已阅读并同意签订<span class="text-#3f95ff" @click.stop="openSinglePopup">《单身告知书》</span>和<span
                    class="text-#3f95ff" @click.stop="openSinglePopup">《单身承诺书》</span>
            </div>
            <Button name="提交" class="!w-[470px] !h-[83px] mx-auto mt61px" @click="onSubmit" />
        </div>

        <van-popup v-model:show="show" round position="bottom">
            <van-picker :columns="columns" @cancel="show = false" @confirm="onConfirm"
                :columns-field-names="customFieldName" v-if="currentTopic.optionType === 'select'" />
            <van-date-picker v-model="currentDate" @confirm="onConfirmDate" @cancel="show = false"
                v-if="currentTopic.optionType === 'datePicker'" />
        </van-popup>
        <van-calendar v-model:show="showCalendar" @confirm="onCalendarConfirm" />
        <singlePopup :showPop="showPop" @closePopup="closeSinglePopup"></singlePopup>
        <applyPopup :showPop="showApplyPop" :type="popupType" :msg="popupMsg" @closePopup="closeApplyPopup">
        </applyPopup>
    </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { showToast } from 'vant'
import { useUserStore } from '@/store/modules/user';
import { useRouter } from 'vue-router';
import { applicationRecord, signUp } from '@/api/activity';
import Button from '@/components/Button/button.vue';

import title from "@/assets/friendship/apply_title.png"
import singlePopup from '../components/singlePopup.vue';
import applyPopup from '../components/applyPopup.vue';
import iconWgx from "@/assets/friendship/icon_wgx.png"
import iconYgx from "@/assets/friendship/icon_Ygx.png"


const showPop = ref(false)
const showApplyPop = ref(false)
const popupType = ref('error')
const popupMsg = ref('')

// 自定义字段映射
const customFieldName = {
    text: 'optionContent',
    value: 'topicOptionId',
}

const useStore = useUserStore()
const router = useRouter()
const route = useRoute()
// Reactive state variables
const formDataRef = ref(null)
const formData = ref({})
const topicInfoList = ref([])
const show = ref(false)
const showCalendar = ref(false)
const columns = ref([])
const currentTopic = ref({})
const currentDate = ref([new Date().getFullYear(), new Date().getMonth() + 1, new Date().getDate()])
const checked = ref(false)
// Mounted hook
onMounted(async () => {
    // getDetail()
})

function closeSinglePopup() {
    showPop.value = false
    checked.value = true
}
function openSinglePopup() {
    showPop.value = true
}
function closeApplyPopup() {
    showApplyPop.value = false
}
// 计算属性
const activityDetail = computed(() => useStore.activityDetail || {});

// Watchers
watch(activityDetail, (newVal) => {
    if (newVal) {
        topicInfoList.value = newVal?.signUpInfo?.topicInfoList || []
    }
})

// Mounted hook
onMounted(async () => {
    if (activityDetail.value) {
        topicInfoList.value = activityDetail.value?.signUpInfo?.topicInfoList || []
        //修改报名信息
        if (route.query.autoId) {
            const { data } = await applicationRecord({
                activityId: activityDetail.value.activityId,
            });
            data?.answerRecord?.forEach(t => {
                const { topicInfoId, content } = t
                formData.value[topicInfoId] = content
            })
            // 处理回显数据
            topicInfoList.value.forEach(t => {
                const { topicInfoId, optionType } = t
                if (optionType === 'checkbox') {
                    formData.value[topicInfoId] = formData.value[topicInfoId].split(",")
                }
            })
        }
    }
})

const onConfirm = ({ selectedOptions }) => {
    formData.value[currentTopic.value.topicInfoId] = selectedOptions?.[0].optionContent
    show.value = false
}

const onConfirmDate = ({ selectedValues }) => {
    formData.value[currentTopic.value.topicInfoId] = selectedValues?.join('-')
    show.value = false
}


// Methods
const onSelect = (item) => {
    currentTopic.value = item
    columns.value = item.options
    show.value = true
}

const onSelectCalendar = (item) => {
    currentTopic.value = item
    showCalendar.value = true
}

const onCalendarConfirm = (date) => {
    showCalendar.value = false
    formData.value[currentTopic.value.topicInfoId] = new Date(date).format('yyyy-MM-dd')
}

const onSubmit = async () => {
    // submit
    try {
        await formDataRef.value.validate()

        // await showConfirmDialog({
        //     title: '温馨提示',
        //     message: '请确认以上信息是否填写无误?',
        // })
        if (!checked.value) {
            popupType.value = 'error'
            popupMsg.value = '请您仔细阅读并同意签订《单身承诺书》和《单身告知书》'
            showApplyPop.value = true
            return
        }
        // on confirm
        const form = JSON.parse(JSON.stringify(formData.value))
        const topicAnswers = {}

        topicInfoList.value.forEach(t => {
            const { topicInfoId, optionType } = t
            if (optionType === 'checkbox') {
                topicAnswers[topicInfoId] = form[topicInfoId] && form[topicInfoId].join(',')
            } else if (optionType === 'uploader') {
                topicAnswers[topicInfoId] = form[topicInfoId].map(item => item.url)?.[0]
            } else {
                topicAnswers[topicInfoId] = form[topicInfoId]
            }
        })
        const platform = sessionStorage.getItem('platform')
        let a = {
            autoId: route.query.autoId ?? undefined,
            activityId: activityDetail.value.activityId,
            platform,
            topicAnswers,
        }


        const { code, message } = await signUp({
            autoId: route.query.autoId ?? undefined,
            activityId: activityDetail.value.activityId,
            platform,
            topicAnswers,
            gender: useUserStore().userInfo?.gender
        })

        if (code === 200) {
            // await showConfirmDialog({
            //     title: '温馨提示',
            //     message: '报名信息提交成功,感谢您的参与~',
            //     showConfirmButton: true,
            //     showCancelButton: activityDetail.value.luckDraw === 'y',
            //     confirmButtonText: activityDetail.value.luckDraw === 'y' ? '去抽奖' : '我知道了',
            //     cancelButtonText: activityDetail.value.luckDraw === 'y' ? '返回' : ''
            // })
            popupType.value = 'success'
            popupMsg.value = '审核结果及活动详细信息将电话通知，请您保持电话的畅通'
            showApplyPop.value = true
            // on confirm
            if (activityDetail.value.luckDraw === 'y') {
                router.replace('/activityHome/lottery')
            } else {
                router.go(-1)
            }
        } else {
            showToast(message)
        }
    } catch (error) {
        console.log(error)
        showToast({
            message: '请完善报名信息',
        })
    }
}
</script>

<style lang="scss" scoped>
.signUpActivity-form {
    background-color: #8EC5FC;
    background-image: linear-gradient(135deg, #8EC5FC 0%, #E0C3FC 80%);


    .van-cell {
        display: block;

        ::after {
            display: none;
        }
    }

    // :deep(.van-field__control) {
    //     font-weight: 400;
    //     font-size: 28px;
    //     color: #999999;
    //     padding: 25px;
    //     background: #F1F6FB;
    //     border-radius: 10px;
    //     border: 1px solid #5AA4FF;
    // }
    :deep(.van-field__body) {
        font-weight: 400;
        font-size: 28px;
        color: #999999;
        padding: 25px;
        background: #F1F6FB;
        border-radius: 10px;
        border: 1px solid #5AA4FF;
    }

    :deep(.van-field__label) {
        color: #333;
        margin-bottom: 26px;
    }

    :deep(.van-checkbox-group) {
        display: flex;
        justify-content: flex-end;
    }

    .van-cell:after {
        border: none;
    }

    .radioBox {
        :deep(.van-field__body) {
            border: none;
            background: #fff;
            padding: 0;

        }

        :deep(.van-field__label) {
            color: #333;

        }

        .van-radio--horizontal {
            margin-right: 80px;
        }

    }

    // :deep(.van-checkbox__icon),
    // :deep(.van-icon:before),
    :deep(.van-checkbox__icon .van-icon),
    :deep(.van-radio__icon .van-icon),
    :deep(.van-radio__icon) {
        display: flex;
        align-items: center;
        width: 30px;
        height: 30px;
        line-height: 30px;
    }

    :deep(.van-checkbox__icon) {
        display: flex;
        align-items: center;
    }

    .submit {
        background-image: url('@/assets/activity/apply_btn.png');
        font-size: 33px;
        background-size: 100% 100%;
        color: #FFFFFF;
    }
}
</style>
