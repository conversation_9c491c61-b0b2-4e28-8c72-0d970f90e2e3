import {showImagePreview} from 'vant';
export default function registerDirectives(app){
	
    // 自定义图片预览指令
    app.directive('previewImg', {
        mounted(el, binding) {
            const img = el as HTMLImageElement;
            img.addEventListener('click', (event) => {
                event.stopPropagation()
                if(binding.value) showImagePreview([binding.value])
            })
        },
        updated(el, binding) {
            const img = el as HTMLImageElement;
            img.addEventListener('click', (event) => {
                event.stopPropagation()
            if(binding.value) showImagePreview([binding.value])
            })
        },
        unmounted(el) {
            const img = el as HTMLImageElement;
            img.removeEventListener('click', () => {});
        }
    });
    // 自定义格式化数据展示
    app.directive('formatNum', {
        mounted(el,bindings){
            let formatdenominator = bindings.value;
            if(parseFloat(el.textContent) >= Number(formatdenominator)){
                el.textContent =(parseFloat(el.textContent)/Number(formatdenominator)).toFixed(2);
            }
        },
        updated(el,bindings){
            let formatdenominator = bindings.value;
            if(parseFloat(el.textContent) >= Number(formatdenominator)){
                el.textContent =(parseFloat(el.textContent)/Number(formatdenominator)).toFixed(2);
            }
        },
    });
}