<template>
    <div class="waterIntergral" v-if="showParent">
        <div class="text-left relative defalut-bubble" :class="activeClass">
            <img src="@/assets/public/icon-bubbleBig.png" :class="'w-' + bigSize" />
            <div class="absolute text-#5AA4FF w-full h-full
            left-50% top-50% -translate-50% flex items-center justify-center leading-none"
                :class="'text-' + scorefontSize">+{{ score }}分</div>
        </div>
        <div class="defalut-bubble flex justify-end -mt-26px" :class="activeClass1">
            <div>
                <img src="@/assets/public/icon-bubbleMid.png" class="mt-20px" :class="'w-' + smallSize" />
            </div>
            <div>
                <img src="@/assets/public/icon-bubbleMid.png" class="-mt-20px ml-5px" :class="'w-' + midSize" />
            </div>

        </div>
    </div>
</template>
<script lang="ts" setup>

const props = defineProps({
    bigSize: {//大水泡大小
        type: String,
        default: "100px",
    },
    midSize: {//中水泡大小
        type: String,
        default: "30px",
    },
    smallSize: {//小水泡大小
        type: String,
        default: "20px",
    },
    scorefontSize: {//分数大小
        type: String,
        default: "32px",
    },
    score: {
        type: Number,
        default: 1,
    },
    show: {
        type: Boolean,
        default: false,
    },
    duration: {//动画持续时间
        type: Number,
        default: 2000,
    },

})
const emit = defineEmits(['update:show'])
const activeClass = ref('') //大水泡类名
const activeClass1 = ref('')//小水泡类名
const showParent = ref(false) //是否显示最外层
watch(() => props.show, (val) => {
    if (val) {
        showParent.value = true
        activeClass.value = 'bubble-big'
        activeClass1.value = 'bubble-mid'
        setTimeout(() => {
            emit('update:show', false)
            activeClass.value = 'bubble-big-colse'
            activeClass1.value = 'bubble-mid-colse'
            setTimeout(() => {
                showParent.value = false
            }, 1500)
        }, props.duration)
    }
})
</script>
<style scoped lang="scss">
.waterIntergral {
    .defalut-bubble {
        opacity: 0;
        transform: translateY(100%);
    }

    .bubble-big {
        animation: flyToTop 1s ease-in-out forwards;
        opacity: 0;

    }

    .bubble-mid {
        animation: flyToTop 1.1s ease-in-out forwards;
        opacity: 0;
    }

    .bubble-big-colse {
        animation: fadeOut 1.5s ease-in-out forwards;
    }

    .bubble-mid-colse {
        animation: fadeOut 1.5s ease-in-out forwards;
    }
}

// 向上飞入动画
@keyframes flyToTop {
    0% {
        transform: translateY(100%);
        opacity: 0;
    }

    100% {
        transform: translateY(0%);
        opacity: 1;
    }
}

// 浅出动画
@keyframes fadeOut {
    0% {
        opacity: 1;
        transform: translateY(0%);
    }

    100% {
        opacity: 0;
        transform: translateY(100%);
    }
}
</style>