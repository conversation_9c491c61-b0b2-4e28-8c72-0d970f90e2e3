<template>
  <div class="bg-[#f6f7f8] min-h-[100vh]">
    <div v-for="(item, index) in Data.topicList" :key="index"
      class="mb-[20px] bg-[#fff] px-29px box-border pt-[39px] pb-[19px]">
      <topicList :data="[item]" type="examine" />
    </div>
    <div class="px-29px box-border pb-150px">
      <div class="bg-[#fff] rounded-[20px] p-[22px] box-border">
        <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
          <evaluateList :data="Data.evaluateList">
            <template #shortContent="{ item, index }">
              <div class="text-28px mt-20px">{{ item.content }}</div>
            </template>
          </evaluateList>
        </refreshList>

      </div>
    </div>
    <div class="flex fixed bottom-0 bg-#fff text-#333 text-25px h-[122px] w-100% bottom-box justify-around">
      <!-- <div class="flex items-center justify-center" >
            <img loading="lazy" src="@/assets/interest/icon_comment.png" alt=""  class="w-40px  mr-20px">
            <div>评论</div>
        </div> -->
      <div class="input send-text flex items-center  flex-1 rounded-37px min-h-full  relative w-60%">
        <!-- <div class="w-33px ml-40px" v-show="!conment">
                    <img loading="lazy" src="@/assets/public/comment_icon.png" class="w-full" />
                </div> -->
        <van-field v-model="conment" placeholder="写评论" type="textarea" rows="1" autosize class="conment"
          style="width: 80%;"></van-field>
        <div class="send absolute right-17px text-center leading-49px
                w-104px h-49px rounded-23px bg-[#fff] text-26px text-[#999] bg-[#F1F1F1] conment"
          @click="sendText(Data.topicList[0])">发送</div>
      </div>
      <div class="w-40% flex  items-center justify-around text-28px">
        <div class="flex items-center"><van-icon name="eye-o" color="#999999" class="mr-20px" />{{
          Data.topicList[0]?.readCount }}</div>
        <div class="flex items-center" @click="like(Data.topicList[0])"><van-icon
            :name="Data.topicList[0]?.likeState ? 'good-job' : 'good-job-o'"
            :color="Data.topicList[0]?.likeState ? '#5AA4FF' : '#333'" />{{ Data.topicList[0]?.likeCount }}
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useRoute } from "vue-router";
const route = useRoute();
import evaluateList from "@/components/List/evaluateList.vue";
import topicList from "@/components/List/topicList.vue";
import { commentDetail, commentsList, CommentsLikeOperate, interestGroupComments } from "@/api/interestGroup";
import Popup from "@/components/Popup/popup.vue";
import { showSuccessToast, showFailToast } from "vant";
import refreshList from '@/components/refreshList/index.vue';

const conment = ref("");//评论
const Data = ref({
  topicList: [],
  evaluateList: [],
  showPop: false,
  titleName: '',
  objInfo: {},
  pageNum: 1,
});
onMounted(() => {
  getDetail();
  getcommentsList();
});
//获取详情
function getDetail() {
  commentDetail({ commentsId: route.query.commentsId }).then(res => {
    if (res.code == 200) {
      Data.value.topicList = [res.data];
    }

  })
}
//动态列表
const loadMoreRef = ref(null);
function getcommentsList() {
  commentsList({
    // dataSources: "group", //评论来源 : group:小组  activity：活动
    // commentType:'dynamic',//评论类型 （groupComment：小组评价，dynamic：小组动态，activityComment：活动评论，activityDynamic：活动评价 ）
    pageSize: 10,
    pageNum: Data.value.pageNum,
    sortType: 'desc',
    orderBy: 'create_time',
    pid: route.query.commentsId
  }).then(res => {
    if (res.code == 200) {
      if (Data.value.pageNum === 1) Data.value.evaluateList = [];
      Data.value.evaluateList = Data.value.evaluateList.concat(res.data);
      //重置刷新状态及 判断是否加载完成
      if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(Data.value.evaluateList.length, res.total);
      }
    }
  })
}
// 刷新
const onRefreshList = () => {
  Data.value.pageNum = 1;
  getcommentsList();
};
// 加载更多
const onLoadMore = () => {
  Data.value.pageNum++;
  getcommentsList();
};

//点赞
function like(item) {
  CommentsLikeOperate({
    commentsId: item.commentsId
  }).then(res => {
    if (res.code == 200) {
      // likeState
      if (item.likeState) {
        item.likeState = false;
        item.likeCount--;
        showFailToast('取消点赞')
      } else {
        item.likeState = true;
        item.likeCount++;
        showSuccessToast('点赞成功')
      }

    }
  })
}
//评论
function sendText(item) {
  let params = {
    content: conment.value,
    groupId: item.groupId,
    dataSources: "group", //评论来源 : group:小组  activity：活动
    commentType: 'reply', //评论类型 （groupComment：小组评价，dynamic：小组动态，activityComment：活动评论，activityDynamic：活动评价 ）
    pid: item.commentsId
  }
  interestGroupComments(params).then(res => {
    if (res.code == 200) {
      conment.value = ""
      showSuccessToast("提交成功,等待审核");
    }
  })
}

</script>
<style lang="scss" scoped>
.send-text {
  :deep(.van-cell) {
    font-size: 28px;
  }
}
</style>