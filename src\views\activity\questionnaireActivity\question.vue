<template>
  <div class="questionnaireActivity p-28px w-full min-h-full bg-[#dde8f8]">
    <div class="w-full bg-[#fff] rounded-19px p-30px">
      <div class="text-center">问卷名称：{{ activityDetail?.questionnaireInfo.questionnaireName }}</div>
      <p class="text-28px text-[#666] leading-38px border-b-1px pb-30px mb-20px">请您花费几分钟时间填写本问卷，您的意见和建议对我们非常重要。感谢您的参与!
      </p>
      <div v-if="topicInfoList && topicInfoList.length" class="question-container"
        v-for="(item, index) in topicInfoList" :key="item.topicInfoId">
        <div class="text-30px font-bold text-[#333] leading-48px mb-20px mt-30px" :id="item.topicInfoId">
          <span class="text-red-600" v-if="item.ifMust === 'y'">*</span>{{ index + 1 }}、{{ item.topicContent }}
        </div>
        <!-- 单选 -->
        <div v-if="item.optionType === 'radio'">
          <van-radio-group v-model="formData[item.topicInfoId]" direction="horizontal">
            <div
              class="flex items-center w-full bg-[#fafafa] mb-20px rounded-28px text-28px text-[#333] leading-48px p-15px px-18px"
              v-for="i in item.options" :key="i.topicOptionId">
              <van-radio :name="i.optionNo" class="w-full">
                <template #icon="props">
                  <div class="flex items-center h-full">
                    <img loading="lazy" class="w-32px" :src="props.checked ? selectedRadio : unselectedRadio" />
                  </div>
                </template>
                {{ i.optionContent }}
              </van-radio>
            </div>
          </van-radio-group>
        </div>
        <!-- 多选 -->
        <div v-if="item.optionType === 'checkbox'">
          <van-checkbox-group v-model="formData[item.topicInfoId]" direction="horizontal" class="flex flex-col">
            <div
              class="flex items-center w-full bg-[#fafafa] mb-20px rounded-28px text-28px text-[#333] leading-48px p-15px px-18px"
              v-for="i in item.options" :key="i.topicOptionId">
              <van-checkbox :name="i.optionNo" shape="square" class="w-full">
                <template #icon="props">
                  <div class="flex items-center h-full">
                    <img loading="lazy" class="w-32px" :src="props.checked ? selectedCheckbox : unselectedCheckbox" />
                  </div>
                </template>
                {{ i.optionContent }}
              </van-checkbox>
            </div>
          </van-checkbox-group>
        </div>
        <!-- 多行输入 -->
        <div v-if="item.optionType === 'textarea'">
          <van-field v-model="formData[item.topicInfoId]" type="textarea" :placeholder="item.topicContent"
            maxlength="300" class="border-1px border-[#eee] rounded-14px !py-15px !px-20px" rows="2" autosize />
        </div>
        <!-- 单行输入 -->
        <div v-if="item.optionType === 'input'">
          <van-field v-model="formData[item.topicInfoId]" :placeholder="item.topicContent" maxlength="100"
            class="border-1px border-[#eee] rounded-14px !py-15px !px-20px" />
        </div>
        <!-- 评分 -->
        <template v-if="item.optionType === 'rate'">
          <van-rate v-model="formData[item.topicInfoId]" />
        </template>

        <!-- 日期时间 -->
        <div class="flex items-center text-30px" v-if="item.optionType === 'datePicker'">
          {{ formData[item.topicInfoId] }} <van-button type="default" size="small" @click="onSelect(item)"
            class="ml-20px">选择日期</van-button>
        </div>
        <!-- 地区选择 -->
        <div class="flex items-center text-30px" v-if="item.optionType === 'areaPicker'">
          {{ formData[item.topicInfoId] }} <van-button type="default" size="small" @click="onSelect(item)"
            class="ml-20px">选择地区</van-button>
        </div>
        <!-- 日历 -->
        <div class="flex items-center text-30px" v-if="item.optionType === 'calendar'">
          {{ formData[item.topicInfoId] }} <van-button type="default" size="small" @click="onSelectCalendar(item)"
            class="ml-20px">选择日期</van-button>
        </div>
      </div>
      <van-popup v-model:show="show" round position="bottom">
        <van-picker :columns="columns" @cancel="show = false" @confirm="onConfirm"
          :columns-field-names="customFieldName" v-if="currentTopic.optionType === 'select'" />
        <van-date-picker v-model="currentDate" @confirm="onConfirmDate" @cancel="show = false"
          v-if="currentTopic.optionType === 'datePicker'" />

      </van-popup>
      <van-calendar v-model:show="showCalendar" @confirm="onCalendarConfirm" />
      <div class="w-full flex justify-center flex-col items-center mt-50px mb-20px">
        <van-button class="submit-btn w-60/100 flex justify-center border-0" @click="onSubmit">提交</van-button>
      </div>
    </div>

  </div>
</template>

<script setup>
import selectedRadio from '@/assets/activity/selected-radio.png'
import selectedCheckbox from '@/assets/activity/selected-checkbox.png'
import unselectedRadio from '@/assets/activity/unselected-radio.png'
import unselectedCheckbox from '@/assets/activity/unselected-checkbox.png'
import { ref, computed, onMounted, watch } from 'vue'

import { showConfirmDialog, showDialog, showToast } from 'vant';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/store/modules/user';
import { questionnaireSubmit } from '@/api/activity';

// 获取 Vuex store
const useStore = useUserStore();
const router = useRouter();
// Reactive state
// 自定义字段映射
const customFieldName = {
  text: 'optionContent',
  value: 'topicOptionId',
}


const topicInfoList = ref([])
const show = ref(false)
const showCalendar = ref(false)
const startTime = ref(new Date().getTime())
const currentDate = ref([new Date().getFullYear(), new Date().getMonth() + 1, new Date().getDate()])
const formData = ref({})
const columns = ref([])
// Reactive state
const currentTopic = ref(null)

// 计算属性
const activityDetail = computed(() => useStore.activityDetail || {})

// Methods
const onSelect = (item) => {
  currentTopic.value = item
  show.value = true
}

const onSelectCalendar = (item) => {
  currentTopic.value = item
  showCalendar.value = true
}

const onConfirm = ({ selectedOptions }) => {
  formData.value[currentTopic.value.topicInfoId] = selectedOptions?.[0].optionContent
  show.value = false
}

const onConfirmDate = ({ selectedValues }) => {
  if (selectedValues) {
    formData.value[currentTopic.value.topicInfoId] = selectedValues.join('-')
  }
  show.value = false
}

const onCalendarConfirm = (date) => {
  showCalendar.value = false
  formData.value[currentTopic.value.topicInfoId] = new Date(date).toLocaleDateString('en-CA') // 统一格式：yyyy-MM-dd
}

// 提交表单
const onSubmit = async () => {
  // 校验必填项
  for (let index = 0; index < topicInfoList.value.length; index++) {
    const item = topicInfoList.value[index]
    if (item.ifMust === 'y') {
      if (!formData.value[item.topicInfoId] || (Array.isArray(formData.value[item.topicInfoId]) && !formData.value[item.topicInfoId]?.length)) {
        const dom = document.querySelector(`#${item.topicInfoId}`)
        dom.scrollIntoView({ behavior: 'smooth' })
        showToast({
          message: `请完善第${index + 1}题必填项`,
          duration: 2000
        })
        return
      }
    }
  }

  // 弹出确认对话框
  try {
    await showConfirmDialog({
      title: '温馨提示',
      message: '请确认以上信息是否填写无误?',
    })

    // 提交数据
    const form = JSON.parse(JSON.stringify(formData.value))
    const topicAnswers = []

    topicInfoList.value.forEach(t => {
      const { topicInfoId, optionType, options } = t
      if (['radio', 'checkbox'].includes(optionType)) {
        if (optionType === 'checkbox') {
          // 多选
          if (form[topicInfoId] && form[topicInfoId].length) {
            topicAnswers.push({
              topicInfoId,
              answerContent: form[topicInfoId]?.join(','), // 选项序号
              remark: form[topicInfoId]?.map(i => options[i - 1].optionContent)?.join('；'), // 题目内容
            })
          }
        } else {
          // 单选
          if (form[topicInfoId]) {
            topicAnswers.push({
              topicInfoId,
              answerContent: form[topicInfoId], // 选项序号
              remark: options[form[topicInfoId] - 1]?.optionContent, // 题目内容
            })
          }
        }
      } else if (optionType === 'uploader') {
        if (form[topicInfoId]) {
          topicAnswers.push({
            topicInfoId,
            answerContent: form[topicInfoId]?.[0].url, // 选项序号
            remark: form[topicInfoId]?.[0].url, // 题目内容
          })
        }
      } else {
        if (form[topicInfoId]) {
          topicAnswers.push({
            topicInfoId,
            answerContent: form[topicInfoId], // 选项序号
            remark: form[topicInfoId], // 题目内容
          })
        }
      }
    })

    const platform = sessionStorage.getItem('platform')
    const { code, message } = await questionnaireSubmit({
      activityId: activityDetail.value.activityId,
      platform,
      topicAnswers,
      totalTime: (new Date().getTime() - startTime.value) / 1000,
    })

    if (code === 200) {
      await showConfirmDialog({
        title: '温馨提示',
        message: '问卷提交成功,感谢您的参与~',
        showConfirmButton: true,
        showCancelButton: activityDetail.value.luckDraw === 'y',
        confirmButtonText: activityDetail.value.luckDraw === 'y' ? '去抽奖' : '我知道了',
        cancelButtonText: activityDetail.value.luckDraw === 'y' ? '返回' : ''
      })

      if (activityDetail.value.luckDraw === 'y') {
        router.replace('/activityHome/lottery')
      } else {
        router.replace('/activityHome/activityDetail')
      }
    } else {
      await showDialog({
        title: '温馨提示',
        message: message,
        confirmButtonText: '我知道了',
      }).then(() => {
        router.replace('/activityHome/activityDetail')
      })

    }
  } catch (error) {
    // 用户取消了操作
  }
}

watch(() => activityDetail.value, () => {
  if (activityDetail.value.activityId) {
    const { questionnaireInfo: { topicInfoList: topicInfos } } = activityDetail.value
    topicInfoList.value = topicInfos
  }
})
// Mounted hook
onMounted(() => {
  if (activityDetail.value.activityId) {
    const { questionnaireInfo: { topicInfoList: topicInfos } } = activityDetail.value
    topicInfoList.value = topicInfos
  }
})
</script>

<style lang="scss" scoped>
.questionnaireActivity {
  font-family: Source Han Sans CN;

  .question-container {
    margin-bottom: 20px;

    .optionSelect {
      background: #fff1d8;
    }
  }

  .submit-btn {
    height: 83px;
    background: linear-gradient(0deg, #ffcc73, #ff9046);
    border-radius: 39px;
    font-size: 33px;
    font-family: Alibaba PuHuiTi;
    font-weight: 500;
    color: #ffffff;
    line-height: 80px;
  }
}
</style>
