<template>
  <div class="">
    <!-- :style="{ backgroundImage: `url(${dealStatus('banner')})` }" bg-no-repeat bg-cover bg-center-->
    <div class="h-327px w-100% flex flex-col justify-center pl-60px pb-50px box-border relative">
      <img loading="lazy" :src="dealStatus('banner')" alt="" class="h-100% w-100% absolute left-0 top-0" />
      <div class="relative">
        <div v-if="Data.status === '1'">
          <img loading="lazy" :src="wait_txt" alt="" class="h-43px w-auto mb-10px">
          <div class="text-28px text-#fff">请耐心等待~</div>
        </div>
        <div v-else-if="Data.status === '2'">
          <img loading="lazy" :src="fail_txt" alt="" class="h-43px w-auto mb-10px">
          <div class="text-28px text-#fff">请重新提交报名信息</div>
        </div>
        <div v-else-if="Data.status === '3'">
          <img loading="lazy" :src="success_txt" alt="" class="h-43px w-auto mb-10px">
          <div class="text-28px text-#fff">全力以赴，展现最好的自己！</div>
        </div>
      </div>
    </div>
    <div class="px-34px box-border">
      <div class="relative bg-#fff rounded-30px">
        <Title title="报名信息" />
        <div>
          <template v-for="(item, index) in formPrams" :key="item.key">
            <div class="flex items-center text-30px mt-40px">
              <div class="text-#666 " :class="{ 'just': item.name?.length < 5 }">{{ item.name }}</div>
              <span class="text-#666">:</span>
              <div class="text-#333 ml-26px">{{ Data.info[item.key] }}</div>
            </div>
          </template>
          <div class="flex items-center text-30px mt-40px">
            <div class="text-#666 just">提交时间</div>
            <span class="text-#666">:</span>
            <div class="text-#333 ml-26px">2024-10-10 10:00:00</div>
          </div>
          <div class="flex items-center text-30px mt-40px" v-if="Data.status === '2' || Data.status === '3'">
            <div class="text-#666 just">审核时间</div>
            <span class="text-#666">:</span>
            <div class="text-#333 ml-26px">2024-10-12 10:00:00</div>
          </div>
          <div class="flex items-center text-30px mt-40px" v-if="Data.status === '2'">
            <div class="text-#666 just">失败原因</div>
            <span class="text-#666">:</span>
            <div class="text-#E14026 ml-26px">不符合竞赛需求</div>
          </div>
          <div v-if="Data.status === '2'">
            <Button name="重新提交" class=" mt-80px m-auto w-450px h-78px" @click="toJump('/vitality/personalInfo')" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import Title from "@/components/Title/vitalityTitle.vue/";
import formPrams from "./formParams.ts";
import shz from "@/assets/vitality/personalInfo/shz.jpg";
import shsb from "@/assets/vitality/personalInfo/shsb.jpg";
import shtg from "@/assets/competition/shtg.jpg";
import wait_txt from "@/assets/competition/wait_txt.png";
import fail_txt from "@/assets/competition/fail_txt.png";
import success_txt from "@/assets/competition/success_txt.png";
import router from "@/router";
import Button from '@/components/Button/button.vue'
const Data = ref({
  status: "3", //1-审核中  2-审核失败  3-审核成功
  info: {
    name: "刘一菲",
    idNum: "510***********4158",
    sex: "女",
    date: "1996-09-08",
    phone: "185****9845",
    workUnit: "南充市总工会",
    area: "南充市顺庆区金泉路",
    address: "68号502",
    reson: "请输入正确的手机号码", //失败原因
  },
});
function dealStatus(type: string) {
  switch (Data.value.status) {
    case "1":
      return type === "banner" ? shz : "";
    case "2":
      return type === "banner" ? shsb : "";
    case "3":
      return type === "banner" ? shtg : "";
  }
}
function toJump(url: string) {
  router.push({ path: url });
}
function repeatSubmit() {
  Data.value.status = "1";
}
</script>
<style lang="scss" scoped>
.big_title {
  font-weight: bold;
  font-size: 46px;
  color: #ffffff;
  line-height: 98px;
  text-stroke: 3px #3183ED;
  -webkit-text-stroke: 3px #3183ED;
}

.just {
  text-align: justify;
  text-align-last: justify;
  width: 120px;
}
</style>