import { h5Http,cloudManageHttp } from '@/utils/http/axios';
import { BasicResponse } from '.';

enum API {
  findVenueTypeManageVOList = '/findVenueTypeManageVOList',
  h5FindVenuePositionVoList = '/h5FindVenuePositionVoList',
  view = '/h5GetVenuePositionVoByDto',
  findList = '/findList',
  h5 = '/getById',
  findRecordList = '/findRecordVOList',
  getRecordByRecordId = '/getRecordByRecordId',
  cancelRecord = '/cancelRecord',
  addRecord = '/addRecord',
  punchSign = '/punchSign',
  checkReservationRecord = '/checkReservationRecord',
  administratorsFindRecordList='/administratorsFindRecordList',
  whetherAdministrators='/whetherAdministrators'
}

function getApi(url?: string) {
  if (!url) {
    return '/venueInfo';
  }
  return '/venueInfo' + url;
}

// 列表
export const h5FindVenuePositionVoList = (params: Recordable) => {
  return h5Http.get<BasicResponse>(
    { url: getApi(API.h5FindVenuePositionVoList), params },
    {
      isTransformResponse: false,
    }
  );
};

export const findVenueTypeManageVOList = (params: Recordable) => {
  return h5Http.get<Recordable[]>(
    { url: getApi(API.findVenueTypeManageVOList), params },
    {
      isTransformResponse: true,
    }
  );
};

// view
export const view = (params: Recordable) => {
  return h5Http.get<Recordable>(
    {
      url: getApi(API.view),
      params,
    },
    {
      isTransformResponse: true,
    }
  );
};

// 删除
export const deleteLine = (autoId: number[] | number) => {
  return h5Http.delete<BasicResponse>(
    {
      url: getApi() + '?autoId=' + autoId,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 添加评论
export const addAComment = (params: Recordable) => {
  return h5Http.post<BasicResponse>({
    url: '/venuePositionComment/addAComment',
    params,
  });
};

// 场所list
export const findList = (params: Recordable) => {
  return h5Http.get<BasicResponse>({
    url: getApi(API.findList),
    params,
  });
};

// 查询阵地下评价
export const venuePositionCommentList = (params: Recordable) => {
  return h5Http.get<BasicResponse>({
    url: '/venuePositionComment/findVoListH5',
    params,
  });
};

// 查询场地详情
export const venueInfoDetail = (params: Recordable) => {
  return h5Http.get<Recordable>(
    {
      url: getApi(API.h5),
      params,
    },
    { isTransformResponse: true }
  );
};

// 统计
export const h5PositionTopStatistics = () => {
  return h5Http.get<Recordable>(
    {
      url: '/dataSummary/h5PositionTopStatistics',
    },
    { isTransformResponse: true }
  );
};

// 预约记录列表
export const findRecordList = (params: Recordable) => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(API.findRecordList),
      params,
    },
    { isTransformResponse: false }
  );
};

// 预约记录列表
export const getRecordByRecordId = (params: Recordable) => {
  return h5Http.get<Recordable>(
    {
      url: getApi(API.getRecordByRecordId),
      params,
    },
    { isTransformResponse: true }
  );
};
// 取消预约
export const cancelRecord = (params: Recordable) => {
  return h5Http.post<BasicResponse>({
    url: getApi(API.cancelRecord),
    params,
  });
};

// 预约
export const addRecord = (params: Recordable) => {
  return h5Http.post<BasicResponse>({
    url: getApi(API.addRecord),
    params,
  });
};

// 打卡
export const punchSign = (params: Recordable) => {
  return h5Http.post<BasicResponse>({
    url: getApi(API.punchSign),
    params,
  });
};

export const checkReservationRecord = (params: Recordable) => {
  return h5Http.post<BasicResponse>({
    url: getApi(API.checkReservationRecord),
    params,
  });
};

// 收藏
export const collectVenueInfo = (params: Recordable) => {
  return h5Http.get<BasicResponse>({ url: '/venueInfo/getCurrentCollectList', params });
};
// 阵地审核记录列表
export const administratorsFindRecordList = (params: Recordable) => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(API.administratorsFindRecordList),
      params,
    },
    { isTransformResponse: false }
  );
};
// 是否管理员
export const whetherAdministrators = (params: Recordable) => {
  return h5Http.get<BasicResponse>({ url: '/venueInfo/whetherAdministrators', params });
};
// 管理员审核阵地预约记录
export const administratorsAuditRecord = (params: Recordable) => {
  return h5Http.post<BasicResponse>({ url: '/venueInfo/administratorsAuditRecord', params });
};
// 管理员审核阵地预约记录详情
export const administratorsGetRecordByRecordId = (params: Recordable) => {
  return h5Http.get<BasicResponse>({ url: '/venueInfo/administratorsGetRecordByRecordId', params } ,{ isTransformResponse: true });
};
// 管理员审核拉入黑名单
export const administratorsSaveVenueBlacklist = (params: Recordable) => {
  return h5Http.post<BasicResponse>({ url: '/venueInfo/administratorsSaveVenueBlacklist', params } ,{ isTransformResponse: false });
};
// 黑名单列表
export const findVenueBlacklistList = (params: Recordable) => {
  return h5Http.get<BasicResponse>({ url: '/venueInfo/administratorsBlackList', params } ,{ isTransformResponse: false });
};
// 移除黑名单
export const removeVenueBlacklistById = (params: Recordable) => {
  console.log(params);
  
  return h5Http.delete<BasicResponse>({ url: '/venueInfo/removeVenueBlacklistById?autoId='+params } ,{ isTransformResponse: false });
};

