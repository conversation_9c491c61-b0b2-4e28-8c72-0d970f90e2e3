import { h5Http } from '@/utils/http/axios';
// 思想引领数据统计
export function getIdeasLeadingData(params:any) {
  return h5Http.get({
    url:'/dataSummary/ideologicalGuidanceStatistics',
    params
  })
}
// 区域会员行为趋势
export function getRegionMemberBehaviorTrend(params:any) {
  return h5Http.get({
    url:'/dataSummary/areaUserBehavior',
    params
  })
}
// 区域会员阅读量统计
export function getRegionMemberReadingStatistics(params:any) {
  return h5Http.get({
    url:'/dataSummary/regionalDataAnalysis',
    params
  })
}
