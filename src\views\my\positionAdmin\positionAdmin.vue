<template>
  <div class="bg-[#F5F5F5]" :class="$style['reserve-record']">
    <div class="tab-box mb-36px">
      <van-tabs v-model:active="data.tab.active" sticky line-width="20" @click-tab="onClickTab">
        <van-tab :title="item.label" v-for="item in data.tab.nav" :key="item.value" :name="item.value"
          title-class="tab-title"></van-tab>
      </van-tabs>
    </div>
    <div class="px-20px">
      <div v-if="data.tab.active == ''" @click="toBlackList()"
        class="flex justify-between bg-#fff rounded-16px px-30px py-18px mb-28px text-30px text-#333 font-500">
        <div class="flex items-center"> <img loading="lazy" src="@/assets/position/reserve/black.png" alt=""
            class="w-42px h-42px mr-12px" /> 黑名单管理</div>
        <van-button block
          class="w-[150px] h-[50px] rounded-[39px] justify-center flex items-center text-[26px] text-[#fff]" style="
                background: linear-gradient(0deg, #a1cbff 0%, #5aa4ff 100%);
                ">
          去查看
        </van-button>
      </div>
      <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
        <div v-for="item in data.list" :key="item"
          class="bg-[#FFF] rounded-16px list-items mb-22px p-20px text-26px leading-50px text-[#666] relative"
          @click="handleDetail(item)">
          <div class="text-30px text-[#333] leading-60px font-500">{{ item.venueName || '' }}</div>
          <div>所属阵地：{{ item.positionName || '' }}</div>
          <div>
            预约日期：{{ item.reservationDate }} {{ utils.formatTimeWithoutSeconds(item.reservationStartTime) }}-{{
              utils.formatTimeWithoutSeconds(item.reservationEndTime)
            }}
          </div>
          <div>联系人：{{ item.userName }}</div>
          <div>手机号码：{{ item.phone }}</div>
          <van-divider class="!my-10px text-[#C5C5C5]" dashed />
          <div class="flex w-full justify-between items-center">
            <div class="w-75%">
              <div v-show="item.auditTime && item.state === 'review'">审核时间：{{ item.auditTime }}</div>
              <div v-show="item.auditTime && item.state === 'used'" class="text-#338FFF">签到时间：{{ item.useTime }}</div>
              <div v-show="item.state === 'cancel'" class="text-#338FFF">取消时间：{{ item.cancellationTime }}</div>
              <div v-show="(item.state === 'useExpire' || item.state === 'expire')" class="text-#338FFF">过期时间：{{
                utils.formatTimeWithoutSeconds(item?.reservationDate) }} {{
                  utils.formatTimeWithoutSeconds(item?.reservationEndTime) }}</div>
              <div v-if="item.auditOpinion && item.state === 'refuse'" class="text-red-500 w-100% truncate">失败原因：{{
                item.auditOpinion }}</div>
            </div>
            <div v-if="item.state !== 'review'"
              class="w-150px h-54px text-center leading-54px text-[#fff] detail-btn  rounded-27px">
              查看详情
            </div>
            <div v-else class="flex items-center pt-[20px]  box-border ">
              <div class="flex items-center justify-end w-[100%]">
                <van-button block @click.stop="onRefuse(item, 'refuse')"
                  class="w-[128px] h-[48px] rounded-[39px] justify-center flex items-center text-[30px] text-[#5AA4FF] border-[1px] border-[#5AA4FF] border-solid mr-[30px]">
                  拒绝
                </van-button>
                <van-button block
                  class="w-[130px] h-[50px] rounded-[39px] justify-center flex items-center text-[30px] text-[#fff]"
                  style="
                    background: linear-gradient(0deg, #a1cbff 0%, #5aa4ff 100%);
                    " @click.stop="onPass(item, 'pass')">
                  通过
                </van-button>
              </div>
            </div>
          </div>
          <img loading="lazy" class="absolute top-1/4 right-30px w-151px h-151px" :src="StateImage[item.state]" />
        </div>
      </refreshList>
    </div>
    <Popup :show="Data.showPop" :titleName="Data.titleName" :placeHolder="'请输入' + Data.titleName"
      @submit-content="submitContent" @close-popup="closePopup" />
  </div>
</template>

<script lang="ts" setup>
import { useDictionary } from '@/store/modules/dictionary';
import refreshList from '@/components/refreshList/index.vue';
import { administratorsFindRecordList, administratorsAuditRecord } from '@/api/position';
import { useUserStore } from '@/store/modules/user';
import pass from '@/assets/position/reserve/pass.png';
import review from '@/assets/position/reserve/wait.png';
import refuse from '@/assets/position/reserve/refuse.png';
import cancel from '@/assets/position/reserve/cancel.png';
import expire from '@/assets/position/reserve/expire.png';
import used from '@/assets/position/reserve/used.png';
import useExpire from '@/assets/position/reserve/useExpire.png';
import Popup from "@/components/Popup/popup.vue";
import { showToast } from 'vant';
import utils from '@/utils/utils';
const dictionary = useDictionary();

const userStore = useUserStore();

const router = useRouter();

const StateImage: Recordable = {
  review,
  pass,
  refuse,
  cancel,
  expire,
  used,
  useExpire,
};

const loadMoreRef = ref();

const data = ref<any>({
  tab: {
    active: 0,
    nav: [],
    pageNum: 1,
    list: [],
  },

});
const Data = ref<any>({
  showPop: false,
  pageNum: 1,
  objInfo: {},
  titleName: ''
})
const onClickTab = (item: Recordable) => {
  data.value.tab.active = item.name;
};

function getList() {
  administratorsFindRecordList({
    pageSize: 10,
    pageNum: unref(data).pageNum,
    state: unref(data).tab?.active,
    phone: userStore.getUserInfo?.phone,
  }).then(res => {
    if (res.code == 200) {
      if (data.value.pageNum === 1) {
        data.value.list = [];
      }
      data.value.list = data.value.list.concat(res.data);
      //重置刷新状态及 判断是否加载完成
      if (loadMoreRef.value) {
        loadMoreRef.value?.onLoadSuc(data.value.list.length, res.total);
      }
    }
  });
}
// 刷新
const onRefreshList = () => {
  data.value.pageNum = 1;
  getList();
};
// 加载更多
const onLoadMore = () => {
  data.value.pageNum++;
  getList();
};
//黑名单
function toBlackList() {
  router.push({ path: '/my/positionBlack' });
}
//详情
function handleDetail({ recordId }: Recordable) {
  router.push({ path: '/my/positionDetail', query: { recordId } });
}
/**
* 点击拒绝  处理函数
*
* @param item 请求项，可以是任意类型的值
*/
function onRefuse(item: any, type) {
  Data.value.showPop = true;
  item.type = type;
  Data.value.titleName = '审核不通过原因'
  Data.value.objInfo = item;

}
//通过
function onPass(item, type) {
  item.type = type;
  Data.value.objInfo = item;
  Data.value.titleName = '审核通过原因'
  Data.value.showPop = true;
}
//确认提交  拒绝
function submitContent(val: string) {
  // if (!val && Data.value.objInfo.type == 'refuse') {
  //   showToast({
  //     message: "请输入拒绝原因",
  //     icon: "none",
  //   });
  //   return;
  // }
  administratorsAuditRecord({
    recordId: Data.value.objInfo.recordId,
    state: Data.value.objInfo.type,
    auditOpinion: val,
    phone: userStore.getUserInfo?.phone,
  }).then(res => {
    if (res.code == 200) {
      showToast("审核成功");
      onRefreshList();
      Data.value.showPop = false;
    } else {
      showToast(res.message)
    }
  })

}
//关闭拒绝弹窗
function closePopup() {
  Data.value.showPop = false;
}
onMounted(() => {
  setTimeout(() => {
    data.value.tab.nav = [{ label: "全部", value: '' }, ...dictionary.getDictionaryOpt?.['venueRecordState']] as Recordable[];
    data.value.tab.active = data.value.tab.nav[0].value;
  }, 200)
});

watch(
  () => unref(data).tab.active,
  () => {
    onRefreshList();
  },
  { deep: true }
);
const route = useRoute();
watch(() => route.query, (val) => {
  if (val.active) data.value.tab.active = val.active;
})
</script>

<style lang="less" module>
.reserve-record {
  :global {
    min-height: 100vh;

    .van-divider {
      &::before {
        border-color: #c5c5c5;
      }
    }

    .van-tab {
      font-size: 30px;
      z-index: 2;
    }

    .detail-btn {
      background: linear-gradient(to right, #5BA5FF, #5CB6FF);
    }
  }

}
</style>