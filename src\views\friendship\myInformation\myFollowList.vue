<script lang="ts" setup>
import male from '@/assets/friendShip/male_icon_w.png'
import female from '@/assets/friendShip/female_icon_w.png'
import { followListApi, followUserApi } from '@/api/friendship/index';
import Empty from '@/components/Empty/index.vue';
import refreshList from '@/components/refreshList/index.vue';
import followed from '@/assets/friendShip/file_follow.png'
import followeach from '@/assets/friendShip/file_followeach.png'
import utils from '@/utils/utils';
import router from '@/router';
import { showToast } from 'vant'
import {
    useRoute
} from "vue-router";
const route = useRoute();

const active = ref(0)

const ListObj = ref({
    pageIndex: 1,
    List: [],
    isLoad: false,
    loading: false,
    finished: false,
    finishedText: '',
    RecordCount: 0
})
const getList = () => {
    switch (active.value) {
        case 0:
            getFollowList('like')
            break
        case 1:
            getFollowList('fans')

            break

    }

}
const showPop = ref(false)
const popupType = ref('first')
const btnClick = ref(false)
// 关注方法
const followFn = (item) => {
    if (btnClick.value) return
    btnClick.value = true
    followUserApi({
        singleUserId: item.singleUserId
    }).then(res => {
        active.value == 0 ? showToast('取关成功') : showToast('关注成功')
        showPop.value = false
        // item.likeFlag = !item.likeFlag
        // getDetail()
        onRefresh()
        btnClick.value = false
    })
}
const loadMoreRef = ref(null)
//详情
function toPage(path, query) {
    router.push({
        path,
        query
    })
}

// 我的关注和粉丝

const getFollowList = (type) => {
    followListApi({
        pageSize: 10,
        pageNum: ListObj.value.pageIndex++,
        queryType: type,
    }).then(res => {
        ListObj.value.loading = false
        ListObj.value.isLoad = false
        ListObj.value.pageIndex == 1 ? ListObj.value.List = res.data.list
            : ListObj.value.List = [...ListObj.value.List, ...res.data.list]
        if (loadMoreRef.value) {
            loadMoreRef.value.onLoadSuc(ListObj.value.length, res.data.total);
        }
    }).catch(err => {

    })
}
// 加载更多
const onLoadMore = () => {
    ListObj.value.pageIndex++;
    getList();
};

// 刷新
const onRefresh = () => {
    ListObj.value.List = []
    ListObj.value.pageIndex = 1
    getList()
}
const detailChoose = ref({})
// 弹窗
const showPopFn = (item) => {
    detailChoose.value = item
    if (item.likeFlag) {
        showPop.value = true
    } else {
        followFn()
    }

}

onMounted(() => {
    if (route.query.nav == 0) {
        active.value = 0
    } else {
        active.value = 1

    }
    getList();
})
</script>
<template>
    <div class="follow-list">
        <van-tabs v-model:active="active" @click="onRefresh()" class="nav-box">
            <van-tab title="我的关注"></van-tab>
            <van-tab title="我的粉丝"></van-tab>
        </van-tabs>
        <div class="list-box">
            <refreshList key="relist" @onRefreshList="onRefresh" @onLoadMore="onLoadMore" ref="loadMoreRef">
                <div class="follow-item" v-for="(item, index) in ListObj.List" :key="index"
                    @click="toPage('/friendship/detail', { singleUserId: item.singleUserId })">
                    <div class="live-info flex justify-between items-start">
                        <div class="info-left flex">
                            <div class="logo w-94px h-94px mr-21px">
                                <img loading="lazy" class="w-full h-full rounded-50%"
                                    :src="utils.judgeStaticUrl(item.avatar, true)" alt="">
                            </div>
                            <div class="info-text">
                                <div class="name text-28px text-#333 font-500 mb-15px">{{ item.nickname }}</div>
                                <div class="sex-time flex"> <span class="sex mr-20px" :class="item.gender">
                                        <img loading="lazy" :src="item.gender == 'male' ? male : female" alt="">{{
                                            item.age
                                        }}
                                    </span> <span class="text-26px text-#999 ">{{ item.height }}cm·{{
                                        utils.getDictionaryLabel('modelEducation', item.education) }}</span></div>
                            </div>
                        </div>
                        <span class="follow-state" @click.stop="showPopFn(item)"
                            :class="item.likeFlag ? 'followed' : 'follow'"><img loading="lazy" class="w-25px mr-13px"
                                v-show="item.likeFlag" :src="item.otherLikeFlag ? followeach : followed" alt="">{{
                                    !item.likeFlag ? '关注' : item.otherLikeFlag ? '已互关' : '已关注' }}</span>

                    </div>
                </div>
            </refreshList>

        </div>
        <van-popup :show="showPop" position="bottom" :style="{ width: '100%' }">
            <div class="w-full h-full flex flex-col items-center bg-#f5f5f5 text-32px font-400 font-400 ">
                <div class="py56px text-center   text-#f43f31 bg-#fff w-full" v-if="popupType == 'first'"
                    @click="popupType = 'second'">
                    取消关注
                </div>
                <div v-else class="flex flex-col w-full">
                    <div class="pt55px pb60px w-full bg-#fff text-center text-28px text-#444">
                        确定不再关注
                    </div>
                    <div class="text-center text-32px text-#333  bg-#fff w-full pt27px pb37px border-t-solid border-t-1px border-t-#eeee "
                        @click="followFn(detailChoose)">
                        确定
                    </div>
                </div>
                <div class="text-center text-32px text-#333  bg-#fff w-full pt50px pb80px mt12px"
                    @click="showPop = false">
                    取消
                </div>
            </div>
        </van-popup>
    </div>
</template>
<style lang="scss" scoped>
// padding-bottom: 20px;
:deep(.nav-box) {
    .van-tabs__wrap {
        height: 98px;

        .van-tab {
            font-weight: 400;
            font-size: 28px;
            color: #8D9099;
        }

        .van-tab--active {
            font-weight: 500;
            font-size: 32px;
            color: #404455;
        }

        .van-tabs__line {
            width: 40px;
            height: 6px;
            background: linear-gradient(86deg, #C7E0FF 0%, #5AA4FF 100%);
            border-radius: 3px;
            bottom: 40px;
        }
    }
}

.van-popup {
    border-radius: 36px 36px 0px 0px;
}

.list-box {
    border-top: 12px solid #F5F5F5;
    box-sizing: border-box;
    padding: 20px 31px;

    .follow-item {
        padding: 43px 0 27px;
        border-bottom: 1px solid #EBEBEB;

        .live-info {
            width: 100%;

            .follow-state {
                width: 162px;
                height: 48px;
                border-radius: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 400;
                font-size: 24px;

                &.follow {
                    background: #F4698D;
                    color: #FFF;
                }

                &.followed {
                    border: 2px solid #999999;
                    color: #999999;
                }
            }


            .info-left {

                .info-text {
                    display: flex;
                    flex-flow: column;

                    .sex-time {
                        .sex {
                            font-weight: 400;
                            font-size: 24px;
                            color: #FFFFFF;
                            width: 85px;
                            height: 36px;

                            border-radius: 18px;
                            display: flex;
                            align-items: center;
                            justify-content: center;

                            >img {
                                width: 19px;
                                margin-right: 7px;
                            }

                            &.female {
                                background: linear-gradient(90deg, #FAA8AB, #FF7097);
                            }

                            &.male {
                                background: linear-gradient(90deg, #9FC9FF, #4196FF);
                            }
                        }
                    }
                }
            }

        }
    }
}
</style>