<template>
    <!-- 新闻类通用cell组件 -->
    <div class="cell  py-25px relative box-border" :class="{ 'border_bottom': props.showborderBottom }"
        @click="toDetail()">
        <div class="flex flex-wrap w-full">
            <div class="flex w-full items-center"
                v-if="props.content?.displayType == 'uncoveredDrawing' || props.content?.displayType == 'oneCoverPicture'">
                <div class="flex-1">
                    <div
                        class="cell_title text-[#323333] text-32px text_overFlow_2 leading-relaxed pt-10px pr-12px box-border">
                        {{ props.content?.newsTitle }}</div>
                    <div class="cell_bottom flex items-center text-[#999] text-[26px] mt-18px">
                        <div class="top_tag text-[#CC3333] text-24px w-77px h-37px  justify-center flex items-center
                            border border-solid border-[#CC3333] rounded-[6px]  mr-23px"
                            v-if="showTag && props.content?.whetherIcon">
                            置顶
                        </div>
                        <!-- <div class="publisher" v-if="props.content?.newsSource&&!showTag && !props.content?.whetherIcon" >
                            {{ props.content?.newsSource }}
                        </div> -->
                        <span class="cell_time">{{ props.content?.releaseTimeChinese }}</span>
                        <div class="cell_read ml-36px">
                            <van-icon name="eye-o" />
                            <span class="ml-7px">{{ props.content?.newsClicks || 0 }}</span>
                        </div>
                        <div class="m-20px flex-1 text-right">
                            <slot name="rightView"></slot>
                        </div>
                    </div>
                </div>
                <img loading="lazy" :src="utils.judgeStaticUrl(props.content?.oneCoverDrawing)" alt=""
                    v-if="props.content?.oneCoverDrawing != null" class="w-200px h-133px rounded-10px object-cover">
            </div>
            <div v-if="props.content?.displayType == 'multipleCoverImages'" class="w-full">
                <div class="cell_title text-[#323333] text-32px truncate leading-relaxed pt-10px pr-px box-border">
                    {{ props.content?.newsTitle }}</div>
                <div class="flex mt-10px w-100% image-box">
                    <div class="w-32% mr-15px" v-for="(item, index) in props.content?.coverDrawingList" :key="index">
                        <img loading="lazy" :src="utils.judgeStaticUrl(item)" alt=""
                            class="w-full h-full rounded-10px  object-cover">
                    </div>
                </div>
                <div class="cell_bottom flex items-center text-[#999] text-[26px] mt-18px">
                    <div class="top_tag text-[#CC3333] text-24px w-77px h-37px  justify-center flex items-center
                        border border-solid border-[#CC3333] rounded-[6px]  mr-23px"
                        v-if="showTag && props.content?.whetherIcon">
                        置顶
                    </div>
                    <!-- <div class="publisher mr-20px" v-if="props.content?.newsSource">
                        {{ props.content?.newsSource }}
                    </div> -->
                    <span class="cell_time">{{ props.content?.releaseTimeChinese }}</span>
                    <div class="cell_read ml-36px">
                        <van-icon name="eye-o" />
                        <span class="ml-7px">{{ props.content?.newsClicks || 0 }}</span>
                    </div>
                    <div class="m-20px flex-1 text-right">
                        <slot name="rightView"></slot>
                    </div>
                </div>

            </div>
            <div v-if="props.content?.displayType == 'videoCoverImage'" class="w-full ">
                <div class="cell_title text-[#323333] text-32px truncate leading-relaxed pt-10px pr-px box-border">
                    {{ props.content?.newsTitle }}</div>
                <div class="relative w-100% rounded-10px mt-10px h-400px">
                    <div class="absolute top-1/2 left-1/2 -translate-1/2"><van-icon name="play-circle-o" color="#fff"
                            size="80" /></div>
                    <img loading="lazy" :src="utils.judgeStaticUrl(props.content?.oneCoverDrawing)" alt=""
                        class="w-full h-full object-fill rounded-15px object-cover">
                </div>
                <div class="cell_bottom flex items-center text-[#999] text-[26px] mt-18px">
                    <div class="top_tag text-[#CC3333] text-24px w-77px h-37px  justify-center flex items-center
                        border border-solid border-[#CC3333] rounded-[6px]  mr-23px"
                        v-if="showTag && props.content?.whetherIcon">
                        置顶
                    </div>
                    <!-- <div class="publisher mr-20px" v-if="props.content?.newsSource">
                        {{ props.content?.newsSource }}
                    </div> -->
                    <span class="cell_time">{{ props.content?.releaseTimeChinese }}</span>
                    <div class="cell_read ml-36px">
                        <van-icon name="eye-o" />
                        <span class="ml-7px">{{ props.content?.newsClicks || 0 }}</span>
                    </div>
                    <div class="m-20px flex-1 text-right">
                        <slot name="rightView"></slot>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import utils from '@/utils/utils';
import { readOperate } from '@/api/public';
import { showConfirmDialog } from 'vant';
defineOptions({
    name: 'newsCell',
})
const props = defineProps({
    content: {
        type: Object,
        default: {},
    },
    showborderBottom: {
        type: Boolean,
        default: true,
    },
    categoryCode: {
        type: String,
        default: ''
    },
    showTag: {
        type: Boolean,
        default: true,
    },
})
//详情
function toDetail() {
    if (props.content?.whetherExternalLink) {
        readOperate({ sourceId: props.content?.newsId }).then(res => { })
        // 2025-1-8 外部链接在新闻详情页嵌套展示 props.content?.whetherExternalLink 传false
        utils.openNewsLink({
            title: props.content?.newsTitle,
            url: props.content?.externalLinkAddress,
            shareTitle: props.content?.newsTitle,
            shareUrl: props.content?.externalLinkAddress,
            dataId: props.content?.newsId,
        }, false, props?.categoryCode)
        return false


        if (props.content?.whetherPrompt) {
            showConfirmDialog({
                title: "",
                message: "是否跳转外部链接",
            }).then(() => {
                utils.openNewsLink({
                    title: props.content?.newsTitle,
                    url: props.content?.externalLinkAddress,//window.location.origin+'/newsDetail?newsId='+props.content?.newsId+'&categoryCode='+props?.categoryCode,
                    shareTitle: props.content?.newsTitle,
                    shareUrl: props.content?.externalLinkAddress,//window.location.origin+'/newsDetail?newsId='+props.content?.newsId+'&categoryCode='+props?.categoryCode,
                    dataId: props.content?.newsId,
                }, props.content?.whetherExternalLink, props?.categoryCode);
                return
            }).catch((res) => { });
        }
        else {
            utils.openNewsLink({
                title: props.content?.newsTitle,
                url: props.content?.externalLinkAddress,//window.location.origin+'/newsDetail?newsId='+props.content?.newsId+'&categoryCode='+props?.categoryCode,
                shareTitle: props.content?.newsTitle,
                shareUrl: props.content?.externalLinkAddress,//window.location.origin+'/newsDetail?newsId='+props.content?.newsId+'&categoryCode='+props?.categoryCode,
                dataId: props.content?.newsId,
            }, props.content?.whetherExternalLink, props?.categoryCode);
        }
    } else {
        utils.openNewsLink({
            title: props.content?.newsTitle,
            url: props.content?.externalLinkAddress,//window.location.origin+'/newsDetail?newsId='+props.content?.newsId+'&categoryCode='+props?.categoryCode,
            shareTitle: props.content?.newsTitle,
            shareUrl: props.content?.externalLinkAddress,//window.location.origin+'/newsDetail?newsId='+props.content?.newsId+'&categoryCode='+props?.categoryCode,
            dataId: props.content?.newsId,
        }, props.content?.whetherExternalLink, props?.categoryCode)
    }
}
onMounted(() => {

})
</script>

<style scoped lang="scss">
.cell {
    .text_overFlow_2 {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }
}

.image-box {
    >div:last-child {
        margin-right: 0 !important;
    }
}

.border_bottom {
    border-bottom: 1px solid #ccdff7;
}
</style>