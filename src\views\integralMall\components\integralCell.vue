<template>
    <div class="pt-35px px-25px -">
        <div class="cell flex justify-between items-center pb-26px" :style="!borderBottomShow ? 'border:none' : ''">
            <div class="left_item">
                <div class="title text-#333 text-30px">{{ props.content[titleKey] }}</div>
                <div class="time text-[#A1A1A1] text-24px mt-16px">{{ props.content[timeKey] }}</div>
            </div>
            <div class="right_item text-[#FF4344] text-32px" :class="{ '!text-[#4EC081]': sigalType === '-' }">
                {{ sigalType }}{{ props.content[numberKey] }}
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { useDictionary } from '@/store/modules/dictionary';
const dictionary = useDictionary()
const sigalType = computed(() => {
    let operatype = ''
    dictionary.getDictionaryOpt?.['integralOperateType'].find((item: any) => {
        if (item.value === props.content[props.singalKey]) operatype = item.value
    })
    return operatype === 'decrement' ? '-' : '+'
})
const props = defineProps({
    content: {
        type: Object,
        default: () => {
            return {
                title: '标题',
                createTime: '2021-09-03 15:46:27',
                score: '10',
                operateType: 'decrement'
            }
        }
    },
    titleKey: {
        // 标题
        type: String,
        default: 'title'
    },
    timeKey: {
        // 时间
        type: String,
        default: 'createTime'
    },
    numberKey: {
        // 积分
        type: String,
        default: 'score'
    },
    singalKey: {
        type: String,
        default: 'operateType'
    },
    borderBottomShow: {
        type: Boolean,
        default: true
    },
})
</script>

<style lang="scss" scoped>
.cell {
    border-bottom: 1px solid #ececec;
}
</style>
