<template>
    <div class="goods-list py-30px bg-#F9F9F9">
        <div class="header my-20px flex items-center px-30px">
            <div class="search flex rounded-28px items-center flex-1">
                <div class="ml-24px">
                    <img loading="lazy" src="@/assets/integralMall/search_icon.png" class="w-30px" />
                </div>
                <div class="ml-10px flex-1 h-56px search-input">
                    <van-field v-model="combinationSearch" placeholder="请搜索您想要的商户/商品" clearable @clear="searchChange">
                    </van-field>
                </div>
                <div class="text-26px text-[#FFF] w-101px h-56px bg-[#FF4344] box-border
                    rounded-28px flex items-center justify-center ml-24px leading-0" @click="searchChange">
                    搜索
                </div>
            </div>
            <div class="filter-icon flex items-center ml-15px" @click="filterShow = true">
                <img loading="lazy" src="@/assets/integralMall/filter_icon.png" class="w-30px" />
                <div class="text-26px text-[#333] ml-6px">筛选</div>
            </div>
        </div>
        <!-- tab选择 -->
        <div class="tab-list h-80px bg-[#F9F9F9] sticky top-0 z-99">
            <van-tabs v-model:active="tabActive" title-active-color="#333" title-inactive-color="#333"
                background="transparent" @change="searchChange">
                <van-tab v-for="item, index in typeTabList" :name="item.value" :title="item.label"
                    :key="index"></van-tab>
            </van-tabs>
        </div>
        <!-- end -->
        <div class="px-30px">
            <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
                <div class="flex flex-wrap justify-between">
                    <div v-for="(item, index) in list" :key="index" class="w-336px mt-20px box-border overflow-hidden">
                        <goodsListCell :content="item"></goodsListCell>
                    </div>
                </div>
            </refreshList>
        </div>
        <!-- 购物车icon -->
        <cartIcon></cartIcon>
        <!-- 筛选弹窗 -->
        <filterPopup v-model:show="filterShow" :defaultForm="filterForm" @confirm="confirmFliter">
            <template #default="{ form }">
                <div class="exchange-values mt-10px">
                    <div class="text-32px font-medium">价格</div>
                    <div class="flex items-center input-values mt-26px">
                        <van-field placeholder="最低价格" type="number" v-model="form.userMinPrice" clearable />
                        <div class="line w-64px h-1px bg-#CCC mx-14px"></div>
                        <van-field placeholder="最高价格" type="number" v-model="form.userMaxPrice" clearable />
                    </div>
                </div>
            </template>
        </filterPopup>
    </div>
</template>
<script lang="ts" setup>
import { showToast } from 'vant';
const filterPopup = defineAsyncComponent(() => import('../components/filterPopup.vue'))
import refreshList from '@/components/refreshList/index.vue';
import goodsListCell from '../components/goodsCell.vue';
import cartIcon from '../components/cartIcon.vue';
const combinationSearch = ref('') //搜索条件
const filterShow = ref(false)//筛选弹窗
const tabActive = ref('')
import { useDictionary } from '@/store/modules/dictionary';
const dictionary = useDictionary()
import { inclusiveGoodsList } from '@/api/mall/inclusive'


const list = ref<any>([]);
let pageNum = 1;
// tab标签列表
const typeTabList = computed(() => {
    return dictionary.getDictionaryOpt?.['inclusiveProductColumn']
})
const filterForm = reactive({
    userMaxPrice: '',
    userMinPrice: ''
})


const confirmFliter = (val: any) => {
    const { userMaxPrice, userMinPrice } = val
    if (!userMaxPrice && userMinPrice) return showToast('请输入最高价格')
    if (userMaxPrice && !userMinPrice) return showToast('请输入最低价格')

    if (Number(userMinPrice) > Number(userMaxPrice)) {
        showToast('最大分值不能小于最小分值')
        return
    }
    filterForm.userMaxPrice = userMaxPrice || ''
    filterForm.userMinPrice = userMinPrice || ''
    filterShow.value = false
    onRefreshList()
}

const searchChange = () => {
    onRefreshList()
}

const loadMoreRef = ref()
// 刷新
const onRefreshList = () => {
    pageNum = 1
    loadMoreData()
}
// 加载更多
const onLoadMore = () => {
    pageNum++
    loadMoreData()
}
const loadMoreData = async () => {
    const { code, data, total } = await inclusiveGoodsList({
        inclusiveProductColumn: tabActive.value,
        productName: combinationSearch.value,
        systemQueryType: 'h5',
        orderBy: 'create_time',
        sortType: 'desc',
        pageSize: 10,
        pageNum,
        ...filterForm
    })
    if (pageNum === 1) list.value = []
    if (code === 200) {
        if (pageNum === 1) list.value = data
        else list.value = [...list.value, ...data]
    }
    //重置刷新状态及 判断是否加载完成
    if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(list.value.length, total)
    }
}


onBeforeMount(() => {
    tabActive.value = typeTabList.value[0]?.value
})
onMounted(() => {
    onRefreshList()
})
</script>
<style scoped lang="scss">
.goods-list {
    .header {
        .search {
            border: 2px solid #FF4344;
            background-color: #fff;

            :deep(.van-cell) {
                padding: 0;
                height: 100%;

            }

            :deep(.van-field__body) {
                height: 100%;
                font-size: 24px;
            }

            --van-field-clear-icon-size: 25px;
        }
    }

    .tab-list {
        :deep(.van-tabs--line .van-tabs__wrap) {
            height: 80px;
        }

        :deep(.van-tab) {
            line-height: 1;
            height: 100%;
            font-size: 30px;
        }

        :deep(.van-tab--active) {
            font-size: 32px;
        }

        :deep(.van-tabs__nav--line) {
            height: 100%;
            padding-bottom: 15px;
        }

        :deep(.van-tabs__line) {
            background-color: #FF4344;
            width: 40px;
            height: 6px;
            bottom: 15px;
            border-radius: 3px
        }

        // :deep(.van-tab__text) {
        //     font-size: 28px;
        // }
    }

    .exchange-values {
        :deep(.van-cell__value) {
            font-size: 26px;
        }

        .input-values {
            :deep(.van-cell) {
                background-color: #F2F0F1;
                border-radius: 23px;
                padding: 11px 22px;
            }
        }
    }
}
</style>