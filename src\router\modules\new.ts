export default [
    {
        path: '/newsDetail',
        name: 'newsDetail',
        component: () => import('@/views/news/detail.vue'),
        meta: {
            title: '新闻详情',
            isShowTabBar: false,
            isBack: true,
            keepAlive:false
        },
    },
    {
        path: '/previewNewsDetail',
        name: 'previewNewsDetail',
        component: () => import('@/views/news/previewNews.vue'),
        meta: {
            title: '新闻详情',
            isShowTabBar: false,
            isBack: false,
            keepAlive:false
        },
    },
    {
        path: '/newsDetailB',
        name: 'newsDetailB',
        component: () => import('@/views/news/detail.vue'),
        meta: {
            title: '新闻详情',
            isShowTabBar: false,
            isBack: true,
            keepAlive:false
        },
    }
]