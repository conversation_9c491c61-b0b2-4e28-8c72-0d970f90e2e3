<template>
    <!-- 数据类型和时间筛选组件 -->
    <div class="date-select flex tems-center justify-between">
        <div class="flex flex-1">
            <div class="text-#4898FB px-26px py-5px default-line  flex items-center leading-none
                justify-center rounded-24px  mr-30px text-28px" @click="clickDateType(index)"
                v-for="item, index in dateTypeArr" :key="index" :class="{ 'active-btn': defaultType === index }">
                {{ item.label }}
            </div>
        </div>
        <div @click="handleDatePicker"
            class="date default-line text-#76B7FB text-28px rounded-24px px-18px py-5px  flex items-center justify-center">
            <span>{{ labelTime }}</span>
            <van-icon name="arrow-down" color="#76B7FB"></van-icon>
        </div>

        <van-popup v-model:show="dateSelectShow" position="bottom">
            <!-- 日期选择器 -->
            <van-date-picker v-model="targetDate" :title="pickerTitle" :min-date="minDate" :max-date="maxDate"
                :formatter="formatter" :columns-type="columnsType" @confirm="confirmDate" @cancel="cancel" />
        </van-popup>
    </div>
</template>
<script lang="ts" setup>
const props = defineProps({
    dateTypeArr: {
        type: Array,
        default() {
            return () => []
        }
    } as any,
    labelKey: {
        type: String,
        default() {
            return 'label'
        }
    },
    valueKey: {
        type: String,
        default() {
            return 'value'
        }
    },
    defaultTime: {
        type: Array,
        default() {
            return []
        }
    } as any,
    defaultType: {
        type: Number,
        default() {
            return 0
        }
    },
    columnsType: {
        type: Array,
        default() {
            return ['year', 'month']
        }
    }
})
const emit = defineEmits(['update:defaultTime', 'changeType'])
const labelTime = computed(() => {
    if (props.columnsType.includes('day')) {
        return `${props.defaultTime[0]}年${props.defaultTime[1]}月${props.defaultTime[2]}日`
    } else if (props.columnsType.includes('month')) {
        return `${props.defaultTime[0]}年${props.defaultTime[1]}月`
    } else {
        return `${props.defaultTime[0]}年`
    }
})
const pickerTitle = computed(() => {
    if (props.columnsType.includes('day')) {
        return '请选择日期'
    } else if (props.columnsType.includes('month')) {
        return '请选择年月'
    } else {
        return '请选择年'
    }
})

// 日期展示
const dateSelectShow = ref(false)
const targetDate = ref<any>([])
const minDate = new Date(1949, 0, 1)
// 最大月份当月
const getYear = new Date().getFullYear()
const getMonth = new Date().getMonth()
const maxDate = new Date(getYear, getMonth, 1)

const formatter = (type, option) => {
    if (type === 'year') {
        option.text += '年';
    }
    if (type === 'month') {
        option.text += '月';
    }
    return option;
};
const handleDatePicker = () => {
    targetDate.value = props.defaultTime
    dateSelectShow.value = true
}
const confirmDate = ({ selectedValues }) => {
    emit('update:defaultTime', selectedValues)
    dateSelectShow.value = false
}
const cancel = () => {
    dateSelectShow.value = false
}
const clickDateType = (index: any) => {
    emit('changeType', index)
}
//end
</script>
<style scoped lang="scss">
.date-select {
    .default-line {
        border: 1px solid #76B7FB;
    }

    .active-btn {
        border: none;
        background-color: #4898FB;
        color: #fff;
    }
}
</style>