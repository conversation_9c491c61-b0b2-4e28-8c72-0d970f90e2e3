<template>
  <div class="flex items-top w-full border-b-solid border-b-1 border-slate-100 py-3 justify-between"
    @click="onUserClick(content)">
    <img loading="lazy" :src="avatar" class="avatar w-60px h-60px rounded-[50%] mt-1 object-cover" />

    <div class="w-[calc(100%-200px)] text-#999 text-24px">
      <div class="truncate text-28px text-#333 mb-2"> {{ content?.nickName }} </div>
      <span>{{ dayjs(content?.updateTime).format('MM-DD') }}</span>
      {{ content?.statefulFlowType == 'collect' ? '收藏' : '点赞' }}你的视频
    </div>
    <img loading="lazy" :src="judgeStaticUrl(content?.pictures)"
      class="avatar w-100px h-100px rounded-md object-cover" />
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import { useUserStore } from '@/store/modules/user';
import { useRouter } from 'vue-router';
import defaultAvatar from '@/assets/public/head_default.png';
import female from '@/assets/public/female.png';
import male from '@/assets/public/male.png';
import { judgeStaticUrl } from '@/utils/utils';
const router = useRouter();
const useStore = useUserStore();
const props = defineProps({
  content: {
    type: Object,
  },
});
const avatar = computed(() => {
  if (props.content?.userId && props.content?.userId === useStore.getUserInfo?.userId) {
    if (useStore.getUserInfo?.avatar) return judgeStaticUrl(useStore.getUserInfo?.avatar)
    if (useStore.getUserInfo?.gender === '男') return male
    if (useStore.getUserInfo?.gender === '女') return female
    return defaultAvatar
  }
  else return defaultAvatar;
})

function onUserClick(item: any) {
  router.push({
    path: '/video-personalCenter',
    query: { userId: item.userId, nickname: item.nickName },
  });
}
</script>

<style lang="scss" scoped></style>
