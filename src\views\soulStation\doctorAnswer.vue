<template>
  <div :class="$style['psychological-counseling']">
    <div class="banner w-full min-h-[378px] px-[28px] py-[32px]">
      <Doctor :showIcon="false" />
    </div>
    <div class="h-[calc(100%-328px)] relative px-[30px] rounded-[30px] bg-[#fff] relative top-[-50px]">
      <refreshList @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
        <van-cell v-for="item in Data.listData" class="!flex items-center px-0 w-full mb-10px" @click="handleAsk(item)">
          <div>
            <img loading="lazy" :src="item.avatar" class="w-[66px] h-[66px] rounded-[20px]" />
          </div>
          <div class="flex flex-col justify-between pl-[37px] w-full">
            <div class="flex items-center justify-between w-full text-26px">
              <span class="text-[#333333]"> {{ item.userName || '' }}</span>
              <span class="text-[#999999] mt-26px"> {{ fixDateTime(item.psychologicaDialogue?.createTime) }}</span>
            </div>

            <div class="w-full flex justify-between items-center">
              <div class="text-[24px] text-[#999999] truncate w-full max-w-[390px]">
                {{ item?.psychologicaDialogue?.content || '' }}
              </div>
              <div v-if="item.notReadNum > 0"
                class="rounded-full flex justify-center items-center w-[40px] h-[40px] bg-[#EB4135] text-[22px] text-[#fff]">
                {{ item.notReadNum > 99 ? '99' : item.notReadNum }}</div>
            </div>
          </div>
        </van-cell>
      </refreshList>
    </div>
  </div>
</template>

<script lang="ts" setup>
import refreshList from '@/components/refreshList/index.vue';
import male from '@/assets/public/male.png';
import female from '@/assets/public/female.png';
import defaultImg from '@/assets/public/head_default.png';
import dayjs from 'dayjs';
import Doctor from './components/Doctor.vue';
import { getPsychologicalUserList } from '@/api/soulStation';
import {judgeStaticUrl} from '@/utils/utils';
const router = useRouter();

const Data = ref({
  pageNum: 1,
  listData: [],
});

// 刷新
const onRefreshList = () => {
  Data.value.pageNum = 1;
  getList();
};
// 加载更多
const onLoadMore = () => {
  Data.value.pageNum++;
  getList();
};
//搜索列表
const loadMoreRef = ref(null);
//获取专家列表
function getList() {
  getPsychologicalUserList({
    pageNum: Data.value.pageNum,
    pageSize: 10,
  }).then(res => {
    if (res.code == 200) {
      res.data.forEach((item: Recordable) => {
        item.avatar = judgeStaticUrl(item.avatar) || defaultImg;
      });
      if (Data.value.pageNum === 1) Data.value.listData = [];
      Data.value.listData = Data.value.listData.concat(res.data);
      //重置刷新状态及 判断是否加载完成
      if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(Data.value.listData.length, res.total);
      }
    }
  });
}
onMounted(() => {
  getList();
});

function handleAsk(item) {
  router.push({
    path: '/expertDoctor',
    query: {
      psychologicalExpertId: item.psychologicalExpertId,
      psychologicalUserId: item.psychologicalUserId,
    },
  });
}

function fixDateTime(time: string | number | dayjs.Dayjs | Date | null | undefined) {
  const day = dayjs(dayjs().format('YYYY-MM-DD')).diff(dayjs(time).format('YYYY-MM-DD'), 'day');
  switch (day) {
    case 0:
      return '今天' + dayjs(time).format('HH:mm');
    case 1:
      return '昨天' + dayjs(time).format('HH:mm');
    default:
      return dayjs(time).format('YYYY-MM-DD HH:mm');
  }
}
</script>

<style lang="less" module>
.psychological-counseling {
  :global {
    .banner {
      background-color: #f1f8fe;
    }

    .expert.tab--active {
      background-image: url('@/assets/workers-voice/change-left.png');
      background-repeat: no-repeat;
      background-size: 100% 97px;
      -webkit-animation: slide-in-left 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
      animation: slide-in-left 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    }

    .consult.tab--active {
      background-image: url('@/assets/workers-voice/change-right.png');
      background-repeat: no-repeat;
      background-size: 100% 97px;
      -webkit-animation: slide-in-right 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
      animation: slide-in-right 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    }

    .van-cell {
      .van-cell__value {
        display: flex;
        align-items: center;
        text-align: left;
      }

      &::after {
        left: 0;
        right: 0;
        border-color: #eeeeee !important;
      }
    }
  }
}
</style>
