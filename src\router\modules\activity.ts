
export default [
    {
        path: "/activity",
        name: "activity",
        component: () => import("@/views/activity/activity.vue"),
        meta: {
            title: "活动",
            isShowTabBar: true,
            isBack: false,
            keepAlive:true,
            updatePath:['/','/activity','/home','/inclusive','/countyDetail','/my','/county',]
        }
    },
    {
        path: '/activityHome/lotteryRecord',
        name: 'lotteryRecord',
        component: () => import('@/views/activity/common/lotteryRecord.vue'),
        meta: {
            title: '中奖记录',
            isShowTabBar: false,
            isBack: true,
            keepAlive:true,
            updatePath:['/activityHome/activityDetail','/activityHome/questionnaireActivity',
                '/activityHome/vieAnswerActivity','/my',
                '/activityHome/lottery','/activityHome/lotteryActivity',
                '/activityHome/questionnaireActivity','/activityHome/vieAnswerActivity',
                '/integralMall','/integralMall/treasureHunt','/integralTree',
            ]
        },
    },
    {
        path: '/activityHome/myWorks',
        name: 'activeMyWorks',
        component: () => import('@/views/activity/voteActivity/myWorks.vue'),
        meta: {
            title: '我的作品',
            isShowTabBar: false,
            isBack: true,
            keepAlive:true,
            updatePath:['/activityHome/activityDetail']
        },
    },
    {
        path: '/activityHome/vote',
        name: 'activityVote',
        component: () => import('@/views/activity/voteActivity/vote.vue'),
        meta: {
            title: '投票',
            isShowTabBar: false,
            isBack: true,
            keepAlive:true,
            updatePath:['/activityHome/activityDetail']
        },
    },
    {
        path: "/activityHome",
        name: "activityHome",
        component: () => import("@/views/activity/home.vue"),
        meta: {
            title: "活动",
            isShowTabBar: false,
            isBack: false,
        },
        children: [
            {
                path: 'myWorks/detail',
                name: 'myWorksDetail',
                component: () => import('@/views/activity/voteActivity/worksDetail.vue'),
                meta: {
                    title: '详情',
                    isShowTabBar: false,
                    isBack: true,
                    keepAlive:false,
                   
                },
            },
            
            {
                path: 'contribute',
                name: 'contribute',
                component: () => import('@/views/activity/voteActivity/contribute.vue'),
                meta: {
                    title: '投稿',
                    isShowTabBar: false,
                    isBack: true,
                    keepAlive:false,
                   
                },
            },
            {
                path: 'activityDetail',
                name: 'activityDetail',
                component: () => import('@/views/activity/common/activityDetail.vue'),
                meta: {
                    title: '活动详情',
                    isShowTabBar: false,
                    isBack: true
                },
            },
            {
                path: "lotteryActivity",
                name: "activityLottery",
                component: () => import("@/views/activity/lotteryActivity/index.vue"),
                meta: {
                    title: "抽奖活动-首页",
                    isShowTabBar: false,
                    isBack: true
                }
            },
            {
                path: 'lottery',
                name: 'lottery',
                component: () => import('@/views/activity/common/lottery.vue'),
                meta: {
                    title: '活动抽奖',
                    isShowTabBar: false,
                    isBack: true
                },
            },
            // {
            //     path: 'lotteryRecord',
            //     name: 'lotteryRecord',
            //     component: () => import('@/views/activity/common/lotteryRecord.vue'),
            //     meta: {
            //         title: '中奖记录',
            //         isShowTabBar: false,
            //         isBack: true,
            //         keepAlive:true,
            //         updatePath:['/activityHome/activityDetail','/activityHome/questionnaireActivity',
            //             '/activityHome/vieAnswerActivity','/my',
            //             '/activityHome/lottery','/activityHome/lotteryActivity',
            //             '/activityHome/questionnaireActivity','/activityHome/vieAnswerActivity',
            //             '/integralMall','/integralMall/treasureHunt','/integralTree',
            //         ]
            //     },
            // },
            {
                path: 'editAddress',
                name: 'editAddress',
                component: () => import('@/views/activity/common/editAddress.vue'),
                meta: {
                    title: '编辑地址',
                    isShowTabBar: false,
                    isBack: true
                },
            },
            {
                path: 'activityComment',
                name: 'activityComment',
                component: () => import('@/views/activity/common/activityComment.vue'),
                meta: {
                    title: '活动评论',
                    isShowTabBar: false,
                    isBack: true
                },
            },
            {
                path: 'vieAnswerActivity',
                name: 'vieAnswerActivity',
                component: () => import('@/views/activity/vieAnswerActivity/index.vue'),
                meta: {
                    title: '竞答活动',
                    isShowTabBar: false,
                    isBack: true
                },
            },
            {
                path: 'vieAnswerActivity/question',
                name: 'vieAnswerActivity-question',
                component: () => import('@/views/activity/vieAnswerActivity/question.vue'),
                meta: {
                    title: '竞答活动',
                    isShowTabBar: false,
                    isBack: true
                },
            },
            {
                path: 'vieAnswerActivity/rank',
                name: 'vieAnswerRank',
                component: () => import('@/views/activity/vieAnswerActivity/rank.vue'),
                meta: {
                    title: '排行榜',
                    isShowTabBar: false,
                    isBack: true
                },
            },
            {
                path: 'signUpActivity',
                name: 'signUpActivity',
                component: () => import('@/views/activity/signUp/index.vue'),
                meta: {
                    title: '报名活动',
                    isShowTabBar: false,
                    isBack: true
                },
            },
            {
                path: 'signUpActivity/form',
                name: 'signUpActivity-form',
                component: () => import('@/views/activity/signUp/signUpForm.vue'),
                meta: {
                    title: '报名活动',
                    isShowTabBar: false,
                    isBack: true
                },
            },
            {
                path: 'signUpActivity/record',
                name: 'signUpActivity-record',
                component: () => import('@/views/activity/signUp/record.vue'),
                meta: {
                    title: '我的报名',
                    isShowTabBar: false,
                    isBack: true
                },
            },
            {
                path: 'questionnaireActivity',
                name: 'questionnaireActivity',
                component: () => import('@/views/activity/questionnaireActivity/index.vue'),
                meta: {
                    title: '问卷活动',
                    isShowTabBar: false,
                    isBack: true
                },
            },
            {
                path: 'questionnaireActivity/question',
                name: 'questionnaireActivity-question',
                component: () => import('@/views/activity/questionnaireActivity/question.vue'),
                meta: {
                    title: '问卷活动',
                    isShowTabBar: false,
                    isBack: true
                },
            },
            {
                path: "couponActivity",
                name: "couponActivity",
                component: () => import("@/views/activity/couponActivity/index.vue"),
                meta: {
                    title: "票券活动-首页",
                    isShowTabBar: false,
                    isBack: true
                }
            },
            {
                path: 'vitality/volunteerService/activityDetail',
                name: 'volunteerService-activityDetails',
                component: () => import('@/views/vitality/volunteerService/activityDetails.vue'),
                meta: {
                    title: '活动详情',//志愿服务
                    isShowTabBar: false,
                    isBack: true,
                },
            },
            {
                path: "interestDetail",
                name: "interestDetail",
                component: () => import("@/views/interestGroup/interestList/detail.vue"),
                meta: {
                    title: "详情",
                    isShowTabBar: false,
                    isBack:true
                }
            },
            {
                path: 'friendship/activity/detail',
                name: 'FriendshipActivityDetail',
                component: () => import('@/views/friendship/activity/detail.vue'),
                meta: {
                    title: '联谊活动详情',
                    isShowTabBar: false,
                    isBack:true
                }
            },
            {
                path: 'friendship/activity/apply',
                name: 'FriendshipActivityApply',
                component: () => import('@/views/friendship/activity/apply.vue'),
                meta: {
                    title: '联谊活动报名',
                    isShowTabBar: false,
                    isBack:true
                }
            },
        ]
    },
    {
        path: "/couponRecordDetail",
        name: "couponRecordDetail",
        component: () => import("@/views/activity/common/couponRecordDetail.vue"),
        meta: {
            title: "票券详情",
            isShowTabBar: false,
            isBack: true
        }
    },
]
