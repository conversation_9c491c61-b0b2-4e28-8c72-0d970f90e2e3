<template>
    <div class="activityApplyDetail">
        <img loading="lazy" :src="stateObj[record.state]?.icon" alt="" class="h-70px w-auto mx-auto my60px block">
        <div class="info_box box-border w-[calc(100%-56px)] mx-auto  pt86px pb-66px flex flex-col justify-between">
            <div class="info_content h-[calc(100%-103px)] px-56px overflow-auto">
                <div class="flex text-28px leading-45px mb-35px" v-for="(item, index) in record.answerRecord">
                    <div class="text-#666 mr-26px">
                        {{ item.title }}:
                    </div>
                    <div class="text-#333 leading-40px">
                        {{ item.content }}
                    </div>
                </div>
                <div class="flex text-28px leading-45px mb-35px">
                    <div class="text-#666 mr-26px">
                        提交时间:
                    </div>
                    <div class="text-#333 leading-40px">
                        {{ record.createTime }}
                    </div>
                </div>
                <div class="flex text-28px leading-45px mb-35px" v-if="record.state !== 'wait' && record.auditTime">
                    <div class="text-#666 mr-26px">
                        审核时间:
                    </div>
                    <div class="text-#333 leading-40px">
                        {{ record.auditTime }}
                    </div>
                </div>
                <div class="flex text-28px leading-45px mb-35px"
                    v-if="record.state === 'refuse' && record.auditOpinion">
                    <div class="text-#666 mr-26px">
                        失败原因:
                    </div>
                    <div class="text-#333 leading-40px text-[#E14026]">
                        {{ record.auditOpinion }}
                    </div>
                </div>
            </div>
            <div class="w-431px h-83px rounded-40px btn_pink flex items-center justify-center text-36px text-#fff mx-auto"
                @click="toSignUp" v-if="record.state === 'refuse'">重新提交</div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import fontOning from "@/assets/friendship/font_oning.png"
import fontPass from "@/assets/friendship/font_pass.png"
import fontError from "@/assets/friendship/font_err.png"
import { applicationRecord } from "@/api/activity";
import { useUserStore } from "@/store/modules/user";
import { showToast } from "vant";
const record = ref({})
const route = useRoute()
const router = useRouter()
const useStore = useUserStore();
const stateObj = ref({
    wait: {
        text: '审核中',
        icon: fontOning,
    },
    pass: { text: '审核通过', icon: fontPass },
    refuse: {
        text: '审核失败',
        icon: fontError,
    },
})

onMounted(() => {
    getSignUpRecord()
})


const getSignUpRecord = async () => {
    const { code, data, message } = await applicationRecord({
        activityId: route.query.activityId || sessionStorage.getItem('activityId'),
    });
    if (code) {
        record.value = data
    } else {
        showToast(message)
    }
}

const toSignUp = () => {
    router.push({
        path: '/activityHome/friendship/activity/apply',
        query: {
            autoId: record.value.autoId,
            activityId: record.value.activityId
        }
    })
}




</script>

<style lang="scss" scoped>
.activityApplyDetail {
    background-image: url("@/assets/friendship/bg_record.png");
    background-size: 100% 100%;

    .info_box {
        background-image: url("@/assets/friendship/border_record.png");
        background-size: 100% 100%;
        height: 75vh;
    }

    .btn_pink {
        background: linear-gradient(93deg, #FE779A 0%, #FA9CAB 100%);
    }
}
</style>
