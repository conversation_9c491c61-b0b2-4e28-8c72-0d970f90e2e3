<template>
    <!-- 工匠表单 -->
    <van-form ref="formRef" @submit="getForm" label-align="top">
        <van-field v-model="form.userName" name="userName" label="工匠姓名" placeholder="请输入工匠姓名"
            :rules="[{ required: true, message: '请输入工匠姓名' }]" required />

        <van-field v-model="form.phone" name="phone" label="联系电话" readonly placeholder="请输入联系电话" :rules="rules.telRules"
            required />

        <van-field v-model="form.identityCardNumber" name="identityCardNumber" label="身份证号码" placeholder="请输入身份证号码"
            :rules="rules.idCardRules" required />

        <!-- <inputSelect :value="genderName" :columns="sexClomnu" name="genderName" label="性别" placeholder="请选择性别"
            @onConfirm="(val) => onConfirmSelect(val, 'gender')" :requiredRule="[{ required: true, message: '请选择性别' }]"
            rightIcon="arrow-down" required /> -->

        <van-field v-model="form.gender" required label="性别" placeholder="请选择性别"
            :rules="[{ required: true, message: '请选择性别' }]" class="radioBox">
            <template #input>
                <van-radio-group v-model="form.gender" direction="horizontal" shape="square">
                    <van-radio :name="i.value" v-for="i in sexClomnu" :key="i.value">{{
                        i.label
                        }}</van-radio>
                </van-radio-group>
            </template>
        </van-field>

        <van-field v-model="form.companyName" name="companyName" label="所属工会" placeholder="请输入所属工会"
            :rules="[{ required: true, message: '请输入所属工会' }]" required />
        <inputSelect :value="typeName" :columns="WorkerSortClomnu" name="typeBizId" label="所属类别" placeholder="请选择所属类别"
            :requiredRule="[{ required: true, message: '请选择所属类别' }]" rightIcon="arrow-down" required
            @onConfirm="(val) => onConfirmSelect(val, 'typeBizId')" />

        <van-field v-model="form.personalStyle" name="personalStyle" label="个人摘要" required type="textarea"
            maxlength="200" rows="4" autosize placeholder="请输入个人摘要" show-word-limit></van-field>

        <div class="special mb-63px">
            <van-field label="选择头像" required :rules="[{ required: true, message: '请上传头像' }]">
                <template #input>
                    <van-uploader v-model="avatar" reupload max-count="1" accept="image/*" :after-read="afterRead">
                        <div class="uploader w-140px">
                            <img loading="lazy" src="@/assets/public/upload_icon.png" class="w-full">
                        </div>
                    </van-uploader>
                </template>
            </van-field>
            <van-field label="资格证书" required :rules="[{ required: true, message: '请上传资格证书' }]">
                <template #input>
                    <van-uploader v-model="fileList" max-count="3" accept="image/*" :after-read="afterRead">
                        <div class="uploader w-140px">
                            <img loading="lazy" src="@/assets/public/upload_icon.png" class="w-full">
                        </div>
                    </van-uploader>
                </template>
            </van-field>
        </div>
    </van-form>
</template>

<script lang="ts" setup>
import inputSelect from '@/components/inputSelect/index.vue'
import { telRules, idCardRules } from '@/utils/rulesValidator'
import { uploadFile } from '@/api/public';
import { showFailToast, showSuccessToast } from 'vant';
import { getwokerCraftType } from '@/api/workerCraftsman'
import { useDictionary } from '@/store/modules/dictionary';
import { useUserStore } from "@/store/modules/user";
import { judgeStaticUrl } from '@/utils/utils';
import { getGenderByIdCard } from "@/utils/tool"
const props = defineProps({
    defaultForm: {
        type: Object,
        default: () => { }
    }
})
const useStore = useUserStore();
const userInfo = computed(() => {
    return useStore.userInfo
})


const dictionary = useDictionary();
const emit = defineEmits(['getForm'])
const form = ref<any>({
    userName: '',
    phone: userInfo.value?.phone,
    identityCardNumber: '',
    companyName: '',
    personalStyle: '',
    modelType: 1,//工匠
    avatar: '',
    evidentiaryMaterial: '',
    typeBizId: '',
    gender: ''
})
// 出生年月日
watch(() => form.value.identityCardNumber, (val) => {
    form.value.gender = getGenderByIdCard(val)
})
// 文件上传
const fileList = ref<Recordable>([])
const avatar = ref<Recordable>([])
const afterRead = (file) => {
    let filedata = {
        operateType: "82", //操作模块类型
        file: file.file,
    }
    file.status = "uploading"
    uploadFile(filedata).then((res) => {
        if (res.code == 200 && res.data) {
            file.status = "success";
            file.url = judgeStaticUrl(res.data[0])
            file.originUrl = res.data[0]
            showSuccessToast('上传成功')
        } else {
            file.status = "failed";
            showFailToast(res.message);
        }
    })
}
// end
const rules = {
    telRules,
    idCardRules,
}
// 性别下拉
const sexClomnu = computed(() => {
    return dictionary.getDictionaryOpt?.['gender']
})
const genderName = ref('')
// end

// 所属类别
const WorkerSortClomnu = ref([])
const getTypes = async () => {
    const { data } = await getwokerCraftType('1')
    WorkerSortClomnu.value = data.map((item: any) => {
        return {
            label: item.typeName,
            value: item.typeBizId,
        }
    })
}
const typeName = ref('')
// end

const formRef = ref<FormInstance>()
const getForm = (val) => {
    form.value.avatar = avatar.value[0]?.originUrl
    form.value.evidentiaryMaterial = fileList.value.map((item: any) => item.originUrl).join(',')
    emit('getForm', form.value)
}
const submit = () => {
    formRef.value?.submit()
}
const onConfirmSelect = (val: Recordable, name: string) => {
    form.value[name] = val[0]?.value
    switch (name) {
        case 'gender':
            genderName.value = val[0]?.label
            break;
        case 'typeBizId':
            typeName.value = val[0]?.label
            break
        default:
            break;
    }
}
watch(() => props.defaultForm, (val: any) => {
    if (val) {
        form.value = val
        genderName.value = sexClomnu.value.find((item: any) => item.value == val.gender)?.label
        typeName.value = val.typeName

        fileList.value = val.evidentiaryMaterial?.split(',').map((item: string) => {
            return {
                url: judgeStaticUrl(item),
                objectUrl: judgeStaticUrl(item),
                originUrl: item
            }
        })
        avatar.value = val.avatar?.split(',').map((item: string) => {
            return {
                url: judgeStaticUrl(item),
                objectUrl: judgeStaticUrl(item),
                originUrl: item
            }
        })
        if (form.value?.typeName) {
            delete form.value.typeName
        }
    }
})
onMounted(async () => {
    await getTypes()
})
// 暴露给父组件调用
defineExpose({
    submit,
    formRef,
})
</script>

<style scoped lang="scss">
:deep(.van-field__label) {
    font-weight: 500;
    font-size: 32px;
    color: #333333;
    margin-bottom: 27px;
}

:deep(.van-field__body) {
    background-color: #F2F2F2;
    border-radius: 12px;
    padding: 30px;
    font-size: 30px;
}

:deep(.van-radio__icon--checked .van-icon) {
    background-color: #F33C40;
    border-color: #F33C40;
}

.special {
    :deep(.van-field__body) {
        background-color: transparent;
        padding: 0;
    }
}
</style>