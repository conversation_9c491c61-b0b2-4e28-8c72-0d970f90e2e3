<template>
    <div class="treasure w-full relative h-100vh">
        <div class="winner-tag absolute right-0 w-40px h-140px flex items-center leading-28px
        text-#E13B14 text-24px text-center top-130px" @click="toRecord">
            中奖记录
        </div>
        <div class="absolute left-0 right-0 text-center top-30% z-66" :class="{ 'scaleRotate': isLottery }">
            <img loading="lazy" src="@/assets/integralMall/magic_div.png" class="w-50%">
        </div>
        <div class="absolute left-0 right-0 text-center top-40% z-1">
            <img loading="lazy" src="@/assets/integralMall/mask_line.png" class="w-60%" />
        </div>
        <div class="absolute bottom-112px left-0 right-0">
            <div class="btn relative mx-auto" style="width: calc(100% -  85px);">
                <img loading="lazy" src="@/assets/integralMall/active_btn.png" alt="" class="w-full" />
                <div class="text absolute top-50% left-0% right-0% -translate-y-50% text-center" @click="toWinner">
                    <div class="bigTitle text-58px leading-none font-bold text-[#fff]">开启盲盒</div>
                    <div class="smallTitle text-36px leading-none font-medium text-[#fff] mt-14px">{{
                        activityInfo?.integralScore
                        }}积分/次
                    </div>
                </div>
            </div>
            <div class="message_tips bg-[#FF8E63] text-[#fff] mx-85px rounded-20px py-20px px-54px text-center mt-54px">
                每日{{ activityInfo?.luckDrawInfo?.numberPerDay }}次参与抽奖机会
            </div>
        </div>

        <surprisePopup v-model:show="showSuprise" :surpiseType="supriseType">
            <!-- 积分 -->
            <div class="text-[#E71E03] text-40px font-medium flex items-center tracking-wide"
                v-if="supriseType === 'integral'">
                <span>恭喜获得</span>
                <!-- 分值 -->
                <span class="text-50px">{{ currentLottery?.prizeContent }}</span>积分
            </div>
            <!-- 商品/优惠券/红包 -->
            <div class="flex flex-col justify-center mt-60px h-50%" v-else>
                <div class="text-center">
                    <div class="text-32px text-#333 tracking-wide 
                    text-ellipsis line-clamp-2 whitespace-nowrap text-wrap">{{ currentLottery?.prizeName }}
                    </div>
                    <div class="text-28px text-#666 mt-10px tracking-wide 
                        text-ellipsis line-clamp-2 whitespace-nowrap text-wrap" v-if="currentLottery?.prizeContent">
                        {{ currentLottery?.prizeContent }}
                    </div>
                </div>
                <div class="text-[#E71E03] text-40px font-bold mt-40px text-center tracking-wide">恭喜您中奖啦~</div>
            </div>
        </surprisePopup>
        <confirmPopop v-model:show="showTips" confirmText="我知道了" :showCancel="false" confirmColor="#FE4018"
            @confirm="showTips = false" :tips="errorTips">
        </confirmPopop>
    </div>
</template>
<script lang="ts" setup>
import { integralLotteryDetail, getLottery } from '@/api/mall/integral';
import { getAwardState } from '@/api/activity'
import surprisePopup from './components/surprisePopup.vue';
import confirmPopop from '@/components/Popup/confirmPopop.vue'
import { showToast } from 'vant';
import { useUserStore } from '@/store/modules/user';
import { myIntegralNum } from '@/api/mall/integral'
onMounted(async () => {
    getIntegralNum();
    await getActivtiDetail();
    getLotteryNumber();
});
const showSuprise = ref(false);
const supriseType = ref('integral') // 积分/礼品 goods
const activityInfo = ref<any>(null);
const useStore = useUserStore()
const userInfo = computed(() => useStore.userInfo)
let currentUseNum = ref(0);

// 获取活动详情
const getActivtiDetail = async () => {
    const { code, data, message } = await integralLotteryDetail({
        activityMode: 'integralLottery'
    })
    if (code === 200 && data) {
        activityInfo.value = data;
    } else {
        // showToast(message)
    }
}
// 获取用户积分
const userIntegral = ref(0) //用户可使用积分
const getIntegralNum = () => {
    myIntegralNum(
        {
            userId: userInfo.value?.userId
        }
    ).then(res => {
        if (res.code === 200) {
            userIntegral.value = res.data?.userIntegral || 0
        }
    })
}
// 获取抽奖次数
const getLotteryNumber = () => {
    getAwardState({
        userId: userInfo.value?.userId,
        activityId: activityInfo.value?.activityId,
    }).then(res => {
        if (res.code === 200) {
            currentUseNum.value = res.data || 0
        }
    })
}
//抽奖
let isLottery = ref(false);
const currentLottery = ref<any>({});
const showTips = ref(false);
const errorTips = ref('')
const toWinner = async () => {
    if (!activityInfo.value) {
        errorTips.value = '活动暂未开展，敬请期待~'
        showTips.value = true
        return
    }
    // 积分门槛校验
    if (userIntegral.value < activityInfo.value?.integralThreshold) {
        errorTips.value = `您的积分为不足，当前活动需要积分达到${activityInfo.value?.integralThreshold}才能参与,您的积分为：${userIntegral.value}`
        showTips.value = true
        return
    }

    // 抽奖次数校验
    if (currentUseNum.value >= activityInfo.value?.luckDrawInfo?.numberPerDay) {
        errorTips.value = `每日可抽奖${activityInfo.value?.luckDrawInfo?.numberPerDay}次,您今日已无抽奖机会,明日再来吧~`
        showTips.value = true
        return
    }

    if (isLottery.value) return
    isLottery.value = true
    const { code, data, message } = await getLottery()

    setTimeout(() => {
        isLottery.value = false
        if (code === 200) {
            // 扣除抽奖消耗积分
            userIntegral.value -= activityInfo.value?.integralScore

            // 奖品信息
            currentLottery.value = data

            if (data?.prizeType === '2') {
                supriseType.value = 'integral'
                // 如果抽取到积分 则增加积分
                userIntegral.value += Number(data?.prizeContent)
            }
            else supriseType.value = 'goods'

            // 如果抽取到奖品 则显示弹窗
            if (data?.prizeType !== '1') showSuprise.value = true

            else {
                errorTips.value = `谢谢参与，奖品与您擦肩而过，请再接再厉！`
                showTips.value = true
            }
            // 抽奖次数+1
            currentUseNum.value++
        } else {
            showToast(message)
        }
    }, 1000);
}
// end
// 中奖记录
const router = useRouter()
const toRecord = () => {
    if (!activityInfo.value?.activityId) {
        showToast('活动未开展，敬请期待~')
        return
    }
    router.push({
        path: '/activityHome/lotteryRecord',
        query: {
            activityId: activityInfo.value?.activityId,
        }
    })
}
</script>
<style scoped lang="scss">
.treasure {
    background: url('@/assets/integralMall/treasure_bg.png'), #fdbe8f;
    background-size: 100% auto;
    background-repeat: no-repeat;

    .winner-tag {
        background: linear-gradient(0deg, #FCE6A9 0%, #FFECEB 100%);
        border-radius: 16px 0px 0px 16px;
    }

    .scaleRotate {
        transform-origin: 50% 50%;
        animation: rotateAnimation 1s linear infinite;
    }
}

// 魔方旋转
@keyframes rotateAnimation {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(-360deg);
    }
}
</style>