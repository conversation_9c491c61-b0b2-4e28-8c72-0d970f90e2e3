<template>
  <div class="bg-[#f6f7f8] min-h-[100vh] overflow-y-scroll pb-[122px]" ref="scrollBoxRef">

    <div class="flex flex-col h-full">
      <div class="mb-[20px] bg-[#fff] box-border  pb-[19px]">
        <shareList :data="Data.topicList" :is-show-detail="false" />
      </div>
      <div class="bg-[#fff] box-border mb-[20px] flex-1">
        <div class="text-28px text-[#333] pt-[34px] pb-[24px] pl-[28px] comment-title">评论 <span>{{
          Data.evaluateList.length || '' }}</span></div>
        <div class="p-[22px] box-border border-t-solid border-t-2 border-[#efefef]"
          v-if="Data.evaluateList && Data.evaluateList.length">
          <evaluateList :data="Data.evaluateList">
            <template #shortContent="{ item, index }">
              <div class="text-28px mt-20px  ">{{ item.content }}</div>
            </template>
          </evaluateList>
        </div>
        <Empty v-else></Empty>
      </div>
    </div>

    <div class="flex fixed bottom-0 bg-#fff text-#333 text-25px min-h-[122px] w-100% bottom-box justify-around">
      <div class="input flex items-center  flex-1 rounded-37px min-h-full w-100% pl-28px ">
        <van-field v-model="conment" placeholder="写评论" type="textarea" rows="1" autosize
          class="conment bg-[#f2f2f2] rounded-[20px]" style="width: 85%;" :leftIcon="leftIcon"></van-field>
        <div class="send text-center leading-49px h-49px rounded-23px 
        bg-[#fff] text-28px text-[#999] ml-[10px] conment" @click="sendText(Data.topicList[0])">发送</div>
      </div>
      <div class="w-20% flex  items-center justify-end pr-[28px]">
        <!-- <div class="flex items-center"><van-icon name="eye-o" size="25" color="#999999" class="mr-20px" />{{
          Data.topicList[0]?.readCount }}</div> -->
        <div class="flex items-center" @click="like(Data.topicList[0])"><van-icon
            :name="Data.topicList[0]?.likeStatus ? 'good-job' : 'good-job-o'"
            :color="Data.topicList[0]?.likeStatus ? '#5AA4FF' : '#333'" size="25" />{{ Data.topicList[0]?.likeNum || ''
            }}
        </div>
      </div>
    </div>

    <!-- 气泡样式 -->
    <div class="fixed bottom-140px z-100" :style="`left:85%`">
      <waterIntergral v-model:show="showBubble" :score="scoreNum"></waterIntergral>
    </div>

  </div>
</template>
<script lang="ts" setup>
import evaluateList from "@/components/List/evaluateList.vue";
import { shareInsertReply, experienceSharingReplyFindVoListH5 } from "@/api/digitalSchools/group";
import { showSuccessToast, showFailToast, showToast } from "vant";
import { likeOperate } from '@/api/public';
import Empty from '@/components/Empty/index.vue';
import leftIcon from '@/assets/public/comment_icon.png';
import shareList from "../components/shareList.vue";
import { useDigitalSchoolStore } from '@/store/modules/digitalSchool';
const waterIntergral = defineAsyncComponent(() => import('@/components/Bubble/waterIntergral.vue'))

const digitalSchoolStore = useDigitalSchoolStore();
const conment = ref("");//评论
const Data = ref({
  topicList: [],
  evaluateList: [],
  showPop: false,
  titleName: '',
  objInfo: {},
  pageNum: 1,
});
const scrollBoxRef = ref(null)

onMounted(() => {
  Data.value.topicList = [digitalSchoolStore.getShareDetail]
  Data.value.evaluateList = Data.value.topicList && Data.value.topicList[0]?.experienceSharingReplyList || [];
  scrollToEnd()
});


//消息自动滚到底部
function scrollToEnd() {
  nextTick(() => {
    scrollBoxRef.value.scrollTop = scrollBoxRef.value.scrollHeight;

  });
}

//点赞
function getCommentList(item) {
  experienceSharingReplyFindVoListH5({
    experienceSharingId: Data.value.topicList[0].experienceSharingId,
    pageSize: 0
  }).then(res => {
    scrollToEnd()
    if (res.code == 200) {
      Data.value.evaluateList = res.data || []
    }
  })
}

//点赞
// 气泡提示框参数设置
const showBubble = ref(false);
const scoreNum = ref(0);
let isReq = false;

function like(item) {
  if (isReq) return
  isReq = true;
  likeOperate({
    sourceId: item.experienceSharingId
  }).then(res => {
    isReq = false;
    if (res.code == 200) {
      // likeState
      if (item.likeStatus) {
        item.likeStatus = false;
        item.likeNum--;
        showFailToast('取消点赞')
      } else {
        item.likeStatus = true;
        item.likeNum++;
        showToast('点赞成功')
      }
      if (res.data?.score) {
        scoreNum.value = res.data?.score;
        showBubble.value = true
      }
    }
  })
    .catch(() => {
      isReq = false;
    })
}
//评论
function sendText(item) {
  if (!conment.value) return
  let params = {
    content: conment.value,
    experienceSharingId: item.experienceSharingId
  }
  shareInsertReply(params).then(res => {
    if (res.code == 200) {
      conment.value = ""
      showSuccessToast("提交成功");
      getCommentList()
    }
  })
}

</script>
<style lang="scss" scoped>
.comment-title {
  width: fit-content;
  position: relative;

  ::after {
    content: "";
    width: 50%;
    height: 6px;
    background: linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%);
    border-radius: 3px;
    display: block;
    position: absolute;
    bottom: 4px;
    left: calc(50% - 14px);

  }
}
</style>