<template>
    <div>
        <!-- 劳模表单 -->
        <van-form ref="formRef" @submit="getForm" label-align="top">
            <van-field v-model="form.userName" name="userName" label="劳模姓名" placeholder="请输入劳模姓名"
                :rules="[{ required: true, message: '请输入劳模姓名' }]" required />

            <van-field v-model="form.phone" name="phone" label="联系电话" readonly placeholder="请输入联系电话"
                :rules="rules.telRules" required />

            <van-field v-model="form.identityCardNumber" name="identityCardNumber" label="身份证号码" placeholder="请输入身份证号码"
                :rules="rules.idCardRules" required />

            <van-field v-model="form.companyName" name="companyName" label="所属工会" placeholder="请输入所属工会"
                :rules="[{ required: true, message: '请输入所属工会' }]" required />

            <!-- <inputSelect :value="genderName" :columns="sexClomnu" name="genderName" label="性别" placeholder="请选择性别"
                @onConfirm="(val) => onConfirmSelect(val, 'gender')"
                :requiredRule="[{ required: true, message: '请选择性别' }]" rightIcon="arrow-down" required /> -->
            <van-field v-model="form.gender" required label="性别" placeholder="请选择性别"
                :rules="[{ required: true, message: '请选择性别' }]" class="radioBox">
                <template #input>
                    <van-radio-group v-model="form.gender" direction="horizontal" shape="square">
                        <van-radio :name="i.value" v-for="i in sexClomnu" :key="i.value">{{
                            i.label
                            }}</van-radio>
                    </van-radio-group>
                </template>
            </van-field>

            <inputSelect :value="nationnalName" :columns="nationalityClomnu" name="nationnalName" label="民族"
                :filterSearch="true" placeholder="请选择民族" :requiredRule="[{ required: true, message: '请选择民族' }]"
                @onConfirm="(val) => onConfirmSelect(val, 'nationality')" rightIcon="arrow-down" required />

            <van-field v-model="form.dateOfBirth" name="dateOfBirth" label="出生年月" placeholder="根据身份证号码自动计算"
                :rules="[{ required: true, message: '请填写身份号码，将自动计算出生年月' }]" readonly required />


            <inputSelect :value="educationName" :columns="modelEducationClomnu" name="educationName" label="文化程度"
                placeholder="请选择文化程度" :requiredRule="[{ required: true, message: '请选择文化程度' }]" rightIcon="arrow-down"
                required @onConfirm="(val) => onConfirmSelect(val, 'education')" />

            <inputSelect :value="politicsStateName" :columns="politicsStateClomnu" name="politicsStateName" label="政治面貌"
                placeholder="请选择政治面貌" :requiredRule="[{ required: true, message: '请选择政治面貌' }]" rightIcon="arrow-down"
                required @onConfirm="(val) => onConfirmSelect(val, 'politicsState')" :filterSearch="true" />

            <van-field v-model="form.workUnitName" name="workUnitName" label="工作单位及职务职称" placeholder="请输入工作单位及职务职称"
                :rules="[{ required: true, message: '请输入工作单位及职务职称' }]" required />

            <van-field v-model="form.whenModelWorker" name="whenModelWorker" label="何时获得劳模称号及年号"
                placeholder="请输入何时获得劳模称号及年号" :rules="[{ required: true, message: '请输入何时获得劳模称号及年号' }]" required />

            <van-field v-model="form.certificateNumber" name="certificateNumber" label="证书编号" placeholder="请输入证书编号"
                required></van-field>

            <inputSelect :value="typeName" :columns="WorkerSortClomnu" name="typeBizId" label="所属类别"
                placeholder="请选择所属类别" :requiredRule="[{ required: true, message: '请选择所属类别' }]" rightIcon="arrow-down"
                required @onConfirm="(val) => onConfirmSelect(val, 'typeBizId')" />

            <van-field v-model="form.personalStyle" name="personalStyle" label="个人摘要" required type="textarea"
                maxlength="200" rows="4" autosize placeholder="请输入个人摘要" show-word-limit></van-field>

            <div class="special mb-63px">
                <van-field label="选择头像" required :rules="[{ required: true, message: '请上传头像' }]">
                    <template #input>
                        <van-uploader v-model="avatar" reupload max-count="1" accept="image/*" :after-read="afterRead">
                            <div class="uploader w-140px">
                                <img loading="lazy" src="@/assets/public/upload_icon.png" class="w-full">
                            </div>
                        </van-uploader>
                    </template>
                </van-field>
                <van-field label="证明材料" required :rules="[{ required: true, message: '请上传资料' }]">
                    <template #input>
                        <van-uploader v-model="fileList" max-count="3" accept="image/*" :after-read="afterRead">
                            <div class="uploader w-140px">
                                <img loading="lazy" src="@/assets/public/upload_icon.png" class="w-full">
                                <div class="text-[#999] text-25px text-center">上传资料</div>
                            </div>
                        </van-uploader>
                    </template>
                </van-field>
            </div>
        </van-form>

    </div>
</template>

<script lang="ts" setup>
import inputSelect from '@/components/inputSelect/index.vue'
import type { FormInstance } from 'vant';
import { type authFormType } from '@/api/workerCraftsman';
import { telRules, idCardRules } from '@/utils/rulesValidator'
import { useDictionary } from '@/store/modules/dictionary';
import { uploadFile } from '@/api/public';
import { showFailToast, showSuccessToast } from 'vant';
import { getwokerCraftType } from '@/api/workerCraftsman'
import { getBirthdate } from '@/utils/tool'
import { judgeStaticUrl } from '@/utils/utils';
import { useUserStore } from "@/store/modules/user";
import { getGenderByIdCard } from "@/utils/tool"
const props = defineProps({
    defaultForm: {
        type: Object,
        default: () => { }
    }
})

const useStore = useUserStore();
const userInfo = computed(() => {
    return useStore.userInfo
})
// const changeNum = (val: any) => {
//     if (val) form.value.gender = getGenderByIdCard(val)
// }
const dictionary = useDictionary();
const emit = defineEmits(['getForm'])
const form = ref<authFormType>({
    userName: '',
    phone: userInfo.value?.phone,
    identityCardNumber: '',
    companyName: '',
    gender: '',
    nationality: '',
    dateOfBirth: '',
    education: '',
    politicsState: '',
    workUnitName: '',
    whenModelWorker: '',
    modelType: 0,//劳模
    avatar: '',
    evidentiaryMaterial: '',
    certificateNumber: '',
    personalStyle: '',
})
const rules = {
    telRules,
    idCardRules,
}
// 出生年月日
watch(() => form.value.identityCardNumber, (val) => {
    form.value.dateOfBirth = getBirthdate(val)
    form.value.gender = getGenderByIdCard(val)
})

// 性别下拉
const sexClomnu = computed(() => {
    return dictionary.getDictionaryOpt?.['gender']
})
const genderName = ref('')
// end
// 民族
const nationalityClomnu = computed(() => {
    return dictionary.getDictionaryOpt?.['nation']
})
const nationnalName = ref('')
// end
// 文化程度
const modelEducationClomnu = computed(() => {
    return dictionary.getDictionaryOpt?.['modelEducation']
})
const educationName = ref('')
// end
// 政治面貌
const politicsStateClomnu = computed(() => {
    return dictionary.getDictionaryOpt?.['politicsState']
})
const politicsStateName = ref('')
// end
// 所属类别
const WorkerSortClomnu = ref([])
const getTypes = async () => {
    const { data } = await getwokerCraftType('0')
    WorkerSortClomnu.value = data.map((item: any) => {
        return {
            label: item.typeName,
            value: item.typeBizId,
        }
    })
}
const typeName = ref('')
// end

// 文件上传
const fileList = ref<Recordable>([])
const avatar = ref<Recordable>([])
const afterRead = (file: any) => {
    let filedata = {
        operateType: "82", //操作模块类型
        file: file.file,
    }
    file.status = "uploading"
    uploadFile(filedata).then((res) => {
        if (res.code == 200 && res.data) {
            file.status = "success";
            file.url = judgeStaticUrl(res.data[0])
            file.originUrl = res.data[0]
            showSuccessToast('上传成功')
        } else {
            file.status = "failed";
            showFailToast(res.message);
        }
        console.log(file, "文件上传完成");
    })
}
// end
const formRef = ref<FormInstance>()
const getForm = (val) => {
    form.value.avatar = avatar.value[0]?.originUrl
    form.value.evidentiaryMaterial = fileList.value.map((item: any) => item.originUrl).join(',')
    emit('getForm', form.value)
}
const submit = () => {
    formRef.value?.submit()
}

const onConfirmSelect = (val: Recordable, name: string) => {
    form.value[name] = val[0]?.value
    switch (name) {
        case 'gender':
            genderName.value = val[0]?.label
            break;
        case 'nationality':
            nationnalName.value = val[0]?.label
            break
        case 'education':
            educationName.value = val[0]?.label
            break
        case 'politicsState':
            politicsStateName.value = val[0]?.label
            break
        case 'typeBizId':
            typeName.value = val[0]?.label
            break
        default:
            break
    }
}
watch(() => props.defaultForm, (val: any) => {
    if (val) {
        form.value = val
        genderName.value = sexClomnu.value.find((item: any) => item.value == val.gender)?.label
        nationnalName.value = nationalityClomnu.value.find((item: any) => item.value == val.nationality)?.label
        educationName.value = modelEducationClomnu.value.find((item: any) => item.value == val.education)?.label
        politicsStateName.value = politicsStateClomnu.value.find((item: any) => item.value == val.politicsState)?.label
        typeName.value = val.typeName
        fileList.value = val.evidentiaryMaterial?.split(',').map((item: string) => {
            return {
                url: judgeStaticUrl(item),
                objectUrl: judgeStaticUrl(item),
                originUrl: item
            }
        })
        avatar.value = val.avatar?.split(',').map((item: string) => {
            return {
                url: judgeStaticUrl(item),
                objectUrl: judgeStaticUrl(item),
                originUrl: item
            }
        })

    }
},)
onMounted(async () => {
    await getTypes()
})
// 暴露给父组件调用
defineExpose({
    submit,
    formRef,
})
</script>

<style scoped lang="scss">
:deep(.van-field__label) {
    font-weight: 500;
    font-size: 32px;
    color: #333333;
    margin-bottom: 27px;
}

:deep(.van-field__body) {
    background-color: #F2F2F2;
    border-radius: 12px;
    padding: 20px 30px;
    font-size: 30px;
}

:deep(.van-radio__icon--checked .van-icon) {
    background-color: #F33C40;
    border-color: #F33C40;
}

.special {
    :deep(.van-field__body) {
        background-color: transparent;
        padding: 0;
    }
}
</style>