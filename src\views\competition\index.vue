<template>
  <div>
    <img loading="lazy" :src="banner" alt="" class="w-100% h-300px" />
    <div style="background-size: 100% 100%"
      class="flex items-center mt-[-30px] relative bg-no-repeat bg-center h-112px w-100%"
      :style="{ backgroundImage: `url(${Data.tab.nav[Data.tab.active]?.bg})` }">
      <div v-for="(tabItem, index) in Data.tab.nav" :key="index"
        class="w-1/2 flex justify-center text-30px text-#637180" :class="{
          'tab-active text-#333333 text-32px font-medium':
            index === Data.tab.active,
        }" @click="tabClick(index)">
        {{ tabItem.name }}
      </div>
    </div>
    <div class="mt-20px" v-if="Data.tab.active === 0">
      <!-- tab切换 -->
      <div class="z-10000 text-30px flex justify-around text-#525252">

        <div v-for="(item, i) in Data.tabName" :key="i" :class="Data.tabIndex == i ? 'text-#5AA4FF' : ''"
          @click="activityTabBtn(i, item)">
          <div>
            {{ Data.tabName[i]?.currentName ? Data.tabName[i]?.currentName : item.name }}
            <van-icon name="arrow-down"></van-icon>
          </div>
        </div>
      </div>
      <!-- tab切换下拉 -->
      <div class="dialog-activity" v-show="Data.isShow">
        <van-popup :show="Data.isShow" round position="top">
          <div class="p-24px box-border">
            <div class="flex items-start flex-wrap bg-#fff box-border">
              <div
                class="dropdown_menu_item box-border px-20px py-6px mb-15px rounded-25px bg-#f7f7f7 text-#4c4c4c mr-15px text-28px"
                :class="Data.tabName[Data.tabIndex].index == index
                  ? ' !bg-#A1CBFF !text-#fFF'
                  : ''
                  " v-for="(item, index) in Data.tabName[Data.tabIndex].children" :key="index"
                @click="dropdownMenuBtn(index, item)">
                {{ item.label }}
              </div>
            </div>
          </div>
        </van-popup>
      </div>
      <refreshList key="actList" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
        <div class="p-30px box-border w-100% flex items-center flex-wrap">
          <div v-for="(item, index) in Data.activityList" :key="index"
            class="jsbm-item-box bg-#F7F7F7 rounded-12px mb-20px" @click="toDetail(item)"
            :class="{ 'mr-20px': index % 2 == 0 }">
            <img loading="lazy" :src="item.appCover ? utils.judgeStaticUrl(item.appCover) : defaultImg" alt=""
              class="h-164px w-100% rounded-t-12px" />
            <div class="px-15px box-border">
              <div class="truncate my-15px text-#3F4853 text-28px">
                {{ item.activityName }}
              </div>
              <div class="text-#999999 text-20px"> {{ item.activityStartTime }} - {{ item.activityEndTime }}</div>
              <div class="flex items-center justify-between mt-25px mb-18px">
                <div
                  class="px-10px py-3px box-border text-24px rounded-6px flex items-center justify-center border-1px border-solid"
                  :class="item.progress === '2'
                    ? ' border-#5AA4FF  text-#5AA4FF'
                    : ' border-#999999  text-#999999'
                    ">
                  {{ item.progressMsg }}
                </div>
                <div class="flex items-center">
                  <van-icon name="eye-o" size="15" class="z-1" color="#999999" />
                  <span class="ml-7px text-#999999 text-24px">{{ item.readCount }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </refreshList>
    </div>
    <div v-else class="p-30px box-border">
      <div class="two-tab-box mb-36px">
        <van-tabs v-model:active="Data.twoTab.active" sticky type="card" color="#F2F2F2" title-inactive-color="#666666"
          title-active-color="#5AA4FF" @click-tab="onClickTab">
          <van-tab :title="item.categoryName" v-for="(item, index) in Data.twoTab.nav" :key="index"
            title-class="tab-title"></van-tab>
        </van-tabs>
      </div>
      <div>
        <refreshList @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
          <newsCell v-for="(item, index) in Data.list" :key="index" :content="item"></newsCell>
        </refreshList>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import banner from "@/assets/competition/ncjs_banner.jpg";
import zuo from "@/assets/competition/zuo.png";
import you from "@/assets/competition/you.png";
import { useDictionary } from "@/store/modules/dictionary";
const dictionary = useDictionary();
import refreshList from "@/components/refreshList/index.vue";
import newsCell from "@/components/Cell/newsCell.vue";
import { activityInfoList } from "@/api/activity.ts";
import { useRoute } from "vue-router";

import { useUserStore } from "@/store/modules/user.ts";

import { getSubordinateColumn, getNewsList } from '@/api/news/index';
import utils from "@/utils/utils";
import { encryptUserInfo } from '@/api/news';
import defaultImg from "@/assets/competition/work.jpg";
import { toDetail } from "@/hooks/useValidator";
const route = useRoute();
const Data = ref({
  tab: {
    active: 0,
    nav: [
      { name: "竞赛报名", bg: zuo },
      { name: "竞赛动态", bg: you },
    ],
  },
  twoTab: {
    active: 0,
    nav: [
      // { name: "公告" },
      // { name: "政策文件" },
      // { name: "获奖信息" },
      // { name: "比赛新闻" },
    ],
  },
  tabIndex: 0,
  tabName: [
    {
      id: 0,
      index: 0,
      name: "区域",
      code: "areaCode",
      children: [{ label: "全部", value: "" }],
      currentName: ''
    },
    {
      id: 1,
      index: 0,
      name: "状态",
      code: "progress",
      children: [
        { label: "全部", value: "" },
        { label: "进行中", value: 2 },
        { label: "已结束", value: 3 },
        { label: "未开始", value: 1 },
      ],
      currentName: ''
    },
    {
      id: 2,
      index: 0,
      name: "类型",
      code: "activityType",
      children: [],
      currentName: ''
    },
  ],
  isShow: false,
  jsbmList: [
    // status 1-进行中  2-已结束
    { status: 1 },
    { status: 2 },
    { status: 3 },
    { status: 1 },
    { status: 2 },
  ],
  list: [],
  pageNum: 1,
  params: {},
  activityList: [],
  activityCategory: 'competition',
});
onMounted(() => {
  const areaNames = dictionary.getDictionaryOpt?.["regionCode"]?.map(
    (t: { label: string }) => {
      const { label } = t;
      return { value: label, label };
    }
  );
  Data.value.tabName[0].children = [{ label: "全部", value: "" }, ...areaNames];

  const activityTypes = dictionary.getDictionaryOpt?.["competitionActivityType"];
  Data.value.tabName[2].children = [
    { label: "全部", value: "" },
  ];
  if (activityTypes && activityTypes.length) Data.value.tabName[2].children = [
    { label: "全部", value: "" }, ...activityTypes
  ]

  getActList();
});
function tabClick(index: number) {
  Data.value.tab.active = index;
  Data.value.twoTab.active = 0;
  if (Data.value.tab.active == 1) {
    getColumn()
  } else {
    getActList()
  }
}
/*全部/区域/状态 tab切换   */
function activityTabBtn(index, item) {
  // tab切换 下划线滑动
  if (Data.value.tabIndex == index) {
    Data.value.isShow = !Data.value.isShow;
  } else {
    Data.value.isShow = true;
  }
  Data.value.tabIndex = index;
}
/* 区域/状态 tab下拉的选项 */
function dropdownMenuBtn(index, item) {
  Data.value.tabName[Data.value.tabIndex].index = index
  Data.value.tabName[Data.value.tabIndex].currentName = item.label === '全部' ? '' : item.label
  Data.value.isShow = false;
  Data.value.tabName.forEach(t => {
    const { code, children, index } = t
    Data.value.params[code] = children[index].value
  })
  Data.value.pageNum = 1;
  getActList();
}

//获取活动列表
const loadMoreRef = ref(null)
function getActList() {
  activityInfoList({
    ...Data.value.params,
    activityCategory: Data.value.activityCategory,
    pageSize: 10,
    pageNum: unref(Data).pageNum,
  }).then(res => {
    if (res.code == 200) {
      if (Data.value.pageNum === 1) {
        Data.value.activityList = [];
      }
      res.data = res.data?.map(t => {
        const { activityStartTime, activityEndTime } = t
        t.activityStartTime = activityStartTime?.split(' ')[0] ?? ''
        t.activityEndTime = activityEndTime?.split(' ')[0] ?? ''
        return t
      })
      Data.value.activityList = Data.value.activityList.concat(res.data);
      //重置刷新状态及 判断是否加载完成
      if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(Data.value.activityList.length, res.total);
      }
    }

  })
}
// 刷新
const onRefreshList = () => {
  Data.value.tabName.forEach(t => {
    t.index = 0
    t.currentName = ''
  })
  Data.value.tabIndex = 0
  Data.value.params = {}
  Data.value.pageNum = 1;
  if (Data.value.tab.active == 1) {
    getLists()
  } else {
    getActList()
  }
};
// 加载更多
const onLoadMore = () => {
  Data.value.pageNum++;
  if (Data.value.tab.active == 1) {
    getLists()
  } else {
    getActList()
  }
};
function onClickTab(e: { name: string }) {
  Data.value.twoTab.active = e.name;
  Data.value.pageNum = 1;
  getLists();
}
//获取栏目
async function getColumn() {
  getSubordinateColumn({
    categoryCode: 'jing_sai_dong_tai',
    platformType: 30,
  }).then(res => {
    Data.value.twoTab.nav = res.data;
    getLists();
  });
}
async function getLists() {
  let res = await getNewsList({
    categoryCode: Data.value.twoTab.nav[Data.value.twoTab.active]?.categoryCode,
    platformType: 30,
    pageNum: Data.value.pageNum,
    pageSize: 10,
  });
  if (Data.value.pageNum === 1) Data.value.list = [];
  Data.value.list = Data.value.list.concat(res.data);
  //重置刷新状态及 判断是否加载完成
  if (loadMoreRef.value) {
    loadMoreRef.value.onLoadSuc(Data.value.list.length, res.total);
  }
}

function getEncryption(item) {
  encryptUserInfo({
    recordId: item?.activityId,
    userInfo: useUserStore()?.getUserInfo,
  }).then(res => {
    if (res.code == 200 && res.data) {
      let url = item.externalLinkUrl; //返回的url
      url += item.externalLinkUrl.indexOf("?") == -1 ? "?" : "&"; //判断是否有问号
      utils.openActLink({
        title: item.activityName,
        url: url + "token=" + res.data,
        shareName: item.activityName,//'分享标题'
        shareurl: url,//'分享地址'
        dataid: item?.activityId,
        type: 1,//类型 1-工会活动，2-普惠活动
        uid: item.companyId,
        win: item?.activityId,
        isExternal: 'y'
      })

    } else {
      utils.openActLink({
        title: item.activityName,
        url: item.externalLinkUrl,
        shareName: item.activityName,//'分享标题'
        shareurl: url,//'分享地址'
        dataid: item?.activityId,
        type: 1,//类型 1-工会活动，2-普惠活动
        uid: item.companyId,
        win: item?.activityId,
        isExternal: 'y'
      })

    }
  })
}
</script>
<style lang="scss" scoped>
.tab-active {
  position: relative;

  &::after {
    content: "";
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 6px;
    background: linear-gradient(90deg, #5eacfd 0%, #c9ebff 100%);
    border-radius: 3px;
  }
}

.dialog-activity {

  /* 活动主页 tab切换 下拉的弹窗  */
  .van-popup {
    position: absolute;
    top: 470px;
  }

  .van-overlay {
    background: rgba($color: #000000, $alpha: 0.3);
    top: 500px;
  }

  .dropdown_menu_box {
    border-radius: 0px 0px 12px 12px;

    .dropdown_menu_item {}
  }
}

.jsbm-item-box {
  width: calc((100% - 20px) / 2);
}

.two-tab-box {
  :deep(.van-tabs__nav--card) {
    border: none !important;
    margin: 0 !important;
    height: 48px !important;
  }

  :deep(.van-tab) {
    flex: none;
    padding: 0 25px;
    box-sizing: border-box;
  }

  :deep(.van-tab--card) {
    border-right: none !important;
    border-radius: 24px !important;
    background-color: #f2f2f2;
    color: #666;
    margin-right: 26px;
  }

  :deep(.van-tab--card):last-child {
    margin-right: 0;
  }

  :deep(.van-tab--active) {
    background-color: #f6faff !important;
    border: 1px solid #5aa4ff !important;
    font-weight: 500;
    font-size: 28px;
    color: #5aa4ff;
  }

  :deep(.tab-title) {
    font-weight: 400;
    font-size: 28px;
    color: #666;
  }
}
</style>
