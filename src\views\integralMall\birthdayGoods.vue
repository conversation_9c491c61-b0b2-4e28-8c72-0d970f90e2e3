<template>
    <div class="birthMall flex flex-col">
        <div class="banner realtive z-1">
            <img loading="lazy" src="@/assets/integralMall/birth_banenr.png" class="w-full" alt="" />
            <img loading="lazy" src="@/assets/integralMall/birth_text.png" class="w-48% absolute left-110px top-50px"
                alt="" />
        </div>
        <div class="flex-1 overflow-scroll -mt-50px relative z-10 bg-#fff list-view p-25px">
            <refreshList key="relist" @onRefreshList="onRefreshList" :immeCheck="true" @onLoadMore="onLoadMore"
                ref="loadMoreRef">
                <div class="flex flex-wrap justify-between">
                    <div v-for="(item, index) in list" :key="index" class="w-336px mt-20px box-border overflow-hidden">
                        <goodsListCell :content="item" sourceType="2"></goodsListCell>
                    </div>
                </div>
            </refreshList>
        </div>
    </div>
</template>
<script lang="ts" setup>
import goodsListCell from './components/goodsListCell.vue';
import refreshList from '@/components/refreshList/index.vue';
import { integralGoodsList } from '@/api/mall/integral';
const list = ref<any>([]);
const pageNum = ref(1);
const loadMoreRef = ref<any>(null)
// 刷新
const onRefreshList = () => {
    pageNum.value = 1
    loadMoreData()
}
// 加载更多
const onLoadMore = () => {
    pageNum.value++
    loadMoreData()
}
const loadMoreData = async () => {
    const { code, data, total } = await integralGoodsList({
        pageNum: pageNum.value,
        pageSize: 10,
        systemQueryType: 'h5',
        birthdayQueryFlag: true,
        orderBy: 'create_time',
        params: 'desc',
        userSearchType: true,
    })
    if (pageNum.value === 1) {
        list.value = []
    }
    if (code === 200) list.value = list.value.concat(data || [])
    if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(list.value.length, total)
    }
}
</script>
<style lang="scss" scoped>
.list-view {
    border-radius: 20px 20px 0px 0px;
}
</style>