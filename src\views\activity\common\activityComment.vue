<template>
  <div class="activityComment pb-135px">
    <div class="w-full p-28px border-b-19px border-b-[#f5f5f5] b-b-solid">
      <template v-if="activityDetail.appCover">
        <div class="w-full h-280px mb-20px rounded-14px">
          <img loading="lazy" :src="judgeStaticUrl(activityDetail.appCover)" alt="" class="w-full h-full rounded-14px">
        </div>
      </template>
      <template v-else>
        <div class="w-full h-280px mb-15px rounded-14px flex justify-center items-center text-[#fff] text-50px"
          :style="{ backgroundImage: `url(${defaultCover})`, backgroundSize: '100% 100%' }">
        </div>
      </template>
      <div class="text-[#333] text-[34px] font-500 leading-60px">
        {{ activityDetail?.activityName || '--' }}
      </div>
      <div class="flex justify-between w-full items-center">
        <div class="text-28px text-[#999] flex items-center">
          <img loading="lazy" src="@/assets/activity/icon-readCount.png" alt="" class="w-38px mr-10px">
          {{ activityDetail?.readCount }}
        </div>
        <div
          class="w-125px h-40px rounded-20px border-1px border-[#BF1A05] b-solid text-25px text-[#BF1A05] flex justify-center items-center">
          {{ activityDetail?.progressMsg || '-' }}
        </div>
      </div>
    </div>
    <div class="px-28px pt-30px">
      <div class="text-33px font-550 text-[#333] flex mb-30px">
        <div class="w-10px h-35px bg-[#BF1A05] rounded-5px mr-15px"></div>
        <span>评论</span>
      </div>
      <div>
        <template v-if="commentList && commentList.length">
          <div class="flex pt-30px border-b-1px border-b-[#EBEBEB] b-b-solid" v-for="item in commentList"
            :key="item.commentsId">
            <div class="flex-shrink-0 mr-15px">
              <img loading="lazy" src="@/assets/activity/icon_portrait.png" alt="" class="w-56px h-56px">
            </div>
            <div class="pt-5px flex-1 ">
              <div class="flex items-center mb-20px flex-1 justify-between">
                <div class="text-31px text-[#333] font-550  leading-36px">
                  {{ item.userName }}
                </div>
                <div class="text-24px text-[#B3B3B3]">{{ item.createTime }}</div>
              </div>
              <div class="text-28px text-[#666] leading-39px pb-30px ">
                {{ item.content }}
              </div>
            </div>
          </div>
        </template>
        <van-empty description="暂无评论" v-else />
      </div>
    </div>
    <div class="flex px-28px fixed left-0 bottom-0 justify-between w-full h-105px items-center footer">
      <van-field v-model="message" label="" left-icon="smile-o" placeholder="输入评论"
        class="mr-20px !bg-[#F5F5F5] h-70px flex items-center !py-0 rounded-35px" />
      <Button class="flex-shrink-0 send-btn" @click="onSend" name="发送"></Button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import { useUserStore } from "@/store/modules/user";
import { showToast } from 'vant';
import { activityValidator } from '@/hooks/useValidator.js';
import { addComment, getCommentList } from '@/api/activity.ts'
import Button from '@/components/Button/button.vue';
import defaultCover from '@/assets/activity/default_cover.jpg';
const useStore = useUserStore();
import { judgeStaticUrl } from '@/utils/utils';
const message = ref('')
const pageSize = 50
const pageNum = ref(1)
const commentList = ref([])

const activityDetail = computed(() => useStore.activityDetail || {});

// 生命周期钩子
onMounted(() => {
  if (activityDetail.value) {
    getCommentLists();
  }
});

// 方法
const onSend = async () => {
  if (!activityValidator()) return;
  if (!message.value.length) {
    showToast({
      message: '请输入内容',
      duration: 1500,
      icon: 'warning-o',
    });
  } else {
    const res = await addComment({
      activityId: activityDetail.value.activityId,
      content: message.value,
    });
    if (res.code == 200) {
      showToast({
        message: '发送成功，请等待管理员审核',
        duration: 2000,
        icon: 'success',
      });
      message.value = '';
    } else {
      showToast({
        message: res.message,
      });
    }
  }
};

// 获取评论列表
const getCommentLists = async () => {
  const res = await getCommentList({
    activityId: activityDetail.value.activityId,
    state: 'pass',
    pageSize: 0,
    orderBy: 'create_time',
    sortType: 'desc',
  });
  if (res.code == 200) {
    commentList.value = res.data || [];
  }
};
</script>

<style lang="scss" scoped>
.activityComment {
  .footer {
    box-shadow: 0px 0px 8px 0px rgba(204, 204, 204, 0.31);
  }

  .send-btn {
    width: 118px;
    height: 69px;
    background: linear-gradient(90deg, #FD705E, #FDA778);
    // box-shadow: 0px 7px 11px 2px rgba(96, 16, 0, 0.5);
    border-radius: 35px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    font-size: 28px;
    color: #FFFFFF;
    padding: 0;
    border: 0;
  }
}
</style>
