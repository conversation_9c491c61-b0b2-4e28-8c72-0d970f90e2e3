<template>
    <div class="square bg-[#F5F5F5] flex flex-col">
        <div class="tabs h-125px bg-#fff flex items-center justify-between px-34px sticky top-0 z-999">
            <van-tabs v-model:active="active" class="flex-1" @change="changeTab">
                <van-tab v-for="item, index in tabs" :name="item.value" :title="item.title"></van-tab>
            </van-tabs>
            <div class="add-btn text-#fff text-30px w-50px leading-none
            h-50px rounded-25px flex items-center justify-center" v-if="myInfo.singleStatus != 'y'"
                @click="toPage('/friendship/pushDynamic')">
                <van-icon name="plus" />
            </div>
        </div>
        <div class="list mt-12px px-30px bg-#fff py-40px relative z-1 flex-1">
            <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
                <dynamicsCell v-for="item, index in list" @handleRefresh="refreshPart(index, true)" :detailObj="item"
                    :singleStatus="myInfo.singleStatus" :key="index" @click="toDetail(item, index)">
                </dynamicsCell>
            </refreshList>
        </div>
    </div>
</template>
<script setup lang="ts">
defineOptions({
    name: 'FriendshipSquare'
})
import dynamicsCell from '../components/dynamicsCell.vue';
import refreshList from '@/components/refreshList/index.vue';
import { getDynamicsList, queryMyDetail } from '@/api/friendShip/index'
import { useFriendShipStore } from '@/store/modules/friendShip.ts'
import useRefreshFun from '@/hooks/app.ts'
const friendShipStore = useFriendShipStore()
const active = ref('all')
const tabs = ref([
    {
        title: '全部',
        value: 'all'
    },
    {
        title: '关注',
        value: 'like'
    },
])

const pageNum = ref(1);
const loadMoreRef = ref()
const list = ref<any>([]);
const pageSize = 10
const changeTab = () => {
    if (loadMoreRef.value) loadMoreRef.value.resetStatus()
    onRefreshList()
}
const onLoadMore = () => {
    pageNum.value++;
    getMyDynamics()
}
const onRefreshList = () => {
    pageNum.value = 1;
    getMyDynamics();
};
const getMyDynamics = async () => {
    const { data, code, total } = await getDynamicsList({
        pageNum: pageNum.value,
        pageSize,
        queryType: active.value
    })
    if (code === 200) {
        if (pageNum.value === 1) list.value = data;
        else list.value = [...list.value, ...data];
        if (active.value == 'like') {
            list.value.map(item => {
                item.likeFlag = true
            })
        }
    }
    if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(list.value.length, total);
    }
}

// 局部刷新
const refreshPart = (index: number, refresh: boolean = false) => {
    let currenPageNum = Math.floor(index / pageSize) + 1
    if (!refresh) return friendShipStore.setdynamicPageNum(currenPageNum)
    refreshHandle(currenPageNum)
}
const refreshHandle = (currenPageNum: number) => {
    if (currenPageNum > 1) {
        let endIndex = ((currenPageNum - 1) * pageSize) - 1
        list.value = list.value.slice(0, endIndex)
    }
    pageNum.value = currenPageNum
    getMyDynamics()
}

// 其他页面触发刷新执行事件
const savePageNum = computed(() => friendShipStore.getDynamicsPageNum)
const changePage = () => {
    refreshHandle(savePageNum.value)
}
const { refresh } = useRefreshFun()
refresh([
    { name: 'changePage', funsName: [changePage] }
])
// end


const router = useRouter()
const toPage = (path: string, query: {}) => {
    router.push({
        path,
        query
    })
}

const toDetail = (item: any, index: number) => {
    // 存储当前动态
    refreshPart(index)
    router.push({
        path: '/friendship/dynamicDetail',
        query: {
            postId: item.postId
        }
    })
}
const myInfo = ref({})
const getMyDetail = () => {
    queryMyDetail().then(res => {
        if (res.code == 200) {
            myInfo.value = res.data
        }
    })
}
onMounted(() => {
    getMyDetail()
    getMyDynamics();
})
</script>
<style scoped lang="scss">
.tabs {
    .add-btn {
        background: linear-gradient(90deg, #FF8635, #FF4055);
    }

    :deep(.van-tabs--line .van-tabs__wrap) {
        height: 125px;
    }

    :deep(.van-tabs__line) {
        width: 40px;
        height: 6px;
        background: linear-gradient(86deg, #5AA4FF 0%, #C7E0FF 100%);
        border-radius: 3px;
    }

    :deep(.van-tabs__nav--line) {
        height: 100%;
        padding-bottom: 15px;
    }

    :deep(.van-tab) {
        line-height: 1;
        height: 100%;
        font-size: 30px;
        position: relative;
        z-index: 999;
    }

    :deep(.van-tab--active) {
        font-size: 32px;
    }
}
</style>