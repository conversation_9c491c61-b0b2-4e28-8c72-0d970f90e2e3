<template>
    <div class="ali-detail-main relative">
        <img loading="lazy" src="@/assets/leaderBoard/ali/banner.png" class="w-full banner absolute top-0 z-1" />
        <div class="relative z-2 mt-162px px-30px content flex flex-col pb-50px border-box">
            <dateSelect :dateTypeArr="dateTypeArr" :defaultType="currentIndex" v-model:default-time="currentDate"
                @changeType="changeType"></dateSelect>
            <!-- 活跃度指数 -->
            <div class="active-index text-center mt-44px w-full h-204px 
                flex items-center justify-center relative z-2 py-40px border-box">
                <div>
                    <div class="num text-40px text-#63A8FF font-bold leading-none" style="font-family: Arial;">999.17
                    </div>
                    <div class="text-#333 text-28px mt-26px leading-none">市本级ALI(活跃度指数)</div>
                </div>
            </div>
            <!-- 指数卡片 -->
            <indexCard v-for="item, index in indexArr" :key="index" :card-item="item"></indexCard>
            <!-- 历史趋势统计 -->
            <div class="echarts py-38px px-30px rounded-20px mt-24px">
                <div class="header flex items-center justify-between text-#090A0B">
                    <div class="flex items-center">
                        <img loading="lazy" src="@/assets/leaderBoard/ali/icon_history.png" class="w-36px h-36px" />
                        <div class="ml-14px">历史趋势</div>
                    </div>
                    <div class="select py-6px px-16px rounded-30px text-#6EAEFC" @click="showPickerFn">
                        <span class="mr-20px text-26px">ALI</span>
                        <van-icon name="arrow-down" color="#6EAEFC"></van-icon>
                    </div>
                </div>
                <div class="mt-20px w-full h-500px" ref="echartRef" id="aliTrend"></div>
            </div>
        </div>
        <van-popup v-model:show="selectShow" position="bottom">
            <van-picker title="选择" :columns="columns" @confirm="onConfirmSelect" @cancel="selectShow=false"/>
        </van-popup>
    </div>
</template>
<script setup lang="ts">
import { useECharts } from '@/utils/useECharts'
import * as echarts from 'echarts'
import dateSelect from './components/dateSelect.vue'
import indexCard from './components/indexCard.vue'

const dateTypeArr = ref([
    {
        label: '年榜',
        value: '1'
    },
    {
        label: '月榜',
        value: '2'
    }
])
const currentMonth = new Date().getMonth() + 1 < 10 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1
const currentDate = ref([new Date().getFullYear(), currentMonth])
const currentIndex = ref(0)
const changeType = (val: any) => {
    currentIndex.value = val

}
const indexArr = ref([
    {
        title: '用户活跃度指数',
        icon: 'lively',
        data: [
            {
                rank: 3,
                title: '用户访问量',
                num: 136765,
                progress: 40
            }
        ]
    },
    {
        title: '赋能指数',
        icon: 'fn',
        data: [
            {
                rank: 3,
                title: '用户访问量',
                num: 136765,
                progress: 40
            }
        ]
    },
    {
        title: '传播指数',
        icon: 'spread',
        data: [
            {
                rank: 3,
                title: '用户访问量',
                num: 136765,
                progress: 40
            }
        ]
    },
    {
        title: '峰值传播指数',
        icon: 'fz',
        data: [
            {
                rank: 3,
                title: '用户访问量',
                num: 136765,
                progress: 40
            }
        ]
    },
    {
        title: '单篇最高阅读量',
        icon: 'read',
        data: [
            {
                rank: 3,
                title: '用户访问量',
                num: 136765,
                progress: 40
            }
        ]
    }
])

const echartRef = ref<HTMLDivElement | null>(null)
const { setOptions } = useECharts(echartRef as Ref<HTMLDivElement>)
onMounted(() => {
    initChart()
})
const initChart = () => {
    let data = {
        x: [
            '2025.3.1', '2025.3.2', '2025.3.3', '2025.3.4', '2025.3.5',
            '2025.3.6'
        ],
        y: [
            '1411', '1026', '1200', '1400', '1008', '1411', '1200', '1400', '1008', '1411'
        ]
    }
    // 绘制图表
    var option = {
        tooltip: {
            show: true,
            trigger: 'axis',
            formatter: (params: any) => {
                return `指数：${params[0].value}<br>ALIl:无<br>全省最高值：无<br>排名：10`;
            },
        },
        grid: {
            top: '15%',
            left: '0%',
            right: '0%',
            bottom: '0%',
            containLabel: true,
        },
        legend: {
            show: true,
            icon: 'square',
            orient: 'horizontal',
            top: '0%',
            right: '5%',
            width: '100%',
            itemWidth: 10,
            itemHeight: 10,
            itemGap: 15,
            textStyle: {
                color: '#666666',
                fontSize: 12,
            },
        },
        xAxis: [
            {
                type: 'category',
                splitLine: {
                    show: false,
                },
                axisTick: {
                    show: false,
                },
                splitArea: {
                    show: false,
                },
                axisLine: {
                    show: false,
                },
                boundaryGap: true,
                axisLabel: {
                    show: true,
                    interval: 0,
                    // rotate: 30,
                    fontSize: 12,
                    lineHeight: 15,
                    // margin: 10,
                    color: '#999',
                    // hideOverlap: true,
                },
                data: data.x,
            },
        ],
        yAxis: [
            {
                name: '数量',
                nameTextStyle: {
                    color: '#999',
                },
                type: 'value',
                min: 0,
                minInterval: 1,
                splitLine: {
                    show: true,
                },
                axisLine: {
                    show: false,
                    lineStyle: {
                        color: '#E6E6E6',
                    },
                },
                axisLabel: {
                    // margin: 20 / 1920 * e,
                    textStyle: {
                        color: '#999',
                    },
                },
                axisTick: {
                    show: false,
                },
            },
        ],
        dataZoom: [
            {
                // 设置滚动条的隐藏与显示
                show: false,
                // 数据窗口范围的起始数值
                startValue: 0,
                // 数据窗口范围的结束数值（一页显示多少条数据）
                endValue: 4,
            },
            {
                // 没有下面这块的话，只能拖动滚动条，
                // 鼠标滚轮在区域内不能控制外部滚动条
                type: 'inside',
                // 滚轮是否触发缩放
                zoomOnMouseWheel: false,
                // 鼠标滚轮触发滚动
                moveOnMouseMove: true,
                moveOnMouseWheel: true,
            },
        ],
        series: [
            {
                name: '',
                type: 'line',
                smooth: true,
                symbol: 'circle',
                showSymbol: false,
                z: 1,
                label: {
                    show: false,
                },
                lineStyle: {
                    width: 2,
                    shadowColor: 'rgba(48,224,189,0.3)',
                    shadowBlur: 2,
                    shadowOffsetY: 3
                },
                itemStyle: {
                    normal: {
                        color: '#4C99F7',
                    },
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [{
                        offset: 0,
                        color: '#fff'
                    },
                    {
                        offset: 1,
                        color: '#B4DFFB'
                    }
                    ])
                },
                data: data.y,
            },
        ],
    }
    setOptions(option)
}

const selectShow = ref(false)
const showPickerFn = () => {
    selectShow.value = !selectShow.value
}
const columns = ref([
    { text: '杭州', value: 'Hangzhou' },
    { text: '宁波', value: 'Ningbo' },
    { text: '温州', value: 'Wenzhou' }
])
const onConfirmSelect = (value: any) => {
    console.log(value)

    selectShow.value = false
}
</script>
<style lang="scss" scoped>
.ali-detail-main {
    width: 100%;
    min-height: 100vh;
    background: linear-gradient(to bottom, #D8E9FE 0%, #F6FBFF 20%, #F6FBFF 80%, #DCEEFB 100%);
    box-sizing: border-box;
    position: relative;

    .content {
        height: calc(100% - 162px);
    }

    .active-index {
        background-image: url('@/assets/leaderBoard/ali/alibg.png');
        background-size: 100% auto;
    }

    .echarts {
        background: linear-gradient(0deg, #FFFFFF 54%, #E5F3FF 100%);

        .select {
            border: 2px solid #6EAEFC;
        }
    }
}
</style>