import { KeepAlive } from 'vue';

export default [
  {
    path: '/video',
    name: 'video',
    component: () => import('@/views/video/video.vue'),
    meta: {
      title: '视频专区',
      isShowTabBar: false,
      isBack: true,
      keepAlive:true,
      updatePath:['/home']
    },
  },
  {
    path: '/video-playback',
    name: 'videoPlayback',
    component: () => import('@/views/video/playback.vue'),
    meta: {
      title: '视频详情',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/video-personalCenter',
    name: 'videoPersonalCenter',
    component: () => import('@/views/video/personalCenter.vue'),
    meta: {
      title: '视频个人中心',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/video-uploadWork',
    name: 'videoUploadWork',
    component: () => import('@/views/video/uploadWork.vue'),
    meta: {
      title: '上传作品',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/video-cardinfo',
    name: 'videoCardInfo',
    component: () => import('@/views/video/cardInfo.vue'),
    meta: {
      title: '获奖信息',
      isShowTabBar: false,
      isBack: true,
    },
  },
]