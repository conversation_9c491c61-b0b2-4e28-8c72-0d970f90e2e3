import { defineStore } from 'pinia';
import { store } from '@/store';

interface digitalSchoolState {
    shareDetail?: Object;
}

export const useDigitalSchoolStore = defineStore({
    id: 'app-digitalSchool',
    persist: false,
    state: ():digitalSchoolState => ({
        shareDetail:[]
    }),
    getters: {
        getShareDetail(state) {
            return state.shareDetail || {};
        },
    },
    actions: {
        setShareDetail(info: Object = {},) {
            this.shareDetail=info
        },
    },
});

// Need to be used outside the setup
export function useDigitalSchoolStoreWithOut() {
    return useDigitalSchoolStore(store);
}
