<script setup lang="ts">
import { onMounted, ref } from "vue";
import {
  useRouter,
  useRoute
} from "vue-router";
import backImg from "@/assets/public/back.png";
import utils from "@/utils/utils";
import { showToast } from "vant";

const router = useRouter();
const route = useRoute();

const isApp = function () {
  // 通过ua标识判断
  let userAgent = navigator.userAgent.toLowerCase(); //获取UA信息 
  let env = "";
  let flag = false;
  // console.log(userAgent.indexOf("chuangongzhijia") )
  if (userAgent.indexOf("chuangongzhijia") != -1) { //判断ua中是否含有和app端约定好的标识chuangongzhijia
    //川工之家app内   
    flag = true;
  } else {
    flag = false;
  };
  return flag;
};
//打开服务
const citySercive = (url, name, type, win) => {
  let param = {
    name: name,
    url: url,
    action: type,
    win: win || 'public_nav',
  }
  SendEvent('h5citysub', param)
}
//发送消息事件
const SendEvent = (name, params) => {
  if (isApp()) {
    api.sendEvent({
      name: name, //事件名称
      extra: params //自定义参数 数据格式 字符串或对象
    });
  }
}
const GetQueryString = (name) => {
  let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
  let r = window.location.search.substr(1).match(reg);
  if (r != null) return unescape(r[2]);
  return null;
}
function backBtn() {
  if (isApp() && GetQueryString('win')) {
    citySercive(
      route.query.fullPath,
      route.meta.title,
      route.name == 'newsDetail' ? 'close' : 'back',
      GetQueryString('win'))

  } else {
    router.go(-1)
  }

}
const offset = ref({ x: -window.innerWidth, y: window.innerHeight - 200 });

</script>

<template>
  <div>
    <van-floating-bubble v-model:offset="offset" axis="xy" magnetic="x" :icon="backImg" :gap="5"
      class="floating-bubble-back" @click="backBtn()" />
  </div>
</template>
