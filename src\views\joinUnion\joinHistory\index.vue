<template>
  <div
    :class="$style['join-history']"
    class="box-border"
  >
    <van-tabs
      v-model:active="active"
      title-active-color="#59a4ff"
      color="#59a4ff"
      @change="handleChange"
    >
      <van-tab
        v-for="item in stateArr"
        :title="item.label"
        :name="item.value"
      >
      </van-tab>
    </van-tabs>
    <refreshList
      @onRefreshList="onRefreshList"
      @onLoadMore="onLoadMore"
      ref="loadMoreRef"
    >
      <div v-for="(item,index) in list" :key="index"class="m-30px rounded-20px bg-[#fff] shadow-drop-2-center"@click="handleDetail(item)" >
        <div class="flex justify-between text-[30px] p-[var(--van-cell-group-inset-padding)] py-16px items-center" >
          <span class="text-[#2f2f2f] font-bold">{{ item.createTime || '' }}</span>
          <span :class="item.auditState=='return'?'text-#E72740':item.auditState=='pass'?'text-#5DD1AE':'text-#5AA4FF'">{{
            fixState(item)?.dictName || ''
          }}</span>
        </div>
        <div :style="{ border: '1px solid #f4f4f4' }" class="mx-32px"></div>
          <van-form :readonly="true"  input-align="right" >
            <van-cell-group inset>
              <van-field  v-model="item.nickname" name="nickname" label="申请人"  />
              <van-field v-model="item.phone" name="phone" label="联系电话" />
              <van-field v-model="item.identityNumber"  name="identityNumber"  label="身份证号"  />
            </van-cell-group>
          </van-form>
           <!-- 取消申请 -->
          <div v-if="item?.auditState=='wait'" :style="{ 'border-top': '1px solid #f4f4f4' }" class="p-21px flex justify-end w-full box-border">
            <div  class="w-[162px] h-[50px] rounded-[25px] justify-center flex items-center text-[30px] text-[#fff]" style="background: linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%);" @click.stop="cancel(item)">取消申请</div> 
          </div>
     </div>
    </refreshList>
    <div class="flex items-center justify-between w-[100%] px-[90px] box-border fixed bottom-40px left-1/2 -translate-x-1/2">
        <van-button
          block
          class="w-[260px] h-[76px] rounded-[39px] justify-center flex items-center text-[30px] text-[#fff]  mr-[30px]"
          style="background: linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%);" @click="check('scan')"
        >
        扫码入会
      </van-button>
      <van-button
        block
        class="w-[260px] h-[78px] rounded-[39px] justify-center bg-#CFE2FA flex items-center text-[30px] text-[#5AA4FF] border-none"
         @click="check('join')"
      >
        我要入会
      </van-button>
      </div>
      <Popup :show="Data.showPop" :titleName="'取消原因'" :placeHolder="'请输入取消原因'" @submit-content="joinContent" @close-popup="closePopup"/>
  </div>
</template>

<script lang="ts" setup>
import { list as apiList ,scanQRCodeCheck,cancelApply} from '@/api/joinUnion';
import refreshList from '@/components/refreshList/index.vue';
import { useDictionary } from '@/store/modules/dictionary';
import { concat } from 'lodash-es';
import { useUserStore } from '@/store/modules/user';
import { showFailToast, showToast } from 'vant';
import utils from '@/utils/utils';
const useStore = useUserStore();
const router = useRouter();
import Popup from '@/components/Popup/popup.vue';
const Data=ref({
  showPop:false,
  objData:null
})
const dictionary = useDictionary();

const active = ref<string>('');

const stateArr = computed(() => [
  { label: '全部', value: '' },
  ...dictionary.getDictionaryOpt?.['comAuditStateShow'].filter(item => item.value !== 'refuse'),
]);

const list = ref<Recordable>([]);

const pageNum = ref<number>(1);

//获取列表
const loadMoreRef = ref<any>();

const fixState = (item: Recordable) =>
  dictionary.getDictionaryMap?.[`comAuditStateShow_${item.auditState}`];

const onLoad = () => {
  apiList({
    auditState: unref(active) ? unref(active) : undefined,
    pageSize: 10,
    pageNum: unref(pageNum),
  }).then(({ data, total = 0 }) => {
    if (unref(pageNum) === 1) list.value = [];

    list.value = concat(unref(list), data);

    unref(loadMoreRef)?.onLoadSuc(unref(list)?.length, total);
  });
};
//确认提交
function joinContent(val) {
    if(!val){
        showToast({
            message:"请输入取消原因",
            icon: 'none',
        })
        return
    }
    cancelApply({
      autoId:Data.value.objData.autoId,
      auditState:"cancel",
      auditRemark:val
  }).then(res=>{
    if(res.code==200){
      showToast('取消成功');
      Data.value.showPop=false;
      onLoad()
    }else{
      showFailToast(res.message)
    }
  })
}
   

//关闭
function closePopup(){
    Data.value.showPop=false;
}
//取消申请
function cancel(item) {
  Data.value.showPop=true;
  Data.value.objData=item;
  
}
// 加载更多
const onLoadMore = () => {
  pageNum.value++;
  onLoad();
};

const onRefreshList = () => {
  pageNum.value = 1;
  onLoad();
};

function handleChange() {
  unref(loadMoreRef)?.resetStatus();
  onRefreshList();
}

// 详情
function handleDetail(record: Recordable) {
  router.push({ path: '/joinHistoryDetail', query: { autoId: record.autoId } });
}
//扫码入会校验
function check(type) {
  scanQRCodeCheck({
    unionId:useStore.getUserInfo?.userId
  }).then(res=>{
    if(res.code==200){
      if(type=='scan'){
        utils.scan('USER',[{
        Name:'smrh',
      }])
      }else{
        router.push({path:'/join'})
      }
      
    }else{
      showFailToast(res.message)
    }
  })
}
onMounted(() => {
  onLoad();
});
</script>

<style lang="less" module>
.join-history {
  :global {
    background: #f6f7f8;
    .van-cell {
      width: unset !important;
    }

    .van-form {
      .van-cell {
        padding-left: unset !important;
        padding-right: unset !important;
        padding-top: 10px;
        padding-right: 10px;

        &::after {
          border: unset !important;
        }

        .van-field__label {
          color: #8b8b8b !important;
        }

        .van-field__body {
          color: #333333 !important;
        }
      }
    }
    .btn {
      background: url("@/assets/public/button.png") no-repeat;
      background-size: 100% 100%;
    }
  }
}
</style>
