<template>
    <div class="subject overflow-hidden h-100%  relative">
        <img loading="lazy" src="@/assets/home/<USER>/suject_bg.png" alt="" class="fixed w-100% max-h-100% h-100%">
        <div class="w-full h-100% overflow-hidden p-32px box-border">
            <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
                <div v-for="(item, index) of Data.list" :key="index" class="relative h-240px w-full mb-18px"
                    @click="toPage(item)">
                    <img loading="lazy" :src="item.appBannerPath ? useStore.getPrefix + item.appBannerPath : bannerImg" alt=""
                        class="w-full h-full rounded-15px">
                    <div class="absolute bottom-0 w-full flex py-14px px-24px items-center text-bg box-border">
                        <div class="text-box rounded-16px truncate text-#fff text-28px">{{ item.specialName }}</div>
                        <img loading="lazy" src="@/assets/home/<USER>/arrow.png" alt="" class="24px h-23px">
                    </div>
                </div>
            </refreshList>

        </div>

    </div>
</template>
<script setup lang="ts">
defineOptions({
    name: 'subjectIndex'
})
import router from '@/router';
import { h5SpecialList, encryptUserInfo } from '@/api/news';
import { useUserStore } from '@/store/modules/user';
import refreshList from '@/components/refreshList/index.vue';
import bannerImg from '@/assets/home/<USER>/subject.jpg'
import { showConfirmDialog, showToast } from 'vant';
import utils from '@/utils/utils';
const useStore = useUserStore();
const Data = ref({
    list: [],
    pageNum: 1
})

//获取列表
const loadMoreRef = ref(null);
// 刷新
const onRefreshList = () => {
    Data.value.pageNum = 1;
    getLists();
};
// 加载更多
const onLoadMore = () => {
    Data.value.pageNum++;
    getLists();
};
async function getLists() {
    let res = await h5SpecialList({
        platformType: 30,
        pageNum: Data.value.pageNum,
        pageSize: 10,
        whetherInquireNews: false,
    });
    if (Data.value.pageNum === 1) Data.value.list = [];
    Data.value.list = Data.value.list.concat(res.data);
    //重置刷新状态及 判断是否加载完成
    if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(Data.value.list.length, res.total);
    }
}
onMounted(async () => {
    await getLists();
});
function toPage(item) {
    // "specialType": "custom", //专题类型(自定义专题--custom,系统默认专题--systemDefault)
    if (item.specialType == "custom") {
        if (item.pageType == "defaultTemplate") {
            router.push({
                path: "/subjectList",
                query: {
                    specialId: item.specialId,
                },
            });
            return;
        } else if (item.pageType == "customSinglePage") {
            //跳转方式:builtIn-->> 内置 external-->> 外部,内置项目中打开,外部新开窗口打开
            if (item.skipType == "builtIn") {
                router.push({
                    path: item.appPageLink,
                });
            } else {
                if (item.whetherPrompt == "y") {
                    showConfirmDialog({
                        title: "",
                        message: "是否跳转外部链接",
                    }).then(() => {
                        getUserInfo(item);
                    }).catch((res) => { });
                }
                else {
                    getUserInfo(item);
                }

            }
        }
    }
}
//用户信息加密
function getUserInfo(item) {
    //是否过期
    if (item.whetherExpire) {
        showToast(("授权已过期！"));
    } else {
        getEncryption(item);//获取登录返回的用户信息
    }
}
function getEncryption(item) {
    if (!useStore.getUserInfo?.userId) {
        showToast("请先登录！")
    } else {
        encryptUserInfo({
            recordId: item.specialId,
            userInfo: useStore.getUserInfo,
        }).then(res => {
            if (res.code == 200 && res.data) {
                let url = item.appPageLink; //返回的url
                url += item.appPageLink.indexOf("?") == -1 ? "?" : "&"; //判断是否有问号
                if (utils.isApp()) {
                    utils.citySercive(url + "token=" + res.data, item.specialName, 'open', item.specialId)
                } else {
                    window.location.href = url + "token=" + res.data
                }
            } else {
                if (utils.isApp()) {
                    utils.citySercive(item.appPageLink, item.specialName, 'open', item.specialId)
                } else {
                    window.location.href = item.appPageLink
                }

            }
        })
    }
}
</script>
<style lang="scss" scoped>
.subject {
    // background: url("@/assets/home/<USER>/suject_bg.png") no-repeat;
    // background-size: cover;

    height: 100%;

    .text-bg {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 0px 0px 16px 16px;
    }

    .text-box {
        width: calc(100% - 24px);
    }
}
</style>