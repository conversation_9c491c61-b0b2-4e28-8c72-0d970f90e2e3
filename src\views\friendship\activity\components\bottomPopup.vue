<template>
    <div>
        <van-popup :show="showPop" class="popup1" position="bottom" :style="{ width: '100%', height: '60%' }">
            <div class="pt-47px px-24px box-border w-full h-full ">
                <img loading="lazy" :src="iconClose" alt="" srcset="" class="w-44px h-44px" @click="close">
                <div class="pt38px mb34px h-45vh overflow-scroll">
                    <div v-for="(item, index) in data">
                        <div class="text-#333 text-34px font-500 mb28px">{{ item.name }}</div>
                        <div class="flex flex-wrap ">
                            <div v-for="(el, i) in item.list " class="type mr-30px"
                                :class="i == item.active ? 'type_active' : ''" @click="select(index, i)" :key="i">
                                {{ el.name }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bottom_box flex justify-evenly">
                    <div class="reset_btn flex items-center justify-center" @click="restClick">重置</div>
                    <div class="confirm_btn flex items-center justify-center" @click="confirmClick">确定</div>
                </div>
            </div>
        </van-popup>
    </div>

</template>
<script lang="ts" setup>
import iconClose from "@/assets/public/icon_close.png"
import { ref } from 'vue';
const emit = defineEmits(['restClick', 'confirmClick', 'selectClick']);
import router from "@/router";
const show = ref(false)
function close() {
    show.value = false;
    emit('closePopup', false)
}
const props = defineProps({
    showPop: {
        type: Boolean,
        default: false
    },
    data: {
        type: Array,
        default: [

        ]
    }
})
// const data= ref( [
//             {
//                 name: '区域', list: [
//                     { name: '全部', value: '' },
//                     { name: '南部县', value: '' },
//                     { name: '蓬安县', value: '' },

//                 ]
//             },
//             {
//                 name: '类型', list: [
//                     { name: '全部', value: '' },
//                     { name: '心动约会', value: '' },
//                     { name: '脱单计划', value: '' },
//                     { name: '汇聚良缘', value: '' },

//                 ]
//             },

//         ])

function restClick() {
    emit('restClick')

}
function confirmClick() {
    emit('confirmClick')

}

function select(index, i) {
    emit('selectClick', { parentIndex: index, childIndex: i })

}
function toPage(item) {
    if (item.path) {
        router.push(item.path)
    }
}
</script>
<style lang="scss" scoped>
.van-overlay {
    background: rgba(0, 0, 0, .4);
}

.van-popup {
    border-radius: 36px 36px 0px 0px;
}

.type {
    width: 210px;
    height: 75px;
    background: #F5F5F5;
    border-radius: 12px;
    display: flex;
    font-size: 28px;
    justify-content: center;
    align-items: center;
    margin-bottom: 28px;

    &:nth-child(3n+3) {
        margin-right: 0 !important;
    }
}

.type_active {
    background: #EAF1F9;
    color: #5AA4FF;
}

.bottom_box {
    background: #FFFFFF;
    box-shadow: 0px 0px 12px 0px rgba(204, 204, 204, 0.31);
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding-top: 15px;
    padding-bottom: 56px;

    .reset_btn {
        width: 330px;
        height: 72px;
        background: #EAF1F9;
        border-radius: 36px;
        border: 1px solid #5AA4FF;
        font-family: Source Han Sans CN;
        font-weight: 500;
        font-size: 32px;
        color: #5AA4FF;
    }

    .confirm_btn {
        width: 330px;
        height: 72px;
        background: linear-gradient(-23deg, #68AEFB, #93CFFD);
        border-radius: 36px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        font-size: 32px;
        color: #FFFFFF;
    }
}
</style>