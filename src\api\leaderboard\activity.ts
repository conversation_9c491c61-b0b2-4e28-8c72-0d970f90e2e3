import {h5Http,openHttp}from '@/utils/http/axios'
// 普惠活动开展情况
export const inclusiveActivityInfo = (params:any) => {
    return h5Http.get({
        url:'/dataSummary/inclusive/byAreaName',
        params
    })
}
// 工会优惠券发放情况
export const unionCouponInfo = (params:any) => {
    return h5Http.get({
        url:'/dataSummary/inclusive/coupon',
        params
    })
}

// ====活动====
// 工会活动统计
export const unionActivityInfo = (params:any) => {
    return h5Http.get({
        url:'/dataSummary/activity/index',
        params
    })
}
// 工会活动类型统计
export const unionActivityType = (params:any) => {
    return h5Http.get({
        url:'/dataSummary/activity/byActivityMode',
        params
    })
}
// 区域会员行为
export const memberBehavior = (params:any) => {
    return h5Http.get({
        url:'/dataSummary/activity/behaviorByArea',
        params
    })
}
// 会员行为趋势统计
export const memberBehaviorTrend = (params:any) => {
    return h5Http.get({
        url:'/dataSummary/activity/behaviorByDateRange',
        params
    })
}

// 单个活动
// 数据统计
export const activityStaticData = (params:any) => {
    return h5Http.get({
        url:'/activityInfo/statistics/base',
        params
    })
}
// 活动奖品发放情况
export const activityPrizeInfo = (params:any) => {
    return h5Http.get({
        url:'/activityInfo/statistics/prize',
        params
    })
}
// 活动新增用户趋势
export const activityNewUserTrend = (params:any) => {
    return h5Http.get({
        url:'/activityInfo/statistics/newUser',
        params
    })
}
// 区域会员参与情况
export const activityMemberParticipation = (params:any) => {
    return h5Http.get({
        url:'/dataSummary/activity/byAreaName',
        params
    })
}
// 每日活动访问情况
export const activityEveryDayVisit = (params:any) => {
    return h5Http.get({
        url:'/activityInfo/statistics/byDate',
        params
    })
}
// ====end====

// ====普惠商家====
// 普惠商户入驻&&类型统计
export const inclusiveMerchantInfo = (params:any) => {
    return openHttp.get({
        url:'/openDataSummary/merchant/base',
        params
    })
}
// ====end====