import { useUserStore } from '@/store/modules/user';
import { showConfirmDialog, showDialog, showFailToast, showToast,showImagePreview } from 'vant';
import { useDictionary } from '@/store/modules/dictionary';
import router from '@/router';
import { getPrefix } from '@/api/login';
export const isApp = function () {
  // 通过ua标识判断
  let userAgent = navigator.userAgent.toLowerCase(); //获取UA信息
  let env = '';
  let flag = false;
  // console.log(userAgent.indexOf("chuangongzhijia") )
  if (userAgent.indexOf('chuangongzhijia') != -1) {
    //判断ua中是否含有和app端约定好的标识chuangongzhijia
    //川工之家app内
    flag = true;
  } else {
    flag = false;
  }
  return flag;
};
/*
 判断是不是uniApp 环境
**/ 
const isUniapp = function() {
  // 通过ua标识判断
  let userAgent = navigator.userAgent.toLowerCase(); //获取UA信息 
  let flag = false;
  
  if (userAgent.indexOf("cgzjuni") != -1) { //判断ua中是否含有和app端约定好的标识chuangongzhijia
      //川工之家app内   
      flag = true;
  } else {
      flag = false;
  };
  return flag;
};
// 获取链接url传参
const getUrlKey = name => {
  return (
    decodeURIComponent(
      (new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || [
        ,
        '',
      ])[1].replace(/\+/g, '%20')
    ) || null
  );
};
const newsAction = (apiParams, eventCode) => {
  const useStore = useUserStore();

  let name = 'h5news';
  let win = +new Date();
  let winStr = +new Date() + apiParams.dataId;
  let protocolAndHost = window.location.protocol + '//' + window.location.host;
  if (getUrlKey('win')) {
    win = getUrlKey('win');
    if (win) {
      win = win; //+ winStr;
    } else {
      win = winStr;
    }
  } else {
    win = winStr;
  }
  //判断是否登陆
  let isLogin = false;
  if (useStore.getUserInfo?.userId) {
    isLogin = true;
  } else {
    isLogin = false;
  }

  let params = {
    win: getUrlKey("win")||apiParams.dataId, // win, //窗口标识 多级页面打开时必传，将附在地址栏作为参数
    appkey: "9f247cb68b62459dabc17f398eddc918", //授权码，必填	全省通用接口授权码
    eventcode: eventCode,
    title: apiParams.title, //标题，必填
    url: apiParams.source=='act'?`${protocolAndHost}/activityHome/activityDetail?activityId=${apiParams.dataId}`:`${protocolAndHost}/newsDetail?newsId=${apiParams.dataId}&showTool=${eventCode=='share'?'0':'1'}`, //地址，必填
    dataid: apiParams.dataId, //数据id	字符串，非空将增加工分并回调分享通知
    uid: apiParams.uid, //工会id	新闻所属工会
  };
  SendEvent(name, params); //给app发送消息
};
//发送消息事件
const SendEvent = (name, params) => {
  if (isApp()) {
    api.sendEvent({
      name: name, //事件名称
      extra: params, //自定义参数 数据格式 字符串或对象
    });
  }
};
/**
 *打开外链 (活动等模块)
 *@param title     外链标题
 *@param url       外链地址
 *@param name      外链名字
 *@param shareUrl  外链分享的地址
 *@param id        外链要传的id
 *@param fail      打开失败
 *@param parameter 参数
 *@param isIframe  是否用（内部）iframe打开
 * */
const openOuterChain = function (title, url, name, shareUrl, id, fail, parameter, isTip = true) {
  if (isApp()) {
    //app中打开
    // OpenOtherAcitivity(title, url, name, shareUrl, id, fail, parameter);
  } else {
    //浏览器中打开
    location.href = url;
  }
};
/**
 *
 * 在川工之家APP中通过发送事件的方式打开新闻详情
 *
 * @param apiParams.title       必填    新闻的标题
 * @param apiParams.url         必填    新闻的地址
 * @param apiParams.shareTitle  必填    新闻的分享标题
 * @param apiParams.shareUrl    必填    新闻的分享地址
 * @param apiParams.dataId      必填    新闻的数据id
 * @param apiParams.isOper      必填    是否操作 展示底部悬浮的可操作点赞收藏区域（目前针对外链）
 * @param isExternal            必填    新闻的外部链接
 *
 * apiParams = {title:"",url:"",shareTitle:"",shareUrl:"",dataId:"",type:1}
 * ***********/

const openNewsLink = (apiParams, isExternal, code) => {
  
  const useStore = useUserStore();

  let name = 'h5news';
  let win = +new Date();
  let winStr = +new Date() + apiParams.dataId;
  let protocolAndHost = window.location.protocol + '//' + window.location.host;
  
  //判断是否登陆
  let isLogin = false;
  if (useStore.getUserInfo?.userId) {
    isLogin = true;
  } else {
    isLogin = false;
  }

  if (!isExternal) {
    apiParams.url = protocolAndHost + '/newsDetail';
    apiParams.shareUrl = protocolAndHost + '/newsDetail';
  }
  let params = {
    win: apiParams.dataId, //窗口标识 多级页面打开时必传，将附在地址栏作为参数
    appkey: '9f247cb68b62459dabc17f398eddc918', //授权码，必填	全省通用接口授权码  南充频道9f247cb68b62459dabc17f398eddc918
    title: apiParams.title, //标题，必填
    url: `${apiParams.url}?newsId=${apiParams.dataId}&categoryCode=${code}&showTool=${getUrlKey('showTool')?getUrlKey('showTool'):'1'}`, //地址，必填
    sharename: apiParams.shareTitle, //分享标题
    shareurl: `${apiParams.url}?newsId=${apiParams.dataId}&categoryCode=${code}&showTool=0`, //分享地址	    非空具有分享功能
    dataid: apiParams.dataId, //数据id	字符串，非空将增加工分并回调分享通知
    isoper: apiParams.isOper ? 1 : 0, //是否操作 0否  1是   展示底部悬浮的可操作点赞收藏区域（目前针对外链）  暂时注释源代码，传0
    backimg: apiParams.isOper ? protocolAndHost + '/assets/public/back.png' : '', ////返回按钮图片地址
    uid: apiParams.uid, //工会id	新闻所属工会
    islogin: isLogin ? 1 : 0, //是否登陆 0否  1是  用于多及页面唤起登陆成功后的状态同步
  };
  if (isApp()) {
    SendEvent(name, params); //给app发送消息
  } else {
    if (isExternal) {
      openOuterChain(
        apiParams.title,
        apiParams.url,
        apiParams.shareTitle,
        apiParams.shareUrl,
        null,
        null,
        true
      ); //打开新闻外链
    } else {
      router.push({
        path: '/newsDetail',
        query: {
          newsId: apiParams.dataId,
          categoryCode: getUrlKey('categoryCode'),
          showTool:getUrlKey('showTool')?getUrlKey('showTool'):'1'
        },
      });
    }
  }
};
//打开活动链接
const openActLink = (apiParams) => {
  let protocolAndHost = window.location.protocol + '//' + window.location.host;
  let name = 'h5activity';
  let win = +new Date();
  let params = {
    win: apiParams.win?apiParams.win:getUrlKey("win")||Math.floor(Math.random() * 100), //窗口标识 多级页面打开时必传，将附在地址栏作为参数
    appkey: "9f247cb68b62459dabc17f398eddc918", //授权码，必填	全省通用接口授权码  南充频道9f247cb68b62459dabc17f398eddc918
    title: apiParams.title,
    url: apiParams.url,
    sharename: apiParams.shareName, //'分享标题'
    shareurl: apiParams.shareurl, //'分享地址'
    dataid: apiParams.dataid,
    t: apiParams.type,//类型 1-工会活动，2-普惠活动
    uid: apiParams.companyId,
  };
  if (isApp()&&apiParams.isExternal=='n') {
    params.url = protocolAndHost + apiParams.url ;
  }
  
  if (isApp()) {
    SendEvent(name, params); //给app发送消息
  } else {
    // router.push(apiParams.url)
    if(apiParams.isExternal=='y'){
      window.location.href=apiParams.url
    }else{
      router.push({
        path: apiParams.url,
        // query:{
        //   activityId:apiParams.id
        // }
      });
    }
   
  }
};
// 普通分享
const SharedWorker = (name:any, url:any,callback:any) => {
  let param = {
    name: name,
    url: url,
  };
  
  if(isUniapp()){
    SendEvent('shar', param);
    callback(true)

  }
  else if (isApp()) {
    SendEvent('shar', param); //apicloud
    callback(true)
  }
  else {
    showFailToast('请在川工之家App分享');
    callback(false)
  }
};
/**
 *判断资源是服务器资源还是网络资源
 * @param url 资源地址
 */
import headIcon from '@/assets/public/head_default.png';
// import app from '@/hooks/app';
export const judgeStaticUrl = (url: string,avatar = false) => {
  const useStore = useUserStore();
  if(!url){
    return avatar? headIcon:''
  }
  else if (url.indexOf('http') > -1) {
    return url;
  } 
  else{
    return useStore.getPrefix + url;
  } 
}

//打开服务
const citySercive = (url, name, type, win) => {
  let param = {
    name: name,
    url: url,
    action: type,
    title:name,
    win: win || 'public_nav',
  };
  SendEvent('h5citysub', param);
};
/**
 * 获取地址栏参数
 * @param {any} name 参数名称
 */
const GetQueryString = (name) => {
  let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
  let r = window.location.search.substr(1).match(reg);
  if (r != null) return unescape(r[2]);
  return null;
}
//扫码唤起
/**
 *
 * Scan    扫码 调用的川工之家APICloud的方法
 * @param  eventcode 非必传  类型：字符串               扫码事件（Default-无事件[用户扫码时,可为空]，MapPlace-场所预约核销[管理员核销]，UserTrain-培训课程核销[讲师核销]） PrattOrder-普惠订单核销，PrattBooking-普惠订座核销）
 * @param  custparam 非必传  类型：字符串或 JSON 对象   自定义参数（默认为空，具体询开发）   订单、订座扫一扫核销参数：ShopId ，PrattToken
 *
 **/
const scan = (eventcode = 'Default', custparam = null, type = null) => {
  const useStore = useUserStore();

  if (custparam) {
    custparam = JSON.stringify(custparam);
  } else {
    let obj = [
      {
        Name: 'token',
        Value: useStore.getUserInfo?.userId,
      },
    ];
    custparam = JSON.stringify(obj);
  }
  // let name = "h5qrscan"; //发送消息的名称
  // 2024-07-15 扫码事件名字修改
  let name = 'h5qrscancode';
  let params = {
    appkey: '9f247cb68b62459dabc17f398eddc918',
    eventcode: eventcode,
    custparam: custparam, //根据业务来
  };
  if (isApp()) {
    //app中打开
    //发送扫码消息
    SendEvent(name, params);
  } else {
    let message = '请在川工之家App进行扫码';
    if (type == 'open') {
      message = '请在川工之家App打开';
    }
    //浏览器中打开
    showDialog({
      title: '温馨提示',
      message: message,
      theme: 'round-button',
      confirmButtonText: '我知道了', //按钮文案
      className: 'dialog',
    }).then(() => {
      // on close
    });
  }
};
//定位信息
const getPosition = (win,func:any='') => {
  if (isApp()) {
    try {
      if(typeof func === 'function') window.h5PositionReturn = func
      let param = {
        allkey: true,
        appkey: '9f247cb68b62459dabc17f398eddc918',
        win: 'public_nav',
      };
      SendEvent('h5position', param);
    }
    catch (error) {
      console.log(error)
    }
  } 
  // else {
  //   showToast('请在川工之家App登录后使用');
  // }
};
//导航
const mapNav = params => {
  let param = {
    lat: params.lat,
    appkey: '9f247cb68b62459dabc17f398eddc918',
    win: params.win || 'public_nav',
    lon: params.lon,
    name: params.name,
  };
  if (isApp()) {
    SendEvent('h5mapnavigation', param);
  } else {
    showToast('请在川工之家App登录后使用');
  }
};
//拨打电话
const getTel = params => {
  let param = {
    appkey: '9f247cb68b62459dabc17f398eddc918',
    number: params.number,
  };
  if (isApp()) {
    SendEvent('h5tel', param);
  } else {
    showToast('请在川工之家App登录后使用');
  }
};
/**
  * vantImagePreview 图片预览
  * vant ImagePreview二次封装  
  * @param imagesArr      图片数组 ['https://img01.yzcdn.cn/vant/apple-1.jpg','https://img01.yzcdn.cn/vant/apple-1.jpg']
  * @param startPosition  指定初始位置  
*/
const imagePreview = function (imagesArr, startPosition = 0) {
  showImagePreview ({
      images: imagesArr,
      startPosition: startPosition,
      closeable: true, //展示关闭按钮
  })
};
/**
 * 富文本中存在文件预览
 **/ 
const filePreview = (url:string,fileName='',fileDirectory='') => {
  if(isUniapp()){
      api.sendEvent(
          {
              name: 'filePreview',
              extra:{
                  appkey: "9f247cb68b62459dabc17f398eddc918",
                  url,
                  fileName,
                  fileDirectory
              } //自定义参数 数据格式 字符串或对象
          }
      );
      return
  }
  window.open(url)
}

//打开服务
const openService = params => {
  if (isApp()) {
    SendEvent('h5openservice', {
      ...params,
      appkey: '9f247cb68b62459dabc17f398eddc918',
      mark:params?.MenusIfrmaeName
    });
  } else {
    showToast('请在川工之家App登录后使用');
  }
};
//时间排序
//prop：对象数组排序的键，
//align：排序方式，"positive"正序，"inverted"倒序。
function compare(prop, align) {
  return function (a, b) {
    let value1 = a[prop];
    let value2 = b[prop];
    if (align == 'positive') {
      //正序
      return new Date(value1) - new Date(value2);
    } else if (align == 'inverted') {
      //倒序
      return new Date(value2) - new Date(value1);
    }
  };
}
// 获取数据字典 返回对应label
//dictionaryType：数据字典查询类型
//value:对应值
function getDictionaryLabel(dictionaryType, value) {
const dictionary = useDictionary();
  let label
  try {
      let c = dictionary.getDictionaryOpt?.[dictionaryType].find(t => { return t.value == value })
      label = c.label
  } catch {
      label = value
  }
  return label
}

function isPointInCircle(point1, point2, radius) {
  // point1: 当前位置, point2: 圆心位置, radius: 圆的半径
  const x1 = point1.longitude; // 经度
  const y1 = point1.latitude;  // 纬度
  const x2 = point2.longitude;
  const y2 = point2.latitude;
 
  if(radius===0)return false
  let dx=x2- x1
  let dy= y2- y1
  return dx*dx+ dy*dy<=radius*radius
}
/**
 * 剪切板复制  by denglanlan
 * @param {string} text 文本内容
 **/  
const copyToClibboard = async (text:string) => {
  if (navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
      try {
      await navigator.clipboard.writeText(text);
      showToast({
          message:"已复制",
      })
      } catch (error) {
      showToast({
          message:"复制失败",
      })
      }        
  }else if(document.execCommand("copy")){
      // 创建一个临时的文本输入框
      let tempInput = document.createElement("input");
      // 设置输入框的值为要复制的文本
      tempInput.value = text;
      // 将输入框添加到页面中
      document.body.appendChild(tempInput);
      // 选中输入框中的文本
      tempInput.select();
      // 执行复制命令
      document.execCommand("copy");
      // 移除临时输入框
      document.body.removeChild(tempInput);

      showToast({
          message:"已复制",
      })
  }
}
function formatTimeWithoutSeconds(timeStr) {
  // 正则表达式匹配 "时:分:秒" 并将秒替换为空字符串
  return timeStr?.replace(/(\d{2}):(\d{2}):(\d{2})$/, '$1:$2');
}
const changeRouterKeepAlive = (name: string, keepAlive: boolean) => {
	router.options.routes.map((item: any) => {
    if (item.name === name) {
        item.meta.keepAlive = keepAlive;
    }
	});
}

const isJSON = (str:any) =>{
  if (typeof str == 'string') {
      try {
          let obj=JSON.parse(str);
          if (typeof obj == 'object' && obj) {
              return true;
          } else {
              return false;
          }
      } catch(e) {
          return false;
      }
  }
}

export const isLoginDialog = function (callback:any) {
  let userAgent = navigator.userAgent.toLowerCase(); //获取UA信息
  let isApp = false;
  if (userAgent.indexOf("chuangongzhijia") != -1 || isUniapp()) {
    //判断ua中是否含有和app端约定好的标识chuangongzhijia
    isApp = true;
  } else {
    isApp = false;
  }
  let isLogin = false;
  if (useUserStore()?.getUserInfo?.userId) {
    isLogin = true;
    if (callback) {
      callback(isLogin);
    }
  } else {
    isLogin = false;
    if (callback) {
      callback(isLogin);
    }
    showDialog({
      title: '温馨提示',
      message: '尊敬的用户，您当前未登录，请在川工之家App登录后操作。',
      confirmButtonText: '我知道了',
      lockScroll: false,
    });
    // showToast({ message: `尊敬的用户,您当前未登录，请在川工之家App登录后操作。` });

    // if(!isApp)
    // else{
    //   showConfirmDialog({
    //     title: "登录体验完整功能",
    //     message: "尊敬的用户您好！你暂未登录，请登录后体验完整功能！",
    //     confirmButtonText: "去登录", //确认按钮文案
    //     cancelButtonText: "取消", //取消按钮文案
    //   })
    //     .then(() => {
    //         api.sendEvent({
    //             name: "h5login",
    //             extra:{
    //               appkey: "9f247cb68b62459dabc17f398eddc918",
    //               win: "public_nav",
    //             }
    //         })
    //     })
    // }
  }
}

export const commonBackFn = (route:any) => {
  // 通过外链打开,需要调用app返回方法
  if (isApp() && GetQueryString('win')) {
      citySercive(
          route.query.fullPath,
          route.meta.title,
          route.name == 'newsDetail' ? 'close' : 'back',
          GetQueryString('win')
      )
  }else{
      router.go(-1);
  }
}
/**
* 打开小程序  调用的川工之家APICloud的方法
* @param  name   必传     类型：字符串   事件名 h5applet
* @param  id     必传     类型：字符串   小程序原始 id
* @param  type   非必传   类型：对象     默认null,传值是为了区分是否先用弹窗提示
* @param  path   非必传   类型：字符串   默认空,小程序传参

* @constructor
**/
export const openMiniProgram = (name = "h5applet", id:string, type:any = null,path = '') => {
  if (isApp()){
    const params:any = {
      appkey:'9f247cb68b62459dabc17f398eddc918',
      id: id,
      type: 'release',//小程序的类型，test:开发版    preview:体验版    release:正式版
      win:'public_nav'
    }
    if(path) params.path = path

    if (type) {
      showConfirmDialog({
        title: "提示",
        message: `即将打开${type}小程序，是否继续？`,
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        className: "close",
      }).then(() => {
        SendEvent(name, params)
      })
    }
    else{
      SendEvent(name, params)
    }
  }else{
    showToast('请在川工之家App登录后使用')
  }
};


export default {
  filePreview,
  imagePreview,
  isApp,
  getUrlKey,
  SendEvent,
  openNewsLink,
  newsAction,
  judgeStaticUrl,
  SharedWorker,
  citySercive,
  scan,
  getPosition,
  openActLink,
  mapNav,
  getTel,
  openService,
  compare,
  getDictionaryLabel,
  isPointInCircle,
  copyToClibboard,
  formatTimeWithoutSeconds,
  changeRouterKeepAlive,
  isJSON,
  isLoginDialog,
  GetQueryString,
  commonBackFn,
  openMiniProgram
};
