<template>
    <van-popup v-model:show="props.show" position="bottom" @click-overlay="close">
        <div class="popup-content bg-[#fff] py-50px px-30px">
            <div class="mb-20px" @click="close">
                <img loading="lazy" src="@/assets/public/icon_close.png" class="w-44px" />
            </div>
            <div class="max-h-40vh overflow-scroll">
                <van-checkbox-group v-model="checkedArr" shape="square" class="flex flex-wrap">
                    <van-checkbox :name="it.autoId" v-for="it in labelList" :key="it.autoId">
                        <template #icon="props">
                            <div class="text-#444 text-30px flex items-center justify-center leading-none
                            px-22px py-22px rounded-10px bg-[#F1F6FB] label-btn mt-10px mr-10px"
                                :class="{ 'label-active': props.checked }">
                                {{ it.labelName }}
                            </div>
                        </template>
                    </van-checkbox>
                </van-checkbox-group>
            </div>

            <div class="flex justify-between mt-50px px-30px">
                <div @click="selectType('reset')"
                    class="bg-[#EAF1F9] text-[#5AA4FF] text-32px reset-btn rounded-30px w-44% h-72px leading-72px text-center">
                    重置
                </div>
                <div class="text-32px text-#fff confirm-btn rounded-30px w-44% h-72px leading-72px text-center"
                    @click="selectType('confirm')">确认</div>
            </div>
        </div>
    </van-popup>
</template>
<script setup lang="ts">
import { getLabelList } from '@/api/friendship/profile'
const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    defaultChecked: {
        type: Array,
        default: () => [],
    },
})
watch(() => props.defaultChecked, (val) => {
    checkedArr.value = val
}, { deep: true })

const checkedArr = ref<any>([])
const labelList = ref<any>([])
const emit = defineEmits(['update:show', 'confirmed'])
onMounted(() => {
    getLabels()
})
const getLabels = () => {
    getLabelList({
        pageNum: 1,
        pageSize: 0
    }).then((res: any) => {
        if (res.code === 200) {
            labelList.value = res?.data
        }
    })
}
const selectType = (value: string) => {
    if (value === 'reset') {
        checkedArr.value = []
    }
    const filterChecked = labelList.value.filter((item: any) => checkedArr.value.includes(item.autoId))
    emit('confirmed', filterChecked)
}
const close = () => {
    emit('update:show', false)
}
</script>
<style lang="scss" scoped>
.van-popup {
    background: transparent;
}

.popup-content {
    border-radius: 20px 20px 0px 0px;

    :deep(.van-checkbox__icon) {
        height: fit-content;
        font-size: unset;
        line-height: none;
    }

    :deep(.van-checkbox--horizontal) {
        margin-right: 0;
    }

    :deep(.van-checkbox-group--horizontal) {
        gap: 14px;
    }

    .label-btn {
        border: 1px solid #5AA4FF;
    }

    .label-active {
        background-color: #3F95FF;
        color: #fff;
    }

    .reset-btn {
        border: 1px solid #5AA4FF;
    }

    .confirm-btn {
        background: linear-gradient(-23deg, #68AEFB, #93CFFD);
    }
}
</style>