<template>
    <div class="order-cell w-90% border-box bg-#fff rounded-16px p-28px mx-auto mt-20px mb-20px">
        <div class="order-title pb-30px">
            <div class="flex justify-between ">
                <div class="time text-28px text-#666">{{ content?.companyName }}</div>
                <div class="status text-28px" :style="{ color: orderStatusSetting.statusColor }">
                    {{ content.statusText }}</div>
            </div>
            <div class="text-26px mt-20px text-#666">
                订单号:{{ content.orderId }}
            </div>
        </div>
        <div class="order-goods w-full">
            <template v-if="content?.snapshotVo?.transProductSnapshot?.goodsFlatten?.length < 3">
                <div class="mb-24px w-full" @click="toRawDetail"
                    v-for="goods, index in content?.snapshotVo?.transProductSnapshot?.productInfoList" :key="index">
                    <!--小于3 商品信息 -->
                    <div class="mt-20px flex justify-between" v-for="priceGoods, index1 in goods?.priceListInfo"
                        :key="index1">
                        <div class="cover w-140px h-140px rounded-20px">
                            <img loading="lazy" :src="judgeStaticUrl(priceGoods?.productSubImg)"
                                v-previewImg="judgeStaticUrl(priceGoods?.productSubImg)"
                                class="w-full h-full rounded-20px object-cover">
                        </div>
                        <div class="text-infos flex-1 overflow-hidden ml-24px flex flex-col justify-between">
                            <div class="title text-28px text-#333 flex  justify-between">
                                <div class="text-ellipsis line-clamp-2 whitespace-nowrap text-wrap">{{
                                    goods?.productName }}</div>
                                <div class="ml-10px">
                                    <span class="text-24px">￥</span>{{ priceGoods?.nowPrice }}
                                </div>
                            </div>
                            <div class="productSub flex justify-between items-center 
                                mt-24px text-#999 text-26px w-full">
                                <div class="">{{ priceGoods?.productSubName }}</div>
                                <div>x{{ priceGoods?.currentProductCount }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <template v-else>
                <!-- 大于或等于3 商品信息 -->
                <div class="mt-20px w-full flex items-center" @click="toRawDetail">
                    <div class="overflow-hidden flex-1">
                        <img loading="lazy" :src="judgeStaticUrl(goods?.productSubImg)"
                            v-previewImg="judgeStaticUrl(goods?.productSubImg)"
                            v-for="goods, index in content?.snapshotVo?.transProductSnapshot?.goodsFlatten.slice(0, 3)"
                            :key="index" class="w-140px h-140px rounded-20px object-cover mr-20px" />
                    </div>
                    <div class="text-#999 text-30px">
                        <span>共{{ content?.snapshotVo?.transProductSnapshot?.goodsFlatten?.length }}件</span>
                        <van-icon name="arrow"></van-icon>
                    </div>
                </div>
            </template>
            <!-- 店铺付款金额 -->
            <div class=" prices-info flex justify-end items-center text-26px">
                <span v-if="content?.discountAmount && content?.discountAmount !== '0.00'">优惠金额：<span
                        class="text-#FF4344 font-bold">￥{{ content?.discountAmount }}</span></span>
                <span class="ml-42px">
                    <span>{{ content?.orderState === 'payment' ? '需付款' : '合计：' }}</span>
                    <span class="text-#FF4344 font-bold">￥{{ content?.payAmount }}</span>
                    <span class="text-#FF4344" v-if="content?.payIntegral">+{{ content?.payIntegral }}积分</span>
                </span>
            </div>

            <!-- 操作栏 -->
            <div class="controll-btns flex items-center justify-end pt-18px mt-20px"
                :class="{ 'border-top': orderStatusSetting.orderBtnArr.length > 0 }">
                <div class="text-28px btns default-btn rounded-24px py-12px px-20px 
                leading-none mr-20px" :class="{ 'active-btn': item.type === 'pay' || item.type === 'receive' }"
                    v-for="item, index in orderStatusSetting.orderBtnArr" :key="index" @click.stop="handleBtn(item)">
                    {{ item.text }}
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { judgeStaticUrl } from '@/utils/utils';
const props = defineProps({
    content: {
        type: Object,
        default: () => { }
    }
})
const emit = defineEmits(['toDetail', 'handleControll'])


// 订单状态颜色
// 订单按钮数组:取消订单\立即支付\确认收货\申请退款\去评价

const orderStatusSetting = computed(() => matchSet())
const matchSet = () => {
    let statusColor = ''
    let orderBtnArr: any = []
    let refundBtn: any = []
    // 实际商品才有退款按钮，虚拟商品没有退款按钮

    // 是否已经申请退款 -已申请退款则没有以下按钮信息
    let isRefund = props.content?.snapshotVo?.transProductSnapshot?.goodsFlatten.every((it: any) => it.backResultState)

    if (props.content?.snapshotVo.transProductSnapshot.productType === 'actual') {
        // 商品是不是全部退货了
        if (!isRefund) refundBtn = [{ text: '申请退款', type: 'refund' }]
    }

    switch (props.content?.orderState) {
        // 待支付
        case 'payment':
            statusColor = '#FFA025'
            if (!isRefund) orderBtnArr = [
                { text: '取消订单', type: 'cancel' },
                { text: '立即支付', type: 'pay' }
            ]
            break
        // 待发货
        case 'deliver':
            statusColor = '#5AA4FF'
            orderBtnArr = [
                ...refundBtn
            ]
            break
        case 'receive':
            statusColor = '#5AA4FF'
            if (!isRefund) orderBtnArr = [
                ...refundBtn,
                { text: '确认收货', type: 'receive' }
            ]
            break
        case 'cancel':
        case 'closed':
        case 'expired':
            statusColor = '#999'
            break
        case 'over':
            statusColor = '#333'
            if (!isRefund && props.content?.evaluated === 'n') orderBtnArr = [
                { text: '去评价', type: 'evaluate' }
            ]
            break
        default:
            statusColor = '#333'
            break
    }
    return {
        statusColor,
        orderBtnArr
    }
}

const handleBtn = (item: any) => {
    emit('handleControll', item)
}
// 查看详情
const toRawDetail = () => {
    emit('toDetail')
}
</script>
<style scoped lang="scss">
.order-cell {
    .order-title {
        border-bottom: 1px solid #EFEFEF;
    }

    .controll-btns {
        .default-btn {
            border: 1px solid #999;
            color: #999;
        }

        .active-btn {
            background: linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%);
            color: #fff;
            border: none;
        }

        .btns:last-child {
            margin-right: 0
        }
    }

    .border-top {
        border-top: 1px solid #EFEFEF;
    }
}
</style>