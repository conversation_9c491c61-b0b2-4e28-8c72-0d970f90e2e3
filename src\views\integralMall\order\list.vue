<template>
    <!-- 订单列表 -->
    <div class="order-list flex flex-col">
        <div class="headers px-30px bg-[#F9F9F9] pb-30px">
            <div class="tabs h-85px">
                <van-tabs v-model:active="active" background="transparent" @change="onClickTab">
                    <van-tab v-for="item, index in tabs" :name="item.value" :title="item.title" :key="index"></van-tab>
                </van-tabs>
            </div>
            <div class="order-status flex justify-between mt-37px text-28px">
                <div class="bg-[#F3F1F1] text-[#333] px-35px py-13px
                rounded-25px text-center leading-none" :class="{ 'active-status': orderActive === item.value }"
                    v-for="item, index in orderStatus" :key="index" @click="clickStatus(item.value)">
                    {{ item.title }}
                </div>
            </div>
        </div>
        <div class="list bg-[#F9F9F9] flex-1 px-28px">
            <refreshList ref="loadMoreRef" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" :slotEmpty="true">
                <orderMallCell v-for="item, index in list" :key="index"></orderMallCell>
                <template #noData>
                    <van-empty :image="emptyImg" description="暂时没有订单哦" :image-size="['60%', 'auto']">
                    </van-empty>
                </template>
            </refreshList>
        </div>
    </div>
</template>
<script setup lang="ts">
defineOptions({
    name: 'integralOrderList'
})
import emptyImg from '@/assets/integralMall/no_content.png'
import refreshList from "@/components/refreshList/index.vue";
import orderMallCell from "../components/orderMallCell.vue";
onMounted(() => {
    onRefreshList()
})
const active = ref('1')
const tabs = [
    {
        title: "线上发货",
        value: '1'
    },
    {
        title: "线下兑换",
        value: '2'
    }
]
const orderActive = ref('')
const orderStatus = [
    {
        title: "全部",
        value: ''
    },
    {
        title: "未发货",
        value: '1'
    },
    {
        title: "待收货",
        value: '2'
    },
    {
        title: "已完成",
        value: '3'
    },
]
const onClickTab = () => {
    if (loadMoreRef.value) loadMoreRef.value.resetStatus()
    onRefreshList()
}

const clickStatus = (value: string) => {
    orderActive.value = value
    if (loadMoreRef.value) loadMoreRef.value.resetStatus()
    onRefreshList()
}
const loadMoreRef = ref(null);
const list = ref([])
let pageNum = 1
const onRefreshList = () => {
    pageNum = 1
    getList()
}
const onLoadMore = () => {
    pageNum++
    getList()
}
const getList = async () => {

    try {
        await new Promise((resolve) => {
            if (pageNum === 1) list.value = []
            setTimeout(() => {

                for (let i = 0; i < 15; i++) {
                    list.value.push(list.value.length + 1);
                }
                resolve(true);
            }, 1000)
        })
        //重置刷新状态及 判断是否加载完成
        if (loadMoreRef.value) {
            loadMoreRef.value.onLoadSuc(list.value.length, 20)
        }
    } catch (error) {

    }
}
</script>
<style lang="scss" scoped>
.order-list {
    .headers {
        .tabs {
            :deep(.van-tabs--line .van-tabs__wrap) {
                height: 85px;
            }

            :deep(.van-tab) {
                line-height: 1;
                height: 100%;
            }

            :deep(.van-tabs__nav--line) {
                height: 100%;
                padding-bottom: 15px;
            }

            :deep(.van-tabs__line) {
                background: linear-gradient(86deg, #C7E0FF 0%, #5AA4FF 100%);
                ;
                width: 40px;
                height: 6px;
                bottom: 15px;
                border-radius: 3px
            }

            :deep(.van-tab__text) {
                font-size: 30px;
            }
        }
    }
}

.active-status {
    background-color: #E7F2FF;
    color: #418DEA;
    font-size: 28px;
}
</style>