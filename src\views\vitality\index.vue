<template>
  <div class="bg-[#f5f5f5] min-h-100vh w-100vw flex flex-col">
    <img loading="lazy" :src="bannner" alt="" class="w-100% h-300px" />
    <div class="bg-#fff mt-[-30px] relative rounded-t-[16px] p-28px box-border w-100%">
      <div class="W-100% flex mb-40px">
        <div :class="{ 'mr-22px': ind === 0 }" v-for="(it, ind) in Data.card" :key="it.name"
          :style="{ backgroundImage: `url(${it.bg})` }"
          class="bg-no-repeat bg-cover bg-center flex-[0.5] h-140px rounded-12px flex flex-col justify-between p-27px box-border"
          @click="toJump(it.path)">
          <div class="text-30px font-medium" :style="{ color: it.color }">
            {{ it.name }}
          </div>
          <div class="w-137px h-36px text-22px text-#fff flex justify-center rounded-18px items-center"
            :style="{ background: it.bgClolor }">
            {{ it.tip }}
          </div>
        </div>
      </div>
      <div class="flex justify-between mb-23px">
        <img loading="lazy" src="@/assets/vitality/zgjbz_title.png" alt="" class="h-35px" />
        <div class="text-#999999 text-24px flex items-center" @click="toJump('/vitality/walkActivity')">
          更多活动
          <img loading="lazy" src="@/assets/public/icon_right.png" alt="" class="w-8px h-13px mx-10px" />
        </div>
      </div>
      <div v-if="!Data.jbzActivity?.length" class="h-240px">
        <Empty description="" imageWidth="20vw" />
      </div>
      <div class="flex w-100%" v-else>
        <swiper :autoplay="{
          delay: 2500,
          disableOnInteraction: false,
        }" :loop="true" :modules="Data.modules" key="vitalitySwiper">
          <swiper-slide class="rounded-10px w-100%" v-for="(item, index) of Data.jbzActivity" :key="index"
            @click="tominProgram(item)">
            <img loading="lazy" :src="judgeStaticUrl(item.appCover) || swiper_bannner" alt=""
              class="w-100% h-240px rounded-8px" />
          </swiper-slide>
        </swiper>
      </div>
    </div>
    <div class="flex-1 bg-#fff mt-16px p-28px box-border">
      <div class="flex justify-between mb-23px w-100%">
        <img loading="lazy" src="@/assets/vitality/mlng_title.png" alt="" class="h-35px" />
      </div>
      <refreshList @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
        <newsCell v-for="(item, index) in Data.list" :key="index" :content="item" :showborder-bottom="false"></newsCell>
      </refreshList>
    </div>
  </div>
</template>
<script lang="ts" setup>
import swiper_bannner from "@/assets/activity/tu.png";
import bannner from "@/assets/vitality/banner.jpg";
import newsCell from "@/components/Cell/newsCell.vue";
import Empty from '@/components/Empty/index.vue';
import sql_bg from "@/assets/vitality/sql_bg.png";
import zyfw_bg from "@/assets/vitality/zyfw_bg.png";
import { activityInfoList } from "@/api/activity";
import { useUserStore } from "@/store/modules/user";
const useStore = useUserStore();
import { Swiper, SwiperSlide } from "swiper/vue";

import "swiper/css";
import "swiper/css/scrollbar";
import "swiper/css/pagination";
import { Scrollbar, A11y, Pagination } from "swiper/modules";
import router from "@/router";
import refreshList from '@/components/refreshList/index.vue';
import { getCategoryInfo, getNewsList } from '@/api/news/index';
import { openMiniProgram, isApp, judgeStaticUrl } from "@/utils/utils";
import { getDetails } from '@/api/activity.ts';
const Data = ref({
  card: [
    {
      name: "志愿服务活动",
      tip: "爱心传递",
      bg: zyfw_bg,
      color: "#E63B47",
      bgClolor: "linear-gradient(90deg, #FF6A6A 0%, #FFA391 100%)",
      path: '/vitality/volunteerService'
    },
    {
      name: "送清凉活动",
      tip: "清凉一夏",
      bg: sql_bg,
      color: "#008EDD",
      bgClolor: "linear-gradient(90deg, #0AA0F4 0%, #81C4F9 100%)",
      path: '/vitality/coolSummer'
    },
  ],
  modules: [Scrollbar, A11y, Pagination],
  jbzActivity: [
    // { img: jbz_default_img },
    // { img: jbz_default_img },
    // { img: jbz_default_img },
  ],
  list: [],
  tabData: {},
  pageNum: 1,
  companyId: ''
});
onMounted(() => {
  getActList();
  getColumn();
});
function toJump(path: string) {
  if (!path) return;
  router.push({ path });
}
function getActList() {
  activityInfoList({
    activityCategory: "walk",
    pageSize: 3,
    pageNum: 1,
  }).then((res: any) => {
    if (res.code == 200) {
      Data.value.jbzActivity = res.data;
    }
  });
}
//获取栏目
async function getColumn() {
  // 按照要求注释
  // getCategoryInfo({
  //      categoryCode: 'mei_li_nv_gong',
  //       platformType: 30,
  // }).then(res => {
  //   Data.value.tabData=res.data;

  // });
  getLists();
}
// 刷新
const onRefreshList = () => {
  Data.value.pageNum = 1
  Data.value.list = [];
  getLists()
}
// 加载更多
const onLoadMore = () => {
  Data.value.pageNum++
  getLists()
}
//新闻列表
const loadMoreRef = ref(null)
async function getLists() {
  let res = await getNewsList({
    categoryCode: 'mei_li_nv_gong',
    platformType: 30,
    pageNum: Data.value.pageNum,
    pageSize: 10,
  });
  if (Data.value.pageNum === 1) Data.value.list = [];
  Data.value.list = Data.value.list.concat(res.data);
  //重置刷新状态及 判断是否加载完成
  if (loadMoreRef.value) {
    loadMoreRef.value.onLoadSuc(Data.value.list.length, res.total);
  }
}

const tominProgram = (item: any) => {
  // 对接跳转小程序
  if (item.activityCategory === 'walk' && isApp()) {
    getDetails({ activityId: item.activityId })//用于记录浏览
    const path = `/pages/guidance/guidance?exActiviId=${item.activityId}&token=${useStore.getSaToken}`
    openMiniProgram('h5applet', 'gh_193b1d6dda72', '云上职工健步走', path)
  } else {
    router.push({
      path: "/activityHome/activityDetail",
      query: {
        activityId: item.activityId,
      }
    })
  }
}

// ====end====
</script>