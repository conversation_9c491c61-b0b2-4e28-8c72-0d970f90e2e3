<template>
  <div class="editAddress">
    <van-form ref="formRef">
      <van-field v-model="formData.consignee" name="姓名" label="姓名" placeholder="请输入姓名"
        :rules="[{ required: true, message: '请填写姓名' }]" input-align="right" error-message-align="right" />
      <van-field v-model="formData.consigneeMobile" type="tel" name="电话" label="电话" maxlength="11" placeholder="请输入联系电话"
        input-align="right" error-message-align="right" :rules="[{ required: true, message: '请填写联系电话' }]" />
      <van-field v-model="formData.provinceCode" is-link readonly label="所在地区" placeholder="请选择所在地区"
        :rules="[{ required: true, message: '请选择所在地区' }]" input-align="right" error-message-align="right"
        @click="show = true" />
      <van-popup v-model:show="show" round position="bottom">
        <van-cascader v-model="formData.cascaderValue" title="请选择所在地区" :options="areaList" @close="show = false"
          @finish="onFinish" :fieldNames="fieldNames" />
      </van-popup>
      <van-field v-model="formData.address" rows="1" autosize type="textarea" name="详细地址" label="详细地址" maxlength="100"
        placeholder="请输入详细地址" input-align="right" error-message-align="right"
        :rules="[{ required: true, message: '请填写详细地址' }]" />
    </van-form>
    <div style="margin: 16px" class="save flex justify-center items-center flex-col">
      <van-button round block type="info" native-type="submit" class="save-btn" @click="onSubmit">保存</van-button>
    </div>
  </div>
</template>

<script setup>
  import areaList from '@/utils/sc_areaData.js'
  import { showToast, showConfirmDialog } from 'vant'
  import { ref, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { addMailInfo } from '@/api/activity.ts'
  const formData = ref({});
  const show = ref(false);
  const fieldNames = {
    text: 'text',
    value: 'text',
    children: 'children',
  };
  const route = useRoute();
  const router = useRouter();
  onMounted(() => {
    const query = route.query;
    for (const key in query) {
      formData.value[key] = query[key];
    }
    const { provinceCode, consignee, address } = formData.value;
    if (provinceCode) {
      const split = decodeURI(provinceCode).split(' ');
      formData.value.cascaderValue = split[split.length - 1];
    }
    formData.value = {
      ...formData.value,
      consignee: consignee ? decodeURI(consignee) : '',
      provinceCode: provinceCode ? decodeURI(provinceCode) : '',
      address: address ? decodeURI(address) : '',
    };
  });
  const formRef = ref(null);
  const onSubmit = () => {
    formRef.value
      .validate()
      .then(async () => {
        showConfirmDialog({
          title: '温馨提示',
          message: '请确认地址信息是否无误?',
        })
          .then(async () => {
            // on confirm
            const { code } = await addMailInfo(formData.value); // 假设 yourApiFunction 是你的 API 调用函数
            if (code === 200) {
              showToast({
                message: '保存成功',
                duration: 1000,
                type: 'success',
                onClose: () => {
                  router.go(-1);
                },
              });
            }
          })
          .catch(() => {
            // on cancel
          });
      })
      .catch(() => {
        showToast({
          message: '请完善收货地址信息',
        });
      });
  };

  const onFinish = ({ selectedOptions }) => {
    formData.value.provinceCode = selectedOptions.map(option => option.text).join(' ');
    show.value = false;
  };
</script>

<style lang="scss" scoped>
  .editAddress {
    width: 100%;
    min-height: 100%;
    background-color: #f5f5f5;
    padding: 28px;

    :deep(.van-cell) {
      margin-bottom: 20px;
      border-radius: 14px;
    }

    .save {
      p {
        font-size: 28px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #dd4135;
      }

      .save-btn {
        // background-image: url('@/assets/activity/liangshan/hundredDayIntegral/save-btn.png');
        background: linear-gradient(-90deg, #fa4528 0%, #ff9454 100%);
        background-size: 100% 100%;
        background-repeat: no-repeat;
        border: none;
        font-size: 38px;
        letter-spacing: 10px;
        text-align: center;
        color: #ffffff;
      }
    }
  }
</style>
