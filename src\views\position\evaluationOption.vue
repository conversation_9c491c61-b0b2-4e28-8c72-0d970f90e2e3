<template>
  <div class="px-3 border-box" :class="$style.eval">
    <div class="flex items-center py-[40px]">
      <span class="h-[31px] title-line  w-[5px] inline-block mr-1 rounded-lg" />
      <span> 请您对该阵地服务进行评价 </span>
    </div>
    <van-form>
      <div class="w-full flex justify-center items-center flex-col">
        <van-rate v-model="star" clearable size="30px" color="#ff7243" void-color="#888888"
          class="w-full justify-evenly" />
        <div class="w-full flex justify-evenly van-rate mt-2">
          <div class="text-[26px] w-1/5 flex justify-center items-center" :class="item === star ? '' : 'text-[#888888]'"
            v-for="(item, index) in 5">
            <i class="van-badge__wrapper van-icon">
              {{ startStr[index] }}
            </i>
          </div>
        </div>
      </div>
      <div class="pt-5">
        <van-field label-align="top" class="message-clazz" v-model="message" name="describes" rows="4" autosize label=""
          type="textarea" maxlength="200" placeholder="您的宝贵意见助我们变更好，可为您提供更优质服务哦~" show-word-limit />
      </div>

      <div class="pl-[20px] pt-5">
        <van-uploader v-model="fileList" multiple :max-count="3" />
      </div>
    </van-form>




    <div class="flex justify-center items-center fixed  w-full mt-60px">
      <Button name="提交评价" class="!w-[470px] !h-[70px]" @click="onClick" />
    </div>

    <van-popup v-model:show="showCenter" round :style="{ padding: '64px' }" :close-on-click-overlay="false"
      class="success-msg">
      <div class="absolute top-0"><img loading="lazy" :src="submitSuccess" class="w-[319px] h-[245px]" /></div>

      <div class="bg-[#fff] rounded-[36px] flex justify-center items-center flex-col pb-3">
        <div class="text-[#333333] text-[36px] mt-120px">提交成功</div>
        <p class="text-[#666666] text-[26px] overflow-ellipsis px-5">感谢您的宝贵建议我们会继续努力做得更好~</p>

        <Button name="返回" class="!w-[470px] !h-[70px]" @click="handleClick" />
      </div>
    </van-popup>
  </div>
</template>

<script lang="ts" setup>
import { addAComment } from '@/api/position';
import { uploadFile } from '@/api/public';
import Button from '@/components/Button/button.vue';
import { join, map } from 'lodash-es';
import { showFailToast, showToast } from 'vant';
import submitSuccess from '@/assets/position/submit-success.png';

const route = useRoute();

const router = useRouter();

const fileList = ref();

const message = ref();

const star = ref();

const showCenter = ref<boolean>(false);

const positionInfoId = computed(() => route.query.positionInfoId);

const startStr = ['很不满意', '不满意', '一般', '满意', '非常满意'];

async function onClick() {
  if (!unref(message)) {
    showToast('请填写评价！');
    return;
  }

  if (!(unref(star) >= 1)) {
    showToast('请对该阵地打分！');
    return;
  }

  Promise.all(map(unref(fileList), v => uploadFile({ file: v.file, operateType: 42 }))).then(
    response => {
      const positionPicture = join(
        map(response, v => v.data?.[0] || ''),
        ','
      );

      addAComment({
        positionPicture,
        positionContent: unref(message),
        positionScore: unref(star),
        positionInfoId: unref(positionInfoId),
      }).then(({ code, message }) => {
        if (code === 200) {
          showCenter.value = true;
        } else {
          showFailToast(`评价失败！${message || ''}`);
        }
      });
    }
  );
}

function handleClick() {
  showCenter.value = false;
  router.go(-1);
}
</script>

<style lang="less" module>
.eval {
  :global {
    * {
      box-sizing: border-box;
    }

    .message-clazz {
      .van-cell__value {
        background-color: #f6f6f6;
        padding: 10px;
        border-radius: 15px;
      }
    }

    .van-uploader__upload,
    .van-image img {
      border-radius: 10px;
    }

    .van-field__word-limit {
      color: #c8cace;
    }

    .success-msg {
      background-color: transparent;
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: center;
      overflow: hidden;
      padding: 32px !important;
      padding-top: 100px !important;
    }

    .title-line {
      background: linear-gradient(to top, #5BA5FF, #5CB6FF);
    }
  }
}
</style>
