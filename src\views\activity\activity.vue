<template>
  <div class="activity w-full">
    <div class="title pt-72px pb-30px flex items-center justify-center">
      <img loading="lazy" src="@/assets/activity/title.png" alt="" class="w-45%">
    </div>
    <div
      class="w-90% mt-50px p-18px box-border m-auto bg-#F7FAFE border-solid border-#FFFFFF border-2px rounded-20px flex justify-around">
      <div class="flex flex-col justify-center items-center" v-for="(item, index) of Data.total" :key="index">
        <div class="text-#666 text-28px">{{ item.title }}</div>
        <div class="text-#5AA4FF text-36px font-bold pt-19px">{{ item.num }}</div>
      </div>
    </div>
    <div class="bg-#fff act-content p-28px mt-22px relative">
      <!-- tab切换 -->
      <div class="z-10000 text-30px flex justify-around text-#525252 h-70px">
        <div v-for="(item, i) in Data.tabName" :key="i" :class="Data.tabIndex == i ? 'text-#5AA4FF' : ''"
          @click="activityTabBtn(i, item)">
          <div>
            {{ Data.tabName[i]?.currentName ? Data.tabName[i]?.currentName : item.name }}
            <van-icon name="arrow-down"></van-icon>
          </div>
        </div>
      </div>
      <!-- tab切换下拉 -->
      <div class="dialog-activity" v-show="Data.isShow">
        <van-popup :show="Data.isShow" round position="top" @click-overlay="Data.isShow = false">
          <div class="p-24px box-border">
            <div class="flex items-start flex-wrap   bg-#fff box-border ">
              <div
                class="dropdown_menu_item box-border px-20px py-6px mb-15px rounded-25px bg-#f7f7f7 text-#4c4c4c mr-15px text-28px"
                :class="Data.tabName[Data.tabIndex].index == index ? ' !bg-#A1CBFF !text-#fFF' : ''"
                v-for="(item, index) in Data.tabName[Data.tabIndex].children" :key="index"
                @click="dropdownMenuBtn(index, item)">
                {{ item.label }}
              </div>
            </div>
          </div>
        </van-popup>
      </div>
      <div class="list-container pt-22px pb-130px">
        <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
          <div v-for="(item, index) of Data.activityList" :key="index" class="bg-#FFF rounded-20px list-items mb-22px"
            @click="toDetail(item)">
            <div class="h-300px w-full relative rounded-20px overflow-hidden">
              <img loading="lazy" :src="item.appCover ? judgeStaticUrl(item.appCover) : defaultCover" alt=""
                class="w-full h-full">
              <div class="act_status_box absolute right-0 z-99 text-#fff text-24px top-0">
                <div
                  :class="item.progressMsg == '未开始' ? 'act_status_no' : item.progressMsg == '进行中' ? 'act_status_ing' : ''">
                  {{ item.progressMsg }}
                </div>
              </div>
            </div>
            <div class="p-20px box-border w-full">
              <div class="truncate w-100% text-30px">{{ item.activityName }}</div>
              <div class="text-#666 text-26px py-12px flex items-center"><van-icon name="underway"
                  class="mr-12px text-[#CCCCCC]" size="14" />
                {{ item.activityStartTime }} - {{ item.activityEndTime }}
              </div>
              <div class="flex justify-between text-24px text-#ccc">
                <div><van-icon name="location" class="mr-12px" size="14" />{{ item.companyName }}</div>
                <div><van-icon name="friends" class="mr-12px" size="14" />{{ item.readCount }}</div>
              </div>
            </div>
          </div>
        </refreshList>

      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import defaultCover from '@/assets/activity/tu.png'
import { activityInfoList, statisticsUnionActivity } from "@/api/activity.ts";
import refreshList from '@/components/refreshList/index.vue';
import { useDictionary } from "@/store/modules/dictionary";
import { useRoute, } from 'vue-router';
import { judgeStaticUrl } from "@/utils/utils";
import dayjs from "dayjs";
import { toDetail } from "@/hooks/useValidator";
const route = useRoute();

const dictionary = useDictionary()

const Data = ref({
  total: [
    { title: '总访问量', num: 0 },
    { title: '活动总数', num: 0 },
    { title: '进行中', num: 0 },
  ],
  tabIndex: 0,
  tabName: [
    {
      id: 0,
      index: 0,
      name: "区域",
      //由areaCode改成companyId
      code: 'companyId',
      children: [
        { label: "全部", value: '' }
      ],
      currentName: ''
    },
    {
      id: 1,
      index: 0,
      name: "状态",
      code: 'progress',
      children: [
        { label: "全部", value: "" },
        { label: "进行中", value: 2 },
        { label: "已结束", value: 3 },
        { label: "未开始", value: 1 }
      ],
      currentName: ''
    },
    {
      id: 2,
      index: 0,
      name: "类型",
      code: 'activityType',
      children: [],
      currentName: ''
    }
  ],
  isShow: false,
  pageNum: 1,
  params: {},
  activityList: [],
  activityCategory: 'union',
})
/*全部/区域/状态 tab切换   */
function activityTabBtn(index, item) {
  // tab切换 下划线滑动
  if (Data.value.tabIndex == index) {
    Data.value.isShow = !Data.value.isShow;
  } else {
    Data.value.isShow = true;
  }
  Data.value.tabIndex = index;
}
/* 区域/状态 tab下拉的选项 */
function dropdownMenuBtn(index, item) {
  Data.value.tabName[Data.value.tabIndex].index = index
  Data.value.tabName[Data.value.tabIndex].currentName = item.label === '全部' ? '' : item.label

  Data.value.isShow = false;
  Data.value.tabName.forEach(t => {
    const { code, children, index } = t
    Data.value.params[code] = children[index].value;
  })
  Data.value.pageNum = 1;
  if (Data.value.tabIndex == 0) statistics()
  getActList();
}
//获取活动列表
const loadMoreRef = ref(null)
function getActList() {
  activityInfoList({
    ...Data.value.params,
    activityCategory: Data.value.activityCategory,
    pageSize: 10,
    pageNum: unref(Data).pageNum,
  }).then(res => {
    if (res.code == 200) {
      if (Data.value.pageNum === 1) {
        Data.value.activityList = [];
      }
      res.data = res.data?.map(t => {
        const { activityStartTime, activityEndTime } = t
        t.activityStartTime = dayjs(activityStartTime).format('YYYY.MM.DD') ?? '';
        t.activityEndTime = dayjs(activityEndTime).format('YYYY.MM.DD') ?? '';

        // t.activityStartTime = activityStartTime?.split(' ')[0] ?? ''
        // t.activityEndTime = activityEndTime?.split(' ')[0] ?? ''
        return t
      })
      Data.value.activityList = Data.value.activityList.concat(res.data);
      //重置刷新状态及 判断是否加载完成
      if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(Data.value.activityList.length, res.total);
      }
    }

  })
}
// 刷新
const onRefreshList = () => {
  Data.value.tabName.forEach(t => {
    t.index = 0
    t.currentName = ''
  })
  Data.value.tabIndex = 0
  Data.value.params = {}
  Data.value.pageNum = 1;
  getActList();
};
// 加载更多
const onLoadMore = () => {
  Data.value.pageNum++;
  getActList();
};

const statistics = async () => {
  const { data } = await statisticsUnionActivity({
    activityCategory: Data.value.activityCategory,
    companyId: Data.value.params[Data.value.tabName[0].code]
  })
  Data.value.total[1].num = data?.total ?? 0
  Data.value.total[0].num = data?.readCount ?? 0
  Data.value.total[2].num = data?.hot ?? 0
}
const instance = getCurrentInstance();
onMounted(() => {
  const areaNames = dictionary.dictionaryOBJmap?.['regionCode']?.map((t: any) => {
    const { dictName, remark } = t
    return { value: remark, label: dictName }
  })
  Data.value.tabName[0].children = [{ label: "全部", value: '' }]
  if (areaNames && areaNames.length) Data.value.tabName[0].children = [{ label: "全部", value: '' }, ...areaNames]

  const activityTypes = dictionary.getDictionaryOpt?.['unionActivityType']
  Data.value.tabName[2].children = [{ label: "全部", value: '' }]
  if (activityTypes && activityTypes.length) Data.value.tabName[2].children = [{ label: "全部", value: '' }, ...activityTypes]

  // 区县跳转，默认回显区县活动类型筛选条件
  if (route.query?.companyId) {
    Data.value.tabName[0].index = Data.value.tabName[0].children?.findIndex((t: any) => t.value === route.query.companyId) ?? 0
    Data.value.tabName[0].currentName = Data.value.tabName[0].children?.[Data.value.tabName[0].index]?.label ?? ''
    Data.value.params[Data.value.tabName[0].code] = route.query.companyId
  }


  getActList();
  statistics()
})
</script>
<style lang="scss">
.activity {
  background: url('@/assets/activity/banner.png'), #F6F7F8;
  background-repeat: no-repeat;
  background-size: 100% auto;

  .act-content {
    border-radius: 30px 30px 0px 0px;
    min-height: 75vh;

    .dialog-activity {

      /* 活动主页 tab切换 下拉的弹窗  */
      .van-popup {
        position: absolute;
        // top: 450px;
        top: 70px;
        left: 0;
        right: 0;
        background-color: #fff;
      }

      .van-overlay {
        background: rgba($color: #000000, $alpha: 0.3);
        // top: 450px;
        top: 70px;
        bottom: 0;
        left: 0;
        right: 0;
        position: absolute;
      }

      .dropdown_menu_box {
        border-radius: 0px 0px 12px 12px;

        .dropdown_menu_item {}
      }

    }

    .list-items {
      box-shadow: 0px 3px 10px 0px rgba(128, 153, 162, 0.1);

      .act_status_box {
        >div {
          background-image: url('@/assets/activity/end.png');
          background-size: 100% 100%;
          background-position: center center;
          width: 119px;
          height: 44px;
          line-height: 44px;
          /*box-shadow: 3px 1px 1px 0px #f04128;*/
          /*border-radius: 28px 0px;*/
          text-align: center;
        }

        .act_status_no {
          background-image: url('@/assets/activity/wait.png') !important;

        }

        .act_status_ing {
          background-image: url('@/assets/activity/open.png') !important;
        }
      }
    }
  }
}
</style>
