<template>
  <div class="video_container bg-[#F6F7F8] min-h-full box-border h-fit">
    <!-- 个人信息 -->
    <myinfo :userId="route.query?.userId" :nickname="route.query?.nickname" />
    <!-- 列表 -->
    <div class="list pb-150px h-fit !bg-[#fff] personal h-50vh">
      <refreshList ref="loadMoreRef" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore">
        <div class="flex flex-wrap justify-between p-30px">
          <videoListCell :content="item" v-for="(item, index) in list" :key="item.autoId" @click="onVideoClick(index)">
          </videoListCell>

        </div>
      </refreshList>
    </div>
  </div>
</template>
<script lang="ts" setup>
import refreshList from '@/components/refreshList/index.vue';
import videoListCell from '@/components/Cell/videoListCell.vue';
import myinfo from '@/views/video/components/myinfo.vue';
import { getMyShortVideos } from '@/api/video/index';
import { useRoute, useRouter } from 'vue-router';
import { useVideoStore } from '@/store/modules/video';

const videoStore = useVideoStore();
const route = useRoute();
const router = useRouter();

onMounted(() => {
  getList();
});

let pageNum = 1;
const list = ref<any[]>([]);
const loadMoreRef = ref(null);
// 刷新
const onRefreshList = () => {
  pageNum = 1;
  list.value = [];
  getList();
};
// 加载更多
const onLoadMore = () => {
  pageNum++;
  getList();
};

const getList = async () => {
  const { code, data, total } = await getMyShortVideos({
    pageNum,
    pageSize: 10,
    auditState: 'pass',
    publicityState: true,
    userId: route.query?.userId, //发布人员id
  });
  if (code === 200) {
    list.value = [...list.value, ...data];
  }
  if (loadMoreRef.value) {
    loadMoreRef.value.onLoadSuc(list.value.length, total);
  }
};

function onVideoClick(listnumber: number) {
  let userId = route.query?.userId;
  videoStore.setVideoList(unref(list), 2, userId);
  router.push({
    path: '/video-playback',
    query: { entertype: 2, userId, listnumber },
  });
}
</script>
<style lang="scss" scoped>
.video_container {
  background-repeat: no-repeat;
  background-position: 100% auto;

  .list {
    min-height: calc(100vh - 110px);
  }

  .personal {
    border-radius: 30px 30px 0px 0px;
  }

  .personal_tab {
    :deep(.van-tabs--line .van-tabs__wrap) {
      height: 80px;
    }

    :deep(.van-tab) {
      line-height: 1.2;
      height: 100%;
    }

    :deep(.van-tabs__nav--line) {
      height: 100%;
      padding-bottom: 15px;
    }

    :deep(.van-tabs__line) {
      background: linear-gradient(0deg, #a1cbff 0%, #5aa4ff 100%);
      width: 36px;
      height: 6px;
      bottom: 15px;
    }

    :deep(.van-tab__text) {
      font-size: 28px;
    }
  }
}
</style>
