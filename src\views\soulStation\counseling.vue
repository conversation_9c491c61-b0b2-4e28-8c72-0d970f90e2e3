<template>
  <div :class="$style['psychological-counseling']">
    <div class="banner w-full h-[392px]" />
    <div class="w-full flex bg-[#fff] rounded-xl relative top-[-40px]">
      <div v-for="item in tabArr" :key="item.value"
        class="w-1/2 relative rounded-lg flex justify-center items-center text-[28px] tab"
        :class="`${active === item.value ? 'tab--active h-[97px]  top-[-20px] font-medium ' : ''} ${item.clazz}`"
        @click="onClickTab(item)">
        <van-badge :content="item.value === 'consult' ? notRead > 0 ? notRead : null : null" max="99">
          <img loading="lazy" :src="item.icon" class="w-[50px] h-[50px] mr-2" />
          <div class="tracking-in-expand">{{ item.title }}</div>
        </van-badge>
      </div>
    </div>
    <div class="h-[calc(100%-392px-97px)] relative px-[30px]">
      <Transition name="fade" mode="out-in" appear>
        <component :is="Comps[active]" />
      </Transition>
    </div>
  </div>
</template>

<script lang="ts" setup>
import expert from '@/assets/soulStation/expert.png';
import consult from '@/assets/soulStation/consult.png';
import Expert from './components/Expert.vue';
import Consult from './components/Consult.vue';
import { getNotReadCount } from '@/api/soulStation';
const tabArr = [
  { title: '心理专家', value: 'expert', clazz: 'expert slide-in-left', icon: expert },
  { title: '咨询记录', value: 'consult', clazz: 'consult', icon: consult },
];

const Comps: Recordable = {
  expert: markRaw(Expert),
  consult: markRaw(Consult),
};

const active = ref<string>('expert');
// 路由传值
const route = useRoute();
if (route.query?.active) active.value = route.query?.active as string;
// end

const onClickTab = ({ value }: Recordable) => {
  active.value = value;
};
const notRead = ref(0)
function getNotRead() {
  getNotReadCount({}).then(res => {
    if (res.code == 200) {
      notRead.value = res.data
    }
  })
}
onMounted(() => {
  getNotRead()
})
</script>

<style lang="less" module>
.psychological-counseling {
  :global {
    .banner {
      background-image: url('@/assets/soulStation/psychological-counseling-bg.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    .expert.tab--active {
      background-image: url('@/assets/workers-voice/change-left.png');
      background-repeat: no-repeat;
      background-size: 100% 97px;
      // -webkit-animation: slide-in-left 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
      // animation: slide-in-left 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    }

    .consult.tab--active {
      background-image: url('@/assets/workers-voice/change-right.png');
      background-repeat: no-repeat;
      background-size: 100% 97px;
      // -webkit-animation: slide-in-right 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
      // animation: slide-in-right 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    }

    .van-badge__wrapper {
      display: flex;
      align-items: center;
    }

    .van-cell {
      .van-cell__value {
        display: flex;
        align-items: center;
        text-align: left;
      }

      &::after {
        left: 0;
        right: 0;
        border-color: #eeeeee !important;
      }
    }
  }
}
</style>
