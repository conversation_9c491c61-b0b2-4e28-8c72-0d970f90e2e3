<template>
  <div class="lotteryActivity w-full min-h-full relative flex flex-col justify-end py-60px"
    style="background-size: 100% 100%;"
    :style="{ backgroundImage: `url(${useStore?.activityDetail.appDetailsCover ? useStore.getPrefix + useStore?.activityDetail?.appDetailsCover : backgroundImg})` }">
    <div class="absolute top-50% right-0 write-vertical-right bg-[#fb8136] p-10px py-20px rounded-l-15px text-[#fff] text-28px tracking-3px
      " @click="toDetail">
      活动详情
    </div>
    <div class="w-full px-30px">
      <div class="w-full h-212px flex items-center justify-evenly px-30px pb-10px pr-210px mb-20px"
        :style="{ backgroundImage: `url(${bgCoupon})` }" style="background-size: 100% 100%;" v-for="item in couponList"
        :key="item.couponId" @click="onReceiveCoupon(item)">
        <!-- 无门槛 -->
        <template v-if="item.couponType === 'noLimit'">
          <div class="text-[#fa261b] font-550 pb-10px flex-shrink-0">
            ¥
            <span class="text-85px font-500">
              {{ item.discountAmount || '-' }}
            </span>
          </div>
          <div class="flex flex-col items-center text-[#fa261b] font-550 text-35px">
            <!-- <div>{{ couponType?.[item.couponType] }}</div> -->
            <div class="w-130px leading-43px">无门槛优惠券</div>
          </div>
        </template>
        <!-- 满减券 -->
        <template v-if="item.couponType === 'fullDecrement'">
          <div class="text-[#fa261b] font-550 pb-10px flex-shrink-0">
            ¥
            <span class="text-85px font-500">
              {{ item.discountAmount || '-' }}
            </span>
          </div>
          <div class="flex flex-col items-center text-[#fa261b] font-550 text-35px">
            <div>{{ couponType?.[item.couponType] }}</div>
            <div class="text-28px">满{{ item.amountLimit || '-' }}可使用</div>
          </div>
        </template>
        <!-- 折扣券 -->
        <template v-if="item.couponType === 'discount'">
          <div class="text-[#fa261b] font-550 pb-10px flex-shrink-0">
            <span class="text-85px font-500">
              {{ item.discountPercent || '-' }}折
            </span>
          </div>
          <div class="flex flex-col items-center text-[#fa261b] font-550 text-35px">
            <div>优惠券</div>
            <div class="text-28px">满{{ item.amountLimit || '-' }}可使用</div>
          </div>
        </template>
      </div>
    </div>
    <van-popup v-model:show="show" :close-on-click-overlay="false" :lock-scroll="true" class="bg-transparent">
      <div>
        <div class="w-686px h-604px flex flex-col items-center pt-325px px-120px justify-between pb-50px"
          :style="{ backgroundImage: `url(${bgSuccess})` }" style="background-size: 100% 100%;" v-if="success">
          <div class="pr-30px flex flex-col items-center mb-10px">
            <div class="text-30px text-[#faefc1] font-550 mb-10px">恭喜获得</div>
            <div class="text-48px text-[#fff] font-550 text-center leading-50px"
              v-if="currentCoupon.couponType === 'noLimit'">
              {{ currentCoupon?.discountAmount }}元优惠券
            </div>
            <div class="text-48px text-[#fff] font-550 text-center leading-50px"
              v-if="currentCoupon.couponType === 'fullDecrement'">
              满{{ currentCoupon?.amountLimit }}元减{{ currentCoupon.discountAmount }}优惠券
            </div>
            <div class="text-48px text-[#fff] font-550 text-center leading-50px"
              v-if="currentCoupon.couponType === 'discount'">
              {{ currentCoupon?.discountPercent }}折优惠券
            </div>
          </div>
          <van-button
            class="bg-[#ffce5d] text-[#e61d44] w-250px flex justify-center h-60px items-center rounded-30px mr-20px border-none flex-shrink-0"
            @click="show = false">
            我知道了
          </van-button>
        </div>
        <div v-else
          class="flex items-center relative w-520px h-574px flex-col relative winning pt-70px pb-30px mb-30px px-50px text-center justify-between"
          :style="{ backgroundImage: `url(${bgFail})` }" style="background-size: 100% 100%;">
          <div class="w-full text-[#D1302D] font-500 text-31px">
            谢谢参与，今日票券已抢完，感谢您的支持~
          </div>
          <img loading="lazy" src="@/assets/activity/coupon-img.png" alt="" class="w-200px">
          <img loading="lazy" src="@/assets/activity/btn-iknow.png" alt="" class="w-300px" @click="show = false">
          <!-- <van-button
            class="w-308px h-80px flex justify-center items-center text-[36px] text-[#fff] bg-[#fe4d2b] rounded-40px border-none"
            @click="show = false">
            我知道了
          </van-button> -->
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { showDialog } from "vant";
import { receiveCoupon } from '@/api/activity';
import backgroundImg from '@/assets/activity/background.jpg'
import { ref, onMounted, computed } from 'vue'
import bgCoupon from '@/assets/activity/bg-coupon.png'
import bgSuccess from '@/assets/activity/coupon-success.png'
import bgFail from '@/assets/activity/lottery-popup.png'
import { activityValidator, activityDetailValidator, defaultValidator } from '@/hooks/useValidator'
import { useRouter } from "vue-router";
import { useUserStore } from "@/store/modules/user";
const useStore = useUserStore();
const router = useRouter();

const activityDetail = computed(() => useStore.activityDetail || {});

const show = ref(false)
const success = ref(false)
const couponList = ref([])
const couponType = {
  noLimit: '无门槛券',
  fullDecrement: '满减券',
  discount: '折扣券'
}
const currentCoupon = ref({})
const weekDayName = ['周一', '周二', '周三', '周四', '周五', '周六', '周七']
const onReceiveCoupon = async (item) => {
  if (!activityValidator()) return
  const userInfo = useStore.userInfo
  // 校验票券领取日期
  const { weekLimit, couponId, specifyDate, assignStartTime, assignEndTime, useScore } = item
  const nowDate = new Date().format('yyyy-MM-dd')
  if (assignStartTime && (nowDate < assignStartTime || nowDate > assignEndTime)) {
    showDialog({
      title: '温馨提示',
      message: `尊敬的${userInfo.nickname}会员，优惠券领取时间为${assignStartTime}-${assignEndTime}，感谢您的支持~`,
      confirmButtonText: '我知道了',
      lockScroll: false,
    });
    return
  }
  //  指定周几领取
  const day = new Date().getDay()
  const nowWeekDay = day === 0 ? '7' : day + ''
  if (weekLimit && !weekLimit.includes(nowWeekDay)) {
    let message = ''
    weekLimit.split(',').forEach(item => {
      if (message) {
        message = message + '、' + weekDayName[item - 1]
      } else {
        message = message + weekDayName[item - 1]
      }
    })
    showDialog({
      title: '温馨提示',
      message: `尊敬的${userInfo.nickname}会员，优惠券领取时间为每${message}，感谢您的支持~`,
      confirmButtonText: '我知道了',
      lockScroll: false,
    });
    return
  }

  // 校验每月领取日期 specifyDate
  const date = new Date().getDate()
  let specifyDates = null
  if (specifyDate) {
    specifyDates = specifyDate?.split(',')?.map(item => Number(item))?.sort((a, b) => a - b)
  }
  if (specifyDates && !specifyDates.includes(date)) {
    showDialog({
      title: '温馨提示',
      message: `尊敬的${userInfo.nickname}会员，优惠券领取时间为每月${specifyDates?.join('、')}日，感谢您的支持~`,
      confirmButtonText: '我知道了',
      lockScroll: false,
    });
    return
  }
  if (item.todayUserCount >= item.userDailyLimit && (item.userDailyLimit !== null || item.userDailyLimit !== undefined)) {
    showDialog({
      title: '温馨提示',
      message: `尊敬的${userInfo.nickname}用户，该优惠券每天限领${item.userDailyLimit}张，感谢您的支持~`,
      confirmButtonText: '我知道了',
      lockScroll: false,
    })
    return
  }
  // 单张优惠券领取上限
  if (item.userCount >= item.userLimit && (item.userLimit !== null || item.userLimit !== undefined)) {
    showDialog({
      title: '温馨提示',
      message: `尊敬的${userInfo.nickname}用户，该优惠券限领${item.userDailyLimit}张，感谢您的支持~`,
      confirmButtonText: '我知道了',
      lockScroll: false,
    })
    return
  }

  // 今日是否已领完
  if (item.todayCount >= item.dailyIssueCount && (item.dailyIssueCount !== null || item.dailyIssueCount !== undefined)) {
    showDialog({
      title: '温馨提示',
      message: '该优惠券今日已领完，感谢您的支持。',
      confirmButtonText: '我知道了',
      lockScroll: false,
    })
    return
  }
  const { code } = await receiveCoupon({
    activityId: activityDetail.value.activityId,
    couponId: item.couponId,
  })
  // code 10010 已领光
  if (code == 200) {
    item.todayUserCount++
    item.todayCount++
    item.userCount++
    currentCoupon.value = item
    success.value = true
    show.value = true
    // showToast({
    //   title: '温馨提示',
    //   message: `领取成功`,
    //   type: 'success',
    //   forbidClick: true,
    // })
  } else if (code == 10010) {
    success.value = false
    show.value = true
    // const userInfo = useStore.userInfo
    // showDialog({
    //   title: '温馨提示',
    //   message: `尊敬的${userInfo.nickname}用户，该优惠券今日已领完，感谢您的支持。`,
    //   confirmButtonText: '我知道了',
    //   lockScroll: false,
    // })
    return
  }
}
const toDetail = () => {
  if (!activityDetailValidator()) return
  router.push('/activityDetail')
}
const toLotteryRecord = () => {
  if (!defaultValidator()) return
  router.push('/lotteryRecord')
}
const init = () => {
  couponList.value = activityDetail.value?.couponExtend?.couponInfos || [];
  couponList.value?.forEach(item => {
    item.discountAmount = parseFloat(item.discountAmount)
    item.amountLimit = parseFloat(item.amountLimit)
    item.discountPercent = parseFloat(item.discountPercent)
  })
}
watch(() => activityDetail.value, () => {
  init()
})
onMounted(() => {
  if (activityDetail.value) {
    init()
  }
})
</script>

<style lang="scss" scoped>
.lotteryActivity {
  background-size: 100% 100%;
  font-family: Source Han Sans CN;

  .btn {
    background-image: url('@/assets/activity/draw_btn.png');
    background-size: 100% 100%;
  }

  .btns {
    >div {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 188px;
      height: 56px;
      font-size: 28px;
      color: #FFFFFF;
      background: #D1170B;
      border-radius: 14px;
      border: 1px solid #FFFFFF;
    }
  }
}
</style>
