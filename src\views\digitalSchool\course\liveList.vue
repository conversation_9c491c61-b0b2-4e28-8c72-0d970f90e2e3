<template>
  <div class="bg-#f6f7f8 min-h-100vh w-100%">
    <div class="p-30px box-border">
      <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
        <activityList :data="Data.list" @toDetails="toDetails" bgColor="#fff" :septalLine="false">
          <template #subContent="{ item }">
            <div class="text-24px text-#999 mb-30px">
              <div class="mb-10px">
                <img loading="lazy" src="@/assets/vitality/volunteerService/icon_bmsj.png" alt=""
                  class="w-22px h-22px mr-6px">
                {{ dayjs(item.liveStartTime).format('YYYY-MM-DD HH:mm') || "--" }} 至 {{
                  dayjs(item.liveEndTime).format('YYYY-MM-DD HH:mm') || "--" }}
              </div>
            </div>
          </template>
          <template #status="{ item }">
            <div
              class="list-status  text-#fff text-24px bg-no-repeat bg-cover bg-center w-150px h-48px flex items-center justify-center ml-10px"
              :style="{ backgroundImage: `url(${Data.liveStatusObj[item.isOpenLive]})` }">
              {{ dictionary.getDictionaryMap?.[`liveStatus_${item.isOpenLive}`] ?
                "直播" + dictionary.getDictionaryMap?.[`liveStatus_${item.isOpenLive}`].dictName : "" }}
            </div>

          </template>
        </activityList>
      </refreshList>

    </div>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs'
import activityList from "@/components/activity/list.vue";
import icon_zzzb from "@/assets/digitalSchool/icon_zzzb.png";
import icon_zbjs from "@/assets/digitalSchool/icon_zbjs.png";
import icon_zbwks from "@/assets/digitalSchool/icon_zbwks.png";
import router from "@/router";
import { findVoListLive } from "@/api/digitalSchools/index"
import refreshList from '@/components/refreshList/index.vue';
import { useUserStore } from '@/store/modules/user';
import { useDictionary } from '@/store/modules/dictionary';
const useStore = useUserStore()
const dictionary = useDictionary()
const Data = ref({
  list: [
  ],
  pageNum: 1,
  liveStatusObj: {
    'notStart': icon_zbwks,
    'onGoing': icon_zzzb,
    'hasEnd': icon_zbjs,

  }
});
const loadMoreRef = ref('')

//直播列表
function getLiveList() {
  findVoListLive({
    pageNum: 1,
    pageSize: 10
  }).then(res => {
    if (res.code == 200) {
      // res.data[0].liveStatus = "直播" + dictionary.getDictionaryMap?.[`liveStatus_${res.data[0].isOpenLive}`].dictName;
      if (Data.value.pageNum == 1) {
        Data.value.list = res.data || []
      } else {
        Data.value.list = Data.value.list.concat(res.data);
      }
      Data.value.list.forEach(item => {
        item.appCover = item.liveCover;
        item.activityName = item.catalogueName
      })
      //重置刷新状态及 判断是否加载完成
      if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(Data.value.list.length, res.total);
      }
    }

  })
}

function toDetails(item: any) {
  router.push({ path: "/digitalSchool/course/liveDetails", query: { catalogueId: item.catalogueId, type: item.catalogueType } });
}
// 刷新
const onRefreshList = () => {
  Data.value.pageNum = 1;
  getLiveList();
};
// 加载更多
const onLoadMore = () => {
  Data.value.pageNum++;
  getLiveList();
};

onMounted(() => {
  getLiveList()

})
</script>
<style lang="scss" scoped>
:deep(.list-status) {
  right: 0;
  position: absolute;
  top: 0;
}

:deep(.item-content) {
  padding: 0 20px;
  box-sizing: border-box;
}

:deep(.cover-img) {
  border-radius: 16px 16px 0 0;
}

:deep(.item-box) {
  margin-bottom: 20px;
}
</style>
