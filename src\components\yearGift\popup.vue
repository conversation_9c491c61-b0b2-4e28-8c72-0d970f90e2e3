<template>
    <van-popup v-model:show="props.show" position="top">
        <div class="year_Gift overflow-hidden">
            <div class="template w-fit relative" :class="`template_${template_code}`">
                <img loading="lazy" :src="judgeStaticUrl(content?.templatePicture) || defaultBg"
                    class="h-full w-auto" />
                <div class="content absolute">
                    <div class="header" v-if="positionArr.find(it => it.code === template_code)?.showAccount">
                        <div class="title" v-if="template_code === '1'">亲爱的：</div>
                        <div class="account">
                            <div class="imgs">
                                <img loading="lazy" :src="avatar" class="avator" />
                                <img loading="lazy" src="@/assets/yearGift/hat.png" class="hat" />
                            </div>
                            <div class="name">
                                <span>{{ useStore.getUserInfo?.nickname }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="text" v-if="template_code === '1'">
                        今天是你的生日！祝你生日快乐~ <br>
                        愿一年更比一年好
                    </div>
                    <div class="text" v-else-if="template_code === '3'">
                        今天是你的生日！祝你生日快乐~
                    </div>
                </div>
            </div>
            <div class="button" :class="`template_button_${template_code}`"
                :style="{ background: `linear-gradient(-89deg, ${props.content?.buttonColor.split(',')[0]} 0%, ${props.content?.buttonColor.split(',')[1]} 100%)` }"
                @click="closePopup">{{ props.content?.buttonContent }}</div>
        </div>
    </van-popup>
</template>

<script setup lang="ts">
import { judgeStaticUrl } from "@/utils/utils";
import { type detailInfo } from "@/api/yearGift"
import defaultBg from "@/assets/yearGift/template_1_bg.png";
import { useUserStore } from "@/store/modules/user";
import defaultAvatar from '@/assets/public/head_default.png';
import female from '@/assets/public/female.png';
import male from '@/assets/public/male.png';
const useStore = useUserStore();
// 弹窗根据类型 展示不同模板样式
defineOptions({
    name: "YearGiftPopup",
});
const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    content: {
        type: Object,
        default: () => ({
            templateCode: "1",
            templatePicture: '',
            buttonContent: "收下祝福",
            buttonColor: "#FF6A5C,#F47983"
        } as detailInfo),
    },
})
const emit = defineEmits(["update:show"]);
// 模板号
const template_code = computed(() => {
    return props.content?.templateCode || '1'
})
const avatar = computed(() => {
    if (useStore.getUserInfo?.avatar) return judgeStaticUrl(useStore.getUserInfo?.avatar)
    if (useStore.getUserInfo?.gender === '男') return male
    if (useStore.getUserInfo?.gender === '女') return female
    return defaultAvatar
})

// 基础配置项
const positionArr = [
    {
        code: '1',
        showAccount: true
    },
    {
        code: '2',
        showAccount: true
    },
    {
        code: '3',
        showAccount: true
    },
    {
        code: '4',
        showAccount: false
    }
]

// 关闭弹窗
const closePopup = () => {
    emit("update:show", false);
}
</script>
<style scoped lang="scss">
.year_Gift {
    padding: 0;
    position: relative;
    width: 100vw;
    height: 100vh;
    box-sizing: border-box;

    // flex column布局公共样式
    @mixin common_flex_col_center {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    // 头部flext调整
    @mixin account_flex {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-end;
    }

    .template {
        box-sizing: border-box;
        overflow: hidden;
    }

    // 模板1
    .template_1 {
        height: 65%;
        text-align: center;
        margin: auto;

        .content {
            box-sizing: border-box;
            top: 51%;
            bottom: 20%;
            left: 8%;
            right: 8%;
            display: flex;
            flex-direction: column;

            .header {
                display: flex;
                align-items: center;

                .title {
                    font-weight: 500;
                    font-size: 2.5vh;
                    color: #333;
                }

                .account {
                    text-align: center;
                    margin-left: 6%;
                    width: 20%;

                    .imgs {
                        position: relative;

                        .avator {
                            position: relative;
                            width: 7vh;
                            height: 7vh;
                            object-fit: cover;
                            border-radius: 50%;
                        }

                        .hat {
                            position: absolute;
                            width: 40%;
                            right: -10%;
                            top: -10%;
                        }
                    }

                    .name {
                        position: relative;
                        color: #333;
                        font-size: 2vh;

                        span {
                            position: relative;
                            z-index: 1;
                        }
                    }

                    .name::after {
                        content: " ";
                        display: block;
                        width: 100%;
                        height: 1vh;
                        background: linear-gradient(89deg, #F9D0A4 0%, #FFEBAE 100%);
                        border-radius: 0.15vh;
                        position: absolute;
                        left: 50%;
                        transform: translateX(-50%);
                        bottom: 0.1vh;
                        z-index: 0;
                    }
                }
            }

            .text {
                color: #333;
                font-size: 2vh;
                text-align: center;
                line-height: 2em;
                margin-top: 1vh;
            }
        }
    }

    // 模板2
    .template_2 {
        height: 65%;
        text-align: center;
        margin: auto;
        margin-top: 16%;
        display: flex;
        flex-direction: column;
        transform: translateX(1%);

        .content {
            box-sizing: border-box;
            top: 10%;
            bottom: 25%;
            left: 0%;
            right: 0%;
            display: flex;
            flex-direction: column;

            .header {
                @include account_flex;

                .account {
                    @include common_flex_col_center;
                    width: 100%;

                    .imgs {
                        position: relative;
                        width: 15%;

                        .avator {
                            position: relative;
                            width: 7vh;
                            height: 7vh;
                            object-fit: cover;
                            border-radius: 50%;
                        }

                        .hat {
                            position: absolute;
                            width: 60%;
                            right: -25%;
                            top: -20%;
                        }
                    }

                    .name {
                        font-size: 2vh;
                        color: #FFFFFF;
                    }

                }
            }
        }
    }

    // 模板3
    .template_3 {
        height: 65%;
        text-align: center;
        margin: auto;
        margin-top: 16%;
        display: flex;
        flex-direction: column;
        transform: translateX(1%);

        .content {
            box-sizing: border-box;
            top: 20%;
            bottom: 25%;
            left: 10%;
            right: 10%;
            display: flex;
            flex-direction: column;

            .header {
                @include account_flex;

                .account {
                    @include common_flex_col_center;
                    width: 100%;

                    .imgs {
                        position: relative;
                        width: 15%;

                        .avator {
                            position: relative;
                            width: 7vh;
                            height: 7vh;
                            object-fit: cover;
                            border-radius: 50%;
                        }

                        .hat {
                            position: absolute;
                            width: 60%;
                            right: -25%;
                            top: -20%;
                        }
                    }

                    .name {
                        font-size: 2vh;
                        color: #FFFFFF;
                    }

                }

            }

            .text {
                margin-top: 1.5vh;
                font-size: 1.8vh;
                color: #FFFFFF;
                text-align: center;
            }
        }
    }

    // 模板4
    .template_4 {
        width: 80%;
        text-align: center;
        margin: auto;
        margin-top: 12%;
        display: flex;
        flex-direction: column;
    }

    // 正常button样式
    .button {
        width: 50%;
        height: 6vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(-89deg, #FE5141 0%, #FF9600 100%);
        border-radius: 8vh;
        font-size: 2.5vh;
        color: #FFFFFF;
        white-space: nowrap;
        margin: 0 auto;
        margin-top: 5vh;
    }

    // 模板2 button
    .template_button_2 {
        margin-top: 0;
        transform: translateY(-5vh);
        background: linear-gradient(-89deg, #EB5437 0%, #FFC800 100%);
    }

    // 模板3 button
    .template_button_3 {
        margin-top: 0;
        background: linear-gradient(0deg, #037CFB 0%, #5DADFF 100%);
    }

    // 模板4 button
    .template_button_4 {
        margin-top: 4vh;
        background: linear-gradient(0deg, #7882DE 0%, #AE97C9 100%);
    }
}

.van-popup {
    width: 100vw;
    background: transparent;
    max-width: 100vw;
}
</style>