<template>
    <!-- 单身联谊认证页面 -->
    <div class="apply-form w-full min-h-100vh h-fit">
        <div class="tips h-80px text-#FE799B text-28px bg-[#FDF2F5] pl-32px flex items-center sticky top-0 z-99">
            请依次填写以下信息，*显示的为必填内容
        </div>
        <div class="form px-30px pt-39px pb-50px">
            <!-- 流程 -->
            <div class="auth-flow rounded-20px bg-#fff px-35px py-25px">
                <div class="titles text-[#444] text-32px font-medium">单身认证流程</div>
                <div class="flows flex justify-between mt-30px">
                    <div v-for="item, index in flows" :key="index">
                        <div class="flex items-center">
                            <img loading="lazy" :src="currentflow === item.stage ? item.activeIcon : item.defaultIcon"
                                class="w-114px" />
                            <img loading="lazy" src="@/assets/friendShip/profile/stage_line.png" class="w-120px"
                                v-if="index < flows.length - 1">
                        </div>
                        <div class="text-left mt-14px">
                            <div class="text-#727272 text-24px" :class="{ '!text-#444': currentflow === item.stage }">{{
                                item.title }}
                            </div>
                            <div class="text-#999 text-22px mt-10px" @click="handleShowDesc()"
                                :class="{ 'text-#FF0000': item.stage === '3' && currentflow === item.stage }">
                                {{ item.desc }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 表单 -->
            <div class="auth-form bg-[#fff] pt-40px px-28px pb-66px mt-20px">
                <div class="text-center">
                    <img loading="lazy" src="@/assets/friendShip/profile/title_icon.png" class="w-284px" />
                </div>

                <!-- 表单内容 -->
                <van-form ref="formRef" label-align="top" @submit="getForm" @failed="onFailed" :disabled="formDisabled">

                    <!-- 头像上传 -->
                    <van-field :rules="[{ required: true, message: '请上传头像' }]" class="special avatar">
                        <template #input>
                            <div class="uploader_avator w-full mx-auto text-center mt-56px">
                                <van-uploader v-model="avatar" max-count="1" accept="image/*" reupload
                                    :after-read="afterRead" :disabled="formDisabled">
                                    <div class="imgs relative">
                                        <img loading="lazy" src="@/assets/friendShip/profile/default_avatar.png"
                                            class="w-156px" />
                                        <div class="absolute w-65px h-65px rounded-50% bottom-10px -right-10px
                                            bg-[#D5D5D5] flex items-center justify-center">
                                            <img loading="lazy" src="@/assets/public/camera_icon.png" class="w-30px">
                                        </div>
                                    </div>
                                    <div class="text-[#444] text-28px required">上传头像</div>
                                </van-uploader>
                            </div>
                        </template>
                    </van-field>


                    <van-field v-model="form.nickname" required label="昵称" placeholder="请输入昵称"
                        :rules="rules.nickname" />
                    <van-field v-model="form.gender" required label="性别" placeholder="请选择性别" label-align="left"
                        :rules="rules.gender" class="radioBox">
                        <template #input>
                            <van-radio-group v-model="form.gender" direction="horizontal" shape="square" disabled>
                                <van-radio :name="i.value" v-for="i in genderClomun" :key="i.value">{{
                                    i.label
                                }}</van-radio>
                            </van-radio-group>
                        </template>

                    </van-field>
                    <van-field v-model="form.phone" readonly required label="联系电话" placeholder="请输入联系电话"
                        :rules="rules.phone" />

                    <van-field v-model="form.birthDate" required label="出生日期" readonly placeholder="请输入出生日期" />
                    <van-field v-model="form.age" required label="年龄" readonly placeholder="请输入年龄" />

                    <van-field v-model="form.height" required label="身高（cm）" type="number" placeholder="请输入身高（cm）"
                        :rules="rules.height" />

                    <inputSelect :value="incomeMonthName" name="incomeMonth" required label="月收入" readonly
                        :requiredRule="rules.incomeMonth" :columns="incomeMonthClomun" placeholder="请选择月收入"
                        :disabled="formDisabled" @onConfirm="(val) => onConfirmSelect(val, 'incomeMonth')" />

                    <inputSelect :value="educationName" name="education" required label="学历" placeholder="请选择学历"
                        :filterSearch="true" :requiredRule="rules.education" :columns="modelEducationClomnu"
                        @onConfirm="(val) => onConfirmSelect(val, 'education')" :disabled="formDisabled" />

                    <van-field v-model="form.constellation" name="constellation" label="星座" placeholder="请输入星座" />
                    <van-field v-model="form.currentAddress" name="currentAddress" label="现居地址" placeholder="请输入现居地址"
                        required :rules="rules.currentAddress" />
                    <van-field v-model="form.workAddress" name="workAddress" label="工作地址" placeholder="请输入工作地址" required
                        :rules="rules.workAddress" />
                    <inputSelect :value="workPostName" name="workPost" required label="工作岗位" readonly
                        :columns="workPostClomun" placeholder="请选择工作岗位" :requiredRule="rules.workPost"
                        :filterSearch="true" @onConfirm="(val) => onConfirmSelect(val, 'workPost')"
                        :disabled="formDisabled" />

                    <van-field v-model="form.residence" name="residence" label="户籍所在地" placeholder="请输入户籍所在地" required
                        :rules="rules.residence" />

                    <van-field v-model="form.maritalStatus" name="maritalStatus" required label="婚姻状况" class="special"
                        :rules="rules.maritalStatus">
                        <template #input>
                            <van-radio-group v-model="form.maritalStatus" direction="horizontal" shape="square"
                                :disabled="formDisabled">
                                <van-radio :name="i.value" v-for="i in maritalStatusClomun" :key="i.value">{{
                                    i.label
                                }}</van-radio>
                            </van-radio-group>
                        </template>

                    </van-field>

                    <!-- <van-field v-model="form.singleStatus" name="singleStatus" label="是否脱单" label-align="left" required
                        class="radioBox" :rules="rules.singleStatus">
                        <template #input>
                            <van-radio-group v-model="form.singleStatus" direction="horizontal" shape="square"
                                :disabled="formDisabled">
                                <van-radio :name="i.value" v-for="i in commonYesNo" :key="i.value">{{
                                    i.label
                                }}</van-radio>
                            </van-radio-group>
                        </template>
                    </van-field> -->

                    <van-field v-model="form.houseStatus" name="houseStatus" label="是否有房" label-align="left" required
                        class="radioBox" :rules="rules.houseStatus">
                        <template #input>
                            <van-radio-group v-model="form.houseStatus" direction="horizontal" shape="square"
                                :disabled="formDisabled">
                                <van-radio :name="i.value" v-for="i in commonYesNo" :key="i.value">{{
                                    i.label
                                }}</van-radio>
                            </van-radio-group>
                        </template>
                    </van-field>
                    <van-field v-model="form.carStatus" name="carStatus" label="是否有车" label-align="left" required
                        class="radioBox" :rules="rules.carStatus">
                        <template #input>
                            <van-radio-group v-model="form.carStatus" direction="horizontal" shape="square"
                                :disabled="formDisabled">
                                <van-radio :name="i.value" v-for="i in commonYesNo" :key="i.value">{{
                                    i.label
                                }}</van-radio>
                            </van-radio-group>
                        </template>
                    </van-field>

                    <van-field v-model="fromLabelList" name="labelList" label="兴趣标签" placeholder="请输入兴趣标签"
                        class="special" :rules="rules.labelList">
                        <template #input>
                            <div class="select-label mb-15px flex items-center flex-wrap ">
                                <div class="bg-#E6F2FF text-[#5AA4FF] text-28px py-12px px-28px rounded-12px box-border
                                relative mr-10px" v-for="it, index in labelList" :key="it.autoId">
                                    <span>{{ it.labelName }}</span>
                                    <div class="w-30px h-30px rounded-50% bg-[#3D92FF] absolute right-0 top-0
                                        text-#fff flex items-center justify-center leading-none" v-if="!formDisabled"
                                        @click="removeList(index)">
                                        <div class="w-19px h-4px bg-#fff rounded-2px"></div>
                                    </div>
                                </div>
                                <div v-if="!labelList?.length && formDisabled" class="text-28px">无</div>
                                <div class="add-label text-[#565656] text-28px py-10px px-26px rounded-12px box-border"
                                    v-if="!formDisabled" @click="addLabel">
                                    + 添加
                                </div>
                            </div>
                        </template>
                    </van-field>

                    <van-field v-model="form.biography" name="biography" label="自我介绍" placeholder="请输入自我介绍" rows="3"
                        maxlength="200" show-word-limit type="textarea" :rules="rules.biography" required>
                    </van-field>

                    <!-- 择偶标准 -->
                    <div class="mt-64px">
                        <div class="mate-title font-medium text-[#3F95FF] text-34px text-center">择偶标准</div>
                        <van-field name="age" label="年龄" required class="special min-field">
                            <template #input>
                                <van-field v-model="form.singleMateChoose.lowerAge" placeholder="请输入" type="number"
                                    class="min-input min-input-left"
                                    :rules="rules.singleMateChoose.lowerAge"></van-field>
                                <div class="line w-80px h-2px bg-[#DADADA] mx-22px mt-50px"></div>
                                <van-field v-model="form.singleMateChoose.upperAge" placeholder="请输入" type="number"
                                    class="min-input" :rules="rules.singleMateChoose.upperAge"></van-field>
                            </template>
                        </van-field>
                        <van-field name="height" label="身高(cm)" required class="special min-field">
                            <template #input>
                                <van-field v-model="form.singleMateChoose.lowerHeight" placeholder="请输入" type="number"
                                    class="min-input min-input-left"
                                    :rules="rules.singleMateChoose.lowerHeight"></van-field>
                                <div class="line w-80px h-2px bg-[#DADADA] mx-22px mt-50px"></div>
                                <van-field v-model="form.singleMateChoose.upperHeight" placeholder="请输入" type="number"
                                    class="min-input" :rules="rules.singleMateChoose.upperHeight"></van-field>
                            </template>
                        </van-field>
                        <inputSelect :value="educationName1" name="education" required label="学历" placeholder="请选择学历"
                            :columns="[{ label: '不限', value: '' }, ...modelEducationClomnu]"
                            :requiredRule="rules.singleMateChoose.education" :filterSearch="true"
                            @onConfirm="(val) => onConfirmSelect(val, 'education1')" :disabled="formDisabled" />

                        <inputSelect :value="incomeMonthName1" name="incomeMonth" required label="月收入"
                            :requiredRule="rules.singleMateChoose.education"
                            :columns="[{ label: '不限', value: '' }, ...incomeMonthClomun]"
                            @onConfirm="(val) => onConfirmSelect(val, 'incomeMonth1')" :disabled="formDisabled">
                        </inputSelect>

                        <van-field v-model="form.singleMateChoose.houseStatus" name="houseStatus" label="是否有房"
                            label-align="left" required class="radioBox" :rules="rules.singleMateChoose.houseStatus">
                            <template #input>
                                <van-radio-group v-model="form.singleMateChoose.houseStatus" direction="horizontal"
                                    shape="square" :disabled="formDisabled">
                                    <van-radio :name="i.value" v-for="i in commonYesNo1" :key="i.value">{{
                                        i.label
                                    }}</van-radio>
                                    <van-radio name="none" key="none"> 不限 </van-radio>
                                </van-radio-group>
                            </template>
                        </van-field>
                        <van-field v-model="form.singleMateChoose.carStatus" name="carStatus" label="是否有车"
                            label-align="left" required class="radioBox" :rules="rules.singleMateChoose.carStatus">
                            <template #input>
                                <van-radio-group v-model="form.singleMateChoose.carStatus" direction="horizontal"
                                    shape="square" :disabled="formDisabled">
                                    <van-radio :name="i.value" v-for="i in commonYesNo1" :key="i.value">{{
                                        i.label
                                    }}</van-radio>
                                    <van-radio name="none" key="none"> 不限 </van-radio>
                                </van-radio-group>
                            </template>
                        </van-field>
                    </div>
                </van-form>
                <div class=" text-#333 text-30px mt35px" v-if="!formDisabled">
                    <img loading="lazy" :src="checked ? iconYgx : iconWgx" alt="" srcset=""
                        class="w-30px h-auto v-text-top" @click="checked = !checked">
                    我已阅读并同意签订<span class="text-#3f95ff" @click.stop="showPop = true">《单身告知书》</span>和<span
                        class="text-#3f95ff" @click.stop="showPop = true">《单身承诺书》</span>
                </div>
                <div class="controll-btn text-32px text-[#fff] rounded-40px w-70% mx-auto h-83px flex items-center justify-center mt-30px"
                    @click="submithandle" v-if="!formDisabled">
                    提交
                </div>
            </div>
        </div>
        <!-- 标签弹窗 -->
        <labelPopup v-model:show="showLabel" :defaultChecked="defaultLabel" @confirmed="confirmedLabel" />
        <!-- 协议弹窗 -->
        <singlePopup :showPop="showPop" @closePopup="closeSinglePopup"></singlePopup>
        <!-- 提示弹窗 -->
        <applyPopup :showPop="showApplyPop" :type="popupType" :msg="popupMsg" @closePopup="closeApplyPopup">
        </applyPopup>
    </div>
</template>
<script lang="ts" setup>
import fillInIcon from '@/assets/friendShip/profile/fillin.png'
import fillInIconA from '@/assets/friendShip/profile/fillin_a.png'
import waitCheckIcon from '@/assets/friendShip/profile/wait_check.png'
import waitCheckIconA from '@/assets/friendShip/profile/wait_check_a.png'
import flowCheckedIcon from '@/assets/friendShip/profile/flow_checked.png'
import flowCheckedIconA from '@/assets/friendShip/profile/flow_checked_a.png'
import flowCheckedIconFail from '@/assets/friendShip/profile/flow_checked_fail.png'
import iconWgx from "@/assets/friendship/icon_wgx.png"
import iconYgx from "@/assets/friendship/icon_Ygx.png"

import inputSelect from '@/components/inputSelect/index.vue'
import { getAge } from '@/utils/tool'
import { uploadFile } from '@/api/public';
import { showFailToast, showSuccessToast, showToast } from 'vant';
import { useUserStore } from '@/store/modules/user'
import { useDictionary } from '@/store/modules/dictionary'
import labelPopup from '../components/labelPopup.vue'
import { saveSingleForm, updateSingleForm, getSingleRecord } from '@/api/friendship/profile'
import singlePopup from '../components/singlePopup.vue'
import applyPopup from '../components/applyPopup.vue'
import { judgeStaticUrl } from '@/utils/utils'
import { queryMyDetail } from "@/api/friendship/index"

onMounted(() => {
    initalPage()
})
const userStore = useUserStore()
const dictionary = useDictionary()
const userInfo = computed(() => userStore.userInfo)
const formDisabled = ref(false)
const route = useRoute()
// 初始化页面
const initalPage = async () => {
    // 判断是否认证
    const { data } = await queryMyDetail()
    if (data) {
        if (route.query?.status === 'pass') router.replace({
            path: '/friendship/myProfile'
        })
        else router.replace({
            path: '/friendship/editLog'
        })
        return
    }
    const res = await getSingleRecord()
    if (Array.isArray(res.data) && res.data.length > 0) {
        // 存在表单-回显
        form.value = res.data[0] || {}
        // 流程展示
        if (form.value.auditStatus === 'wait') {
            currentflow.value = '2'
            formDisabled.value = true //审核状态不允许编辑
        }
        else if (form.value.auditStatus === 'refuse') currentflow.value = '3'
        // end
        avatar.value = form.value.avatar?.split(',').map((item: string) => {
            return {
                url: item,
                objectUrl: judgeStaticUrl(item)
            }
        })
        // 兴趣爱好
        if (form.value.singleLabelList) {
            labelList.value = form.value.singleLabelList.map((item: any) => {
                item.autoId = item.labelId
                return item
            })
            delete form.value.singleLabelList
        }
        // 下拉回显
        educationName.value = modelEducationClomnu.value.find((item: any) => item.value == form.value.education)?.label
        workPostName.value = workPostClomun.value.find((item: any) => item.value == form.value.workPost)?.label
        incomeMonthName.value = incomeMonthClomun.value.find((item: any) => item.value == form.value.incomeMonth)?.label

        // 择偶标准
        if (!form.value.singleMateChoose) form.value.singleMateChoose = {}
        else {
            educationName1.value = [{ label: '不限', value: null }, ...modelEducationClomnu.value].find((item: any) => item.value == form.value.singleMateChoose?.education)?.label
            incomeMonthName1.value = [{ label: '不限', value: null }, ...incomeMonthClomun.value].find((item: any) => item.value == form.value.singleMateChoose?.incomeMonth)?.label
        }
        checked.value = true // 编辑状态-已阅读
    }
}

const flows = ref([
    {
        title: '1.填写个人信息',
        desc: '个人信息必须属实',
        defaultIcon: fillInIcon,
        activeIcon: fillInIconA,
        failIcon: '',
        stage: '1'
    },
    {
        title: '2.审核中',
        desc: '约1~2个工作日',
        defaultIcon: waitCheckIcon,
        activeIcon: waitCheckIconA,
        failIcon: '',
        stage: '2'
    },
    {
        title: '3.审核失败',
        desc: '查询失败原因',
        defaultIcon: flowCheckedIcon,
        activeIcon: flowCheckedIconFail,
        stage: '3'
    }
    // {
    //     title: '3.审核通过',
    //     desc: '恭喜通过',
    //     defaultIcon: flowCheckedIcon,
    //     activeIcon: flowCheckedIconA,
    //     stage: '3'
    // }
])

const currentflow = ref('1')
const validatorMaxAge = (val: any) => {
    if (val < form.value.singleMateChoose?.lowerAge) {
        return false
    }
}
const validatorMaxHeight = (val: any) => {
    if (val < form.value.singleMateChoose?.lowerHeight) {
        return false
    }
}
const rules = ref({
    nickname: [{ required: true, message: '请输入昵称', trigger: 'blur' }],
    phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
    gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
    height: [{ required: true, message: '请输入身高', trigger: 'blur' }],
    incomeMonth: [{ required: true, message: '请输入月收入', trigger: 'blur' }],
    education: [{ required: true, message: '请选择学历', trigger: 'change' }],
    currentAddress: [{ required: true, message: '请输入现居地址', trigger: 'blur' }],
    workAddress: [{ required: true, message: '请输入工作地址', trigger: 'blur' }],
    workPost: [{ required: true, message: '请选择工作岗位', trigger: 'change' }],
    maritalStatus: [{ required: true, message: '请选择婚姻状况', trigger: 'change' }],
    residence: [{ required: true, message: '请输入居住地', trigger: 'change' }],
    singleStatus: [{ required: true, message: '请选择单身状态', trigger: 'change' }],
    houseStatus: [{ required: true, message: '请选择是否有房', trigger: 'change' }],
    carStatus: [{ required: true, message: '请选择是否有车', trigger: 'change' }],
    // labelList: [{ required: true, message: '请选择标签', trigger: 'change' }],
    biography: [{ required: true, message: '请输入自我介绍', trigger: 'blur' }],
    singleMateChoose: {
        lowerAge: [{ required: true, message: '请输入最小年龄', trigger: 'change' }],
        upperAge: [{ required: true, message: '请输入最大年龄', trigger: 'change' }, {
            validator: validatorMaxAge, trigger: 'change', message: '最大年龄不能小于最小年龄'
        }],
        lowerHeight: [{ required: true, message: '请输入最低身高', trigger: 'change' }],
        upperHeight: [{ required: true, message: '请输入最高身高', trigger: 'change' }, {
            validator: validatorMaxHeight, trigger: 'change', message: '最高身高不能小于最低身高'
        }],
        education: [{ required: true, message: '请选择择偶学历', trigger: 'change' }],
        incomeMonth: [{ required: true, message: '请选择择偶月收入', trigger: 'change' }],
        houseStatus: [{ required: true, message: '请选择择偶是否有房', trigger: 'change' }],
        carStatus: [{ required: true, message: '请选择择偶是否有车', trigger: 'change' }]
    }
})


// 性别
const genderClomun = computed(() => {
    return dictionary.getDictionaryOpt?.['gender']
})
// end
// 学历
const modelEducationClomnu = computed(() => {
    return dictionary.getDictionaryOpt?.['modelEducation']
})
const educationName = ref('')
const educationName1 = ref('不限')//择偶学历
// end

// 月收入
const incomeMonthClomun = computed(() => {
    return dictionary.getDictionaryOpt?.['incomeMonth']
})
const incomeMonthName = ref('')
const incomeMonthName1 = ref('不限') //择偶月收入
// end

// 工作岗位
const workPostClomun = computed(() => {
    return dictionary.getDictionaryOpt?.['workPost']
})
const workPostName = ref('')
// end
// 公共是否
const commonYesNo = computed(() => dictionary.getDictionaryOpt?.['YesOrNo'])
const commonYesNo1 = computed(() => dictionary.getDictionaryOpt?.['YesOrNo'].filter((item: any) => item.value === 'y'))
// end

// 婚姻状况
const maritalStatusClomun = computed(() => {
    return dictionary.getDictionaryOpt?.['maritalStatus']
})
// end

const avatar = ref<any>([])
const formRef = ref(null)
const form = ref<any>({
    nickname: userInfo.value?.nickname,
    identityNum: userInfo.value?.userIdCardNo,
    companyId: userInfo.value?.companyId,
    phone: userInfo.value?.phone,
    gender: userInfo.value?.gender === '男' ? 'male' : userInfo.value?.gender === '女' ? 'female' : '',
    birthDate: userInfo.value?.dateOfBirth,
    avatar: "",//头像
    age: userInfo.value?.userIdCardNo ? getAge(userInfo.value?.userIdCardNo) : '',//年龄
    constellation: "",
    height: '',
    incomeMonth: "",
    education: "",
    currentAddress: "",
    workAddress: "",
    workPost: "",
    maritalStatus: "",
    residence: "",
    singleStatus: "n",
    houseStatus: "",
    carStatus: "",
    biography: "",
    singleMateChoose: {
        upperAge: '',
        lowerAge: '',
        upperHeight: "",
        lowerHeight: "",
        houseStatus: "none",
        carStatus: "none",
        education: "",
        incomeMonth: ""
    },//择偶标准
})
const fromLabelList = ref('')
// 标签列表
const labelList = ref<any>([])//当前标签
const showLabel = ref(false)
const defaultLabel = ref([]) //默认标签

watch(labelList, (val) => {
    fromLabelList.value = labelList.value.map((item: any) => {
        return item.autoId
    }).join(',')
}, { deep: true })

const confirmedLabel = (val: any) => {
    labelList.value = val
    console.log(labelList.value);
    showLabel.value = false
}
const addLabel = () => {
    defaultLabel.value = labelList.value && labelList.value.length ? labelList.value.map((item: any) => item.autoId) : []
    showLabel.value = true
}
const removeList = (index: number) => {
    labelList.value.splice(index, 1)
}
// end
const afterRead = (file: any) => {
    let filedata = {
        operateType: "162", //操作模块类型
        file: file.file,
    }
    file.status = "uploading"
    uploadFile(filedata).then((res) => {
        if (res.code == 200 && res.data) {
            file.status = "success";
            file.url = res.data[0]
            showSuccessToast('上传成功')
        } else {
            file.status = "failed";
            showFailToast(res.message);
        }
    })
}
const onConfirmSelect = (val: any, name: string) => {
    if (name !== 'education1' && name !== 'incomeMonth1') form.value[name] = val[0]?.value
    else if (name === 'education1') form.value.singleMateChoose.education = val[0]?.value
    else if (name === 'incomeMonth1') form.value.singleMateChoose.incomeMonth = val[0]?.value
    switch (name) {
        case 'education':
            educationName.value = val[0]?.label
            break
        case 'incomeMonth':
            incomeMonthName.value = val[0]?.label
            break
        case 'education1':
            educationName1.value = val[0]?.label
            break
        case 'incomeMonth1':
            incomeMonthName1.value = val[0]?.label
            break
        case 'workPost':
            workPostName.value = val[0]?.label
            break
        default:
            break
    }
}

// 弹窗提示
const showPop = ref(false)
const checked = ref(false)
const showApplyPop = ref(false)
const popupType = ref('error')
const popupMsg = ref('')
function closeSinglePopup() {
    showPop.value = false
    checked.value = true
}

const router = useRouter()
function closeApplyPopup() {
    showApplyPop.value = false
    if (popupType.value === 'success') {
        router.back()
    }
}
const submithandle = () => {
    formRef.value?.submit()
}
const handleShowDesc = () => {
    if (currentflow.value === '3') {
        popupType.value = 'checkError'
        popupMsg.value = form.value.auditRemarks
        showApplyPop.value = true
    }
}
const onFailed = (errorInfo: any) => {
    if (errorInfo?.errors?.length > 0) return showToast(errorInfo?.errors[0].message)
}

// 提交
const getForm = async (val: any) => {
    if (formDisabled.value) {
        showToast('您提交的表单正在审核中')
        return
    }
    if (!checked.value) {
        popupType.value = 'error'
        popupMsg.value = '请您仔细阅读并同意签订《单身承诺书》和《单身告知书》'
        showPop.value = true
        return
    }
    // if (labelList.value.length == 0) {
    //     showToast('请选择兴趣标签')
    //     return
    // }
    const params = {
        ...form.value,
        labelList: form.value.labelList = labelList.value.map((item: any) => {
            return {
                labelId: item.autoId
            }
        }),
        avatar: avatar.value[0]?.url
    }

    let mycode = 0, mymessage = '请先完善资料'
    if (form.value.autoId) {
        const { code, message } = await updateSingleForm(params)
        mycode = code
        mymessage = message

    } else {
        const { code, message } = await saveSingleForm(params)
        mycode = code
        mymessage = message
    }
    if (mycode == 200) {
        popupType.value = 'success'
        // 或点击缘空间首页-我的资料 处查看审核进度
        popupMsg.value = '提交成功，请耐心等待审核'
        showApplyPop.value = true
    } else {
        popupType.value = 'error'
        popupMsg.value = mymessage
        showApplyPop.value = true
    }
}

</script>
<style scoped lang="scss">
.apply-form {
    background-image: url('@/assets/friendShip/profile/bj.png');
    background-size: 100% 100%;

    .auth-form {
        border-radius: 20px 20px 0px 0px;

    }

    .mate-title::before {
        content: '';
        display: inline-block;
        width: 69px;
        height: 20px;
        background-image: linear-gradient(90deg, #ABD1FF 36%, transparent 36%, transparent 86%, transparent 86%);
        background-size: 23px 20px;
        background-repeat: repeat-x;
        transform: skewX(-40deg);
        margin-right: 26px;
    }

    .mate-title::after {
        content: '';
        display: inline-block;
        width: 69px;
        height: 20px;
        background-image: linear-gradient(90deg, #ABD1FF 36%, transparent 36%, transparent 86%, transparent 86%);
        background-size: 23px 20px;
        background-repeat: repeat-x;
        transform: skewX(-40deg);
        margin-left: 45px;
    }


    :deep(.van-cell) {
        padding: 0;
        margin-bottom: 20px;
    }

    :deep(.van-field__body) {
        font-weight: 400;
        font-size: 28px;
        color: #999;
        padding: 25px;
        background-color: #F1F6FB;
        border-radius: 10px;
        border: 1px solid #5AA4FF;
    }

    :deep(.van-field__label) {
        color: #333;
        margin-bottom: 26px;
        font-size: 32px;
        font-weight: 500;
    }

    // :deep(.van-checkbox-group) {
    //     display: flex;
    //     justify-content: flex-end;
    // }


    .radioBox {
        padding: 15px 0;

        :deep(.van-field__body) {
            border: none;
            background: #fff;
            padding: 0;
            display: flex;
            justify-content: flex-end;
        }

        :deep(.van-field__label) {
            color: #333;

        }

        .van-radio--horizontal {
            margin-right: 40px;
        }

        .van-radio-group--horizontal {
            justify-content: flex-end;
            flex: 1;
        }

        :deep(.van-field__error-message) {
            text-align: right;
        }
    }

    .special {
        .van-radio--horizontal {
            margin-right: 20px;
            margin-top: 15px;
        }

        :deep(.van-field__body) {
            border: none;
            background: #fff;
            padding: 0;
            display: flex;
            justify-content: flex-end;
        }

        :deep(.van-checkbox__icon) {
            height: fit-content;
            font-size: unset;
            line-height: none;
        }

        :deep(.van-checkbox--horizontal) {
            margin-right: 0;
        }

        :deep(.van-checkbox-group--horizontal) {
            gap: 14px;
        }

        .label-btn {
            border: 1px solid #5AA4FF;
        }

        .label-active {
            background-color: #3F95FF;
            color: #fff;
        }
    }

    .min-field {
        :deep(.van-field__control--custom) {
            align-items: flex-start;

        }
    }

    .min-input {
        :deep(.van-field__body) {
            font-weight: 400;
            font-size: 28px;
            color: #999;
            padding: 25px;
            background-color: #F1F6FB;
            border-radius: 10px;
            border: 1px solid #5AA4FF;
        }
    }

    :deep(.van-checkbox__icon .van-icon),
    :deep(.van-radio__icon .van-icon),
    :deep(.van-radio__icon) {
        display: flex;
        align-items: center;
        width: 30px;
        height: 30px;
        line-height: 30px;
        border-radius: 4px;
        border-color: #5AA4FF;
        font-size: 24px;
    }

    :deep(.van-checkbox__icon) {
        display: flex;
        align-items: center;
    }

    .add-label {
        border: 1px solid #565656;
    }

    :deep(.van-uploader__preview-image) {
        border-radius: 50%;
    }

    .controll-btn {
        background: linear-gradient(0deg, #3F95FF 0%, #93CFFD 100%);
    }

    .avatar {
        :deep(.van-field__error-message) {
            text-align: center;
        }
    }

    .required::before {
        margin-right: 2px;
        color: var(--van-field-required-mark-color);
        content: "*";
        font-size: 28px;
    }

    :deep(.van-cell:after) {
        border: none !important;
    }
}
</style>
