<template>
    <div class="my-group px-28px py-22px box-border min-h-100vh">
        <!-- <div class="flex justify-between w-100% mb-14px">
            <div class="w-48% rounded-20px bg-#fff py-30px flex items-center justify-center"
                v-for="(item, index) of Data.navData" :key="index" @click="toPage(item)">
                <img loading="lazy" :src="item.src" alt="" class="w-36px">
                <div class="px-24px text-28px">{{ item.label }}</div>
                <img loading="lazy" src="@/assets/public/icon_right.png" alt="" class="w-15px">
            </div>
        </div>
        <div class="py-36px text-32px">我的小组</div> -->
        <div class="w-full h-full">
            <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
                <interestGroupList :data="Data.groupList" :showBtn="true" @details="toDetail">
                    <template #showBtn="{ item }">
                        <div class="w-90px py-6px bg-#ccc rounded-19px text-#fff text-center text-24px"
                            @click.stop="adminOpera(item)">{{ item.identityType == '10' ? '退出' : '解散' }}</div>
                    </template>
                </interestGroupList>
            </refreshList>
        </div>
        <div class="fixed top-0 left-0 w-100% z-999" v-if="Data.showLabel">
            <labelChoose @submit-content="submitContent" :navData="Data.chooseData" />
        </div>
    </div>
</template>
<script lang="ts" setup>
import labelImg from '@/assets/interest/icon_label.png';
import infoImg from '@/assets/interest/icon_info.png';
import interestGroupList from "@/components/List/interestGroupList.vue";
import router from "@/router";
import { myGroupList, interestGroupLabelRelation, interestGroupInfo, exitGroup } from '@/api/interestGroup';
import refreshList from '@/components/refreshList/index.vue';
import { showToast, showConfirmDialog, showFailToast } from "vant";
import labelChoose from "./labelChoose.vue";
import { useUserStore } from '@/store/modules/user';
const useStore = useUserStore();
const Data = ref({
    navData: [
        { label: '我的兴趣标签', src: labelImg, id: 'label' },
        { label: '我的个人信息', src: infoImg, path: '/labelInfo' },
    ],
    groupList: [],
    pageNum: 1,
    showLabel: false,
    chooseData: []
})
const loadMoreRef = ref(null)
//获取兴趣小组列表
const getGroupList = () => {
    myGroupList({
        // groupName:Data.value.searchVal,
        // searchLabelId:Data.value.labelId,
        pageNum: Data.value.pageNum,
        pageSize: 10
    }).then((res) => {
        if (Data.value.pageNum === 1) Data.value.groupList = []
        Data.value.groupList = Data.value.groupList.concat(res.data)
        //重置刷新状态及 判断是否加载完成
        if (loadMoreRef.value) {
            loadMoreRef.value.onLoadSuc(Data.value.groupList.length, res.total)
        }
    })
}
// 刷新
const onRefreshList = () => {
    Data.value.pageNum = 1
    getGroupList()
}
// 加载更多
const onLoadMore = () => {
    Data.value.pageNum++
    getGroupList()
}
//提交标签
const submitContent = (val) => {
    interestGroupLabelRelation({
        sourceType: "user",
        labelIds: val
    }).then(res => {
        if (res) {
            showToast("提交成功");
            Data.value.showLabel = false;
        }
    })
}
//页面跳转
function toPage(item) {
    if (item.id == 'label') {
        Data.value.showLabel = true;
        return
    }

    router.push(item.path)
}
//跳转详情
function toDetail(item) {
    router.push({
        path: '/groupDetail',
        query: {
            groupId: item.groupId
        }
    })
}
function adminOpera(item) {
    console.log(121);
    if (item.identityType == '10') {
        showConfirmDialog({
            title: "提示",
            message: `确认退出${item.groupName}小组?`,
            confirmButtonText: "确认",
            cancelButtonText: "取消",
            className: "close",
        }).then(async () => {
            // 自定义代码
            exitGroup({ groupId: item.groupId }).then(res => {
                if (res.code == 200) {
                    showToast("退出成功");
                    getGroupList();
                } else {
                    showFailToast(res.message)
                }
            })
        });
    } else {
        showConfirmDialog({
            title: "提示",
            message: `确认解散${item.groupName}小组?`,
            confirmButtonText: "确认",
            cancelButtonText: "取消",
            className: "close",
        }).then(async () => {
            // 自定义代码
            interestGroupInfo(item.groupId).then(res => {
                if (res.code == 200) {
                    showToast("解散成功");
                    getGroupList()
                } else {
                    showFailToast(res.message)
                }
            })
        });
    }
}
onMounted(() => {
    Data.value.chooseData = useStore.getLabelData
    getGroupList()
})
</script>
<style lang="scss">
.my-group {
    background: url('@/assets/interest/bg_group.png'), #FAFAFC;
    background-size: 100% 25%;
    min-height: 100vh;
    background-repeat: no-repeat;
}
</style>