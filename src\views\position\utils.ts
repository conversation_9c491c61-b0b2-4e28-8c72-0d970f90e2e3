/*****FIX START******/

import { checkReservationRecord } from '@/api/position';
import { useUserStore } from '@/store/modules/user';
import dayjs from 'dayjs';
import { divide, map, split, startsWith } from 'lodash-es';
import { showFailToast } from 'vant';
import { Router } from 'vue-router';

const weekDay: Recordable = { 1: '一', 2: '二', 3: '三', 4: '四', 5: '五', 6: '六', 7: '日' };

// openWeekDay
export const fixOpenWeekDay = (item: Recordable) => {
  if (!item.openWeekDay) return '';

  const weeks = map(split(item.openWeekDay, ','), (v: string) => weekDay?.[v]);

  return `${
    weeks
      ? weeks?.length === 1
        ? weeks[0]
          ? `周${weeks[0]}`
          : ''
        : weeks[0] && weeks[weeks?.length - 1]
          ? `周${weeks[0]}至周${weeks[weeks?.length - 1]}`
          : ''
      : ''
  }`;
};

// distance
export const fixDistance = (actualDistance: number) =>
  actualDistance > 0 ? `${divide(actualDistance, 1000).toFixed(2)}公里` : 0;

/*****FIX END******/

// 校验是否有预约记录
export function checkReservation(params: Recordable<any>, autoId: number,recordId:string, router: Router) {
  checkReservationRecord(params).then(({ code, message }) => {
    if (code === 200) {
      // 预约
      router?.push({
        path: '/reserve',
        query: {
          autoId,recordId
        },
      });
    } else {
      showFailToast(`无法预约，${message}`);
    }
  });
}

export const exchangeImg = (url: string | undefined) => {
  const userStore = useUserStore();
  return startsWith(url, 'http') ? url : userStore.getPrefix + url;
};

export const exchangeTime = (time: string) => {
  return time ? dayjs(`2024-01-01 ${time}`).format('HH:mm') : '';
};
