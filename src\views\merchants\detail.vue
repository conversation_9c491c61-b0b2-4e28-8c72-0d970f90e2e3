<template>
    <div class="check-info bg-[#F6F7F8] h-fit min-h-screen">
        <van-skeleton title :row="10" :loading="loading">
            <div class="form-content mx-30px bg-#fff mt-30px rounded-20px relative z-2 mb-20px p-30px pb-0">
                <div class="store-info bg-#fff  rounded-20px " v-if="infos">
                    <div class="store flex pb-22px">
                        <img loading="lazy" :src="judgeStaticUrl(infos.companyIcon)"
                            v-previewImg="judgeStaticUrl(infos.companyIcon)" class="w-160px h-160px rounded-20px" />
                        <div class="left-info ml-20px">
                            <div class="text-32px">
                                {{ infos.companyName }}
                                <van-icon name="arrow" class="text-[32px] text-#B3B3B3 ml-10px" />
                            </div>
                            <div class="text-#999999 text-28px mt-30px">
                                <img loading="lazy" src="@/assets/public/icon_address-gray.png" class="w-22px h-26px" />
                                <span class="ml-12px">{{ infos.address }}</span>
                            </div>
                            <div class="text-#999999 text-28px mt-20px">
                                <img loading="lazy" src="@/assets/public/icon_phone-line.png" class="w-24px h-24px" />
                                <span class="ml-12px">{{ infos.contractPhone }}</span>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="infos py-35px" v-if="infos">
                    <div class="flex items-center flex-wrap py-15px" v-for="item, index in infoColumn" :key="index">
                        <div class="label text-30px text-[#808080] mr-20px w-fit">{{ item.label }}: </div>
                        <div class="content" :class="{ 'w-full pt-15px': item.type === 'img' }">
                            <template v-if="item.type === 'img'">
                                <div v-if="infos[item.props]" class="flex flex-wrap">
                                    <img loading="lazy" v-previewImg="judgeStaticUrl(img)"
                                        v-for="img, index in infos[item.props]" :src="judgeStaticUrl(img)" :key="index"
                                        class="mr-10px object-cover w-160px h-160px rounded-12px" />
                                </div>
                                <div v-else class="text-30px text-[#333]">-</div>
                            </template>
                            <div class="text-30px text-[#333]" v-else>
                                {{ infos[item.props] ? infos[item.props] : '-' }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-content mx-30px bg-#fff mt-30px rounded-20px relative z-2 mb-20px p-30px ">
                <div class="text-32px text-[#333] mb-10px font-500">商家资质</div>
                <div class="infos " v-if="infos">
                    <div class="flex items-center flex-wrap py-15px" v-for="item, index in campanyCol" :key="index">
                        <div class="label text-30px text-[#808080] mr-20px w-fit">{{ item.label }}: </div>
                        <div class="content" :class="{ 'w-full pt-15px': item.type === 'img' }">
                            <template v-if="item.type === 'img'">
                                <div v-if="infos[item.props]" class="flex flex-wrap">
                                    <img loading="lazy" v-previewImg="judgeStaticUrl(img)"
                                        v-for="img, index in infos[item.props]" :src="judgeStaticUrl(img)" :key="index"
                                        class="mr-10px object-cover w-160px h-160px rounded-12px" />
                                </div>
                                <div v-else class="text-30px text-[#333]">-</div>
                            </template>
                            <div class="text-30px text-[#333]" v-else>
                                {{ infos[item.props] ? infos[item.props] : '-' }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </van-skeleton>
    </div>
</template>
<script lang="ts" setup>
import { categoryList } from '@/api/merchants'
import { judgeStaticUrl } from '@/utils/utils';
import { getCompanyInfoByAccount } from '@/api/merchants';
const infos = ref({})
const loading = ref(true)
import { useDictionary } from '@/store/modules/dictionary';
const dictionnary = useDictionary()
const campanyCol = reactive([
    {
        label: '统一社会信息代码',
        props: 'labourUnionCode'
    },
    {
        label: '营业执照',
        props: 'licenseImgList',
        type: 'img'
    },
])
const infoColumn = reactive(
    [

        {
            label: '商家类型',
            props: 'Type'
        },
        {
            label: '所属区县',
            props: 'areaCodeName'
        },
        {
            label: '联系人',
            props: 'contractName'
        },
        {
            label: '联系电话',
            props: 'contractPhone'
        },
        {
            label: '商家介绍',
            props: 'introduce'
        }
    ]
)
const belongAreaArr = computed(() => dictionnary.getDictionaryOpt?.['regionCode'])
const getDetail = () => {
    getCompanyInfoByAccount().then(res => {
        if (res.data) {
            res.data.businessTime = `${res.data.openTime} - ${res.data.closeTime}`
            res.data.licenseImgList = res.data.companyMoreInfo.licenseImg ? res.data.companyMoreInfo.licenseImg.split(',') : []
            res.data.labourUnionCode = res.data.companyMoreInfo?.labourUnionCode
            res.data.Type = typeColumn.value.find((item: any) => item.value === res.data.typeId)?.label || ''
            infos.value = res.data
            if (res.data.areaCode) {
                infos.value.areaCodeName = belongAreaArr.value.find((item: any) => item.value === res.data.areaCode)?.label || ''
            }
            loading.value = false

        }
    }).catch((err) => {
        loading.value = false
    })
}
const typeColumn = ref([])
const getTypeList = async () => {
    const res = await categoryList({
        pageSize: 0,
        pageNum: 1
    })
    if (res.data) typeColumn.value = res.data.map((item: any) => {
        return {
            label: item.typeName,
            value: item.autoId,
        }
    })
}
onBeforeMount(
    async () => {
        await getTypeList()
        getDetail()
    }
)

</script>
<style scoped lang="scss">
.form-content {
    // min-height: calc(100vh - 250px);
}

.store-info {
    border-bottom: 1px solid #E6E6E6;
}
</style>