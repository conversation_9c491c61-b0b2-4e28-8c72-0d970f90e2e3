<template>
    <div class="live-item pb-40px">
        <div class="live-info flex justify-between mb-35px">
            <div class="info-left flex items-center" @click.stop="lookUser">
                <div class="logo w-66px h-66px mr-19px rounded-50% bg-cover center"
                    :style="{ backgroundImage: `url(${avatar})` }">
                </div>
                <div class="info-text">
                    <div class="name text-31px text-#333 font-500 leading-none">{{ detailObj.nickname }}</div>
                    <div class="sex-time flex items-center mt-10px" v-if="belongTo !== 'my' && !detailObj.myPushFlag">
                        <span class="sex mr-20px" :class="detailObj.gender">
                            <img loading="lazy" :src="detailObj.gender == 'male' ? male : female" alt=""
                                v-if="detailObj.gender">
                            <span>{{ detailObj.age }}</span>
                        </span>
                        <span class="text-26px text-#999 leading-none">{{ detailObj.height }}cm·{{ educationName
                        }}</span>
                    </div>
                </div>
            </div>
            <div class="info-right flex items-center">
                <div class="ml-20px text-#999 text-24px  flex items-center"
                    v-if="belongTo === 'my' || detailObj.myPushFlag" @click.stop="deleteFn">
                    <img loading="lazy" src="@/assets/friendShip/del.png" class="w-23px" />
                    <span class="ml-10px">删除</span>
                </div>
                <div class="" @click.stop="followFn()" v-else-if="props.singleStatus != 'y'">
                    <div class="follow-state followed" v-if="detailObj.likeFlag">
                        <img loading="lazy" class="w-25px mr-13px" src="@/assets/friendShip/file_follow.png" alt="">
                        <span class="leading-none">已关注</span>
                    </div>
                    <div class="follow-state follow" v-else>
                        <img loading="lazy" class="w-25px mr-13px" src="@/assets/friendShip/follow_icon_a.png" alt="">
                        <span class="leading-none">关注</span>
                    </div>
                </div>

            </div>
        </div>
        <div class="detailObj-box mb-15px">{{ detailObj.content }}</div>

        <div class="img-box" :class="imageUrlArr.length < 2 ? 'one' : imageUrlArr.length < 3 ? 'two' : 'three'"
            v-if="detailObj.imgUrl">
            <div v-for="item, index in imageUrlArr" @click.stop="previewImage(index)"
                :style="{ backgroundImage: `url(${item})` }"></div>
        </div>

        <div class="btm-box flex items-center justify-between">
            <div class="send-time text-26px text-#B2B2B2 ">
                发布于{{ detailObj.releaseTimeChinese }}
            </div>
            <div class="btn-box flex ">
                <div class="like">
                    <img loading="lazy" @click.stop="likeFn()" :src="detailObj.whetherLike ? like : dislike" alt="">
                    <span>{{ detailObj.likeCount }}</span>
                </div>
                <div class="comment">
                    <img loading="lazy" src="@/assets/friendShip/icon_comment.png" alt="" />
                    <span>{{ detailObj.commentVOList?.count || 0 }}</span>
                </div>
            </div>
        </div>
        <!-- 评论内容 -->
        <div class="comment-box mt-15px relative" v-if="showComment && detailObj?.commentVOList?.treeList">
            <!-- :class="{ '!h-fit max-h-max': lookMore }" -->
            <div class="max-h-300px overflow-hidden">
                <commentList :currrentUserId="userInfo?.userId" :contentArry="detailObj.commentVOList.treeList"
                    :postUserId="detailObj?.userId" :commentType="commentType">
                </commentList>
            </div>
            <div class="more text-24px text-#5AA4FF mt-10px">
                查看更多
            </div>
        </div>
        <!-- 气泡样式 -->
        <div class="fixed left-50% top-50% -translate-50% z-100">
            <waterIntergral v-model:show="showBubble" :score="scoreNum" bigSize="150px" mid-size="62px"
                small-size="50px" scorefontSize="40px"></waterIntergral>
        </div>
    </div>
</template>
<script setup lang="ts">
const waterIntergral = defineAsyncComponent(() => import('@/components/Bubble/waterIntergral.vue'))
import male from '@/assets/friendShip/male_icon_w.png'
import female from '@/assets/friendShip/female_icon_w.png'
import like from '@/assets/friendShip/zan_icon_a.png'
import dislike from '@/assets/friendShip/zan_icon_w.png'
import { judgeStaticUrl } from '@/utils/utils';
import { useDictionary } from '@/store/modules/dictionary';
import { likeOperate } from '@/api/public'
import { showConfirmDialog, showImagePreview } from 'vant';
import { followUserApi, deleteDynamic } from '@/api/friendship/index'
import { useUserStore } from '@/store/modules/user';
import { useFriendShipStore } from '@/store/modules/friendShip.ts'
import commentList from './commentList.vue';
const friendShipStore = useFriendShipStore()
const dictionary = useDictionary()
const useStore = useUserStore();
const userInfo = computed(() => {
    return useStore.userInfo
})
const props = defineProps({
    detailObj: {
        type: Object,
        default() {
            return {
                userId: '',
            }
        },
        required: true
    },
    belongTo: {
        type: String,
        default: 'other'
    },
    commentType: {
        type: String,
        default: 'simple'
    },
    showComment: {
        type: Boolean,
        default: true
    },
    singleStatus: {
        type: String,
        default: 'n'
    }
})
watch(() => friendShipStore.getMyFollowed, (val) => {
    if (props.detailObj.userId) props.detailObj.likeFlag = val.includes(props.detailObj.userId)
}, { deep: true })

const avatar = computed(() => {
    return judgeStaticUrl(props.detailObj?.avatar, true)
})
const educationName = computed(() => {
    return getDictionaryLabel('modelEducation', props.detailObj?.education)
})
const imageUrlArr = computed(() => {
    return props.detailObj?.imgUrl ? imageFilter(props.detailObj.imgUrl) : []
})

const emit = defineEmits(['handleRefresh'])//需要列表局部刷新
// 根据数据字典查询
const getDictionaryLabel = (dictionaryType: any, value: any) => {
    let label
    try {
        let c = dictionary.getDictionaryOpt?.[dictionaryType].find((t: any) => { return t.value == value })
        label = c.label
    } catch {
        label = value
    }
    return label
}
const imageFilter = (imgUrl: any) => {
    if (!Array.isArray(imgUrl)) {
        return imgUrl.split(',').map((item: string) => {
            return judgeStaticUrl(item)
        })
    }
}
// 气泡提示框参数设置
const showBubble = ref(false);
const scoreNum = ref(0);

//按钮
let btnClick = false
// 点赞
const likeFn = async () => {
    if (btnClick) return
    btnClick = true
    const { code, data: { score } } = await likeOperate({
        sourceId: props.detailObj.postId
    })
    btnClick = false
    if (code === 200) {
        if (score) {
            scoreNum.value = score
            showBubble.value = true
        }
        emit('handleRefresh')
    }
}
// 关注
const followFn = async () => {
    if (btnClick) return
    btnClick = true
    const { code, data } = await followUserApi({
        singleUserId: props.detailObj.userId
    })
    if (code === 200) friendShipStore.setMyFollowed(data)
    btnClick = false
}
const router = useRouter()
const route = useRoute()
const lookUser = () => {
    if (route.path === '/friendship/myProfile' || route.path === '/friendship/detail') return

    if (props.detailObj.myPushFlag) {
        router.push({
            path: '/friendship/myProfile',
        })
    }
    else {
        router.push({
            path: '/friendship/detail',
            query: {
                singleUserId: props.detailObj.userId,
            }
        })
    }

}
// 删除
const deleteFn = () => {
    showConfirmDialog({
        message: '确定要删除该动态吗？',
    }).then(async () => {
        btnClick = true
        const { code } = await deleteDynamic(props.detailObj.postId)
        if (code === 200) {
            emit('handleRefresh')
        }
        btnClick = false
    })
}

// 图片预览
const previewImage = (index: number) => {
    showImagePreview({
        images: imageFilter(props.detailObj.imgUrl),
        startPosition: index,
    })
}
// 评论查看更多
const lookMore = ref(false)
</script>
<style scoped lang="scss">
.live-item {
    .comment-box {
        width: 100%;
        background: #F5F5F5;
        border-radius: 10px;
        padding: 30px;
        box-sizing: border-box;

        .comment-item {
            margin-bottom: 30px;

            &:nth-last-child(1) {
                margin-bottom: 0;
            }
        }

        .comment-detailObj {
            font-weight: 400;
            font-size: 28px;
            color: #333333;
            line-height: 38px;

            .name {
                font-weight: 550;
                font-size: 30px;
                color: #333333;
            }
        }
    }

    .detailObj-box {
        font-weight: 400;
        font-size: 30px;
        color: #333333;
        line-height: 38px;
    }

    .img-box {
        display: flex;
        flex-flow: row wrap;
        margin-bottom: 12px;

        &.one {
            >div {
                background-size: cover;
                background-position: center;
                width: 100%;
                height: 226px;
                border-radius: 15px;
                background-image: url('@/assets/friendShip/defualt_banner.png');
            }
        }

        &.two {
            justify-content: space-between;

            >div {
                background-size: cover;
                background-position: top center;
                width: calc((100% - 17px) / 2);
                // margin-right: 11px;
                height: 226px;
                border-radius: 15px;
                background-image: url('@/assets/friendShip/defualt_banner.png');

            }

        }

        &.three {

            >div {
                background-size: cover;
                background-position: top center;
                width: calc((100% - 28px)/3);
                margin-right: 14px;
                height: 226px;
                border-radius: 15px;
                margin-bottom: 16px;
                background-image: url('@/assets/friendShip/defualt_banner.png');

                &:nth-child(3n) {
                    margin-right: 0;
                }
            }
        }
    }

    .btn-box {
        >div {
            display: flex;
            align-items: center;
            margin-left: 51px;
            font-weight: 400;
            font-size: 24px;
            color: #999999;

            >img {
                width: 27px;
                margin-right: 13px;
            }
        }
    }

    .live-info {
        width: 100%;

        .info-left {

            .info-text {
                display: flex;
                flex-flow: column;

                .sex-time {
                    .sex {
                        font-weight: 400;
                        font-size: 24px;
                        color: #FFFFFF;
                        width: 85px;
                        height: 36px;
                        border-radius: 18px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        background: linear-gradient(90deg, #9FC9FF, #4196FF);

                        >img {
                            width: 19px;
                            margin-right: 7px;
                        }

                        &.female {
                            background: linear-gradient(90deg, #FAA8AB, #FF7097);
                        }

                        &.male {
                            background: linear-gradient(90deg, #9FC9FF, #4196FF);
                        }
                    }
                }
            }
        }

    }

    .follow-state {
        width: 140px;
        height: 48px;
        border-radius: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: 26px;

        &.follow {
            border: 2px solid #F4698D;
            color: #F4698D;
        }

        &.followed {
            border: 2px solid #999999;
            color: #999999;
        }
    }
}
</style>