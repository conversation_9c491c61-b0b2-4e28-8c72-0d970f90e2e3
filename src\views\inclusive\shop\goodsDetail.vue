<template>
    <div class="goods-detail bg-#F9F9F9 p-30px min-h-100vh w-full pb-140px">
        <van-skeleton title :row="15" :loading="loading">
            <div class="goods-mian p-26px bg-#fff rounded-28px mb-22px">
                <van-swipe class="my-swipe w-full h-517px box-border" :autoplay="3000" indicator-color="white">
                    <van-swipe-item v-for="(item, index) in goodsInfo.productPublicityImg"
                        class="w-full h-full product-img" :key="index">
                        <img loading="lazy" :src="item" v-previewImg="item" class="w-full h-full" />
                    </van-swipe-item>
                </van-swipe>
                <div class="price-display-box flex items-center justify-between w-full h-100px p-20px box-border">
                    <div class="left-price flex items-start">
                        <div>
                            <div class="price-text text-42px font-bold text-#fff">
                                <span class="text-20px">￥</span>
                                {{ specifyInfo[currentSpecifyIndex]?.nowPrice }}
                            </div>
                            <div class="old-price-text text-22px text-#FFDBDB">￥
                                {{ specifyInfo[currentSpecifyIndex]?.oldPrice }}
                            </div>
                        </div>

                        <div class="special-tag ml-30px text-#FF203A bg-#FFECC4 rounded-16px 
                        py-6px px-20px leading-none text-22px mt-5px" v-if="goodsTag">{{ goodsTag }}
                        </div>
                    </div>
                    <div class="right-sales text-#9B7D40 text-20px">
                        <div class="sales-text">销量:{{ specifyInfo[currentSpecifyIndex]?.saleNum }}</div>
                        <div class="sales-text mt-10px">库存:
                            {{ specifyInfo[currentSpecifyIndex]?.reserveType === 'limited'
                                ? specifyInfo[currentSpecifyIndex]?.reserve : '不限' }}</div>
                    </div>
                </div>
                <div class="title mt-30px font-500 leading-1.4em text-30px">
                    {{ goodsInfo.productName }}
                </div>
            </div>


            <div class="goods-params p-26px bg-#fff rounded-28px mb-22px">
                <div class="cell-1  flex items-center">
                    <img loading="lazy" src="@/assets/inclusive/shop/coupon-text.png" class="w-78px block" />
                    <div class="inclusive-coupon-1 flex items-center 
                    ml-18px p-16px text-26px rounded-10px">
                        <div class="info-text">
                            满300减60
                        </div>
                        <div class="rec-text">领取</div>
                    </div>
                    <div class="inclusive-coupon-2 flex items-center 
                    ml-18px p-16px text-26px rounded-10px">
                        <div class="info-text">
                            8.8折优惠
                        </div>
                        <div class="rec-text">领取</div>
                    </div>
                    <div class="arrow-right flex-1 flex justify-end">
                        <van-icon name="arrow" class="text-30px text-#999"></van-icon>
                    </div>
                </div>
                <div class="cell-2 flex items-center mt-38px" v-if="specifyInfo.length" @click="showSpecify = true">
                    <div class="text-28px text-#666 leading-none">规格</div>
                    <div class="arrow-right flex-1 flex justify-end text-28px text-#333 ml-10px">
                        {{ specifyInfo[currentSpecifyIndex]?.productSubName }}
                        <div class="arrow-right flex-1 flex items-center justify-end text-30px text-#999">
                            <span class="leading-none">x{{ currentSpecifyNum }}</span>
                            <van-icon name="arrow"></van-icon>
                        </div>
                    </div>
                </div>
            </div>
            <div class="goods-reviews p-26px bg-#fff rounded-28px mb-22px flex items-center justify-between"
                @click="toPage('/inclusive/rating', 'rate')">
                <div class="text-32px text-#333">商品评分 ({{ goodsEvaluate?.total }})</div>
                <div class="flex items-center mt-10px">
                    <div class="text-#FF6342 text-26px mr-10px">
                        <span class="font-bold text-36px">{{ goodsEvaluate?.comprehensiveScore }}</span>分
                    </div>
                    <div class="arrow-right flex-1 flex justify-end">
                        <van-icon name="arrow" class="text-30px text-#999"></van-icon>
                    </div>
                </div>
            </div>

            <div class="store-info flex items-center justify-between p-26px bg-#fff rounded-28px"
                v-if="goodsInfo?.companyId">
                <div class="flex flex-1">
                    <div class="store-info-left w-114px h-114px rounded-12px bg-#E44845">
                        <img loading="lazy" :src="utils.judgeStaticUrl(merchantInfo?.companyIcon)"
                            v-previewImg="utils.judgeStaticUrl(merchantInfo?.companyIcon)"
                            class="w-full h-full object-fit" />
                    </div>
                    <div class="store-info-right ml-20px flex flex-col justify-between flex-1">
                        <div class="store-name text-#333 text-30px">{{ merchantInfo?.companyName
                        }}</div>
                        <div class="store-address text-#666 text-24px" @click="toAddress">{{ merchantInfo?.address }}
                        </div>
                    </div>
                </div>
                <div class="store-info-btn text-#FE3C5E bg-#FFF0F3 flex items-center justify-center
            ml-14px w-134px h-42px rounded-20px text-24px leading-none"
                    @click="toPage('/inclusive/merchant', 'merchant')">
                    进店逛逛
                </div>
            </div>

            <div class="goods-detail-info p-26px bg-#fff rounded-28px mt-22px mb-22px">
                <div class="second-title text-32px text-#333 flex items-center leading-none">
                    商品详情
                </div>
                <div class="text-28px text-[#666] mt-30px" v-html="goodsRichText">
                </div>
            </div>
        </van-skeleton>
        <!-- 规格弹窗 -->
        <specifyGoodsPopup v-model:show="showSpecify" sourceType="3" :defaultIndex="currentSpecifyIndex"
            :defaultNum="currentSpecifyNum" :list="specifyInfo" @selected="selecedSpeficy" />

        <div class="fixed left-0 bottom-0 w-full fix-bottom h-140px bg-#fff flex items-center justify-between px-30px">
            <div class="collect-icon text-center" @click="handleCollect">
                <img loading="lazy" :src="goodsInfo?.logicallyDelete === 'n' ? collectIconActive : collectIcon"
                    class="w-36px h-36px" />
                <div class="text-#666 text-24px">收藏</div>
            </div>
            <div class="controll-btn flex items-center text-32px">
                <div class="add-cart bg-#FFE9ED color-#FF4344 px-48px py-22px rounded-36px leading-none"
                    @click="handleAddCart">加入购物车</div>
                <div class="buy-btn ml-20px bg-#FF4344 color-#fff px-48px py-22px rounded-36px leading-none"
                    @click="handleBuyNow">立即购买</div>
            </div>
        </div>
        <!-- 气泡样式 -->
        <div class="fixed bottom-140px z-100" :style="`left:${bubbleX}%`">
            <waterIntergral v-model:show="showBubble" :score="scoreNum"></waterIntergral>
        </div>
    </div>
</template>
<script lang="ts" setup>
import useShoppingCart from '@/hooks/shopingCart'
const waterIntergral = defineAsyncComponent(() => import('@/components/Bubble/waterIntergral.vue'))
const specifyGoodsPopup = defineAsyncComponent(() => import('@/components/Popup/specifyGoodsPopup.vue'))
import { integralGoodsDetail, integralSpecifyGoodsDetail, integralGoodsDetailText } from '@/api/mall'
import { inclusiveGoodsStatistics, inclusiveMerchantsDetails } from '@/api/mall/inclusive'
import collectIcon from '@/assets/inclusive/shop/icon_sc.png'
import collectIconActive from '@/assets/inclusive/shop/icon_sc_a.png'
import { collectOperate } from "@/api/public"
import utils from '@/utils/utils'
import { showToast } from 'vant'
import { useDictionary } from '@/store/modules/dictionary'
import { useMallStore } from '@/store/modules/mall'
const mallStore = useMallStore()

const route = useRoute()
let productId = route.query?.productId as string
const loading = ref(true)
const goodsInfo = ref<any>()
// 商家详情
const merchantInfo = ref<any>()
// 规格
const specifyInfo = ref<any>([])
// 商品详情信息
const getDetail = async () => {
    const { code, data } = await integralGoodsDetail(productId)
    if (code === 200) {
        goodsInfo.value = data
        if (data.productId) productId = data.productId

        // 规格信息查询
        const res1 = await integralSpecifyGoodsDetail({
            productId,
            systemQueryType: 'h5'
        })
        if (res1.code === 200) {
            specifyInfo.value = res1.data
            if (route.query?.productSubId && Array.isArray(specifyInfo.value)) {
                const index = specifyInfo.value.findIndex((item: any) => item.productSubId === route.query?.productSubId)
                if (index !== -1) {
                    currentSpecifyIndex.value = index
                    if (route.query?.currentProductCount) currentSpecifyNum.value = Number(route.query.currentProductCount)
                }
            }
        }
        loading.value = false
        // 商家详情信息查询
        const res2 = await inclusiveMerchantsDetails(goodsInfo.value?.companyId)
        if (res2.code === 200) merchantInfo.value = res2.data

    }
    else loading.value = false

    if (!Array.isArray(goodsInfo.value?.productPublicityImg)) {
        goodsInfo.value.productPublicityImg = goodsInfo.value?.productPublicityImg.split(',').map((item: any) => utils.judgeStaticUrl(item))
    }
}
const goodsTag = computed(() => {
    return useDictionary().getDictionaryOpt?.['inclusiveProductColumn']?.find((item: any) => item.value === goodsInfo.value?.inclusiveProductColumn)?.label || ''
})
const showSpecify = ref(false)
const currentSpecifyIndex = ref(0)
const currentSpecifyNum = ref(1)//当前选中规格数量


const selecedSpeficy = ({ index, num }) => {
    currentSpecifyIndex.value = index
    currentSpecifyNum.value = num
    showSpecify.value = false
}

// 商品详情富文本
const goodsRichText = ref('')
const getProductRichtext = async () => {
    const { data, code } = await integralGoodsDetailText(productId)
    if (code === 200) goodsRichText.value = data.productIntroduce
}
// 商品评分阶段统计
const goodsEvaluate = ref<any>()
const getGoodsEvaluate = () => {
    inclusiveGoodsStatistics(productId).then((res) => {
        if (res.code === 200) goodsEvaluate.value = res.data
    })
}


// 收藏
// 气泡提示框参数设置
const bubbleX = ref(2);
const showBubble = ref(false);
const scoreNum = ref(0);
let isReq = false;
const handleCollect = async () => {
    if (isReq) return
    isReq = true
    const { code, data } = await collectOperate({ sourceId: productId })
    isReq = false

    if (code !== 200) return showToast('操作失败')
    const { statefulFlowState, score } = data
    if (statefulFlowState === true) goodsInfo.value.logicallyDelete = 'n'
    else if (statefulFlowState === false) goodsInfo.value.logicallyDelete = 'y'

    if (score) {
        scoreNum.value = score
        showBubble.value = true;
    }
}
// 路由跳转
const router = useRouter()
const toPage = (path: string, from: any) => {
    let query = {}
    if (from === 'rate') query = { productId }
    else if (from === 'merchant') query = { companyId: goodsInfo.value?.companyId }

    router.push({
        path,
        query
    })
}
// 导航
const toAddress = () => {
    utils.mapNav({
        win: route.fullPath,
        name: merchantInfo.value?.address,
        lat: merchantInfo.value?.addressCoordinate?.split(',')[1] || '',
        lon: merchantInfo.value?.addressCoordinate?.split(',')[0] || ''
    })
}

// 购物车
const { addCart } = useShoppingCart()
const handleAddCart = () => {
    const form = {
        companyId: goodsInfo.value?.companyId,
        productId,
        productSubId: specifyInfo.value[currentSpecifyIndex.value]?.productSubId || '',
        amount: currentSpecifyNum.value,
        operateType: "add"
    }
    addCart(form, (res: any) => { })
}

// 立即支付
const handleBuyNow = () => {
    const { productSubId, productSubName,
        productSubImg, nowPrice, oldPrice, purchaseLimit } = specifyInfo.value[currentSpecifyIndex.value]
    const confirmList = [
        {
            companyId: goodsInfo.value?.companyId,
            companyName: merchantInfo.value?.companyName,
            priceNum: nowPrice,
            productInfoList: [
                {
                    productId,
                    productName: goodsInfo.value?.productName,
                    priceListInfo: [
                        {
                            currentProductCount: currentSpecifyNum.value,
                            productId,
                            productSubId,
                            productSubName,
                            productSubImg,
                            nowPrice,
                            oldPrice,
                            purchaseLimit
                        }
                    ]
                }
            ]
        }
    ]
    mallStore.setInclusiveGoodsList(confirmList)
    router.push({
        path: '/inclusive/create',
        query: {
            type: '2',// 1:购物车结算 2.立即支付
        }
    })
}


onBeforeMount(() => {
    getDetail()
    getProductRichtext()
    getGoodsEvaluate()
})
</script>
<style scoped lang="scss">
.inclusive-coupon-1 {
    color: #FE3C5E;
    background-color: #FFF0F3;
    border: 1px solid #FE3C5E;
}

.inclusive-coupon-2 {
    color: #FF6342;
    background-color: #FFF1E7;
    border: 1px solid #FF6342;
}

.price-display-box {
    background-image: url('@/assets/inclusive/shop/price-box.png');
    background-size: 100% auto;
}

.old-price-text {
    text-decoration: line-through;
}

.second-title::before {
    display: block;
    content: '';
    width: 10px;
    height: 35px;
    background-color: #FF4344;
    border-radius: 5px;
    margin-right: 10px;
}

.product-img {
    img {
        border-radius: 28px 28px 0 0;
    }
}
</style>