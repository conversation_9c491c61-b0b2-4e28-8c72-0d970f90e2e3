<template>
    <div class="rating w-full min-h-full px-30px py-44px">
        <div class="all-rating text-32px text-#333">全部评分<span class="text-#666 text-26px">({{ staticData?.total
                }})</span>
        </div>
        <div class="border-bottom pb-38px">
            <div class="comprehensive-rating flex items-center justify-between bg-#F5F5F5 rounded-28px p-30px mt-26px">
                <div class="rating-left flex flex-col items-center">
                    <div class="text-30px mb-12px">综合评分</div>
                    <div class="text-56px font-bold">{{ staticData?.comprehensiveScore }}</div>
                    <div class="rating-icons mt-10px">
                        <van-rate v-model="staticData.comprehensiveScore" readonly allow-half color="#FF4344" />
                    </div>
                </div>
                <div class="rating-right flex-1">
                    <div class="flex items-center" v-for="item, index in rateStage" :key="index">
                        <div class="rate flex w-50% text-28px">
                            <van-rate v-model="item.rate" :count="item.rate" color="#E5E3E3" readonly />
                        </div>
                        <div class="ml-26px bg-#E5E3E3 flex-1 h-10px rounded-10px relative">
                            <div class="absolute h-10px rounded-10px bg-#FF4344"
                                :style="{ width: item.ratePercent + '%' }">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="rating-list">
            <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
                <div class="rating-item flex justify-between py-22px border-bottom" v-for="item, index in list"
                    :key="index">
                    <div class="avator w-56px h-56px mr-22px">
                        <img src="@/assets/public/head_default.png" class="w-full h-full" />
                    </div>
                    <div class="flex-1 flex items-center justify-between">
                        <div class="account">
                            <div class="phone">{{ item.userPhone }}</div>
                            <div class="score mt-16px text-#B3B3B3">
                                <span class="mr-10px">打分</span><van-rate readonly v-model="item.score" allow-half
                                    color="#FF4344" /><span class="ml-10px">{{ item.scoreLevel }}</span>
                            </div>
                        </div>
                        <div class="time text-#B3B3B3 text-24px">{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm') }}
                        </div>
                    </div>
                </div>
            </refreshList>

        </div>
    </div>
</template>
<script lang="ts" setup>
import { inclusiveGoodsStatistics, inclusiveGoodsCommentList } from '@/api/mall/inclusive'
import refreshList from '@/components/refreshList/index.vue'
import dayjs from 'dayjs'
const rateStage = ref(
    [
        {
            rate: 5,
            ratePercent: 0,
        },
        {
            rate: 4,
            ratePercent: 0,
        },
        {
            rate: 3,
            ratePercent: 0,
        },
        {
            rate: 2,
            ratePercent: 0,
        },
        {
            rate: 1,
            ratePercent: 0,
        },
    ]
)
const route = useRoute()
const productId = route.query.productId as string;

const staticData = ref<any>({})//综合及阶段评分
const list = ref<any>([])//评论列表数据
const getInclusiveGoodsStatistics = async () => {
    const { code, data } = await inclusiveGoodsStatistics(productId)
    if (code === 200) {
        const { comprehensiveScore, total } = data
        staticData.value = {
            comprehensiveScore,
            total
        }
        if (data.details && Array.isArray(data.details)) {
            data.details.forEach((item: any) => {
                const index = rateStage.value.findIndex((i: any) => i.rate === item.key)
                if (total) rateStage.value[index].ratePercent = Math.floor((item.value / total * 100))
                else rateStage.value[index].ratePercent = 0
            })
        }
    }
}
let pageNum = 1
const loadMoreRef = ref()
// 刷新
const onRefreshList = () => {
    pageNum = 1
    loadMoreData()
}
// 加载更多
const onLoadMore = () => {
    pageNum++
    loadMoreData()
}
const loadMoreData = async () => {
    const { code, data, total } = await inclusiveGoodsCommentList({
        orderBy: 'a.create_time',
        sortType: 'desc',
        pageSize: 10,
        pageNum,
        productId
    })
    if (pageNum === 1) list.value = []
    if (code === 200) {
        if (pageNum === 1) list.value = data
        else list.value = [...list.value, ...data]
    }
    //重置刷新状态及 判断是否加载完成
    if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(list.value.length, total)
    }
}
onBeforeMount(() => {
    getInclusiveGoodsStatistics()
    loadMoreData()
})
</script>
<style scoped lang="scss">
.comprehensive-rating {
    .rating-left {
        .rating-icons {
            :deep(.van-rate__icon) {
                font-size: 30px;
            }

        }
    }

    .rating-right {
        .rate {
            flex-direction: row-reverse;

            :deep(.van-rate__icon) {
                font-size: 28px;
            }
        }
    }
}

.border-bottom {
    border-bottom: 1px solid #EFEFEF;
}
</style>