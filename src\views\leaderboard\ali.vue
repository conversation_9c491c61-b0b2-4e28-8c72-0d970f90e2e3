<template>
    <div class="ali-main relative">
        <img loading="lazy" src="@/assets/leaderBoard/ali/banner1.png" class="w-full banner absolute top-0 z-1" />
        <div class="relative z-2 mt-162px px-30px content flex flex-col pb-50px border-box">
            <dateSelect :dateTypeArr="dateTypeArr" :defaultType="currentIndex" v-model:default-time="currentDate"
                :columnsType="columnsType" @changeType="changeType">
            </dateSelect>
            <!-- 活跃度指数 -->
            <div class="active-index text-center mt-44px w-full h-204px 
                flex items-center justify-center relative z-2 py-40px border-box">
                <div>
                    <div class="num text-40px text-#63A8FF font-bold leading-none" style="font-family: Arial;">
                        {{ currentAli?.ali || 0 }}
                    </div>
                    <div class="text-#333 text-28px mt-26px leading-none">{{ currentAli?.areaName }}ALI(活跃度指数)</div>
                </div>
                <!-- to-right-animation -->
                <img loading="lazy" src="@/assets/leaderBoard/ali/icon_double_arrow.png" class="w-50px ml-40px " />
            </div>

            <div class="join-line flex justify-between px-56px -mt-50px relative z-3">
                <div class="line">
                    <img loading="lazy" src="@/assets/leaderBoard/ali/line.png" class="w-22px">
                </div>
                <div class="line">
                    <img loading="lazy" src="@/assets/leaderBoard/ali/line.png" class="w-22px">
                </div>
            </div>
            <!-- 排行榜 -->

            <div class="rank-list w-full flex-1 rounded-20px -mt-50px relative z-2 pt-72px px-28px pb-20px">
                <div class="rank-title w-full h-72px text-#63A8FF text-30px font-500"
                    style="font-family: Source Han Sans CN;">
                    <div>排行榜</div>
                    <div>区县名称</div>
                    <div>ALI(活跃度指数)</div>
                </div>
                <template v-if="rankList?.length">
                    <div class="rank-list-content text-30px mt-20px" :class="`rank-list-content-${index}`"
                        v-for="item, index in rankList" :key="index">
                        <div class="text-#3B9EFF">
                            <img loading="lazy" src="@/assets/leaderBoard/ali/icon_first.png" class="w-45px"
                                v-if="index === 0" />
                            <img loading="lazy" src="@/assets/leaderBoard/ali/icon_second.png" class="w-45px"
                                v-else-if="index === 1" />
                            <img loading="lazy" src="@/assets/leaderBoard/ali/icon_third.png" class="w-45px"
                                v-else-if="index === 2" />
                            <span v-else>{{ index + 1 }}</span>
                        </div>
                        <div class="text-#333 company-name"> {{ item?.areaName }} </div>
                        <div class="text-#63A8FF">
                            <span class="text-30px font-bold mr-40px company-index">{{ item?.ali }}</span>
                            <van-icon name="arrow" color="#3B9EFF" class="text-30px"></van-icon>
                        </div>
                    </div>
                </template>
                <Empty v-else />
            </div>
        </div>

    </div>
</template>
<script setup lang="ts">
import { useUserStore } from '@/store/modules/user';
const useStore = useUserStore();
import { aliRanking } from '@/api/leaderboard/ali'
import dateSelect from './components/dateSelect.vue'
import {showLoadingToast,closeToast} from 'vant'
import Empty from '@/components/Empty/index.vue'
const dateTypeArr = ref([
    {
        label: '年榜',
        value: 'year'
    },
    {
        label: '月榜',
        value: 'month'
    }
])
const currentMonth = new Date().getMonth() + 1 < 10 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1
const currentDate = ref([new Date().getFullYear(), currentMonth])
// 默认年榜
const currentIndex = ref(1)
const columnsType = ref(['year', 'month'])
const changeType = (val: any) => {
    currentIndex.value = val
    if (val === 0) {
        columnsType.value = ['year']
        currentDate.value = [new Date().getFullYear()]
    }
    else {
        columnsType.value = ['year', 'month']
        currentDate.value = [new Date().getFullYear(), currentMonth]
    }
}
watch(currentDate, () => {
    getAliRanking()
})

// const router = useRouter()
// const toDetail = () => {
//     router.push('/leaderAliDetail')
// }
const currentUserCode = computed(() => useStore.leaderCode)
const rankList = ref<any>([])
const currentAli = ref<any>({})
const getAliRanking = () => {
    showLoadingToast({
        message: '稍等，ALI正计算中...',
        forbidClick: true
    })
    aliRanking({
        dateRange: currentDate.value.join('-')
    }).then(res => {
        closeToast()
        if(!res.data) return
        rankList.value = res.data.map((item: any) => {
            item.ali = item.ali.toLocaleString()
            return item
        })
        currentAli.value = rankList.value.find((item: any) => item.areaCode === currentUserCode.value)
    })
    .catch(err => {
        closeToast()
    })
}
onMounted(() => {
    getAliRanking()
})
</script>
<style lang="scss" scoped>
.ali-main {
    width: 100%;
    min-height: 100vh;
    background: linear-gradient(to bottom, #D8E9FE 0%, #F6FBFF 20%, #F6FBFF 80%, #DCEEFB 100%);
    box-sizing: border-box;
    position: relative;

    .content {
        height: calc(100% - 162px);
    }


    .active-index {
        background-image: url('@/assets/leaderBoard/ali/alibg.png');
        background-size: 100% auto;
    }

    .rank-list {
        background: linear-gradient(0deg, #FFFFFF 54%, #E5F3FF 100%);

        .rank-title {
            display: grid;
            grid-template-columns: 1fr 1fr 2fr;
            background-image: url('@/assets/leaderBoard/ali/title_bg.png');
            background-size: 100% 100%;
            place-items: center;
        }

        .rank-list-content {
            display: grid;
            grid-template-columns: 1fr 1fr 2fr;
            grid-template-rows: 72px;
            place-items: center;
            background-color: transparent;

        }

        .rank-list-content-0 {
            background-color: rgb(255, 234, 150, .2);

            .company-name,
            .company-index {
                color: #FC9100 !important;
            }

        }

        .rank-list-content-1 {
            background-color: rgb(211, 216, 225, .2);

            .company-name,
            .company-index {
                color: #9FA7B9 !important;
            }
        }

        .rank-list-content-2 {
            background-color: rgb(246, 200, 169, .2);

            .company-name,
            .company-index {
                color: #E38557 !important;
            }
        }
    }

    .to-right-animation {
        animation: toright 1s linear infinite alternate;
    }

    @keyframes toright {
        0% {
            transform: translateX(0);
        }

        100% {
            transform: translateX(10px);
        }
    }
}
</style>