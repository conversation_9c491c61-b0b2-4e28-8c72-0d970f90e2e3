<template>
    <div @touchstart="touchstart" @touchmove="touchmove">
        <van-popup :show="showPop" class="popup1" position="left" :style="{ width: '80%', height: '100%' }">
            <div class="px-33px box-border w-full h-full relative pt-50px overflow-hidden" v-if="showPop">
                <img loading="lazy" src="@/assets/home/<USER>" alt="" class="w-220px  ml-15px">
                <div class="pt-20px text-#5AA4FF text-32px pl-30px pb-60px">心系职工，服务至上</div>
                <div class="flex flex-wrap content max-h-80vh overflow-auto">
                    <div class="w-160px h-160px bg-#F6F7F8 rounded-10px mr-20px mb-20px" v-for="(item, index) of data"
                        :key="index" @click="toPage(item)">
                        <div class="flex flex-col justify-center items-center text-28px h-full">
                            <img loading="lazy" :src="item.src" alt="" class="w-80px h-80px mb-12px" />
                            {{ item.name }}
                        </div>
                    </div>
                </div>
                <div class="absolute w-8px h-45px top-1/2 -translate-y-1/2 bg-#fff rounded-4px right-15px"
                    @click="close()"></div>
            </div>
        </van-popup>
    </div>

</template>
<script lang="ts" setup>
import { getCurrentInstance, ref } from 'vue';
const emit = defineEmits(['closePopup']);
const currentInstance = getCurrentInstance();
import { queryMyDetail } from "@/api/friendship/index"//判断单身联谊是否认证
import router from "@/router";
import utils from '@/utils/utils';
import { useUserStore } from '@/store/modules/user';
import { showToast } from 'vant';
const useStore = useUserStore();
const show = ref(false)
function touchstart(e) {
    currentInstance.startx = e.touches[0].clientX
}
function touchmove(e) {
    currentInstance.movex = e.touches[0].pageX
    if (currentInstance.startx - currentInstance.movex <= 0) {
        show.value = true;
        emit('closePopup', show.value)
    }
    else {
        show.value = false;
        emit('closePopup', show.value)
    }
}
function close() {
    show.value = false;
    emit('closePopup', false)
}
const props = defineProps({
    showPop: {
        type: Boolean,
        default: false
    },
    data: {
        type: Array,
        default: []
    }
})
async function toPage(item) {
    if (item.name === '单身联谊') {
        const res = await queryMyDetail()
        if (res.data) {
            return router.push({
                path: '/friendship',
            })
        } else {
            return router.push({
                path: '/friendship/activity',
            })
        }
        return
    }
    if (item.record) {
        if (item.record.MenusName == '困难帮扶' || item.record.MenusName == '职工书屋') {
            if (!useStore.getUserInfo?.userId) {
                showToast("您当前未登录，请先登录川工之家！");
                return
            }
            if (!useStore.getUserInfo?.companyId) {
                showToast("您不是工会会员，请先加入工会！");
                return
            }
        }
        if (item.record.MenusName == '法律援助') {
            if (!useStore.getUserInfo?.userId) {
                showToast("您当前未登录，请先登录川工之家！");
                return
            }
        }
        utils.openService(item.record);
        return
    }
    if (item.toastText) {
        showToast(item.toastText)
        return
    }
    if (item.path) {
        router.push(item.path);
        show.value = false;
        emit('closePopup', false)
    }
}
</script>
<style lang="scss" scoped>
.van-overlay {
    background: rgba(0, 0, 0, .4);
}

.van-popup {
    background: url("@/assets/home/<USER>") no-repeat;
    background-size: 100% 100%;
}

.content {
    >:nth-child(3n) {
        margin-right: 0;
    }
}
</style>