<template>
    <div class=" p-27px box-border" :class="!props.showBtn?'label-bg':'label-choose'">
        <div class=" text-#333 text-34px font-medium" :class="!props.showBtn?'pt-70px':'pt-110px'">选择更符合你的标签</div>
        <div class="pt-33px text-28px text-#333">选择越多，内容越丰富</div>
        <div class="w-full flex flex-wrap pt-115px label max-h-70vh overflow-scroll " :class="!props.showBtn?'pb-120px':''">
            <div  v-for="(item,index) of navData" :key="index" class="flex w-23%  mr-18px mb-20px" @click="choose(item)">
                <div class="w-100% py-16px  rounded-10px flex text-28px justify-center" :class="item.isChoose?'bg-#5AA4FF text-#fff':'bg-#F6F7F8'">{{ item.labelName }}</div>
            </div>
        </div>
        <div class="btn w-80/100 mt-40px m-auto text-34px text-#fff py-23px text-center fixed bottom-40px left-1/2 -translate-x-1/2" @click="chooseLabel()">{{showBtn?'提交':'确认'}}</div>
    </div>
</template>
<script lang="ts" setup>
import { getCurrentUserLabel } from '@/api/interestGroup';
import { useUserStore } from '@/store/modules/user';
const emit = defineEmits(['submitContent']);
const useStore = useUserStore();
const props = defineProps({
    navData:{
        type:Array,
        default:[]
    },
    showBtn:{
        type:Boolean,
        default:true
    }
})
const Data=ref({
    navData:[],
})

console.log(props.navData);
function choose(item) {
    item.isChoose=!item.isChoose
}
//获取兴趣小组用户信息
const getUserLabel = () => {
    getCurrentUserLabel().then((res) => {
        Data.value.navData=res.data;
        Data.value.navData.map(el=>{
            el.isChoose=false
        })
    })
}
//提交
const chooseLabel = () =>{
    let arr =[]
    props.navData.map(el=>{
        if(el.isChoose){
            arr.push(el.autoId)
        }
    })
    if(!props.showBtn){
        emit('submitContent',props.navData.filter(i =>  i.isChoose));
        return
    }
    emit('submitContent',arr)
}
onMounted(()=>{
    // getUserLabel()
})
</script>
<style lang="scss">
.label-choose{
    background: url('@/assets/interest/bg_label.png'),#F6F7F8;
    background-size: 100% 100%;
    min-height: 100vh;
}
.label-bg{
    background: url('@/assets/interest/label_bg.png'),#BFDAF9;
    background-size: 100% 45%;
    background-repeat: no-repeat;
}
.btn{
    background: url("@/assets/public/button.png") no-repeat;
    background-size: 100% 100%;
}
.van-overlay{
    background: rgba($color: #000000, $alpha: 0.3) !important;
}
.label{
    >div:nth-child(4n){
        margin-right: 0;
    }
}
</style>