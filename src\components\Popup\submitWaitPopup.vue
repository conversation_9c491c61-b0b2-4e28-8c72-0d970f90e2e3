<template>
    <van-popup v-model:show="props.show" position="center" @click-overlay="close">
        <div class="authorPopup relative">
            <img loading="lazy" :src="props.currentBG || popopText[props.status].bgimg" class="imgBg w-full" />
            <div class="content absolute top-50% -translate-y-20% left-70px right-70px  text-center">
                <div class="title text-[#000] text-[36px] font-medium">
                    {{ props.title }}
                </div>
                <div class="message text-[#999] text-[30px] mt-57px mb-57px">
                    {{ props.message }}
                </div>
                <div class="btn he-78px relative leading-none">
                    <img loading="lazy" src="@/assets/workerCraftsman/auth/popup_btn.png" class="w-full"
                        @click.stop="pageToFn" />
                    <span class="absolute left-50% top-50% -translate-50% text-[#fff] text-32px">{{
                        props.btnText }}</span>
                </div>
            </div>
        </div>
    </van-popup>

</template>

<script setup lang="ts">
import successImg from "@/assets/workerCraftsman/auth/auth_success_popup.png"
import waitImg from "@/assets/workerCraftsman/auth/submit_wait_popup.png"
const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    status: {
        type: String,
        default: 'success'
    },
    pageTo: {
        type: Function,
    },
    message: {
        type: String,
        default: ''
    },
    btnText: {
        type: String,
        default: ''
    },
    title: {
        type: String,
        default: ''
    },
    bgimg: {
        type: String,
        default: ''
    },
    currentBG: {
        type: String,
        default: ''
    }
})
const emit = defineEmits(["update:show"]);
const popopText = {
    'wait': {
        bgimg: waitImg,
    },
    'success': {
        bgimg: successImg,
    }
}
const pageToFn = () => {
    emit("update:show", false)
    props.pageTo && props.pageTo()
}
const router = useRouter();
const close = () => {
    emit("update:show", false)
    router.go(-1)
}

</script>

<style scoped lang="scss">
.authorPopup {
    width: 75%;
    margin: 0 auto;
}

.van-popup {
    background: transparent;
    display: flex;
    align-items: center;
}
</style>