export default [
    {
        path: "/subject",
        name: "subjectIndex",
        component: () => import("@/views/home/<USER>/index.vue"),
        meta: {
          title: "专题列表",
          isShowTabBar: false,
          isBack:true,
          keepAlive:true,
          updatePath:['/home']
        }
    },
    {
        path: "/subjectList",
        name: "subjectList",
        component: () => import("@/views/home/<USER>/list.vue"),
        meta: {
          title: "专题新闻",
          isShowTabBar: false,
          isBack:true,
          keepAlive:true,
          updatePath:['/home','/subject']
        }
    },
    {
      path: "/searchList",
      name: "searchList",
      component: () => import("@/views/home/<USER>"),
      meta: {
        title: "新闻搜索",
        isShowTabBar: false,
        isBack:true
      }
  },
]