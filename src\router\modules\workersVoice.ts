export default [
  {
    path: '/workersVoice',
    name: 'WorkersVoice',
    component: () => import('@/views/workersVoice/index.vue'),
    meta: {
      title: '工友之声',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/messageInfoDetail',
    name: 'MessageInfoDetail',
    component: () => import('@/views/workersVoice/detail.vue'),
    meta: {
      title: '留言详情',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/messageWriteOpt',
    name: 'MessageWriteOpt',
    component: () => import('@/views/workersVoice/option.vue'),
    meta: {
      title: '留言',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/my/workersVoice',
    name: 'MyWorkersVoice',
    component: () => import('@/views/my/workersVoice/index.vue'),
    meta: {
      title: '我的留言',
      isShowTabBar: false,
      isBack: true,
    },
  },
];
