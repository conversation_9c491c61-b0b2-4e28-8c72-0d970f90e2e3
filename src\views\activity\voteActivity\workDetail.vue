<template>
  <div class="voteActivity-workDetail w-full min-h-full">
    <div class="voteDetail-body mb-30px">
      <div class="voteDetail-body-header flex mb-30px justify-center">
        <img loading="lazy" :src="visitPrefix && workInfo.opusCover ? visitPrefix + workInfo.opusCover : null" alt=""
          class="w-70/100 rounded-8px" />
      </div>
      <div class="voteDetail-body-introduce">
        <div>编号：<span>{{ workInfo.opusNo || '-' }}</span></div>
        <div>所属单位：<span>{{ workInfo.companyName || '-' }}</span></div>
        <div v-if="workInfo.opusName">名称：<span>{{ workInfo.opusName }}</span></div>
        <div v-html="workInfo.opusContent || '--'"></div>
      </div>
    </div>
    <div class="voteDetail-top mb-30px">
      <div class="flex justify-center voteDetail-top-title mb-30px px-50px text-center">
        {{ activityDetail?.activityName }}{{ workInfo.opusNo }}号投票情况
      </div>
      <div class="flex justify-evenly voteDetail-top-data items-center">
        <div>
          <div class="data-top">{{ workInfo.opusNo || '-' }}<span>号</span></div>
          <div>编号</div>
        </div>
        <div class="divider"></div>
        <div>
          <div class="data-top">{{ workInfo.votesNum || '-' }}<span>票</span></div>
          <div>票数</div>
        </div>
        <div class="divider"></div>
        <div>
          <div class="data-top">{{ workInfo.rank || '-' }}<span>名</span></div>
          <div>排名</div>
        </div>
      </div>
    </div>
    <div class="flex flex-col items-center like-btn my-20px">
      <div class="flex justify-center items-center relative mb-15px" @click.stop="onThumbsUp">
        <img loading="lazy" src="@/assets/activity/Like-btn.png" alt="" class="pulsate-bck" />
      </div>
      <p class="flex items-center text-[#fff] text-30px"> {{ workInfo.likeState ? '已投票' : '投票' }} </p>
    </div>

  </div>
</template>

<script>
import validatorMixins from '@/views/activity/mixins/validator'
import { showToast, showDialog, closeToast } from 'vant';
export default {
  mixins: [validatorMixins], //注入验证方法
  data() {
    return {
      // 今日已投票次数
      thumbsUpNum: 0,
      // 每日可投票次数
      defaultThumbsUpNum: 0,
      workInfo: { fileList: [] },
      // 频繁投票
      clickState: false,
    }
  },
  watch: {
    activityDetail() {
      if (this.activityDetail && this.isLogin) {
        this.getThumbsUpNum()
      }
    },
  },
  computed: {
    visitPrefix() {
      return this.$store.state.visitPrefix
    },
  },
  mounted() {
    if (this.activityDetail && this.isLogin) {
      this.getThumbsUpNum()
    }
    this.$nextTick(() => {
      document.querySelector('.voteActivity-workDetail').scrollTop = 0
    })
    this.getDetail()
  },
  methods: {
    async getDetail() {
      const opusInfoId = this.$route.query.opusInfoId
      if (!opusInfoId) {
        showToast({
          title: '提示',
          message: '作品信息不存在',
        })
        return
      }
      const { code, data } = await this.$api.common.worksDetail({ opusInfoId })
      if (code === 200) {
        const { opusFiles } = data
        data.fileList = opusFiles ? JSON.parse(opusFiles).map(item => `${item.url}`) : []
        console.log(data)
        this.workInfo = data || {}
        sessionStorage.setItem('workInfo', JSON.stringify(this.workInfo))
      }
    },
    // 作品投票
    async onThumbsUp() {
      if (!this.validator()) return
      if (this.clickState) {
        showToast({
          message: '投票太频繁啦,请稍后再试',
          duration: 1500,
          forbidClick: true,
        })
        return
      }
      if (this.thumbsUpNum >= this.defaultThumbsUpNum) {
        showDialog({
          title: '温馨提示',
          message: `每位用户每日最多可投${this.defaultThumbsUpNum || 0}票,明日再来吧~`,
          confirmButtonText: '我知道了',
        })
        return
      }
      const { dailySingleCount } = this.activityDetail?.voteInfo || { dailySingleCount: 0 }
      if (this.workInfo.todayCount >= dailySingleCount) {
        showDialog({
          title: '温馨提示',
          message: `每个作品每天只能投${dailySingleCount || 0}票,继续选择其他作品投票吧~`,
          confirmButtonText: '我知道了',
        })
        return
      }
      this.clickState = true
      const platform = sessionStorage.getItem('platform')
      const { code } = await this.$api.common.giveThumbsUp({
        activityId: this.$store.state.activityId,
        opusType: this.workInfo.opusType,
        opusInfoId: this.workInfo.opusInfoId,
        platform,
      })
      if (code === 200) {
        this.thumbsUpNum++
        this.workInfo.votesNum++
        this.workInfo.todayCount++
        this.workInfo.likeState = true
        setTimeout(() => {
          closeToast()
          showToast({
            message: '投票成功',
            duration: 1500,
            type: 'success',
            forbidClick: true,
          })
        }, 100)
      }
      setTimeout(() => {
        this.clickState = false
      }, 1000)
    },
    // 获取已点赞次数
    async getThumbsUpNum() {
      let { code, data } = await this.$api.common.thumbsUpNum({
        activityId: this.$store.state.activityId,
      })
      if (code === 200) {
        const { dailyMax } = this.activityDetail?.voteInfo || { dailyMax: 0 }
        this.defaultThumbsUpNum = dailyMax
        this.thumbsUpNum = data || 0
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.voteActivity-workDetail {
  background-color: #ffbf71;
  background-size: 100%;
  background-repeat: no-repeat;
  font-family: Source Han Sans CN;
  padding: 30px;


  .voteDetail-top {
    background-color: #fff;
    border-radius: 14px;
    padding: 35px 0;

    .voteDetail-top-title {
      font-size: 30px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #000000;
    }

    .voteDetail-top-data {
      >div {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 26px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #363636;
        width: 25%;

        .data-top {
          font-size: 50px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #ce2618;

          span {
            font-size: 22px;
          }
        }
      }

      .divider {
        width: 1px;
        height: 90px;
        background: #d7d7d7;
      }
    }
  }

  .voteDetail-body {
    background-color: #fff;
    border-radius: 14px;
    padding: 35px;
    padding-bottom: 50px;

    .voteDetail-body-introduce {
      font-size: 30px;
      font-family: Source Han Sans CN;
      font-weight: 500;
      color: #1f1f1f;
      line-height: 50px;

      span {
        font-size: 33px;
      }

      >div {
        margin-bottom: 25px;
      }
    }
  }

  .like-btn {

    >div {
      width: 106px;
      height: 106px;
      background: #ffffff;
      box-shadow: 0px 0px 28px 0px rgba(255, 179, 59, 0.36);
      border-radius: 50%;

      img {
        width: 49px;
        height: 41px;
      }
    }

  }
}

.pulsate-bck {
  -webkit-animation: pulsate-bck 1s ease-in-out infinite both;
  animation: pulsate-bck 1s ease-in-out infinite both;
}

@-webkit-keyframes pulsate-bck {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }

  50% {
    -webkit-transform: scale(0.9);
    transform: scale(0.9);
  }

  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes pulsate-bck {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }

  50% {
    -webkit-transform: scale(0.9);
    transform: scale(0.9);
  }

  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
</style>
