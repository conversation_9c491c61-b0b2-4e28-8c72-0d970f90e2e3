<template>
    <div class="join-box rounded-20px flex">
        <img loading="lazy" :src="judgeStaticUrl(data.logo)" alt="" class="w-88px h-88px rounded-1/2 mt-10px">
        <div class="px-15px w-66% border-box py-20px">
            <div class="text-30px">{{ data.groupName }}</div>
            <div class="flex text-#999 text-26px mt-25px">
                <div class="flex items-center">
                    <img loading="lazy" src="@/assets/interest/icon_num.png" alt="" class="w-28px mr-8px">{{
                        data.memberCount }}人
                </div>
                <!-- <div class="mx-15px flex items-center"><img loading="lazy" src="@/assets/interest/icon_act.png" alt="" class="w-24px mr-8px">{{data.act_num}}次</div> -->
                <!-- <div class="flex items-center"><img loading="lazy" src="@/assets/interest/icon_num.png" alt="" class="w-24px mr-8px">{{data.time}}次</div> -->
            </div>
            <div class="flex mt-20px flex-wrap">
                <div class="rounded-10px text-center px-15px mt-10px text-26px mr-15px py-3px text-#5AA4FF label"
                    v-for="(item, index) of data.labels" :key="index">{{ item }}</div>
            </div>
        </div>
        <div class="flex items-center">
            <div class="text-#fff text-26px h-40px rounded-20px text-center w-95px flex justify-center items-center leading-none"
                :class="data.joinState || data.memberCount == data.memberMax ? '!bg-#999' : 'join'" @click="join()">
                {{ data.joinState ? '退出' : data.memberCount == data.memberMax ? '已满' : '加入' }}</div>
        </div>
        <Popup :show="Data.showPop" :titleName="'加入原因'" :placeHolder="'请输入加入原因'" @submit-content="joinContent"
            @close-popup="closePopup" />
    </div>
</template>
<script lang="ts" setup>
import Popup from '@/components/Popup/popup.vue';
import { showToast, showConfirmDialog, showFailToast } from 'vant';
import { joinGroup, exitGroup } from '@/api/interestGroup';
import { judgeStaticUrl } from '@/utils/utils';
const props = defineProps({
    data: {
        type: Object,
        default: {},
    }
})
const Data = ref({
    showPop: false,
})
//加入
function join() {
    if (props.data.memberCount == props.data.memberMax) {
        return
    }
    if (!props.data.joinState) {
        Data.value.showPop = true;
    } else {
        showConfirmDialog({
            title: "提示",
            message: `确认退出${props.data.groupName}小组?`,
            confirmButtonText: "确认",
            cancelButtonText: "取消",
            className: "close",
        }).then(async () => {
            // 自定义代码
            exitGroup({ groupId: props.data.groupId }).then(res => {
                if (res.code == 200) {
                    showToast("退出成功");
                    props.data.joinState = !props.data.joinState
                } else {
                    showFailToast(res.message)
                }
            })
        });

    }
}

//确认提交
function joinContent(val) {
    if (!val) {
        showToast({
            message: "请输入加入原因",
            icon: 'none',
        })
        return
    }
    joinGroup({
        groupName: props.data.groupName,
        groupId: props.data.groupId,
        reason: val
    }).then(res => {
        if (res.data) {
            showToast("提交成功，等待管理员审核~")
        } else {
            showFailToast(res.message)
        }
        Data.value.showPop = false;
    })
}


//关闭
function closePopup() {
    Data.value.showPop = false;
}
</script>
<style lang="scss">
.join-box {
    background: rgba(133, 187, 255, 0.1);
    padding: 15px 22px;
}

.label {
    background: rgba(90, 164, 255, 0.1);
}

.join {
    background: linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%);
}
</style>