export default [
  {
    path: '/digitalSchool',
    name: 'digitalSchool',
    component: () => import('@/views/digitalSchool/index.vue'),
    meta: {
      title: '职工学院',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/digitalSchool/studyGroup',
    name: 'studyGroup',
    component: () => import('@/views/digitalSchool/studyGroup/index.vue'),
    meta: {
      title: '学习小组',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/digitalSchool/studyGroup/createGroup',
    name: 'createGroup',
    component: () => import('@/views/digitalSchool/studyGroup/createGroup.vue'),
    meta: {
      title: '创建小组',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/digitalSchool/studyGroup/addShare',
    name: 'addShare',
    component: () => import('@/views/digitalSchool/studyGroup/addShare.vue'),
    meta: {
      title: '新增心得分享',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/digitalSchool/studyGroup/commentDetail',
    name: 'commentDetail',
    component: () => import('@/views/digitalSchool/studyGroup/commentDetail.vue'),
    meta: {
      title: '心得分享详情',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/digitalSchool/studyGroup/myShareList',
    name: 'myShareList',
    component: () => import('@/views/digitalSchool/studyGroup/myShareList.vue'),
    meta: {
      title: '我的分享',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/digitalSchool/studyGroup/shareDetail',
    name: 'shareDetail',
    component: () => import('@/views/digitalSchool/studyGroup/shareDetail.vue'),
    meta: {
      title: '我的分享详情',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/digitalSchool/studyGroup/myGroups',
    name: 'myGroups',
    component: () => import('@/views/digitalSchool/studyGroup/myGroups.vue'),
    meta: {
      title: '我的小组',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/digitalSchool/course/liveList',
    name: 'liveList',
    component: () => import('@/views/digitalSchool/course/liveList.vue'),
    meta: {
      title: '直播课程',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/digitalSchool/course/courseList',
    name: 'courseList',
    component: () => import('@/views/digitalSchool/course/courseList.vue'),
    meta: {
      title: '直播课程',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/digitalSchool/studyGroup/groupDetails',
    name: 'digitalSchoolGroupDetails',
    component: () => import('@/views/digitalSchool/studyGroup/groupDetails.vue'),
    meta: {
      title: '小组详情',
      isShowTabBar: false,
      isBack: true,
      keepAlive:true,
      updatePath:['/digitalSchool/studyGroup','/digitalSchool']
    },
  },
  {
    path: '/digitalSchool/course/courseDetails',
    name: 'courseDetails',
    component: () => import('@/views/digitalSchool/course/courseDetails.vue'),
    meta: {
      title: '课程详情',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/digitalSchool/course/liveDetails',
    name: 'liveDetails',
    component: () => import('@/views/digitalSchool/course/liveDetails.vue'),
    meta: {
      title: '直播详情',
      isShowTabBar: false,
      isBack: true,
    },
  },
];
