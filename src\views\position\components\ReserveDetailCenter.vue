<template>
  <div class="pb-[60px]">
    <div class="mb-50px leading-50px">
      <div class="flex items-center leading-60px">
        <div class="w-7px h-30px rounded-4px bg-[#5AA4FF] mr-10px"></div>
        <div class="text-[32px] font-500 text-[#333] font-500">场所信息</div>
      </div>
      <div class="mb-1"
        >所属阵地：<span class="text-[#333]">{{ detailRecord?.positionName }}</span></div
      >
      <div class="mb-1"
        >联 系 人 ：<span class="text-[#333]">{{ detailRecord?.manager }}</span>
        <span class="!text-[#5BA5FF] ml-1">{{ detailRecord?.managerPhone }}</span></div
      >
      <div class="mb-1"
        >场所位置：<span class="text-[#333]">{{ detailRecord?.venueAddress }}</span></div
      >
    </div>
    <div class="leading-50px">
      <div class="flex items-center justify-between">
        <div class="flex items-center leading-60px">
          <div class="w-7px h-30px rounded-4px bg-[#5AA4FF] mr-10px"></div>
          <div class="text-[32px] font-500 text-[#333] font-500">预约信息</div>
        </div>
        <slot name="black"></slot>
      </div>
      <div class="mb-1"
        >场所名称：<span class="text-[#333]">{{ detailRecord?.venueName }}</span></div
      >
      <div class="mb-1"
        >预约时间：<span class="mr-1 text-[#333]">{{ detailRecord?.reservationDate }}</span>
        <span
          >{{ utils.formatTimeWithoutSeconds(detailRecord?.reservationStartTime) }} ~ {{ utils.formatTimeWithoutSeconds(detailRecord?.reservationEndTime) }}</span
        ></div
      >
      <div class="mb-1"
        >预 约 人 ：<span class="text-[#333]">{{ detailRecord?.userName }}</span></div
      >
      <div class="mb-1"
        >手机号码：<span class="!text-[#5BA5FF] ml-1">{{ detailRecord?.desensitizationPhone }}</span></div
      >
      <div class="mb-1"
        >身份证号：<span class="text-[#333]">{{ detailRecord?.idCard }}</span></div
      >
      <div class="mb-1"
        >提交时间：<span class="text-[#333]">{{ utils.formatTimeWithoutSeconds(detailRecord?.createTime) }}</span></div
      >
      <div
        v-if="detailRecord?.auditTime"
        class="mb-1"
        >审核时间：<span class="text-[#333]">{{ utils.formatTimeWithoutSeconds(detailRecord?.auditTime) }}</span></div
      >
      <div
        v-if="detailRecord?.auditOpinion && detailRecord?.state === 'refuse'"
        class="mb-1 text-red-500"
        >失败原因：<span class="!text-red-500">{{ detailRecord?.auditOpinion }}</span>
      </div>
      <div
        v-if="detailRecord?.state === 'used'"
        class="mb-1">签到时间：<span class="text-#333">{{utils.formatTimeWithoutSeconds(detailRecord?.useTime)  }}</span>
      </div>
      <div
        v-if="detailRecord?.state === 'used'"
        class="mb-1">签到地址：<span class="text-#333">{{ detailRecord?.venueAddress }}</span>
      </div>
      <div
        v-if="detailRecord?.state === 'useExpire'"
        class="mb-1">过期时间：<span class="text-#333">{{ utils.formatTimeWithoutSeconds(detailRecord?.reservationDate) }} {{ utils.formatTimeWithoutSeconds(detailRecord?.reservationEndTime) }}</span>
      </div>
      <div
        v-if="detailRecord?.state === 'cancel'"
        class="mb-1">取消时间：<span class="text-#333">{{ utils.formatTimeWithoutSeconds(detailRecord?.cancellationTime) }}</span>
      </div>
      
    </div>
  </div>
</template>

<script lang="ts" setup>
import utils from '@/utils/utils';

withDefaults(defineProps<{ detailRecord: Recordable | undefined }>(), { detailRecord: () => ({}) });
</script>
