<template>
    <div class="flex justify-between items-center" @click="lookMore()">
        <div class="flex items-center">
            <img loading="lazy" :src="getIcon()" alt="" class="w-54px mr-10px">
            <img loading="lazy" :src="getImg()" alt="" class="w-128px">
        </div>
        <div class="text-[#999999] text-[24px]" v-if="showMore">更多>></div>
    </div>
</template>
<script lang="ts" setup>
const props = defineProps({
    titleImg: {
        type: String,
        default: '',
    },
    icon: {
        type: String,
        default: '',
    },
    title: {
        type: String,
        default: '',
    },
    showMore: {
        type: Boolean,
        default: true
    }
})
//获取图标
const getIcon = () => {
    let img = new URL(`../../assets/interest/icon_${props.icon}`, import.meta.url).href
    return img
}

//获取标题图片
const getImg = () => {
    let img = new URL(`../../assets/interest/${props.titleImg}`, import.meta.url).href
    return img
}
const emit = defineEmits(['lookMore']);
//查看更多
function lookMore() {
    emit('lookMore', props.title)
}
</script>