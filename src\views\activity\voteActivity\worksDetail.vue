<template>
    <div class="p-30px detail">
        <div class="w-full   rounded-20px">

            <!-- {{ detailInfo.opusFiles.split(',') }} -->
            <img loading="lazy" v-for="(item, index) of detailInfo.Files" :key="index" :src="utils.judgeStaticUrl(item)"
                alt="" v-if="detailInfo?.fileType == 'image'" class="w-full h-330px rounded-20px mb-20px object-cover">
            <video class="w-100% h-400px bg-#333 z-index-0  rounded-20px object-cover" id="videoId" :autoplay="true"
                :controls="true" controlslist="nodownload  noremoteplayback noplaybackrate" disablePictureInPicture
                :poster="utils.judgeStaticUrl(detailInfo.opusCover)" v-else-if="detailInfo?.fileType == 'video'"
                :src="utils.judgeStaticUrl(detailInfo.opusFiles)" :muted="false" :loop="true" preload="preload"
                language="zh-CN" x5-video-player-type="h5" x5-video-player-fullscreen="fasle"
                x5-video-player-style="position:fixed;z-index:0;" x5-video-orientation="portraint" playsinlin
                z-index="0"></video>
            <img loading="lazy" :src="utils.judgeStaticUrl(detailInfo.opusCover)" alt=""
                class="w-full h-330px rounded-20px mb-20px object-cover" v-else>
        </div>
        <div class="bg-#fff rounded-20px mt-32px p-30px">
            <div class="text-30px text-#333 py-10px">姓名：{{ detailInfo.userName || '--' }}</div>
            <div class="text-30px text-#333 py-15px" v-if="detailInfo.opusNo">编号：{{ detailInfo.opusNo }}</div>
            <div class="text-30px text-#333 py-15px">联系方式：{{ detailInfo.phone || '--' }}</div>
            <div class="text-30px text-#333 py-15px">作品名称：<span class="font-550">{{ detailInfo.opusName || '--' }}</span>
            </div>
            <div class="text-30px text-#333 py-15px">工作单位：{{ detailInfo.workUnit || '--' }}</div>
            <div class="text-30px text-#333 py-15px">地区：{{ detailInfo.areaName || '--' }}</div>
            <div class="text-30px text-#333 py-15px">
                <div class="pb-15px font-550">内容介绍：</div>
                <div class="leading-relaxed rich_text" v-html="detailInfo.opusContent"></div>
            </div>
        </div>
        <div class="bg-#fff rounded-20px mt-20px p-30px" v-if="!route.query.source">
            <div class="text-#1F1F1F text-30px pb-15px">投稿时间：{{ utils.formatTimeWithoutSeconds(detailInfo.createTime) }}
            </div>
            <div class="text-#1F1F1F text-30px" v-if="route.query.status !== 'pass'">审核状态：<span
                    :class="detailInfo.state == 'wait' ? '!text-#5AA4FF' : '!text-#D11111'">{{ detailInfo.state == 'wait' ? '审核中' :detailInfo.state == 'pass' ?'审核通过': '审核驳回' }}</span>
            </div>
            <div class="text-#1F1F1F text-30px pt-15px" v-if="route.query.status == 'refuse'">
                审核时间：{{ utils.formatTimeWithoutSeconds(detailInfo.updateTime) }}</div>
            <div class="text-#1F1F1F text-30px pt-15px" v-if="route.query.status == 'refuse' && detailInfo.opinion">
                审核原因：{{ detailInfo.opinion }} </div>
        </div>
        <div class="bg-#fff rounded-20px mt-20px p-30px flex" v-if="route.query.status == 'pass'">
            <div class="flex flex-col justify-center items-center w-1/3 border-r">
                <div class="text-#5AA4FF text-22px"><span class="text-50px">{{ detailInfo.opusNo || '--' }}</span>号</div>
                <div class="text-#363636 text-26px pt-10px">编号</div>
            </div>
            <div class="flex flex-col justify-center items-center w-1/3">
                <div class="text-#5AA4FF text-22px"><span class="text-50px">{{ detailInfo.votesNum }}</span>票</div>
                <div class="text-#363636 text-26px pt-10px">票数</div>
            </div>
            <div class="flex flex-col justify-center items-center w-1/3 border-l">
                <div class="text-#5AA4FF text-22px"><span class="text-50px">{{ detailInfo.rank || '--' }}</span>名</div>
                <div class="text-#363636 text-26px pt-10px">排名</div>
            </div>
        </div>
        <div v-if="route.query.source"
            class="w-168px py-10px rounded-6px border-solid text-#5AA4FF text-28px text-center m-auto mt-76px mb-20px border-1px border-#5AA4FF"
            @click="voteTicket(detailInfo)">立即投票</div>

    </div>
</template>
<script lang="ts" setup>
defineOptions({
    name: 'worksDetail',
})
import { myVoteDetails, checkCount, opusesAddRecord } from '@/api/activity';
import utils from '@/utils/utils';
import { useUserStore } from '@/store/modules/user';
import { showDialog, showToast } from 'vant';
import useRefreshFun from '@/hooks/app.ts'
const useStore = useUserStore();
const route = useRoute();
const detailInfo = ref({})
function getDetails() {
    // const opusInfoId = route.query.opusInfoId
    //   if (!opusInfoId) {
    //     showToast({
    //       title: '提示',
    //       message: '作品信息不存在',
    //     })
    //     return
    //   }
    myVoteDetails({ opusInfoId: route.query.opusInfoId, activityId: activityDetail.value.activityId }).then(res => {
        if (res.code == 200) {
            detailInfo.value = res.data;
            if (detailInfo.value.fileType) {
                detailInfo.value.Files = detailInfo.value?.opusFiles?.split(',')
            }

        } else {
            showToast({
                title: '提示',
                message: res.message
            })
        }
    })
}
// 计算属性
const activityDetail = computed(() => {
    return useStore.activityDetail || {};
});
//投票
const clickState = ref(false)
const voteTicket = async (item) => {
    console.log(activityDetail.value.voteInfo.dailySingleCount);
    console.log(activityDetail.value.voteInfo.dailyMax);
    if (clickState.value) {
        showToast({
            message: '投票太频繁啦,请稍后再试',
            duration: 2000,
            forbidClick: true,
        })
        return
    }
    const { data: userCount } = await checkCount({
        activityId: activityDetail.value.activityId,
    })
    if (userCount >= activityDetail.value.voteInfo.dailyMax) {
        showDialog({
            title: '温馨提示',
            message: `每位用户每日最多可投${activityDetail.value.voteInfo.dailyMax || 0}票,明日再来吧~`,
            confirmButtonText: '我知道了',
        })
        return
    }
    if (item.todayCount >= activityDetail.value.voteInfo.dailySingleCount) {
        showDialog({
            title: '温馨提示',
            message: `每个作品每天只能投${activityDetail.value.voteInfo.dailySingleCount || 0}票,继续选择其他作品投票吧~`,
            confirmButtonText: '我知道了',
        })
        return
    }
    clickState.value = true;
    const { code } = await opusesAddRecord({
        activityId: activityDetail.value.activityId,
        opusInfoId: item.opusInfoId,
        platform: "app"
    })
    if (code === 200) {
        item.votesNum++;
        item.todayCount++
        showToast({
            message: '投票成功',
            duration: 1000,
            type: 'success',
            forbidClick: true,
        })
        refresh()
    }

    setTimeout(() => {
        clickState.value = false
    }, 1500)
}
const { addRefreshList } = useRefreshFun();
const refresh = () => {
    addRefreshList({ pageName: 'activityVote', funsName: ['changePage'] })
    addRefreshList({ pageName: 'activeMyWorks', funsName: ['changePage'] })
}
onMounted(() => {
    if (route.query.status == 'pass') {
        getDetails()
    } else {
        detailInfo.value = JSON.parse(route.query.info);
        if (detailInfo.value.fileType) {
            detailInfo.value.Files = detailInfo.value?.opusFiles?.split(',')
        }
        console.log(detailInfo.value);

    }

})
</script>
<style scoped lang="scss">
.detail {
    background: linear-gradient(0deg, #F6F7F8, #B9D7FC) no-repeat;
    min-height: 100vh;

    .border-r {
        border-right: 1px solid #D7D7D7;
    }

    .border-l {
        border-left: 1px solid #D7D7D7;
    }

    :deep(p) {
        padding: 0px;
    }
}
</style>