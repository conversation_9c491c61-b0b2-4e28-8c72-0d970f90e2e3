<script lang="ts" setup>
import male from '@/assets/friendShip/male_icon_w.png'
import female from '@/assets/friendShip/female_icon_w.png'
import { applyListApi, queryOthersDetailApi, friendReview } from '@/api/friendship/index';
import refreshList from '@/components/refreshList/index.vue';
import contactPop from '../components/contactPop.vue'
import utils from '@/utils/utils';
import router from '@/router';
import { showToast } from 'vant';
const active = ref(0)
const ListObj = ref({
    pageIndex: 1,
    List: [],
    isLoad: false,
    loading: false,
    finished: false,
    finishedText: '',
    RecordCount: 0
})
const getList = () => {
    switch (active.value) {
        case 0:
            getApplyList('apply')
            break
        case 1:
            getApplyList('audit')
            // getApplyList('apply')
            break
    }

}
const btnClick = ref(false)
const loadMoreRef = ref(null)
// 申请和收到的申请
const getApplyList = (type) => {
    applyListApi({
        pageSize: 10,
        pageNum: ListObj.value.pageIndex++,
        queryType: type,
    }).then(res => {
        ListObj.value.loading = false
        ListObj.value.isLoad = false
        ListObj.value.pageIndex == 1 ? ListObj.value.List = res.data
            : ListObj.value.List = [...ListObj.value.List, ...res.data]
        if (loadMoreRef.value) {
            loadMoreRef.value.onLoadSuc(ListObj.value.length, res.total);
        }
    }).catch(err => {

    })
}
// 加载更多
const onLoadMore = () => {
    ListObj.value.pageIndex++;
    getList();
};
const popshow = ref(false)
const detailObj = ref({})
const showPop = (item) => {
    if (item.authorizeStatus == 'wait') {
        detailObj.value = item
        popshow.value = true
    }

}
const auditApply = (status) => {
    if (btnClick.value) return
    btnClick.value = true
    friendReview({
        authorizeStatus: status,
        autoId: detailObj.value.autoId,
    }).then(res => {
        btnClick.value = false
        if (res.code == 200) {
            status == 'pass' ? showToast('已同意') : showToast('已拒绝')
            popshow.value = false
            onRefresh()
        }
        else {
            showToast(res.message)
        }
    })
}

// 刷新
const onRefresh = () => {
    ListObj.value.List = []
    ListObj.value.pageIndex = 1
    getList()
}
// 打开联系方式弹窗
const popNumshow = ref(false)
const showContactPop = (item) => {
    if (item.authorizeStatus == 'pass') {
        queryOthersDetailApi({
            singleUserId: item.aimUserId
        }).then(res => {
            if(!res.data) return showToast(res.message)
            detailObj.value = res.data
            // detailObj.value = item
            popNumshow.value = true
        })

    }
}
// 关闭联系方式弹窗
const closeApplyPopup = (res) => {
    console.log(res, 'resresres');
    popNumshow.value = res
}
//详情
function toPage(path, query) {
    router.push({
        path,
        query
    })
}
onMounted(() => {
    getList();
})
</script>
<template>
    <div class="follow-list">
        <van-tabs v-model:active="active" @click="onRefresh()" class="nav-box">
            <van-tab title="发起申请"></van-tab>
            <van-tab title="收到申请"></van-tab>
        </van-tabs>
        <div class="list-box">
            <refreshList key="relist" @onRefreshList="onRefresh" @onLoadMore="onLoadMore" ref="loadMoreRef">
                <div class="follow-item" v-for="(item, index) in ListObj.List" :key="index"
                    @click="toPage('/friendship/detail', { singleUserId: active == 0 ? item.aimUserId : item.applyUserId })">
                    <div class="live-info flex justify-between items-start">
                        <div class="info-left flex">
                            <div class="logo w-94px h-94px mr-21px">
                                <img loading="lazy" class="w-full h-full rounded-50%"
                                    :src="utils.judgeStaticUrl(item.avatar, true)" alt="">
                            </div>
                            <div class="info-text">
                                <div class="name text-28px text-#333 font-500 mb-15px">{{
                                    item.nickname ? item.nickname : '账户已注销' }}
                                </div>
                                <div class="sex-time flex"> <span class="sex mr-20px" :class="item.gender">
                                        <img loading="lazy" :src="item.gender == 'male' ? male : female" alt="">{{
                                            item.age
                                        }}
                                    </span> <span class="text-26px text-#999 ">{{ item.height ? item.height + 'cm·' : ''
                                        }}{{
                                            utils.getDictionaryLabel('modelEducation', item.education) }}</span></div>
                            </div>
                        </div>
                        <span class="text-24px mt-25px leading-30px" @click.stop="showContactPop(item)"
                            v-if="active == 0" :class="item.authorizeStatus == 'wait' ? 'text-#4D9DFF' : item.authorizeStatus == 'pass' ? 'text-#3ABF40 border-bottom '
                                : item.authorizeStatus == 'refuse' ? 'text-#FB2A2A' : ''">{{ item.authorizeStatus ==
                                    'wait' ? '等待同意' :
                                    item.authorizeStatus == 'pass' ? '申请通过' : item.authorizeStatus == 'refuse' ? '申请失败' : ''
                            }}</span>

                        <span @click.stop="showPop(item)" v-else
                            :class="item.authorizeStatus == 'wait' ? 'detail-btn' : 'text-24px text-#999999 mt-25px leading-30px'">
                            {{ item.authorizeStatus == 'refuse' ? '已拒绝' : item.authorizeStatus
                                == 'wait' ? '查看申请' : item.authorizeStatus == 'pass' ? '已同意' : '' }}</span>

                    </div>
                </div>
            </refreshList>

        </div>
        <van-popup v-model:show="popshow">
            <div class="detail-box flex flex-col items-center pt-160px ">
                <div class="logo w-184px h-184px mb-10px flex items-center justify-center bg-#FFE5F1 rounded-15px">
                    <img loading="lazy" class="w-160px h-160px rounded-15px"
                        :src="utils.judgeStaticUrl(detailObj.avatar, true)" alt="">
                </div>
                <div class="flex items-center"><span class="text-31px text-#444444">{{ detailObj.nickname }}</span>
                    <span class="ml-10px mt-3px flex items-center justify-center" :class="detailObj.gender"><img
                            loading="lazy" class="w-19px h-19px" :src="detailObj.gender == 'male' ? male : female"
                            alt=""></span>
                </div>
                <span class="leading-50px text-31px text-#444 mt-40px">
                    <p class="my-0 text-center">对方向您发来了</p>
                    <p class="my-0 text-center">查看联系方式申请</p>
                </span>
                <div class="flex justify-around mt-20px">
                    <span
                        class="flex items-center justify-center w-248px h-79px bg-#f3f3f3 rounded-39px text-33px text-#333"
                        @click="auditApply('refuse')">拒绝</span>
                    <span style="background: linear-gradient(90deg,  #FAA8AB, #FF7097);" @click="auditApply('pass')"
                        class="ml-24px flex items-center justify-center w-248px h-79px bg-#f3f3f3 rounded-39px text-33px text-#fff">同意</span>

                </div>
            </div>
        </van-popup>
        <contactPop :showPop="popNumshow" :detailObj="detailObj" @closePopup="closeApplyPopup"></contactPop>

    </div>
</template>
<style lang="scss" scoped>
// padding-bottom: 20px;
.van-popup {
    background: transparent;

    .female {
        width: 30px;
        height: 30px;
        background: linear-gradient(90deg, #FAA8AB, #FF7097);
        border-radius: 50%;
    }

    .male {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: linear-gradient(90deg, #9FC9FF, #4196FF);
    }
}

.detail-num-box {
    width: 625px;
    height: 806px;
    background: url('@/assets/friendship/contact_bg.png') no-repeat;
    background-size: 100% 100%;
    background-position: center center;
}

.detail-box {
    width: 630px;
    height: 730px;
    background: url('@/assets/friendship/apply_file_popup.png') no-repeat;
    background-size: 100% 100%;
    background-position: center center;

}

.border-bottom {
    border-bottom: 1px solid;
}

.detail-btn {
    width: 128px;
    height: 50px;
    background: #FFFFFF;
    border-radius: 24px;
    border: 2px solid #4D9DFF;
    font-weight: 400;
    font-size: 24px;
    color: #4D9DFF;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
}

:deep(.nav-box) {
    .van-tabs__wrap {
        height: 98px;

        .van-tab {
            font-weight: 400;
            font-size: 28px;
            color: #8D9099;
        }

        .van-tab--active {
            font-weight: 500;
            font-size: 32px;
            color: #404455;
        }

        .van-tabs__line {
            width: 40px;
            height: 6px;
            background: linear-gradient(86deg, #C7E0FF 0%, #5AA4FF 100%);
            border-radius: 3px;
            bottom: 40px;
        }
    }
}

.list-box {
    border-top: 12px solid #F5F5F5;
    box-sizing: border-box;
    padding: 20px 31px;

    .follow-item {
        padding: 43px 0 27px;
        border-bottom: 1px solid #EBEBEB;

        .live-info {
            width: 100%;

            .follow-state {
                width: 162px;
                height: 48px;
                border-radius: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 400;
                font-size: 24px;

                &.follow {
                    background: #F4698D;
                    color: #FFF;
                }

                &.followed {
                    border: 2px solid #999999;
                    border: 2px solid #F4698D;
                    color: #999999;
                }
            }


            .info-left {

                .info-text {
                    display: flex;
                    flex-flow: column;

                    .sex-time {
                        .sex {
                            font-weight: 400;
                            font-size: 24px;
                            color: #FFFFFF;
                            width: 85px;
                            height: 36px;

                            border-radius: 18px;
                            display: flex;
                            align-items: center;
                            justify-content: center;

                            >img {
                                width: 19px;
                                margin-right: 7px;
                            }

                            &.female {
                                background: linear-gradient(90deg, #FAA8AB, #FF7097);
                            }

                            &.male {
                                background: linear-gradient(90deg, #9FC9FF, #4196FF);
                            }
                        }
                    }
                }
            }

        }
    }
}
</style>