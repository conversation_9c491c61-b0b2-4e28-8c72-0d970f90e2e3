export default [
  {
    path: '/my',
    name: 'my',
    component: () => import('@/views/my/index.vue'),
    meta: {
      title: '我的',
      isShowTabBar: true,
      isBack: false,
    },
  },
  {
    path: '/my/orders',
    name: 'myOrders',
    component: () => import('@/views/my/orders/index.vue'),
    meta: {
      title: '我的订单',
      isShowTabBar: false,
      isBack: true,
      keepAlive:true,
      updatePath:['/my','/']
    },
  },
  {
    path: '/order/detail',
    name: 'orderDetail',
    component: () => import('@/views/my/orders/detail.vue'),
    meta: {
      title: '订单详情',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/order/refund',
    name: 'orderRefund',
    component: () => import('@/views/my/orders/refund.vue'),
    meta: {
      title: '申请退款',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/order/logistics',
    name: 'orderLogistics',
    component: () => import('@/views/my/orders/logistics.vue'),
    meta: {
      title: '物流信息上传',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/my/yearGift',
    name: 'myYearGiftList',
    component: () => import('@/views/my/yearGift/index.vue'),
    meta: {
      title: '一岁一礼',
      isShowTabBar: false,
      isBack: true,
      keepAlive:true,
      updatePath:['/my']
    },
  },
  {
    path: '/my/manuscript',
    name: 'myManuscript',
    component: () => import('@/views/my/manuscript/index.vue'),
    meta: {
      title: '我的投稿',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/my/yearGiftCard',
    name: 'myYearGiftCard',
    component: () => import('@/views/my/yearGift/card.vue'),
    meta: {
      title: '卡片详情',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/codeReturn',
    name: 'codeReturn',
    component: () => import('@/views/public/codeRetrun.vue'),
    meta: {
      title: '扫码',
      isShowTabBar: false,
      isBack: false,
    },
  },
  {
    path: '/my/digitalSchool',
    name: 'myDigitalSchool',
    component: () => import('@/views/my/digitalSchool/index.vue'),
    meta: {
      title: '学习小组',
      isShowTabBar: false,
      isBack: true,
      keepAlive:true,
      updatePath:['/my']
    },
  },
  {
    path: '/my/digitalSchoolDetail',
    name: 'myDigitalSchoolDetail',
    component: () => import('@/views/my/digitalSchool/detail.vue'),
    meta: {
      title: '学习小组-心得分享详情',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/office',
    name: 'office',
    component: () => import('@/views/my/office/index.vue'),
    meta: {
      title: 'OA办公系统',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/my/friendship',
    name: 'myFriendship',
    component: () => import('@/views/my/friendship/index.vue'),
    meta: {
      title: '单身联谊审核',
      isShowTabBar: false,
      isBack: true,
      keepAlive:true,
      updatePath:['/office']
    },
  },
  {
    path: '/my/friendship/detail',
    name: 'myFriendshipDetail',
    component: () => import('@/views/my/friendship/detail.vue'),
    meta: {
      title: '单身联谊审核详情',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/handAudit',
    name: 'handAudit',
    component: () => import('@/views/my/office/handAudit/index.vue'),
    meta: {
      title: '掌上审核',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/handAuditDetail',
    name: 'handAuditDetail',
    component: () => import('@/views/my/office/handAudit/detail.vue'),
    meta: {
      title: '详情',
      path: '/my/friendship/detail',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/my/collection',
    name: 'myCollection',
    component: () => import('@/views/my/collection/index.vue'),
    meta: {
      title: '我的收藏',
      isShowTabBar: false,
      isBack: true,
      keepAlive:true,
      updatePath:['/my']
    },
  },
  {
    path: '/my/message',
    name: 'myMessage',
    component: () => import('@/views/my/message/index.vue'),
    meta: {
      title: '我的消息',
      isShowTabBar: false,
      isBack: true,
      keepAlive:true,
      updatePath:['/my']
    }
  },
  {
    path: '/my/messageDetail',
    name: 'myMessageDetail',
    component: () => import('@/views/my/message/detail.vue'),
    meta: {
      title: '消息详情',
      isShowTabBar: false,
      isBack: true,
    }
  },
  {
    path: '/my/activity',
    name: 'myActivity',
    component: () => import('@/views/my/activity/index.vue'),
    meta: {
      title: '我的活动',
      isShowTabBar: false,
      isBack: true,
      keepAlive:true,
      updatePath:['/my']
    }
  },
  
];
