<template>
  <div class="bg-no-repeat flex items-center justify-center text-34px text-#fff bg-center w-100% h-100%" :style="{
    backgroundImage: `url(${props.disable ? props.type == 'big' ? button_grey : button_grey_small : props.type == 'big' ? button : button_small})`,
    backgroundSize: '100% 100%',
  }">
    <slot name="btnContent">
      {{ props.name }}
    </slot>

  </div>
</template>
<script lang="ts" setup>
import button from '@/assets/public/button.png';
import button_small from '@/assets/public/butto.png';
import button_grey from '@/assets/public/button_grey.png';
import button_grey_small from '@/assets/public/button_grey_small.png';

const props = defineProps({
  name: {
    type: String,
    default: '',
  },
  disable: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: 'big',
  }
});
</script>
<style lang="scss" scoped>
.button {
  box-shadow: 0px -2px 10px 0px rgba(183, 192, 204, 0.2);
}
</style>
