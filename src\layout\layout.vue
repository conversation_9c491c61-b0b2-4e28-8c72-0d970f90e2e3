<script setup lang="ts">
// import backTopIcon from '@/assets/public/icon_top.png';
import { useRouter, useRoute, type LocationQueryRaw, type RouteParamsRaw } from 'vue-router';
import tabbar from '@/components/Tabbar/index.vue';
import BackBtn from '@/components/BackBtn/index.vue';
const route = useRoute();
// const router = useRouter();
// watch(
//   () => router.currentRoute.value.name,
//   (newValue, oldValue) => {
//     console.log(router.currentRoute.value);
//   },
//   { immediate: true }
// );

import { useAppStore } from '@/store/modules/app'
const appStore = useAppStore();
const includes = computed(() => {
  return appStore.includes;
});

</script>

<template>
  <div class="app-layout min-h-100vh w-100% relative">
    <div class="app-body flex min-h-100vh" ref="bodyRef">
      <RouterView v-slot="{ Component, route }" class="flex-1">
        <keep-alive :include="includes">
          <component :is="Component" :key="route.fullPath"></component>
        </keep-alive>
        <!-- <keep-alive >
          <component v-if="route.meta.keepAlive" :is="Component" class="" :key="route.path" />
        </keep-alive>
        <component v-if="!route.meta.keepAlive" :is="Component" class="" :key="route.path" /> -->
      </RouterView>

      <BackBtn v-if="!route?.meta?.isShowTabBar && !route?.meta?.isWelcome && route?.meta.isBack" />
      <tabbar v-show="route?.meta?.isShowTabBar" />
      <van-back-top right="3vw" bottom="12vh"></van-back-top>
    </div>
  </div>
</template>
<style scoped lang="scss">
.app-footer {
  .van-tabbar {
    .van-tabbar-item__text {
      font-size: 28px;
    }
  }
}

// /* 隐藏滚动条，启用滚动 */
.app-layout {
  // overflow: scroll; /* 或者 overflow: auto */
}

// /* 针对 WebKit 浏览器隐藏滚动条 */
:-webkit-scrollbar {
  width: 0 // display: none;

}
</style>
