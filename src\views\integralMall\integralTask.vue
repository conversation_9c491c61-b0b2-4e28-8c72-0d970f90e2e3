<template>
    <div class="integral-task flex flex-col min-h-100vh">
        <div class="mall-header relative z-1">
            <img loading="lazy" src="@/assets/integralMall/banner.png" class="w-full">
            <div class="integral-text absolute top-30% left-25px right-25px -translate-y-50% flex justify-around">
                <div class="text-[#fff] text-center" @click="toPage('/integralMall/integralDetails', { type: '0' })">
                    <div class="text-28px">累计积分</div>
                    <div class="num text-52px mb-24px mt-10px">{{ mallInfo.summaryIntegral }}</div>
                </div>
                <div class="text-[#fff] text-center" @click="toPage('/integralMall/integralDetails', { type: '0' })">
                    <div class="text-28px">可用积分</div>
                    <div class="num text-52px mb-24px mt-10px">{{ mallInfo.userIntegral }}</div>
                </div>
            </div>
        </div>
        <div class="task-list -mt-190px  bg-[#fff] rounded-16px flex-1 overflow-y-scroll p-40px relative z-2">
            <div class="cell-list flex items-center pb-20px mb-30px" v-for="item, index in list" :key="index">
                <img loading="lazy" :src="item.img" class="w-75px min-h-75px" />
                <div class="flex items-center justify-between h-full flex-1 ml-40px">
                    <div class="right flex-1">
                        <div class="title text-#333 text-32px leading-none">{{ item.ruleName }}</div>
                        <div class="des text-#666 text-28px mt-10px">{{
                            item.ruleCode === 'sign' ?
                                `每日${item.score}分，连续签到${item.cycleDay}日奖励${item.cycleReward}分` : item.completeDescribe
                        }}</div>
                    </div>
                    <div class="left-btn" @click="toMacthRouter(item)">
                        <div class="btn bg-#5AA4FF text-#fff leading-none
                        text-28px py-14px px-26px rounded-35px text-center"
                            :class="{ 'finish-btn !text-#5AA4FF': item.completeStatus }">{{ item.buttonText }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { myIntegralNum, integralTaskList } from "@/api/mall/integral";
import { useUserStore } from '@/store/modules/user';
import task1 from "@/assets/integralMall/task1.png"
import task2 from "@/assets/integralMall/task2.png"
import task3 from "@/assets/integralMall/task3.png"
import task4 from "@/assets/integralMall/task4.png"
import task5 from "@/assets/integralMall/task5.png"
import task6 from "@/assets/integralMall/task6.png"
import task7 from "@/assets/integralMall/task7.png"
import task8 from "@/assets/integralMall/task8.png"
import task9 from "@/assets/integralMall/task9.png"
import task10 from "@/assets/integralMall/task10.png"
const useStore = useUserStore();
const userInfo = computed(() => useStore.userInfo)
onMounted(() => {
    getIntegralNum()
    getList()
})
// 获取统计积分 
const mallInfo = ref<any>({})
const getIntegralNum = async () => {
    const { code, data } = await myIntegralNum({
        userId: userInfo.value?.userId
    })
    if (code === 200) mallInfo.value = data
}

const list = ref<any>([])
const getList = async () => {
    const { code, data } = await integralTaskList()
    if (code === 200) list.value = data.map((item: any) => {
        item.img = matchImage(item.ruleCode)
        return item
    })
}
const matchImage = (code: string) => {
    switch (code) {
        case 'sign':
            return task1
        case 'signBefore':
            return task3
        case 'read':
            return task4
        case 'like':
            return task2
        case 'collect':
            return task10
        case 'share':
            return task9
        case 'browseDistrict':
            return task5
        case 'enterUnion':
            return task6
        case 'venueScan':
            return task8
        case 'birthdayBenefits':
            return task7
        default:
            return ''
    }
}
const toMacthRouter = (item) => {
    // 已完成不跳转
    if (item.completeStatus) return
    let path = '', query = {}
    switch (item.ruleCode) {
        case 'sign':
        case 'signBefore':
            path = '/integralMall'
            break
        case 'read':
        case 'like':
        case 'collect':
        case 'share':
            path = '/home'
            break
        case 'browseDistrict':
            path = '/county'
            break
        case 'enterUnion':
            path = '/joinHistory'
            break
        case 'venueScan':
            path = '/position'
            break
        case 'birthdayBenefits':
            path = '/yearGift'
            break
        default:
            return ''
    }
    toPage(path, query)
}

const router = useRouter()
const toPage = (path: string, query: any = {}) => {
    router.push({
        path,
        query
    })
}
</script>

<style lang="scss" scoped>
.task-list {
    .cell-list {
        border-bottom: 1px solid #E7E8E9;
    }

    .finish-btn {
        background-color: rgba(90, 164, 255, 0.2)
    }
}
</style>