<template>
    <div class="county-detail w-full">
        <div class="flex justify-between px-20px py-10px box-border">
            <div class="left">
                <img loading="lazy" src="@/assets/county/conty_title.png" alt="" class="w-130px">
            </div>
            <div class="right flex items-center" @click.stop="isBook(Data.countyList[Data.activeIndex])">
                <div class=" flex items-center text-#5AA4FF text-26px"
                    v-if="Data?.countyList[Data?.activeIndex]?.subscribeFlag == 'Y'">
                    已订阅:{{ Data.countyList[Data.activeIndex]?.subscribeCityName }}
                    <div class="flex items-center pl-10px">
                        <img loading="lazy" src="@/assets/county/icon-ordered.png" alt="" class="w-26px h-23px">
                    </div>
                </div>
                <div style="color:#03C5C8" class=" flex items-center text-26px"
                    v-if="Data.countyList[Data.activeIndex]?.subscribeFlag == 'N'">
                    <!-- 当前频道 -->
                    <div class="leading-none">订阅: {{ currentCounty }}</div>
                    <img loading="lazy" src="@/assets/county/icon-order.png" alt="" class="w-26px h-23px ml-15px">
                </div>
                <div class="line w-1px h-19px bg-#64A7FE mx-24px"></div>
                <div class="text-#5AA4FF text-26px  flex items-center" @click.stop="backCounty">
                    <img src="@/assets/county/icon-change.png" class="w-27px" />
                    <span class="leading-none ml-12px">切换</span>
                </div>
            </div>
        </div>
        <div class="w-full px-20px py-10px box-border">
            <!-- <Swiper :modules="modules" :slides-per-view="4" :loopedSlides="6" :loop="true"
                :slide-to-clicked-slide="true" :autoplay="{ stopOnLastSlide: false, }" v-if="Data.countyList.length"
                :initial-slide="0" :centered-slides="true" @swiper="onSwiper" @touchStart="onTouchStart"
                :coverflowEffect="Data.coverflowEffect" :observer="true" @realIndexChange="onSlideChange"
                :observeParents="true" effect="coverflow" :grabCursor="true" :space-between="12"
                class="w-full h-full city-swiper" ref="countySwiper" key="countySwiper">
                <swiper-slide v-for="(item, index) in Data.countyList" :key="index" class="!w-170px swiper-slide">
                    <div class="h-full rounded-10px relative">
                        <img loading="lazy" :src="utils.judgeStaticUrl(item.cityPicUrl)" alt=""
                            class="w-170px h-220px rounded-10px" />
                        <div class="text-28px text-#333 text-center">{{ item.subscribeCityName }}</div>
                    </div>
                </swiper-slide>
            </Swiper> -->
            <div class="bigbanner">
                <img loading="lazy" :src="utils.judgeStaticUrl(currentImgUrl)" alt="" class="w-100% rounded-20px" />
            </div>
        </div>
        <div class="total_box bg-#F2FAF9 w-full mt-24px overflow-hidden">
            <div class=" w-full overflow-hidden text-28px " v-if="Data.tradeList.length">
                <ul class="statistics " :style="`animation-duration: ${Data.tradeList.length * 4}s;`">
                    <li class="statisticsList" v-for="(item, index) in Data.tradeList" :key="index">
                        {{ item.titName }}：
                        <span class="infoNum"> {{ item.allNumb }}</span>
                    </li>
                    <li class="statisticsList" v-for="(item, index) in Data.tradeList" :key="index + 'copy'">
                        {{ item.titName }}：
                        <span class="infoNum"> {{ item.allNumb }}</span>
                    </li>
                </ul>
            </div>
        </div>
        <div class="p-30px box-border w-full">

            <div class="flex justify-between w-full h-220px">
                <div class="w-48% inclu p-11px box-border">
                    <Title :titleImg="'inclu-title.png'" :icon="'5.png'" :title="'inclusive'" @lookMore="lookMore">
                    </Title>
                    <van-swipe class="my-swipe w-full h-140px mt-12px" :autoplay="3000" indicator-color="white">
                        <van-swipe-item v-for="(item, index) of Data.incluList" class="w-full h-full rounded-15px"
                            @click="toDetail(item)">
                            <img loading="lazy"
                                :src="item.appCover ? utils.judgeStaticUrl(item.appCover) : defaultIncluCover" alt=""
                                class="w-full h-full rounded-15px" />
                        </van-swipe-item>
                        <van-swipe-item class="w-full h-full rounded-15px" v-if="!Data.incluList?.length">
                            <img loading="lazy" :src="defaultIncluCover" class="w-full h-full rounded-15px" alt="">
                        </van-swipe-item>
                    </van-swipe>
                </div>
                <div class="w-48% union p-11px box-border">
                    <Title :titleImg="'union-title.png'" :icon="'6.png'" :title="'union'" @lookMore="lookMore"></Title>
                    <van-swipe class="my-swipe w-full h-140px mt-12px" :autoplay="3000" indicator-color="white">
                        <van-swipe-item v-for="(item, index) of Data.activityList" class="w-full h-full rounded-15px"
                            @click="toDetail(item)">
                            <img loading="lazy"
                                :src="item.appCover ? utils.judgeStaticUrl(item.appCover) : defaultCover" alt=""
                                class="w-full h-full rounded-15px" />
                        </van-swipe-item>
                        <van-swipe-item class="w-full h-full rounded-15px" v-if="!Data.activityList?.length">
                            <img loading="lazy" :src="defaultCover" alt="" class="w-full h-full rounded-15px">
                        </van-swipe-item>

                    </van-swipe>
                </div>
            </div>
            <div class="interest rounded-10px p-12px box-border mt-15px" v-if="Data.recommend?.length">
                <Title :titleImg="'interest.png'" :icon="'3.png'" :title="'group'" @lookMore="lookMore"></Title>
                <div class="h-300px pt-15px">
                    <swiper v-if="Data.recommend?.length" :autoplay="{
                        delay: 2500,
                        disableOnInteraction: false,
                    }" :loop="false" :modules="modules" :slides-per-view="3" :space-between="15"
                        key="intersetGroupSwiper" class="mySwiper !ml-0">
                        <swiper-slide
                            class="group-box rounded-10px relative !flex items-center justify-center flex-col  p-25px border-box m-auto"
                            v-for="(item, index) of Data.recommend" :key="index" @click="toGroupDetail(item)">
                            <div
                                class="text-#fff text-24px absolute top-0 left-0 w-70px h-30px text-center group-num text-center">
                                {{ item.memberCount }}人</div>
                            <img loading="lazy" :src="utils.judgeStaticUrl(item.logo)" alt=""
                                class="w-56px h-56px group-icon mb-12px m-auto">
                            <div
                                class="text-26px text-center text-ellipsis line-clamp-2 whitespace-nowrap text-wrap h-70px">
                                {{ item.groupName }}</div>

                            <div class="text-#fff text-24px rounded-15px w-75px py-4px mt-12px text-center"
                                :class="item.joinState || item.memberCount == item.memberMax ? 'bg-#999' : 'bg-#5AA4FF'"
                                @click.stop="join(item)">{{ item.joinState ? '退出' : item.memberCount == item.memberMax ?
                                    '已满'
                                    : '加入' }}
                            </div>
                        </swiper-slide>
                    </swiper>
                </div>
            </div>
            <van-tabs v-if='Data.tabData?.length > 1' v-model:active="Data.activetab" background="transparent"
                title-inactive-color="#666666" title-active-color="#333333" @change="onClickTab">
                <van-tab class="flex-1" v-for="(item, index) in Data.tabData" :id="item.categoryId"
                    :title="item.categoryName" :key="index">
                </van-tab>
            </van-tabs>
            <refreshList @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
                <newsCell v-for="(item, index) in Data.list" :key="index" :content="item"></newsCell>
            </refreshList>
        </div>
        <Popup :show="Data.showPop" :titleName="'加入原因'" :placeHolder="'请输入加入原因'" @submit-content="joinContent"
            @close-popup="closePopup" />
        <!-- 首页推送 -->
        <launchPopup :isShow="Data.isShow" :imgList="Data.imgList" @closeLaunch="closeLaunch" v-if="Data.isShow" />
    </div>
</template>
<script lang="ts" setup>
import Title from '@/components/Title/index.vue';
import defaultCover from '@/assets/public/act_de.jpg'
import defaultIncluCover from '@/assets/public/inclu_de.jpg'
import { Swiper, SwiperSlide } from 'swiper/vue';
import refreshList from '@/components/refreshList/index.vue';
import { findH5XQBannerList, saveSubscribeRecord, deleteSubscribeRecord } from '@/api/news';
//用到哪些功能自行添加
import { Scrollbar, A11y, EffectCoverflow } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/scrollbar';
import Popup from '@/components/Popup/popup.vue';
import { getGroupH5, joinGroup, exitGroup } from '@/api/interestGroup';
import { GridItem, showConfirmDialog, showFailToast, showToast } from "vant";
import { useUserStore } from '@/store/modules/user';
import Empty from "@/components/Empty/index.vue";
import { h5CategoryInfo, getNewsList } from '@/api/news/index';
import router from '@/router';
import newsCell from "@/components/Cell/newsCell.vue";
import { activityInfoList } from "@/api/activity.ts";
import utils from '@/utils/utils';
import { userCount } from '@/api/public';
import { useRoute, useRouter } from 'vue-router';
import { toDetail } from "@/hooks/useValidator";
import { addBrowseDistrict } from "@/api/public";
import { asyncAddWebVisitSummary, queryCurrentUserLaunchConfig } from '@/api/public';
import launchPopup from '@/components/Popup/launchPopup.vue';
const route = useRoute();
const useStore = useUserStore();
const modules = [Scrollbar, A11y, EffectCoverflow]
const Data = ref({
    isShow: false,
    imgList: [],
    activeIndex: 0,
    countyList: [],
    incluList: [],
    showLabel: false,
    showPop: false,
    joinObj: {},
    recommend: [],
    list: [],
    pageNum: 1,
    coverflowEffect: {
        rotate: 0,
        stretch: 0,
        depth: 100,
        modifier: 1,
        scale: 0.9,
        slideShadows: false,
    },
    tabData: [],
    activetab: 0,
    activityList: [],
    tradeList: [
        {
            titName: "认证用户数",
            allNumb: '--'
        },
        {
            titName: "工会组织数",
            allNumb: '--'
        },
        {
            titName: "工会会员数",
            allNumb: '--'
        },
        {
            titName: "工会干部数",
            allNumb: '--'
        },
        {
            titName: "注册用户数",
            allNumb: '--'
        }
    ],
})
function closeLaunch(show) {
    Data.value.isShow = false;
}
//获取首页配置
function getLaunch() {
    Data.value.isShow = false;
    Data.value.imgList = []
    queryCurrentUserLaunchConfig({ firstIntoHome: 'y', companyIdH5: Data.value.countyList[Data.value.activeIndex]?.subscribeCityCode }).then(res => {
        if (res.code == 200) {
            if (res.data?.length) {
                Data.value.isShow = true;
                Data.value.imgList = res.data;
            }

        }
    })
}
//新增访问量
function getAddWebVisitSummary() {
    asyncAddWebVisitSummary({ systemTypeCode: 'h5', companyId: Data.value.countyList[Data.value.activeIndex]?.subscribeCityCode }).then(res => { })
}
const useSwiper = ref(null) // swiper实例
const pre = () => {
    useSwiper.value.slidePrev(500, (val) => {
        console.log(val, 'val')
    })
}
const next = () => {
    useSwiper.value.slideNext(500, (val) => {
        console.log(val, 'val')
    })
}
const onSwiper = (swiper) => {
    useSwiper.value = swiper;
    nextTick(() => {
        // let active = Data.value.activeIndex == 6 ? 0 : Data.value.activeIndex == 7 ? 1 : Data.value.activeIndex == 8 ? 2 : Data.value.activeIndex + 3
        // useSwiper.value.slideTo(active, 1000, true);
        useSwiper.value.slideToLoop(Data.value.activeIndex, 1000, true)
        useSwiper.value.update();
    })
}

const pageWidth = (document.documentElement.clientWidth || document.body.clientWidth) / 2;//居中位置
const onTouchStart = (swiper, event) => {
    swiper.loopFix({
        force: true,
    })

    // if (event.clientX < pageWidth) {
    //     swiper.slidePrev(500, (val) => {
    //         console.log(val, 'val')
    //     })
    // } else if (event.clientX > pageWidth) {
    //     useSwiper.value.slideToLoop(swiper.realIndex, 500, true)
    //     // swiper.slideNext(500, (val) => {
    //     //     console.log(val, 'val')
    //     // })
    // }
    // nextTick(() => {
    //     swiper.updateSlides()
    //     swiper.updateProgress()
    // })

}
//改变滑动块
const onSlideChange = (swiper) => {
    Data.value.pageNum = 1;
    Data.value.activeIndex = swiper.realIndex;
    initRequest()
};
//获取会员信息
async function getUserCount() {
    let uninfoArr = [{
        titName: "认证用户数",
        allNumb: "--",
    }, {
        titName: "工会组织数",
        allNumb: "--",
    }, {
        titName: "工会会员数",
        allNumb: "--",
    }, {
        titName: "工会干部数",
        allNumb: "--",
    }, {
        titName: "注册用户数",
        allNumb: "--",
    }]
    const res = await userCount({ ac: Data.value.countyList[Data.value.activeIndex].ac, uid: Data.value.countyList[Data.value.activeIndex]?.subscribeCityCode })
    if (res.code == 200) {
        uninfoArr[0].allNumb = res.data.checkedUserCount;
        uninfoArr[1].allNumb = res.data.unionCount;
        uninfoArr[2].allNumb = res.data.userCount;
        uninfoArr[3].allNumb = res.data.cadreCount;
        uninfoArr[4].allNumb = res.data.registerCount;
    }
    console.log(uninfoArr, 'ssdsdsd');
    Data.value.tradeList = uninfoArr;

}
//获取普惠活动列表
function getActList() {
    activityInfoList({
        activityCategory: 'inclusive',
        pageSize: 10,
        pageNum: 5,
        companyId: Data.value.countyList[Data.value.activeIndex]?.subscribeCityCode
    }).then(res => {
        if (res.code == 200) {
            Data.value.incluList = res.data;
        }

    })
}
//获取工会活动列表
function getUnionList() {
    activityInfoList({
        activityCategory: 'union',
        pageSize: 10,
        pageNum: 5,
        companyId: Data.value.countyList[Data.value.activeIndex]?.subscribeCityCode
    }).then(res => {
        if (res.code == 200) {
            Data.value.activityList = res.data;
        }

    })
}
//预定-取消
function isBook(item) {
    // if (!useStore.getUserInfo?.userId) {
    //     showToast("请先在川工之家登录后重试");
    //     return
    // }
    utils.isLoginDialog((isLogin: boolean) => {
        if (isLogin) {
            if (item.subscribeFlag == 'N') {
                saveSubscribeRecord({
                    userId: useStore.getUserInfo?.userId,
                    subscribeCityCode: item.subscribeCityCode,
                    subscribeCityName: item.subscribeCityName
                }).then(res => {
                    if (res.code == 200) {
                        Data.value.countyList.map(el => {
                            el.subscribeFlag = 'N';
                        })
                        if (item.subscribeFlag == 'N') {
                            item.subscribeFlag = 'Y';
                            showToast("订阅成功");
                            useStore.setCityData(item)
                        }

                    }
                })
            } else {
                deleteSubscribeRecord({
                    userId: useStore.getUserInfo?.userId,
                }).then(res => {
                    if (res.code == 200) {
                        Data.value.countyList.map(el => {
                            el.subscribeFlag = 'N'
                        })
                        item.subscribeFlag = 'N';
                        showToast("取消成功");
                        useStore.setCityData(item)

                    }
                })
            }
        }
    })
}
function onClickTab(val) {
    Data.value.activetab = val;
    Data.value.pageNum = 1;
    if (loadMoreRef.value) loadMoreRef.value.resetStatus();
    onRefreshList();
}
//查看更多
function lookMore(title) {
    let path = "";
    if (title == 'group') {
        path = '/groupList'
    } else if (title == 'union') {
        path = '/activity'
    } else if (title == 'inclusive') {
        path = '/inclusiveActivity'
    }
    router.push({
        path: path,
        query: {
            companyId: Data.value.countyList[Data.value.activeIndex]?.subscribeCityCode,
        }
    })

}
//跳转详情
function toGroupDetail(item) {
    router.push({
        path: '/groupDetail',
        query: {
            groupId: item.groupId
        }
    })
}
//获取兴趣小组列表
const getGroupList = () => {
    getGroupH5({
        pageNum: 1,
        pageSize: 10,
        companyClassicIds: Data.value.countyList[Data.value.activeIndex]?.subscribeCityCode
    }).then((res) => {
        Data.value.recommend = res.data || [];
    })
}
//加入
function join(item) {
    // if (!useStore.getUserInfo?.userId) {
    //     showFailToast("请先在川工之家登录后重试");
    //     return
    // }
    utils.isLoginDialog((isLogin: boolean) => {
        if (isLogin) {
            Data.value.joinObj = item;
            if (item.memberCount == item.memberMax) {
                return
            }
            if (!item.joinState) {
                Data.value.showPop = true;
            } else {
                showConfirmDialog({
                    title: "提示",
                    message: `确认退出${item.groupName}小组?`,
                    confirmButtonText: "确认",
                    cancelButtonText: "取消",
                    className: "close",
                }).then(async () => {
                    // 自定义代码
                    exitGroup({ groupId: Data.value.joinObj.groupId }).then(res => {
                        if (res.code == 200) {
                            showToast("退出成功");
                            item.joinState = !item.joinState
                        } else {
                            showFailToast(res.message)
                        }
                    })
                });

            }
        }
    })
}
//确认提交
function joinContent(val) {
    if (!val) {
        showToast({
            message: "请输入加入原因",
            icon: 'none',
        })
        return
    }
    joinGroup({
        groupName: Data.value.joinObj.groupName,
        groupId: Data.value.joinObj.groupId,
        reason: val
    }).then(res => {
        if (res.data) {
            val = '';
            getGroupList();
            showToast("提交成功，等待管理员审核~")
        } else {
            showFailToast(res.message)
        }

    })
    Data.value.showPop = false;
}
//关闭
function closePopup() {
    Data.value.showPop = false;
}
//获取栏目
async function getColumn() {
    h5CategoryInfo({
        companyId: Data.value.countyList[Data.value.activeIndex]?.subscribeCityCode,
        platformType: 30,
    }).then(res => {
        Data.value.tabData = res.data;
        getLists();
    });
}
// 刷新
const onRefreshList = () => {
    Data.value.pageNum = 1
    Data.value.list = [];
    getLists()
}
// 加载更多
const onLoadMore = () => {
    Data.value.pageNum++
    getLists()
}
//新闻列表
const loadMoreRef = ref(null)
async function getLists() {
    let res = await getNewsList({
        categoryCode: Data.value?.tabData[Data.value.activetab]?.categoryCode,
        platformType: 30,
        pageNum: Data.value.pageNum,
        pageSize: 5,
    });
    if (Data.value.pageNum === 1) Data.value.list = [];
    Data.value.list = Data.value.list.concat(res.data);
    //重置刷新状态及 判断是否加载完成
    if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(Data.value.list.length, res.total);
    }
}
//获取区县列表
const currentImgUrl = ref('')
const currentCounty = ref('')
async function getBannerList() {
    // 有缓存直接取缓存的
    if (useStore?.getCityList) {
        Data.value.countyList = useStore?.getCityList;
    } else {
        const res = await findH5XQBannerList({
            userId: useStore.getUserInfo?.userId
        })
        if (res.code == 200 && res.data.length > 0) {
            Data.value.countyList = res.data
            useStore.setCityList(res.data);
        }
    }
    // 更改为大屏展示
    if (route.query.id) {
        let cuttentIndex = Data.value.countyList.findIndex(item => item.subscribeCityCode === route.query.id)
        if (cuttentIndex > -1) {
            Data.value.activeIndex = cuttentIndex;
            currentImgUrl.value = Data.value.countyList[cuttentIndex]?.bannerPicUrl
            currentCounty.value = Data.value.countyList[cuttentIndex]?.subscribeCityName
        }
    }
    else {
        let cuttentIndex = Data.value.countyList.findIndex(item => item.subscribeFlag === 'Y')
        if (cuttentIndex > -1) {
            Data.value.activeIndex = cuttentIndex;
            currentImgUrl.value = Data.value.countyList[cuttentIndex]?.bannerPicUrl
            currentCounty.value = Data.value.countyList[cuttentIndex]?.subscribeCityName
        }
    }
    if (Data.value.activeIndex == 0) {
        currentImgUrl.value = Data.value.countyList[0]?.bannerPicUrl
        currentCounty.value = Data.value.countyList[0]?.subscribeCityName
    }
    // end

    initRequest()
}
const backCounty = () => {
    router.push({
        path: '/county'
    })
}
function getPublic() {
    addBrowseDistrict({}).then(res => { })
}
function initRequest() {
    getGroupList();
    getColumn();
    getActList();
    getUnionList();

    // 延迟请求
    setTimeout(() => {
        getUserCount();
        getLaunch();
        getAddWebVisitSummary();
    }, 200)
}
onMounted(() => {
    getBannerList();
    getPublic()
})
// watch(Data.value.countyList, () => {

// })
</script>
<style lang="scss" scoped>
.county-detail {
    background: url('@/assets/county/county.png'), #fff;
    background-size: 100%, 100%;
    background-repeat: no-repeat;
    height: 100vh;

    .inclu {
        background: url('@/assets/county/inclu.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }

    .union {
        background: url('@/assets/county/union.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }

    .statistics {
        width: fit-content; // Q1
        white-space: nowrap; // 内容不换行
        animation-name: seamless-scrolling; // Q3
        animation-timing-function: linear; // 动画速度曲线，匀速
        animation-iteration-count: infinite; // 动画循环无限次播放
        display: flex;

        .statisticsList {
            display: flex;
            margin-right: 30px;
            font-size: 25px;
            color: rgba(51, 51, 51, 1);
            white-space: nowrap;
            height: 68px;
            line-height: 68px;
            align-items: center;
            justify-content: center;
            flex-wrap: nowrap;

            .infoNum {
                font-size: 32px;
                color: #5AA4FF;
                font-weight: 500;
                margin-left: 10px;
            }
        }
    }

    @keyframes seamless-scrolling {
        0% {
            transform: translateX(0px);
        }

        100% {
            transform: translateX(-50%); //Q2
        }
    }

    .city-swiper {
        .swiper-slide {
            display: flex;
            justify-content: center;
            align-items: center;
            transition: 300ms;
            transform: scale(0.5);
            filter: blur(.5px);
            opacity: 0.7;
        }

        .swiper-slide-active,
        .swiper-slide-duplicate-active {
            transform: scale(1);
            filter: blur(0px);
            opacity: 1;
        }
    }

    .interest {
        background: linear-gradient(0deg, #FFFFFF 81%, #E1EEFF 100%);
        background-size: 100% 100%;
        background-repeat: no-repeat;
        height: 310px;

        .group-box {
            // height: 189px;
            background: linear-gradient(0deg, #FFFFFF 42%, #EBF4FF 100%);

            .group-num {
                background: linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%);
                border-radius: 10px 0px 10px 0px;
            }

            .group-icon {
                border-radius: 50%;
            }
        }
    }

    :deep(.van-swipe__indicator) {
        width: 14px !important;
        height: 4px !important
    }

    //     .swiper {
    //     width: 100%;
    //     height: 100%;


    //   }
}
</style>