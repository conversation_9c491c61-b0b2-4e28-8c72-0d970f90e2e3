<template>
    <div class="info bg-[#fff] h-fit min-h-screen">
        <div class="banner relative">
            <!-- 判断审核状态 -->
            <img loading="lazy" :src="statusObj[infos?.auditStatus]?.banner" class="w-full" />
            <!-- 审核中/审核失败 -->
            <div class="check-status absolute top-50% -translate-y-50% left-30px right-0"
                v-if="infos?.auditStatus !== 'pass'">
                <img loading="lazy" :src="statusObj[infos?.auditStatus]?.textImg" class="w-40%" />
                <div class="text-28px text-[#fff] w-50% mt-10px" v-if="infos?.auditStatus !== 'pass'">{{
                    infos?.auditInstruction
                        ?
                        infos.auditInstruction :
                        statusObj[infos?.auditStatus]?.text }}</div>
            </div>
        </div>
        <div class="form px-30px pb-50px bg-[#fff] -mt-20px overflow-hidden">
            <div class="title text-32px text-[#333] pl-20px font-medium active mb-20px">
                {{ type === 'worker' ? '劳模资料' : '工匠资料' }}</div>
            <div class="infos" v-if="infos">
                <div class="flex items-center py-15px" v-for="item, index in infoColumn[type].columns" :key="index">
                    <div class="label text-30px text-[#666] mr-20px w-138px">{{ item.label }}: </div>
                    <div class="content flex-1">
                        <template v-if="item.type === 'img'">
                            <div v-if="infos[item.props]" class="flex flex-wrap">
                                <img loading="lazy" class="mr-10px object-cover w-142px h-142px rounded-15px"
                                    @click="previewImages(infos[item.props], index)"
                                    v-for="img, index in infos[item.props]" :src="img" :key="index" />
                            </div>
                            <div v-else class="text-30px text-[#333]">-</div>
                        </template>
                        <div class="text-30px text-[#333]" v-else>
                            {{ infos[item.props] ? infos[item.props] : '-' }}
                        </div>
                    </div>
                </div>
            </div>
            <div class="resubmit_btn relative m-auto w-75% mt-45px" v-if="infos?.auditStatus === 'return'"
                @click="toPage">
                <img loading="lazy" src="@/assets/workerCraftsman/auth/page_btn.png" class="w-full block" />
                <span class="absolute left-50% top-50% -translate-50% leading-none
                text-34px text-[#fff] font-medium">
                    重新提交
                </span>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import waitBanner from '@/assets/workerCraftsman/auth/check_wait_banner.jpg'
import failBanner from '@/assets/workerCraftsman/auth/check_fail_banner.jpg'
import waitText from '@/assets/workerCraftsman/auth/check_wait_text.png'
import failText from '@/assets/workerCraftsman/auth/check_fail_text.png'
import { showImagePreview } from 'vant';
import { checkAuth } from '@/api/workerCraftsman';
import { judgeStaticUrl } from '@/utils/utils'
import { useDictionary } from '@/store/modules/dictionary';
const dictionary = useDictionary();
onMounted(() => {
    if (route.query.type) type.value = route.query.type === '0' ? 'worker' : 'craftsman'
    getInfos(route.query.type as string)
})
const router = useRouter()
const route = useRoute()
const statusObj = {
    'wait': {
        banner: waitBanner,
        textImg: waitText,
        text: '请耐心等待~'
    },
    'return': {
        banner: failBanner,
        textImg: failText,
        text: '',//劳模认证材料不符合,请重新 上传认证材料
    },
    'refuse': {
        banner: failBanner,
        textImg: failText,
        text: '',//劳模认证材料不符合
    },
}
const type = ref('worker')
const infoColumn = reactive({
    // 劳模
    'worker': {
        title: '劳模资料',
        columns: [
            {
                label: '劳模姓名',
                props: 'userName'
            },
            {
                label: '劳模头像',
                props: 'avatar',
                type: 'img'
            },
            {
                label: '联系电话',
                props: 'phone'
            },
            {
                label: '身份证号',
                props: 'identityCardNumber'
            },
            {
                label: '所属工会',
                props: 'companyName'
            },
            {
                label: '性别',
                props: 'genderName'
            },
            {
                label: '民族',
                props: 'nationalityName'
            },
            {
                label: '出生年月',
                props: 'dateOfBirth'
            },
            {
                label: '文化程度',
                props: 'educationName'
            },
            {
                label: '政治面貌',
                props: 'politicsStateName'
            },
            {
                label: '工作单位',
                props: 'workUnitName'
            },
            {
                label: '何时获得劳模称号',
                props: 'whenModelWorker'
            },
            {
                label: '所属类别',
                props: 'typeName'
            },
            {
                label: '个人摘要',
                props: 'personalStyle'
            },
            {
                label: '证明材料',
                props: 'evidentiaryMaterial',
                type: 'img'
            }
        ],
    },
    // 工匠
    'craftsman': {
        title: '工匠资料',
        columns: [
            {
                label: '工匠姓名',
                props: 'userName'
            },
            {
                label: '工匠头像',
                props: 'avatar',
                type: 'img'
            },
            {
                label: '联系电话',
                props: 'phone'
            },
            {
                label: '身份证号',
                props: 'identityCardNumber'
            },
            {
                label: '性别',
                props: 'genderName'
            },
            {
                label: '工作单位',
                props: 'companyName'
            },
            {
                label: '所属类别',
                props: 'typeName'
            },
            {
                label: '个人摘要',
                props: 'personalStyle'
            },
            {
                label: '资格证书',
                props: 'evidentiaryMaterial',
                type: 'img'
            }
        ],
    }
})
const infos = ref<any>(null)
const getInfos = async (modelType: string) => {
    const { code, data } = await checkAuth(modelType)
    if (code === 200 && data) {
        // 当前审核状态
        if (!data) return infos.value = null // 未认证
        infos.value = data
        infos.value.avatar = infos.value.avatar ? [judgeStaticUrl(infos.value.avatar, true)] : []
        infos.value.evidentiaryMaterial = infos.value.evidentiaryMaterial.split(',') ?
            infos.value.evidentiaryMaterial.split(',').map((item: string) => judgeStaticUrl(item)) : []
        // 格式化下拉展示字典
        infos.value.genderName = dictionary.getDictionaryOpt?.['gender'].find((item: any) => item.value === data.gender)?.label || ''
        infos.value.nationalityName = dictionary.getDictionaryOpt?.['nation'].find((item: any) => item.value === data.nationality)?.label || ''
        infos.value.educationName = dictionary.getDictionaryOpt?.['modelEducation'].find((item: any) => item.value === data.education)?.label || ''
        infos.value.politicsStateName = dictionary.getDictionaryOpt?.['politicsState'].find((item: any) => item.value === data.politicsState)?.label || ''
    } else {
        infos.value = null
    }
}
const previewImages = (images: string[], startPosition: number) => {
    showImagePreview({
        images,
        startPosition,
        closeable: true,
    });
}

const toPage = () => {
    if (infos.value?.auditStatus === 'return') {
        router.push({
            path: '/workerCraftsman/auth/form',
            query: {
                type: type.value === 'worker' ? '0' : '1',
                tag: 'reSubmit'
            }
        })
    }
}
</script>

<style scoped lang="scss">
.form {
    .title {
        position: relative;
    }

    .title::after {
        width: 8px;
        height: 31px;
        background-color: #2970FF !important;
        border-radius: 4px;
        display: block;
        content: ' ';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
    }

    .active::after {
        background-color: #F48F39;
    }
}
</style>