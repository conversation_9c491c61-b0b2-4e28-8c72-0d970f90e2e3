<template>
    <div>
        <div v-for="(item, index) in data" :key="index"
            class="bg-#fff px-24px py-22px box-border rounded-16px mb-20px relative" @click="toNewsDetail(item)">
            <div class=" flex items-center h-100%">
                <img loading="lazy" :src="item.newsCoverUrl" class="w-200px h-136px rounded-16px mr-25px" alt="">
                <div class="flex flex-col justify-around min-h-136px">
                    <div class="text-#333333 text-28px text-ellipsis line-clamp-2 whitespace-nowrap text-wrap">
                        {{ item.newsTitle }}</div>
                    <slot name="subContent" :item="item">
                        <div class="flex items-center text-#999999 text-22px">
                            <div class="flex items-center mr-46px">
                                <img loading="lazy" src="@/assets/vitality/volunteerService/icon_bmsj.png" alt=""
                                    class="w-22px h-22px mr-10px" />{{ item.releaseTimeChinese }}
                            </div>
                            <div class="flex items-center">
                                <van-icon name="eye-o" size="15" class="z-1 mr-10px"
                                    color="#999999" />{{ item.newsClicks }}
                            </div>
                        </div>
                    </slot>
                </div>
            </div>
            <slot name="bottomPart" :item="item"></slot>
            <slot name="status" :item="item"></slot>
        </div>
    </div>
</template>
<script setup lang="ts">
const props = defineProps({
    data: {
        type: Array,
        default: [],
    },
});
const emit = defineEmits(['toNewsDetail']);
function toNewsDetail(item) {
    emit('toNewsDetail', item)
}
</script>