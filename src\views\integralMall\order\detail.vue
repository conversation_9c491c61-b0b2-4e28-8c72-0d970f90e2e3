<template>
    <div class="order-detail bg-[#F9F9F9] flex flex-col">
        <div class="flex-1">
            <div class="relative z-1">
                <orderStatusHeader :status="details?.deliveryStatus" />
            </div>
            <div class="-mt-100px relative z-10 ">
                <div class="address flex items-center bg-[#fff] rounded-16px mx-30px py-33px px-30px">
                    <img loading="lazy" src="@/assets/integralMall/order_info_address.png" class="w-37px" />
                    <div class="flex-1 ml-30px" v-if="details?.receiverName">
                        <div class="name text-#333 text-32px font-medium flex items-center flex-wrap">
                            <span class="mr-20px">{{ details?.receiverName }}</span>
                            <span class="flex-1">{{ details?.receiverPhone }}</span>
                            <div class="w-56px  ml-5px"></div>
                        </div>
                        <div class="text-#474747 text-28px mt-20px">地址：{{ details?.detailArea }}{{
                            details?.detailAddress }}</div>
                    </div>
                    <div class="ml-30px text-28px text-#333" v-else>未获取到收件人信息</div>
                </div>
                <div class="address flex items-center bg-[#fff] rounded-16px mx-30px py-33px px-30px">
                    <img loading="lazy" src="@/assets/integralMall/send_ad_icon.png" class="w-37px" />
                    <div class="flex-1 ml-30px" v-if="details?.logisticsCompany">
                        <div class="name text-#333 text-32px font-medium flex items-center flex-wrap">
                            <span class="mr-20px">{{ details?.logisticsCompany }}</span>
                            <span class="flex-1">{{ details?.logisticsNumber }}</span>
                            <div class="copy text-#666 bg-[#EDEDED] p-6px text-22px leading-none rounded-4px w-56px ml-10px"
                                @click="toCopy(1)">
                                复制</div>
                        </div>
                        <div class="text-#474747 text-28px mt-20px">地址：{{ details?.shippingAddress }}</div>
                    </div>
                    <div class="ml-30px text-28px text-#333" v-else>未获取到发货信息</div>
                </div>
            </div>


            <div class="goods mx-30px mt-20px">
                <orderMallCell :showHeader="false" :showBottom="false" :content="details"></orderMallCell>
            </div>
            <div class="transaction rounded-16px bg-[#fff] px-28px py-32px mx-30px">
                <div class="header text-30px text-#333 mb-35px">交易信息</div>
                <div class="flex justify-between items-center">
                    <div class="label text-[#666] text-28px">订单编号</div>
                    <div class="flex-1 ml-44px text-28px">{{ details?.recordId }}</div>
                    <div class="copy text-#666 bg-[#EDEDED] p-6px text-22px leading-none rounded-4px"
                        @click="toCopy(2)">
                        复制</div>
                </div>
                <div class="flex justify-between items-center mt-39px">
                    <div class="label text-[#666] text-28px">创建时间</div>
                    <div class="flex-1 ml-44px text-28px">{{ details?.createTime }}</div>
                </div>
                <!-- 没找到字段 -->
                <div class="flex justify-between items-center mt-39px">
                    <div class="label text-[#666] text-28px">兑换时间</div>
                    <div class="flex-1 ml-44px text-28px">{{ details?.createTime }}</div>
                </div>
                <!-- 暂时不要 -->
                <!-- <div class="flex justify-between items-center mt-39px">
                    <div class="label text-[#666] text-28px">付款时间</div>
                    <div class="flex-1 ml-44px text-28px">{{ details?.createTime }}</div>
                </div> -->
            </div>
        </div>

        <!-- 操作栏 -->
        <div class="controll-btn h-110px bg-#fff flex items-center justify-end px-30px"
            v-if="details?.deliveryStatus === 'receive'">
            <div class="h-70px rounded-35px text-#FFFFFF text-32px receive-btn px-34px 
            leading-70px" v-if="details?.deliveryStatus === 'receive'" @click="receiveShow = true">确认收货</div>
        </div>
        <confirmReceivePopup @confirm="confrimReceive" :bgCover="details?.productCoverImg" v-model:show="receiveShow" />
    </div>
</template>
<script lang="ts" setup>
import orderMallCell from "../components/orderMallCell.vue";
import orderStatusHeader from "../components/statusHeader.vue";
import confirmReceivePopup from "@/components/Popup/confirmReceivePopup.vue";
import { getIntegralOrder, confirmReceiveGoods } from '@/api/mall/integral'
import utils from "@/utils/utils";
import { showConfirmDialog } from "vant";
import useRefreshFun from '@/hooks/app.ts'
onMounted(() => {
    // 订单详情
    if (route.query?.recordId) getOrderDetail();
})
const route = useRoute();
const details = ref<any>({});
const getOrderDetail = () => {
    getIntegralOrder(route.query?.recordId as string).then((res) => {
        details.value = res?.data;
    });
}
const toCopy = (from: number) => {
    if (from === 1) utils.copyToClibboard(details.value?.logisticsNumber)//物流单号
    else utils.copyToClibboard(details.value?.recordId)//订单编号
}
const receiveShow = ref(false);
// 确认收货
const confrimReceive = async () => {
    const { code } = await confirmReceiveGoods(details.value?.recordId as string);
    if (code === 200) {
        showConfirmDialog({
            message: '确认收货成功!',
            confirmButtonText: '知道了',
        })
        receiveShow.value = false;
        getOrderDetail();
        refresh()//刷新列表
    }
}

// 触发刷新兑换记录
const { addRefreshList } = useRefreshFun();
const refresh = () => {
    addRefreshList({ pageName: 'integralDetails', funsName: ['changeOrderConfirm'] })
}
// end
</script>

<style scoped lang="scss">
.receive-btn {
    background: linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%);
}
</style>