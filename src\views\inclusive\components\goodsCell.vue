<template>
    <!-- 商品展示 -->
    <div class="goods_cell w-full bg-[#fff] pb-20px box-border overflow-hidden" @click="navTo">
        <div class="goods_img w-full h-297px bg-cover bg-center"
            :style="{ backgroundImage: `url(${judgeStaticUrl(content?.productCoverImg)})` }"></div>
        <div class="goods_info w-full">
            <div class="goods_name text-28px font-weight box-border
            m-20px overflow-hidden text-ellipsis whitespace-nowrap mb-10px">
                {{ content?.productName }}
            </div>
            <div class="goods_price relative mx-13px flex items-start justify-between">
                <div class="price text-[#FF4344]">
                    <!-- 金额 -->
                    <span class="text-18px">￥</span>
                    <span class="text-30px font-medium">{{ content?.singlePriceInfo?.nowPrice }}</span>
                    <span class="old-price text-#B3B3B3 text-20px ml-10px">￥{{ content?.singlePriceInfo?.oldPrice
                        }}</span>
                </div>
                <div class="cart w-48px h-48px" @click.stop="addShoppingCart">
                    <img src="@/assets/inclusive/shop/icon_gwc.png" alt="" class="w-100% h-100%" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { judgeStaticUrl } from '@/utils/utils';
import useShoppingCart from '@/hooks/shopingCart'
const props = defineProps({
    content: {
        type: Object,
        default: () => ({}),
        required: true,
    }
})
const router = useRouter()

// 商品详情跳转
const navTo = () => {
    router.push({
        path: `/inclusive/goodsDetail`,
        query: {
            productId: props.content?.productId,
        }
    })
}

// 购物车
const { addCart } = useShoppingCart()
const addShoppingCart = () => {
    const { companyId, productId } = props.content
    const { productSubId } = props.content?.singlePriceInfo
    const form = {
        companyId,
        productId,
        productSubId,
        amount: 1,
        operateType: "add"
    }
    addCart(form, (res: any) => {

    })
}
</script>

<style lang="scss" scoped>
.goods_cell {
    border-radius: 0px 0px 16px 16px;

    .goods_img {
        border-radius: 16px 16px 0px 0;
    }

    box-shadow: 0px 3px 11px 0px rgba(0, 45, 92, 0.1);

    .goods_price {
        .price {
            width: calc(100% - 48px);
            word-break: break-all;
        }
    }

    .old-price {
        text-decoration: line-through;
    }
}
</style>