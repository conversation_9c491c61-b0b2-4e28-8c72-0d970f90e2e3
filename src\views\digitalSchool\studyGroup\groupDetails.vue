<template>
  <div class="bg-#f6f7f8 flex flex-col min-h-100vh">
    <div :style="{ backgroundImage: `url('@/assets/interest/bg_group.png')` }"
      class="bg-no-repeat bg-cover bg-center w-100% p-30px box-border">
      <interestGroupList :data="Data.groupList" :showBtn="false">
        <template #dataPart="{ item }">
          <div class="flex items-center text-[#999] text-[26px] mt-20px">
            <img loading="lazy" src="@/assets/interest/icon_num.png" alt="" class="w-34px h-30px mr-10px" />
            {{ item.maxPeople }}人
          </div>
        </template>
        <template #bContent="{ item }">
          <div class="flex items-center text-[#333] text-[30px] my-20px">
            {{ item.groupIntroduce }}
          </div>
        </template>
      </interestGroupList>
    </div>
    <div class="px flex-1 flex flex-col pb-160px">
      <div class="flex-1 bg-#fff flex flex-col rounded-20px">
        <div class="tab-box flex items-center relative sticky top-0 z-999 bg-#fff">
          <div v-for="(tabItem, tabIndex) in Data.tab.nav" :key="tabIndex" :style="{
            backgroundImage: tabIndex === Data.tab.active ? `url(${tabItem.bg})` : 'none',
          }" style="background-size: 120% 100%"
            class="w-1/3 flex justify-center items-center bg-no-repeat bg-center h-120px mt-[-10px] rounded-20px"
            @click="onTab(tabIndex)" :class="tabIndex === Data.tab.active
              ? 'tab-active text-34px text-#333'
              : 'text-30px text-#666'
              ">
            {{ tabItem.name }}
          </div>
        </div>
        <refreshList key="relist" :noshowText="Data.tab.active === 0" @onRefreshList="onRefreshList"
          @onLoadMore="onLoadMore" ref="loadMoreRef">
          <!-- 学习交流 -->
          <div v-if="Data.tab.active === 0" class="px my-40px box-border" ref="scrollBoxRef">
            <div class="text-#969799 text-22px text-center"
              v-show="Data.dialogueList.length === hisTotal && hisTotal !== 0">没有更多了</div>
            <Dialogue :data="Data.dialogueList" v-if="Data.dialogueList && Data.dialogueList.length" />
          </div>
          <!-- 心得分享 -->
          <div v-else-if="Data.tab.active === 1" class="my-40px box-border">
            <shareList :data="Data.xdfxList" type="studyshare" :is-show-detail="false" @detail="detail"
              v-if="Data.xdfxList && Data.xdfxList.length">
              <template #bottomContent="{ item }">
                <div class="flex mt-20px justify-between">
                  <div class="flex items-center w-55/100 bg-#F6F7F8 rounded-18px px-20px">
                    <img loading="lazy" :src="editIcon" alt="" srcset="" class="w-17px h-17px" />
                    <input type="text" class="flex-1 bg-#F6F7F8 border-0 h-40px text-26px pl-15px" placeholder="说点什么..."
                      v-model="item.val" @blur.stop="comment(item)" />
                  </div>

                  <div class="text-#999999 text-24px flex justify-around w-30/100">
                    <!-- <div class="flex items-center"><van-icon name="eye-o" size="20"
                      v-if="type != 'studyshare'" />{{ item.readCount }}</div> -->
                    <div class="flex items-center"><van-icon name="chat-o" size="20" class="mr4px" />{{
                      item.experienceSharingReplySum }}</div>
                    <div class="flex items-center" @click.stop="like(item)"><van-icon
                        :name="item.likeStatus ? 'good-job' : 'good-job-o'"
                        :color="item.likeStatus ? '#5AA4FF' : '#999'" size="20" class="mr4px" />{{ item.likeNum }}
                    </div>
                  </div>
                </div>
              </template>
            </shareList>
          </div>
          <div v-else-if="Data.tab.active === 2" class="overflow-scroll relarive px">
            <div class="flex-1 box-border" v-if="Data.memberList && Data.memberList.length">
              <div v-for="(item, index) of Data.memberList" :key="index" class="py-22px hr flex items-center">
                <img loading="lazy" :src="!item.isChoose ? noChoose : yesChoose" alt="" class="w-30px h-30px mr-28px"
                  :class="Data.showTool && item.isLeader == 'n' ? 'visible' : 'invisible'" @click="choose(item)" />
                <div class="flex items-center justify-between flex-1">
                  <div class="flex items-center">
                    <img loading="lazy" :src="defaultHead" alt="" class="w-76px mr-25px" />
                    <div>{{ item.addUserName }}</div>
                    <div
                      class="bg-#DEEDFF w-100px h-30px flex items-center justify-center text-20px text-#5AA4FF rounded-15px ml-30px"
                      v-if="item.isLeader == 'y'">
                      <img loading="lazy" src="@/assets/interest/icon_admin.png" alt="" class="w-19px mr-5px" />管理员
                    </div>
                  </div>
                  <!-- 管理员 -->
                  <div v-if="item.isLeader == 'y' && Data.isLeaderFlag"
                    class="w-100px h-42px flex items-center ml-20px justify-center text-26px text-#5AA4FF rounded-20px border-1px border-#5AA4FF border-solid"
                    @click="onManage">
                    管理
                  </div>
                </div>
              </div>
            </div>
          </div>
        </refreshList>
      </div>
    </div>

    <div v-if="Data.tab.active === 0"
      class="fixed bottom-0 w-100vw text-input h-118px bg-#fff px-30px box-border flex items-center">
      <div class="flex items-center bg-#F6F7F8 rounded-33px h-66px w-100% px">
        <input type="text" name="" id="" v-model="Data.cont" class="flex-1"
          style="border: none; background: transparent" />
        <img loading="lazy" src="@/assets/digitalSchool/icon_send.png" alt="" class="w-78px h-48px"
          @click="sendMessage" />
      </div>
    </div>
    <div class="flex fixed left-50% bottom-4vh translate-x-[-50%] w-full justify-between box-border px58px"
      v-if="Data.tab.active === 1">
      <Button type="small" class="w-360px !h-78px" @click="
        toPage('/digitalSchool/studyGroup/addShare', { groupBizId: route.query?.groupBizId })
        ">
        <template #btnContent>
          <img loading="lazy" :src="iconAdd" alt="" srcset="" class="w-34px h-34px mr-28px" />
          发布分享
        </template>
      </Button>
      <div
        class="w-260px h-78px rounded-39px flex items-center justify-center text-#4899ff text-34px border-#a6cdff border-solid border-1px bg-#FFF"
        @click="toPage('/digitalSchool/studyGroup/myShareList')">
        我的分享
      </div>
    </div>
    <div class="flex fixed left-50% bottom-4vh translate-x-[-50%] w-full justify-between box-border px58px"
      style="transform: translateX(-50%); width: calc(100% - 30px)" @click="removeMember"
      v-if="Data.tab.active === 2 && Data.showTool && Data.memberList && Data.memberList.length">
      <div class="w-261px h-79px border-1px border-solid border-#5AA4FF bg-#fff
        text-#5AA4FF text-34px rounded-39px flex items-center justify-center mx-auto">
        删除成员
      </div>
    </div>
    <!-- 气泡样式 -->
    <div class="fixed left-50% top-50% -translate-50% z-100">
      <waterIntergral v-model:show="showBubble" :score="scoreNum" bigSize="150px" mid-size="62px" small-size="50px"
        scorefontSize="40px"></waterIntergral>
    </div>
  </div>
</template>
<script lang="ts" setup>
const waterIntergral = defineAsyncComponent(() => import('@/components/Bubble/waterIntergral.vue'))
defineOptions({
  name: 'digitalSchoolGroupDetails',
});
import { useRoute } from 'vue-router';
import interestGroupList from '@/components/List/interestGroupList.vue';
import Button from '@/components/Button/button.vue';
import { likeOperate } from '@/api/public';
import { showFailToast, showSuccessToast, showConfirmDialog, showToast } from 'vant';
import router from '@/router';
import {
  groupsStudyGetVoByDto,
  groupsMessageReplyFindVoList,
  groupsMessageReplySaveMessageByDTO,
  groupMembersInfo,
  removeMemberById,
  isLeaderFlag,
  shareFindVoListH5,
  shareInsertReply,
} from '@/api/digitalSchools/group';
import Dialogue from './components/index.vue';
// import topicList from '@/components/List/topicList.vue';
import bq_left from '@/assets/digitalSchool/bq_left.png';
import bq_middle from '@/assets/digitalSchool/bq_middle.png';
import bq_right from '@/assets/digitalSchool/bq_right.png';
// import female from '@/assets/public/female.png';
// import male from '@/assets/public/male.png';
import defaultHead from '@/assets/public/head_default.png';
import noChoose from '@/assets/interest/icon_no.png';
import yesChoose from '@/assets/interest/icon_yes.png';
import iconAdd from '@/assets/digitalSchool/icon_add.png';
import editIcon from '@/assets/public/comment_icon.png';
import shareList from '../components/shareList.vue';
import { useDigitalSchoolStore } from '@/store/modules/digitalSchool';
import refreshList from '@/components/refreshList/index.vue';
const digitalSchoolStore = useDigitalSchoolStore();
const route = useRoute();
const Data = ref({
  tab: {
    active: 0,
    nav: [
      { name: '学习交流', bg: bq_left },
      { name: '心得分享', bg: bq_middle },
      { name: '小组成员', bg: bq_right },
    ],
  },
  groupList: [],
  dialogueList: [],
  cont: '',
  xdfxList: [],
  memberList: [],
  showTool: false,
  isLeaderFlag: false,
  pageNum: 1
});

onMounted(() => {
  getDetail();
  getIsLeaderFlag();
  getGroupsMessageList();
});


function toPage(path, query) {
  router.push({
    path,
    query,
  });
}
// 心得分享详情
function detail(item) {
  digitalSchoolStore.setShareDetail(item);
  toPage('/digitalSchool/studyGroup/commentDetail', {
    groupBizId: item.groupBizId,
    autoId: item.autoId,
  });
}

//心得分享点赞
// 气泡提示框参数设置
const showBubble = ref(false);
const scoreNum = ref(0);
let isReq = false
function like(item) {
  if (isReq) return
  isReq = true;
  likeOperate({
    sourceId: item.experienceSharingId,
  }).then(res => {
    isReq = false;
    if (res.code == 200) {
      if (item.likeStatus) {
        item.likeStatus = false;
        item.likeNum--;
        showFailToast('取消点赞');

      } else {
        item.likeStatus = true;
        item.likeNum++;
        showToast({ type: 'success', message: '点赞成功', duration: 1500 })
      }
      if (res.data?.score) {
        scoreNum.value = res.data?.score;
        setTimeout(() => { showBubble.value = true }, 1500)
      }
    }
  })
    .catch(() => {
      isReq = false;
    })
}
//心得分享评论
function comment(item) {
  if (!item.val) return;
  let params = {
    content: item.val,
    experienceSharingId: item.experienceSharingId,
  };
  shareInsertReply(params).then(res => {
    if (res.code == 200) {
      item.val = '';
      showSuccessToast('提交成功,等待审核');
    }
  });
}

// 心得分享列表
async function getShareList() {
  const { code, data, total } = await shareFindVoListH5({
    groupBizId: route.query?.groupBizId || '',
    pageSize: 10,
    pageNum: Data.value.pageNum,
  })
  if (code == 200) {
    if (Data.value.pageNum === 1) Data.value.xdfxList = data || [];
    else Data.value.xdfxList = Data.value.xdfxList.concat(data);
  }
  //重置刷新状态及 判断是否加载完成
  if (loadMoreRef.value) {
    loadMoreRef.value.onLoadSuc(Data.value.xdfxList, total);
  }
}

//删除小组成员
function removeMember() {
  let person = Data.value.memberList.filter(item => {
    return item.isChoose;
  });
  if (!person.length) {
    showToast('请先选中要删除的成员');
    return;
  }
  showConfirmDialog({
    title: '提示',
    message: `确认删除${person[0].addUserName}?`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    className: 'close',
  }).then(async () => {
    removeMemberById(person[0].autoId).then(res => {
      if (res.code == 200) {
        showSuccessToast('删除成功');
        getGroupMemberList();
      } else {
        showFailToast(res.message);
      }
    });
  });
}

//查询登录用户是否管理员
function getIsLeaderFlag() {
  isLeaderFlag({
    groupBizId: route.query?.groupBizId || '',
  }).then(res => {
    if (res.code == 200) {
      Data.value.isLeaderFlag = res.data;
    }
  });
}

// 小组成员列表
async function getGroupMemberList() {
  const { code, total, data } = await groupMembersInfo({
    groupBizId: route.query?.groupBizId || '',
    pageSize: 10,
    pageNum: Data.value.pageNum,
  })
  if (code === 200) {
    const list = data.map((item: any) => {
      item.isChoose = false
      return item
    })
    if (Data.value.pageNum === 1) Data.value.memberList = list || [];
    else Data.value.memberList = Data.value.memberList.concat(list);
  }
  //重置刷新状态及 判断是否加载完成
  if (loadMoreRef.value) {
    loadMoreRef.value.onLoadSuc(Data.value.memberList, total);
  }

}

//发送聊天记录
function sendMessage() {
  if (Data.value.cont == '') {
    showFailToast('请输入内容后再发送');
    return;
  }
  groupsMessageReplySaveMessageByDTO({
    groupBizId: route.query?.groupBizId || '',
    describes: Data.value.cont,
  }).then(res => {
    if (res.code == 200) {
      Data.value.pageNum = 1
      getGroupsMessageList()
      Data.value.cont = '';
    } else {
      showFailToast(res.message);
    }
  });
}

const lastScrollHeight = ref(0)
const scrollBoxRef: any = ref(null);
const hisTotal = ref(0)
// 聊天记录列表
async function getGroupsMessageList() {
  const { code, data, total } = await groupsMessageReplyFindVoList({
    groupBizId: route.query?.groupBizId || '',
    pageSize: 4,
    pageNum: Data.value.pageNum,
  })
  if (code === 200 && data.length > 0) {

    const list = data.map((item: any) => {
      item.cont = item.describes;
      // type 1左 2右
      item.type = item.mine ? '2' : '1';
      return item;
    }).reverse()

    if (Data.value.pageNum === 1) Data.value.dialogueList = list || [];
    else Data.value.dialogueList = [...list, ...Data.value.dialogueList]
    hisTotal.value = total;
  }
  if (Data.value.pageNum === 1) scrollToEnd()
  else scrollToPre()
  //重置刷新状态及 判断是否加载完成
  if (loadMoreRef.value) {
    loadMoreRef.value.onLoadSuc(Data.value.dialogueList, total);
  }
}
//消息自动滚到底部
const scrollToEnd = () => {
  nextTick(() => {
    scrollBoxRef.value.scrollTop = scrollBoxRef.value.scrollHeight;
    lastScrollHeight.value = scrollBoxRef.value.scrollTop;
    return;
  });
}
//消息自动滚到上次浏览记录地方
const scrollToPre = () => {
  lastScrollHeight.value = scrollBoxRef.value.scrollHeight;
  nextTick(() => {
    scrollBoxRef.value = scrollBoxRef.value.scrollHeight;
    scrollBoxRef.value.scrollTop = scrollBoxRef.value - lastScrollHeight.value
    return;
  });
};

//小组详情
function getDetail() {
  groupsStudyGetVoByDto({
    groupId: route.query?.groupId || '',
  }).then(res => {
    if (res.code == 200) {
      res.data.appCover = res.data.groupCover;
      Data.value.groupList.push(res.data);
    }
  });
}
function onTab(index: number) {
  Data.value.tab.active = index;
  if (loadMoreRef.value) loadMoreRef.value.resetStatus()
  if (Data.value.tab.active === 0) {
    Data.value.pageNum = 1
    getGroupsMessageList()
  } else {
    onRefreshList()
  }

}

function onManage() {
  Data.value.showTool = !Data.value.showTool;
}
function choose(item: any) {
  item.isChoose = !item.isChoose;
}

const loadMoreRef = ref<any>(null)
// 刷新
const onRefreshList = () => {
  switch (Data.value.tab.active) {
    case 0:
      if (hisTotal.value > 0 && Data.value.dialogueList.length < hisTotal.value) {
        Data.value.pageNum++
        getGroupsMessageList()
      } else {
        loadMoreRef.value.onLoadSuc(Data.value.dialogueList, hisTotal.value)
      }
      break;
    case 1:
      Data.value.pageNum = 1
      getShareList()
      break;
    case 2:
      Data.value.pageNum = 1
      getGroupMemberList()
      break;
    default:
      break;
  }
};
// 加载更多
const onLoadMore = () => {
  switch (Data.value.tab.active) {
    case 1:
      Data.value.pageNum++
      getShareList()
      break;
    case 2:
      Data.value.pageNum++
      getGroupMemberList()
      break;
    default:
      break;
  }
};
</script>
<style lang="scss" scoped>
.tab-active {
  position: relative;
  top: -8px;

  &::after {
    content: '';
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 20px;
    width: 36px;
    height: 6px;
    background: linear-gradient(0deg, #a1cbff 0%, #5aa4ff 100%);
    border-radius: 3px;
  }
}
</style>
