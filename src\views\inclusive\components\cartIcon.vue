<template>
    <div>
        <van-floating-bubble v-model:offset="offset" axis="xy" magnetic="x" :gap="5" class="move_cart_btn_class"
            @click="toCart">
            <img src="@/assets/inclusive/shop/cart_icon.png" alt="" class="w-100%" />
        </van-floating-bubble>
    </div>
</template>
<script lang="ts" setup>
const offset = ref({ x: -window.innerWidth, y: window.innerHeight - 280 });
const router = useRouter();
const toCart = () => {
    router.push("/shoppingCart");
};
</script>
<style lang="scss">
.move_cart_btn_class {
    background-color: transparent !important;
    border-radius: 0 !important;
    overflow: visible;
    width: 152px;
    height: 154px;
}
</style>