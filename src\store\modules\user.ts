import { defineStore } from 'pinia';
import { h5Login, getPrefix,getCadreInfo } from '@/api/login';
import { store } from '@/store';
import { LocationQueryValue, RouteRecordRaw } from 'vue-router';
import { getDetails } from '@/api/activity.ts';
// 区县
import { findH5XQBannerList } from '@/api/news';
import {queryUserAvatar} from '@/api/public';
export interface UserInfo {
  userId: string | number;
  username: string;
  realName: string;
  avatar: string;
  token?: string;
  nickname?: string;
  systemType: string;
  userName: string;
  account?: string;
  accountType: string;
  areaName?: string;
  saToken: string;
  areaCode?: number | string;
  phone?: string;
  gender?: string;

}
interface UserState {
  userInfo: Nullable<UserInfo>;
  token?: string;
  sessionTimeout?: boolean;
  lastUpdateTime: number;
  prefix?: string;
  labelData?: array;
  userGroupData?: object;
  activityDetail?: object;
  cityData?: object,
  locationInfo?: object,
  CadreInfo?: object;//干部信息
  currentMessageInfo?: object; //当前消息详情
  hasShowLoading?: boolean; //是否显示首屏动画
  cityList: Array<any>,
  saToken?: string;
  leaderCode?: string; //当前所属工会编码
  officePermission:object//权限列表

}

const params = {
  // user info
  userInfo: null,
  // token
  token: undefined,
  // Whether the login expired
  sessionTimeout: false,
  // Last fetch time
  lastUpdateTime: 0,
  //前缀
  prefix: '',
  labelData: [],
  userGroupData: null as any,
  activityDetail: null as any,
  cityData: {} as any,//选择区县信息
  locationInfo: null as any,
  CadreInfo : {} as any,//干部信息
  currentMessageInfo: null as any,//当前消息详情
  hasShowLoading: false,//是否显示首屏动画
  cityList: [],//缓存区县列表
  saToken:undefined,
  leaderCode:'',//当前所属工会编码
  officePermission:{},//权限列表
}

export const useUserStore = defineStore({
  id: 'app-user',
  persist: {
  //   //活动详情不进行持久化
  //   paths: Object.keys(params).filter(item => item !== 'activityDetail'),
  },

  state: (): UserState => (params),
  getters: {
    getUserInfo(state): Recordable {
      return state.userInfo || {};
    },
    getToken(state): string {
      return state.token || '';
    },
    getSaToken(state): string {
      return state.saToken || '';
    },
    getPrefix(state): string {
      if(state.prefix) return state.prefix
      else return this.setPrefix()
    },
    getLabelData(state): Array {
      return state.labelData || [];
    },
    getUserGroupData(state): Object {
      return state.userGroupData || {};
    },
    getActivityDetail(state): Object {
      return state.activityDetail || {};
    },
    getCityData(state): Object {
      return state.cityData || {};
    },
    getLocationInfo(state): Object {
      return state.locationInfo || {};
    },
    getActivityId(state): Object {
      return state.activityId || '';
    },
    // 获取干部信息
    getCadreInfo(state): Object {
      return state.CadreInfo || {};
    },
    // 消息详情
    getCurrentMessageInfo(state): Object {
      return state.currentMessageInfo || {};
    },
    // 区县
    getCityList(state): Array {
      return state.cityList || [];
    },

  },
  actions: {
    setToken(info: string | undefined) {
      this.token = info ? info : '';
    },
    setSaToken(info: string | undefined) {
      this.saToken = info ? info : '';
    },
    setUserInfo(info: UserInfo | null) {
      this.userInfo = info;
      this.lastUpdateTime = new Date().getTime();
    },
    setAvatar(avatar: string) {
      if(this.userInfo) this.userInfo.avatar = avatar;
    },
    setLabelData(info: string | undefined) {
      this.labelData = info ? info : [];
    },
    setUserGroupData(info) {
      this.userGroupData = info;
    },
    setCityData(info) {
      this.cityData = info;
    },
    setCityList(cityList) {
      this.cityList = cityList;
    },
    setLocationInfo(info) {
      this.locationInfo = info;
    },
    async setPrefix() {
      const { data } = await getPrefix();
      this.prefix = data.visitPrefix;
      return data.visitPrefix;
    },
    resetState() {
      this.userInfo = null;
      this.token = '';
      this.userGroupData = {},
      this.sessionTimeout = false;
      this.cityData = {};
      this.CadreInfo = {};
      this.activityDetail ={};
      this.labelData ={};
      this.currentMessageInfo = {};
      this.hasShowLoading = false;
      this.saToken = ''
      this.leaderCode = ''
      this.officePermission = {}

      // clearAll(true);
    },
    async setActivityDetail(activityId?: string) {
      if (activityId) {
        const { data } = await getDetails({ activityId });
        data.appDetailsCover=data.appDetailsCover?this.prefix+data.appDetailsCover:''
        this.activityDetail = data;
      } else {
        this.activityDetail = {};
      }

    },
    // 消息详情
    setCurrentMessageInfo(values: any) {
      this.currentMessageInfo = values
    },
    // end
    // 加载页loading·
    setLoadingPageShow(values:boolean){
      this.hasShowLoading = values;
    },
    //   /**
    //    * @description: login
    //    */
    async login(params: { token?: LocationQueryValue }): Promise<null> {
      try {
        const { token } = params;
        let t = token as string;
        // save token
        this.setToken(t);
        return this.afterLoginAction();
      } catch (error) {
        return Promise.reject(error);
      }
    },
    async afterLoginAction(): Promise<null> {
      if (!this.getToken) {
        this.getCityBannerList();
        return null
      }else{
        // get user info
        const userInfo = await this.getUserInfoAction();
        const sessionTimeout = this.sessionTimeout;
        this.getCityBannerList();
        return userInfo;
      }

    },
    async getUserInfoAction(): Promise<UserInfo | null> {
      if (!this.getToken) return null;
  
      const {data} = await h5Login({token:this.getToken});
      this.setSaToken(data?.saToken);
      this.setToken(data?.unionToken)
      this.setUserInfo(data);
      if(data?.cadreInfo?.id){
        setTimeout(() => {
          this.getCadreInfoAction({
            id:data?.cadreInfo?.id
          })
        },1000)
      }
      if(!this.userInfo?.avatar){
        setTimeout(() => {
          queryUserAvatar(this.userInfo?.userId).then(res => {
            if (res.code == 200 && res.data) {
              this.setAvatar(res.data as string)
            }
          })          
        },2000)
      }
      return data;        
      
    },

    // 获取干部信息
    async getCadreInfoAction(params: any) {
      const {code,data} = await getCadreInfo(params);
      if(code === 200) this.CadreInfo = data;
      else this.CadreInfo = {};
    },
    // end

    // 获取区县列表
    async getCityBannerList() {
      findH5XQBannerList({
        userId: this.userInfo?.userId
      }).then(res => {
        if (res.code == 200) {
          res.data.map((el, index) => {
            if (el.subscribeFlag == 'Y') {
              this.setCityData(el)
            }
          })
          this.setCityList(res.data)
        }
      })
    },
    // end
    setLeaderCode(code: string) {
      this.leaderCode = code;
    },
    setOfficePermission(permission) {
      this.officePermission = permission;
    }
  },
});

// Need to be used outside the setup
export function useUserStoreWithOut() {
  return useUserStore(store);
}
