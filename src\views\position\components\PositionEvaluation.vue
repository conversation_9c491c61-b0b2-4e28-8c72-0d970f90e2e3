<template>
  <div>
    <div class="flex justify-between">
      <div class="flex items-center">
        <span class="h-[31px] line-title w-[5px] inline-block mr-1 rounded-lg" />
        <span>
          全部评价
          <span class="text-[#888888]">{{ listNum }}</span>
        </span>
      </div>
      <div
        class="active:shadow-[0_0_5px_0_#5ba5ff] active:rounded-[25px] text-28px w-[210px] h-[50px] px-2 text-[#5AA4FF] border rounded-[25px] border-[#5AA4FF] border-solid flex justify-center items-center"
        @click="handleEvaluation">
        <img loading="lazy" :src="iconPj" class="w-[25px] h-[25px] mr-2 " />
        我要评价
      </div>
    </div>
    <div class="h-[calc(100%-50px)] pt-2">
      <refreshList @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" :immeCheck="true" ref="loadMoreRef">
        <van-cell v-for="item in list">
          <van-row>
            <van-col :span="3" class="flex justify-center">
              <img loading="lazy" :src="defaultAvatar" class="w-[51px] h-[51px]" />
            </van-col>
            <van-col :span="21">
              <div class="flex items-center justify-between text-[24px]">
                <span class="text-[#333333] text-[30px]">{{ item?.evaluationName }}</span>
                <span class="text-[#808080] my-0.5 text-[22px]">{{ item?.time }}</span>
              </div>
              <div class="flex items-center">
                <span class="text-[#FF6342] pr-2 text-[24px]">
                  {{ ((item?.positionScore as number) || 0).toFixed(1) }}
                </span>
                <van-rate v-model="item.positionScore" size="13px" color="#FF6342" readonly />
              </div>
              <p class="text-left text-[#333333] my-2">{{ item?.positionContent }}</p>
              <div class="flex items-center" v-if="item?.images">
                <div class="w-[180px] h-[180px] mr-[10px]" v-for="img in item?.images" @click="handleViewImage(item)">
                  <img loading="lazy" class="w-full h-full rounded-lg" :src="img" />
                </div>
              </div>
            </van-col>
          </van-row>
        </van-cell>
      </refreshList>
    </div>
  </div>
</template>

<script lang="ts" setup>
import iconPj from '@/assets/position/icon-pj.png';
import defaultAvatar from '@/assets/position/default-avatar.png';
import { showImagePreview } from 'vant';
import refreshList from '@/components/refreshList/index.vue';
import { concat, map, split } from 'lodash-es';
import { venuePositionCommentList } from '@/api/position';
import { useUserStore } from '@/store/modules/user';
import {isLoginDialog} from "@/utils/utils"
const userStore = useUserStore();

const router = useRouter();

const pageNum = ref<number>(1);

const loadMoreRef = ref();

const detailRecord = inject<Recordable>('detailRecord', {});

const list = ref<Recordable[]>([]);

const listNum = computed(() =>
  unref(list)?.length > 999 ? '999+' : unref(list)?.length > 0 ? unref(list)?.length : ''
);

function handleViewImage(item: Recordable) {
  if (item?.images) showImagePreview(item?.images);
}

// 加载更多
const onLoadMore = () => {
  pageNum.value++;
  onLoad();
};

const onRefreshList = () => {
  pageNum.value = 1;
  onLoad();
};

const onLoad = () => {
  if (!unref(detailRecord)?.positionInfoId) return;

  venuePositionCommentList?.({
    positionInfoId: unref(detailRecord)?.positionInfoId,
    pageSize: 10,
    pageNum: unref(pageNum),
  }).then(({ data, total = 0 }: Recordable) => {
    if (unref(pageNum) === 1) list.value = [];
    list.value = concat(
      map(unref(data), v => ({
        ...v,
        images: v.positionPicture ? map(split(v.positionPicture || [], ','), p => userStore.getPrefix + p) : null,
      }))
    );
    unref(loadMoreRef)?.onLoadSuc(unref(list)?.length, total);
  });
};

// 评价
function handleEvaluation() {
  isLoginDialog((isLogin:boolean) => {
    if(!isLogin){
      return;
    }
    router.push({
      path: '/evaluationOption',
      query: {
        positionInfoId: unref(detailRecord)?.positionInfoId,
      },
    });    
  })
}
</script>
<style lang="less" scoped>
.line-title {
  background: linear-gradient(to top, #5BA5FF, #5CB6FF);
}
</style>
