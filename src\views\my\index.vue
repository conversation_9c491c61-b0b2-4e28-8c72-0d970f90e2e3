<template>
  <div class="my-page box-border w-full flex flex-col">
    <div class="my-info px-32px relative w-full box-border">
      <div class="flex pt-50px">
        <div class="my-avatar relative h-fit" @click="previewAvatar">
          <img loading="lazy" :src="avatar" alt="" class="w-116px h-116px object-cover rounded-50%" />
        </div>

        <div class="pl-38px w-100%">
          <div class="flex justify-between">
            <div class="text-34px text-#333">{{ useStore.getUserInfo?.nickname }}</div>
            <div class="sign text-#fff text-28px text-center pl-20px" @click="toPage({ path: '/integralMall' })">签到
            </div>
          </div>
          <div class="text-24px text-#666 pt-15px" v-if="useStore.getUserInfo?.companyId"><img loading="lazy"
              src="@/assets/my/icon_cer.png" alt="" class="w-25px h-22px mr-10px max-h-40px" />{{
                useStore.getUserInfo?.companyName }}</div>
          <div class="flex pt-15px flex-wrap">
            <img loading="lazy" src="@/assets/my/cadre.png" alt="" class="max-w-100% mb-5px max-h-50px"
              v-if="useStore.getUserInfo?.cadreFlag" />
            <img loading="lazy" :src="utils.judgeStaticUrl(item.labelImg)" class="max-w-100% mb-5px max-h-50px mr-10px"
              alt="" v-for="(item, index) of Data.useLabel" :key="index" />
          </div>

        </div>
      </div>
      <div class="card h-200px mt-30px">
        <div class="flex justify-between items-center pt-5px leading-none">
          <div class="text-28px text-#fff font-medium w-60% pl-24px pt-3px flex items-center leading-none"><img
              loading="lazy" src="@/assets/my/logo.png" alt="" class="w-34px h-34px mr-17px" />南充市工会数字普惠卡</div>
          <!-- <div class="text-26px text-#5AA4FF pr-40px">省总工分：{{ Data.integralTotal || 0 }}</div> -->
        </div>
        <div class="flex justify-around pt-30px">
          <div class="flex justify-center items-center"
            @click="toPage({ path: '/integralMall/integralDetails', query: { type: 0 } })">
            <div class="flex flex-col justify-center text-#5AA4FF text-26px items-center">
              <div class="text-45px">{{ totalIntegral }}</div>
              <div class="flex items-center mt-10px">累计积分
              </div>
            </div>
            <img loading="lazy" src="@/assets/my/icon_arrow.png" alt="" class="w-20px h-20px ml-23px" />
          </div>
          <div class="flex justify-center items-center"
            @click="toPage({ path: '/integralMall/integralDetails', query: { type: 0 } })">
            <div class="flex flex-col justify-center text-#5AA4FF text-26px items-center">
              <div class="text-45px">{{ userIntegral }}</div>
              <div class="flex items-center mt-10px">可用积分
              </div>
            </div>
            <img loading="lazy" src="@/assets/my/icon_arrow.png" alt="" class="w-20px h-20px ml-23px" />
          </div>
        </div>
      </div>
    </div>
    <div class="p-30px">
      <div class="bg-#fff rounded-20px p-26px box-border flex justify-around text-28px text-#666">
        <div v-for="(item, index) of Data.nav" @click="item.onClick" :key="index"
          class="flex flex-col justify-center items-center relative">
          <img loading="lazy" :src="item.src" alt="" class="w-80px h-80px my-10px" />
          {{ item.name }}
          <!-- 消息未读 -->
          <div class="dot w-1vh h-1vh bg-red rounded-50% absolute top-5px right-5px"
            v-show="item.name === '我的消息' && unReadNum"></div>
        </div>
      </div>
      <div class="relative z-1" v-if="Data.showOffice">
        <img loading="lazy" src="@/assets/my/banner_oa.png" alt="" class="h-100px w-full my-20px relative z-1"
          @click="toPage({ path: '/office' })" />
        <img src="@/assets/my/icon-jiantou.png" class="arrow-right absolute 
        left-45% top-50% z-2 w-50px animation-right" />
      </div>


      <div class="bg-#fff rounded-20px p-30px w-full box-border mt-20px">
        <div class="text-#333 text-32px font-medium apply">我的应用 <span class="line"></span></div>
        <div class="flex flex-wrap content mt-30px">
          <div class="mb-20px w-25%" v-for="(item, index) of Data.applyList" :key="index" @click="toPage(item)">
            <div class="flex flex-col justify-center items-center text-28px text-#666">
              <img loading="lazy" :src="item.src" alt="" class="w-50px h-50px my-10px" />
              {{ item.name }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 技术支持 -->
    <div class="w-full technical-support mx-auto text-center flex-1 flex items-end justify-center pb-190px">
      <img loading="lazy" src="@/assets/my/ylh_title.png" class="w-60%" @click="toCompany()" />
    </div>

    <div class="overlay-image">
      <van-popup v-model:show="showPreview" position="center" closeable close-icon="clear" @click="showPreview = false">
        <div class="flex flex-col items-center justify-center  relative" @click.stop="() => { }">
          <img loading="lazy" :src="avatar" class="h-full min-h-30vh max-h-60vh" />
          <div class="handle mt-30px mx-auto text-center">
            <van-uploader max-count="1" accept="image/*" :after-read="afterRead">
              <div class="bg-#5AA4FF text-#fff text-32px h-60px rounded-30px 
              px-20px flex items-center justify-center leading-none">
                <van-icon name="plus"></van-icon>
                <span class="text-30px">上传头像</span>
              </div>
            </van-uploader>
          </div>
        </div>
      </van-popup>
    </div>
    <imageCutter :visible="showCutter" :fileName="fileName" :image="cutterUrl" @close="getResult">
    </imageCutter>
  </div>
</template>
<script setup lang="ts">
import imageCutter from './components/image-cutter.vue';
// import order from '@/assets/my/icon_order.png';
import collect from '@/assets/my/icon_collect.png';
import news from '@/assets/my/icon_news.png';
import prize from '@/assets/my/icon_prize.png';
import ticket from '@/assets/my/icon_ticket.png';
import scan from '@/assets/my/icon_san.png';
// import apply from '@/assets/my/icon_apply.png';
import mes from '@/assets/my/icon_mes.png';
import position from '@/assets/my/icon_position.png';
import year from '@/assets/my/icon_year.png';
// import group from '@/assets/my/icon_group.png';
import address from '@/assets/my/icon_address.png';
import tg from '@/assets/my/icon_tg.png';
import classes from '@/assets/my/icon_class.png';
import xl from '@/assets/my/icon_xl.png';
import store from '@/assets/my/icon_store.png';
import share from '@/assets/my/icon_share.png';
import star from '@/assets/my/icon_star.png';
import leader from '@/assets/my/icon_leader.png'
import male from '@/assets/public/male.png';
import female from '@/assets/public/female.png';
import defaultImg from '@/assets/public/head_default.png';
import utils from '@/utils/utils';
import { useUserStore } from '@/store/modules/user';
const useStore = useUserStore();
const router = useRouter();
import { getIntegralH5, queryUserLabel, getWorkPermission, saveUserAvatar } from '@/api/public';
import { authLeaderView } from '@/api/leaderboard/ali'
import { personIntegral } from '@/api/integralTree';
import { whetherExpert } from '@/api/soulStation';
import { unReadMessageNum } from '@/api/message';
import { whetherAdministrators } from '@/api/position';
import { uploadFile } from '@/api/public';
import { showFailToast, showLoadingToast, showSuccessToast, closeToast, showConfirmDialog } from 'vant';
import { applyRecord } from '@/api/merchants';
const Data = ref({
  nav: [
    // { name: '我的订单', src: order, onClick: () => toPage({ path: '/my/orders' }) },
    { name: '我的收藏', src: collect, onClick: () => toPage({ path: '/my/collection' }) },
    { name: '我的消息', src: news, onClick: () => toPage({ path: '/my/message' }) },
    { name: '我的奖品', src: prize, onClick: () => toPrize() },
    { name: '我的票券', src: ticket, onClick: () => toTicket() },
  ],
  applyList: [
    { name: '扫一扫', src: scan, id: 'scan' },
    { name: '我的投稿', src: tg, path: '/my/manuscript' },
    { name: '我的活动', src: star, path: '/my/activity' },
    { name: '我的留言', src: mes, path: '/my/workersVoice' },
    { name: '一岁一礼', src: year, path: '/my/yearGift' },
    { name: '学习小组', src: classes, path: '/my/digitalSchool' },
    { name: '我的地址', src: address, path: '/address', query: { noBack: true } },
    { name: '阵地预约', src: share, path: '/reserveRecord' },
  ],
  integralTotal: 0,
  userIntegral: 0,
  useLabel: [],
  showOffice: false,
  // permission: null,
});
const totalIntegral = ref(0);
const toPrize = () => {
  useUserStore().setActivityDetail();
  sessionStorage.removeItem('activityId');
  router.push({
    path: '/activityHome/lotteryRecord',
    query: {
      prizeTypes: '2,3,5',
    },
  });
};

const toTicket = () => {
  useUserStore().setActivityDetail();
  sessionStorage.removeItem('activityId');
  router.push({
    path: '/activityHome/lotteryRecord',
    query: {
      prizeTypes: '7',
    },
  });
};

const toPage = (item: any) => {
  if (item.id == 'scan') {
    utils.scan('USER', [{ Name: 'scan' }]);
    return
  }
  if (item.path) {
    if (item.path === '/leaderboard') {
      /*
      areaType:用户所属区域 
      SCS 省级干部
      SJ 市州级干部
      QX 区县级干部
      QTJ 本市其他级干部
      QTS 其他市州干部

      // 跳转权限
      SCS|QTS 进行暂无权限弹框
      SJ|QX|QTJ 传入返回belongUnionCode值正常进，赋值下个接口areaCode
      */
      if (['SJ', 'QX', 'QTJ'].includes(userCadreInfo.value?.areaType.toUpperCase())) {
        useStore.setLeaderCode(userCadreInfo.value?.belongUnionCode)
        router.push({
          name: 'leaderBoard'
        })
      } else {
        showConfirmDialog({
          title: '抱歉~',
          message: '您没有足够的权限执行该操作。',
          confirmButtonText: '知道了',
        })
      }
      return
    }
    router.push({ path: item.path, query: item.query });
  }
};
//跳转公司地址
function toCompany() {
  if (utils.isApp()) {
    utils.citySercive('http://www.yinlihua.cn/', '银利华科技', 'open', 'user')
  } else {
    location.href = 'http://www.yinlihua.cn/'
  }

}
//获取省总工分
function getIntegral() {
  getIntegralH5({ userId: useStore.getUserInfo?.userId }).then(res => {
    if (res.code == 200) {
      Data.value.integralTotal = res.data.credit;
    }
  });
}
// 个人积分
const userIntegral = ref(0);
const getPersonIntegral = async () => {
  const { code, data } = await personIntegral(useStore.getUserInfo?.userId);
  if (code === 200) {
    userIntegral.value = data?.userIntegral;
    totalIntegral.value = data?.summaryIntegral;
  }
};
//是否专家
function getExpert() {
  whetherExpert({}).then(res => {
    if (res.data?.whetherExpert) {
      Data.value.applyList.push({
        name: '心灵专区',
        src: xl,
        path: '/DoctorAnswer',
        query: {
          psychologicalExpertId: res.data?.psychologicalExpertId,
        }
      });
    }
  });
}
//获取用户标签
function getUserLabel() {
  queryUserLabel({ phone: useStore.getUserInfo?.phone, systemQueryType: 'h5' }).then(res => {
    if (res.code == 200) {
      Data.value.useLabel = res.data;
    }
  });
}
//获取干部权限
function getPermission() {
  getWorkPermission({ cadreId: useStore.getCadreInfo?.cadreId }).then(res => {
    if (res.data) {
      Data.value.showOffice = true;
      // Data.value.permission = res.data;
      useStore.setOfficePermission(res.data);
    }
  });
}
// 个人消息未读数量
let unReadNum = ref(0);
function getUnReadMessageNum() {
  unReadMessageNum({
    userAccount: useStore.getUserInfo?.phone,
  }).then((res: any) => {
    if (res.code === 200) unReadNum.value = res.data;
  });
}
//获取阵地管理员权限
function getWhetherAdministrators() {
  whetherAdministrators({ phone: useStore.getUserInfo?.phone }).then(res => {
    if (res.data) {
      Data.value.applyList.push({ name: '阵地审核', src: position, path: '/my/positionAdmin' },)
    }
  })
}

// 获取动态南充权限
const userCadreInfo = ref<any>({})
function getLeaderBoardPermission() {
  authLeaderView().then(res => {
    userCadreInfo.value = res.data
    const { isCadre } = res.data
    if (isCadre) {
      Data.value.applyList.push({
        name: '动态南充',
        src: leader,
        path: '/leaderboard',
      })
    }
  })
}
// 获取是否商家入驻
function getWhetherMerchant() {
  applyRecord({
    pageNum: 1,
    pageSize: 0
  }).then(res => {
    if (!res.data || !res.data.auditState) {
      Data.value.applyList.push({ name: '商家入驻', src: store, path: '/merchants/guide' })
    }
    else if (res.data?.auditState === 'pass') {
      Data.value.applyList.push({ name: '商家入口', src: store, path: '/merchants/info' },)
    }
    else {
      Data.value.applyList.push({ name: '商家入驻', src: store, path: '/merchants/status' })
    }
  })
}

// ====个人头像====
// 上传
const afterRead = (file: any) => {
  uploadImage(file.file)
  return

  // 裁剪功能
  const reader = new FileReader()
  reader.readAsDataURL(file.file)
  reader.onload = () => {
    cutterUrl.value = String(reader.result)
    fileName.value = file.file.name
    showCutter.value = true
  }
  showPreview.value = false
}
const avatar = computed(() => {
  if (useStore.getUserInfo?.avatar) {
    return utils.judgeStaticUrl(useStore.getUserInfo?.avatar)
  }
  else {
    return useStore.getUserInfo?.gender == '男'
      ? male
      : useStore.getUserInfo?.gender == '女'
        ? female
        : defaultImg
  }
})
// 预览头像
const showPreview = ref(false)
const previewAvatar = () => {
  showPreview.value = true
}

// 裁剪
const showCutter = ref(false)
const cutterUrl = ref<any>(null)
const fileName = ref<any>(null)
const getResult = async (val: any) => {
  if (val) {
    const { file } = val
    await uploadImage(file)
  }
  showCutter.value = false
}
const uploadImage = async (file: any) => {
  showLoadingToast({
    message: '上传中...',
    forbidClick: true
  })
  if (!file) return
  let filedata = {
    operateType: "9", //操作模块类型
    file,
  }
  const res = await uploadFile(filedata)
  closeToast()
  if (res.code == 200 && res.data) {
    saveUserAvatar({
      userId: useStore.getUserInfo?.userId,
      avatar: res.data[0],
    }).then(res1 => {
      if (res1.code == 200) {
        useStore.setAvatar(
          res.data[0] as string
        )
        showSuccessToast("上传成功")
        showPreview.value = false
      }
    })
  } else {
    showFailToast(res.message);
  }
}
// ====end====


onMounted(() => {
  getIntegral();
  getPersonIntegral();
  getUserLabel();
  getPermission();
  getWhetherAdministrators()
  getWhetherMerchant()
  getLeaderBoardPermission()
  setTimeout(() => {
    getExpert();
    getUnReadMessageNum();
  }, 500)

});
</script>
<style lang="scss" scoped>
.my-page {
  height: 100vh;
  background: linear-gradient(0deg, #f7f8f9 33%, #ffffff 100%);
  box-shadow: 16px 0px 98px 0px rgba(93, 209, 174, 0.2);
  width: 100%;

  .my-info {
    background: url('@/assets/my/bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    .sign {
      background: url('@/assets/my/icon_sign.png') no-repeat;
      background-size: 100% 100%;
      width: 130px;
      height: 50px;
      line-height: 50px;
    }

    .card {
      background: url('@/assets/my/card.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  .apply {
    position: relative;
    z-index: 1;
  }

  .line {
    position: absolute;
    width: 130px;
    height: 8px;
    background: linear-gradient(86deg, #a1cbff 0%, #dfedff 100%);
    border-radius: 3px;
    left: 0;
    bottom: 0;
    z-index: -1;
  }

  --van-uploader-size:86px;

  :deep(.van-uploader__preview) {
    margin: 0;
  }

  :deep(.van-uploader__preview-delete) {
    display: none;
  }

  :deep(.van-uploader__preview-image) {
    border-radius: 50%;
  }

  :deep(.van-uploader__preview-cover) {
    position: relative;
    z-index: 1;
  }

  .preview-img-icon {
    position: absolute;
    right: 0;
    bottom: 0;
    z-index: 2;
  }
}

.technical-support {
  opacity: 0.8;
}

.overlay-image {
  --van-overlay-z-index: 1000;

  :deep(.van-popup) {
    width: 100vw;
    background: transparent;
    height: 100vh;
    --van-popup-close-icon-size: 45px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
}

.animation-right {
  animation: toright 1s linear infinite alternate
}

@keyframes toright {
  0% {
    transform: translateX(0) translateY(-50%);
  }

  100% {
    transform: translateX(10px)translateY(-50%);
  }
}
</style>
