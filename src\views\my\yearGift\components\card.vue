<template>
    <div class="gift_card">
        <div class="template" :class="`template_${template_code}`">
            <div class="content relative">
                <div class="content-view relative">
                    <img loading="lazy" :src="judgeStaticUrl(props.content?.backgroundPicture) || defaultCard"
                        class="" />
                    <div class="text-content absolute w-full h-full top-0 box-border">
                        <div class="header">
                            <div class="title leading-none" v-if="template_code === '1'">亲爱的：</div>
                            <div class="account">
                                <div class="imgs">
                                    <img loading="lazy" :src="avatar" class="avator" />
                                    <img loading="lazy" src="@/assets/yearGift/hat.png" class="hat" />
                                </div>
                                <div class="name">
                                    <span>{{ useStore.getUserInfo?.nickname }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="text" v-if="template_code === '1'">
                            今天是你的生日！祝你生日快乐~ <br>
                            愿一年更比一年好
                        </div>
                        <div class="text" v-else-if="template_code === '3'">
                            今天是你的生日！祝你生日快乐~
                        </div>
                    </div>
                </div>
            </div>
            <img loading="lazy" src="@/assets/yearGift/card_bottom.png" class="w-full"
                v-if="template_code === '1' || template_code === '2'">
        </div>
    </div>
</template>

<script setup lang="ts">
import { type detailInfo } from "@/api/yearGift"
import defaultAvatar from '@/assets/public/head_default.png';
import female from '@/assets/public/female.png';
import male from '@/assets/public/male.png';
import { useUserStore } from '@/store/modules/user';
import { judgeStaticUrl } from "@/utils/utils";
import defaultCard from "@/assets/yearGift/template_1_card.png";
const useStore = useUserStore();
defineOptions({
    name: "YearGiftCard",
});
const props = defineProps({
    content: {
        type: Object,
        default: () => ({
            templateCode: "1",
            backgroundPicture: '',
            buttonContent: "立即领取",
            buttonColor: "#FF6A5C,#F47983"
        } as detailInfo),
    }
})
const avatar = computed(() => {
    if (useStore.getUserInfo?.avatar) return judgeStaticUrl(useStore.getUserInfo?.avatar)
    if (useStore.getUserInfo?.gender === '男') return male
    if (useStore.getUserInfo?.gender === '女') return female
    return defaultAvatar
})
// 模板号
const template_code = computed(() => {
    return props.content?.templateCode || '1'
})

</script>

<style scoped lang="scss">
.gift_card {
    overflow: hidden;
    box-sizing: border-box;
    display: flex;
    height: 100%;

    // 背景图公共样式
    .template {
        box-sizing: border-box;
        overflow: hidden;
        flex: 1;
        box-sizing: border-box;
    }

    // flex column布局公共样式
    @mixin common_flex_col_center {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    // 头部flext调整
    @mixin account_flex {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-end;
    }

    // 模板1
    .template_1 {
        background: linear-gradient(0deg, #fde8e2 0%, #ff6354 100%);
        position: relative;

        .content {
            height: calc(100% - 202px);
            display: flex;
            align-items: center;
            justify-content: center;

            .content-view {
                height: 76%;

                >img {
                    height: 100%;
                }
            }

            .text-content {
                padding: 8% 10%;

                .header {
                    display: flex;
                    align-items: center;
                    height: 22%;

                    .title {
                        font-weight: 500;
                        font-size: 2.3vh;
                        color: #333;
                    }

                    .account {
                        text-align: center;
                        margin-left: 9%;
                        width: 30%;
                        height: 100%;

                        .imgs {
                            position: relative;

                            .avator {
                                position: relative;
                                width: 7vh;
                                height: 7vh;
                                object-fit: cover;
                                border-radius: 50%;
                            }

                            .hat {
                                position: absolute;
                                width: 35%;
                                right: -5%;
                                top: -5%;
                            }
                        }

                        .name {
                            position: relative;
                            color: #333;
                            font-size: 2vh;

                            span {
                                position: relative;
                                z-index: 1;
                            }
                        }

                        .name::after {
                            content: " ";
                            display: block;
                            width: 100%;
                            height: 45%;
                            background: linear-gradient(89deg, #F9D0A4 0%, #FFEBAE 100%);
                            border-radius: 1vh;
                            position: absolute;
                            left: 50%;
                            transform: translateX(-50%);
                            bottom: 1%;
                            z-index: 0;
                        }
                    }
                }
            }

            .text {
                color: #333;
                font-size: 1.8vh;
                text-align: center;
                line-height: 2em;
            }
        }
    }

    // 模板2
    .template_2 {
        background: linear-gradient(0deg, #ffe6bb 0%, #ff6029 100%);
        position: relative;

        .content {
            height: calc(100% - 202px);
            display: flex;
            align-items: center;
            justify-content: center;

            .content-view {
                height: 77%;

                >img {
                    height: 100%;
                }
            }

            .text-content {
                padding: 8% 10%;

                .header {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 22%;

                    .account {
                        text-align: center;
                        width: 30%;
                        height: 100%;

                        .imgs {
                            position: relative;

                            .avator {
                                position: relative;
                                width: 7vh;
                                height: 7vh;
                                object-fit: cover;
                                border-radius: 50%;
                            }

                            .hat {
                                position: absolute;
                                width: 35%;
                                right: -5%;
                                top: -5%;
                            }
                        }

                        .name {
                            position: relative;
                            color: #fff;
                            font-size: 2vh;

                            span {
                                position: relative;
                                z-index: 1;
                            }
                        }
                    }
                }
            }
        }
    }

    // 模板3
    .template_3 {
        background: linear-gradient(0deg, #00b5ff 50%, #4cb7fd 100%);
        position: relative;

        .content {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;

            .content-view {
                height: 76%;

                >img {
                    height: 100%;
                }
            }

            .text-content {
                padding: 8% 10%;

                .header {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 22%;

                    .account {
                        text-align: center;
                        width: 30%;
                        height: 100%;

                        .imgs {
                            position: relative;

                            .avator {
                                position: relative;
                                width: 7vh;
                                height: 7vh;
                                object-fit: cover;
                                border-radius: 50%;
                            }

                            .hat {
                                position: absolute;
                                width: 35%;
                                right: -5%;
                                top: -5%;
                            }
                        }

                        .name {
                            position: relative;
                            color: #8dfffe;
                            font-size: 2vh;

                            span {
                                position: relative;
                                z-index: 1;
                            }
                        }
                    }
                }

                .text {
                    color: #fff;
                    font-size: 1.8vh;
                    text-align: center;
                    line-height: 2em;
                }
            }
        }
    }

    // 模板4
    .template_4 {
        background: linear-gradient(0deg, #7091ff 0%, #7091ff 100%);
        position: relative;

        .content {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;

            .content-view {
                height: 76%;

                >img {
                    height: 100%;
                }
            }

            .text-content {
                padding: 8% 10%;

                .header {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 22%;

                    .account {
                        text-align: center;
                        width: 30%;
                        height: 100%;

                        .imgs {
                            position: relative;

                            .avator {
                                position: relative;
                                width: 7vh;
                                height: 7vh;
                                object-fit: cover;
                                border-radius: 50%;
                            }

                            .hat {
                                position: absolute;
                                width: 35%;
                                right: -5%;
                                top: -5%;
                            }
                        }

                        .name {
                            position: relative;
                            color: #fff;
                            font-size: 1.8vh;

                            span {
                                position: relative;
                                z-index: 1;
                            }
                        }
                    }
                }
            }
        }
    }

}
</style>