<template>
    <div class="vote">
        <div class="headers h-110px bg-#fff  sticky top-0 z-99" v-if="tabs.length > 1">
            <van-tabs v-model:active="tabActive" title-active-color="#333" @change="tabChange" v-if="tabs.length"
                title-inactive-color="#333">
                <van-tab :title="item.label" v-for="item in tabs" :key="item.value" title-class="tab-title">
                </van-tab>
            </van-tabs>
        </div>
        <div>
            <div class="px-20px py-10px  bg-#fff" v-if="tabActive == 0">
                <div class="search flex justify-between items-center mb-28px">
                    <van-search v-model="searchText" show-action placeholder="请搜索作品名称、作者名称或编号" :clearable="false"
                        :left-icon="getIcon()" input-align="left">
                        <template #action>
                            <div @click="onSearch" class="search-btn">搜索</div>
                        </template>
                    </van-search>
                    <div class="filter-box" @click="overlayShow = !overlayShow">
                        <img loading="lazy" src="@/assets/friendShip/filter_icon.png" alt="">筛选
                    </div>
                </div>
            </div>
            <div class="px-30px py-20px">
                <div class="total-box rounded-20px p-20px mb-25px flex justify-around" v-if="tabActive == 0">
                    <div class="flex flex-col justify-center items-center">
                        <div class="text-#363636 text-26px pb-10px">总票数</div>
                        <div class="text-#5AA4FF text-22px"><span class="text-32px">{{ staticObj.voteCount }}</span>
                        </div>
                    </div>
                    <div class="flex flex-col justify-center items-center ">
                        <div class="text-#363636 text-26px pb-10px">作品数</div>
                        <div class="text-#5AA4FF text-22px"><span class="text-32px">{{ staticObj.opusCount }}</span>
                        </div>

                    </div>
                    <div class="flex flex-col justify-center items-center ">
                        <div class="text-#363636 text-26px pb-10px">访问量</div>
                        <div class="text-#5AA4FF text-22px"><span class="text-32px">{{ staticObj.readCount }}</span>
                        </div>

                    </div>
                </div>
                <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
                    <div class="flex justify-between flex-wrap" v-if="tabActive == 0">
                        <div class="w-48% rounded-20px bg-#fff box-sh relative mb-20px" v-for="(item, index) of list"
                            :key="index" @click="toPage(item)">
                            <div class="h-164px w-full">
                                <img loading="lazy" :src="judgeStaticUrl(item.opusCover)" alt=""
                                    class="w-full h-full round object-cover">
                            </div>
                            <div class="p-20px">
                                <div class="truncate text-#333333 text-30px py-10px">{{ item.opusName || '--' }}</div>
                                <div class="text-26px text-#999999 truncate py-10px">作者：{{ item.userName || '--' }}
                                </div>
                                <div class="text-26px text-#999999 truncate">编号：{{ item.opusNo || '--' }}</div>
                            </div>
                            <div class="w-168px py-10px rounded-6px border-solid text-#5AA4FF text-28px text-center m-auto mb-20px border-1px border-#5AA4FF"
                                @click.stop="voteTicket(item)">立即投票</div>
                            <div
                                class="absolute top-0 left-0 bg-round text-center text-26px text-#fff flex items-center justify-center">
                                {{ item.votesNum }}票</div>
                        </div>
                    </div>
                    <div v-else>
                        <div class="flex  bg-#FFFFFF rounded-20px mb-22px relative" v-for="(item, index) of list"
                            :key="index" @click="toPage(item)">
                            <div class="w-45% rounded-20px h-170px">
                                <img loading="lazy" :src="judgeStaticUrl(item.opusCover)" alt=""
                                    class="rounded-20px w-full h-full object-cover">
                            </div>
                            <div class="p-26px w-55% box-border">
                                <div class="text-30px text-#333">{{ item.opusName }}</div>
                                <div class="text-25px text-#999 py-10px">发布者：{{ item.userName }}</div>
                                <div class="text-25px text-#999">作品票数：<span class="text-#F59C00">{{
                                    item.votesNum }}票</span></div>
                            </div>
                            <div class="absolute top-0 left-0 w-134px py-9px text-25px text-#fff text-center rank-default flex items-center justify-center"
                                :class="index == 0 ? 'rank-one' : index == 1 ? 'rank-two' : index == 2 ? 'rank-three' : ''">
                                <img loading="lazy" :src="index == 0 ? rankOne : index == 1 ? rankTwo : rankThree"
                                    alt="" v-if="index < 3" class="w-35px pr-5px">
                                <div>NO.{{ item.rank }}</div>
                            </div>
                        </div>
                    </div>
                </refreshList>

            </div>
        </div>
        <van-overlay :show="overlayShow" @click="overlayShow = false" lock-scroll :z-index="100"
            class="my-overlay absolute top-156px">
            <div class="bg-[rgba(0,0,0,.3)] w-full relative mt-30px !h-[calc(100vh-180px)]" @click.stop="showOverLay">
                <div class="bg-#fff p-30px pr-10px max-h-28vh overflow-hidden">
                    <div class="flex flex-wrap">
                        <div v-for="(item, index) in areaList" :key="index"
                            :class="active == index ? 'bg-#EEF6FF text-#5AA4FF' : 'bg-#F6F7F8'"
                            class="kind rounded-10px text-center py-17px px-17px mb-20px text-28px mr-20px"
                            @click.stop="chooseArea(index)">
                            {{ item.dictName }}
                        </div>
                    </div>
                </div>
            </div>
        </van-overlay>
    </div>
</template>
<script lang="ts" setup>
defineOptions({
    name: 'activityVote',
})
import useRefreshFun from '@/hooks/app.ts'
import rankOne from '@/assets/activity/rank/icon_one.png';
import rankTwo from '@/assets/activity/rank/icon_two.png';
import rankThree from '@/assets/activity/rank/icon_one.png';
const tabActive = ref(0);
import { useUserStore } from '@/store/modules/user';
const useStore = useUserStore();
import { findOpusesList, statistics, findRankList, opusesAddRecord, checkCount } from '@/api/activity';
import { judgeStaticUrl } from '@/utils/utils';
import refreshList from '@/components/refreshList/index.vue';
import router from '@/router';
import { showDialog, showToast } from 'vant';
import { useDictionary } from "@/store/modules/dictionary";
const dictionary = useDictionary()
const overlayShow = ref(false);
const areaList = ref([])
const active = ref(0);
function showOverLay() {
    overlayShow.value = !overlayShow.value;
}
const companyId = ref('')
function chooseArea(index: number) {
    active.value = index;
    overlayShow.value = false;
    companyId.value = areaList.value[active.value].dictName;
    console.log(companyId.value);

    onSearch()
}
const tabs = ref([
    { label: '作品展示', value: '0' },
])
const searchText = ref(null)
//获取图标
const getIcon = () => {
    let img = new URL(`@/assets/public/icon_search.png`, import.meta.url).href
    return img
}
const tabChange = (index) => {
    getStatic()
    tabActive.value = index;
    onSearch()
}
// 计算属性
const activityDetail = computed(() => {
    return useStore.activityDetail || {};
});
const onSearch = () => {
    Data.value.pageNum = 1;
    getApplyList()
}
const Data = ref({
    pageNum: 1,
    tabIndex: 0
})
const list = ref([])
//获取活动列表
const loadMoreRef = ref(null)
function getApplyList() {
    let requestMethod = findOpusesList;
    if (tabActive.value == 1) {
        requestMethod = findRankList
    }
    requestMethod({
        pageSize: 10,
        pageNum: unref(Data).pageNum,
        activityId: activityDetail.value.activityId,
        searchText: tabActive.value == 0 ? searchText.value : undefined,
        areaName: tabActive.value == 0 ? companyId.value : undefined
    }).then(res => {
        if (res.code == 200) {
            if (Data.value.pageNum === 1) {
                list.value = [];
            }
            list.value = list.value.concat(res.data);
            //重置刷新状态及 判断是否加载完成
            if (loadMoreRef.value) {
                loadMoreRef.value.onLoadSuc(list.value.length, res.total);
            }
        }
    })
}
// 刷新
const onRefreshList = () => {
    Data.value.pageNum = 1;
    getApplyList()
};
// 加载更多
const onLoadMore = () => {
    Data.value.pageNum++;
    getApplyList()
}
const staticObj = ref({
    opusCount: 0, //作品数量
    voteCount: 0, //投票数量
    readCount: 0 //访问量
})
//获取统计数据
const getStatic = () => {
    statistics({ activityId: activityDetail.value.activityId }).then(res => {
        if (res.code == 200) {
            staticObj.value.opusCount = res.data.opusCount || '--';
            staticObj.value.voteCount = res.data.voteCount || '--';
            staticObj.value.readCount = res.data.readCount || '--';
        }
    })
}
//详情
const toPage = (item) => {
    router.push({
        path: '/activityHome/myWorks/detail',
        query: {
            opusInfoId: item.opusInfoId,
            status: 'pass',
            source: 'list'
        }
    })
}
const clickState = ref(false)
const voteTicket = async (item) => {
    if (clickState.value) {
        showToast({
            message: '投票太频繁啦,请稍后再试',
            duration: 2000,
            forbidClick: true,
        })
        return
    }
    const { data: userCount } = await checkCount({
        activityId: activityDetail.value.activityId,
    })
    if (userCount >= activityDetail.value.voteInfo.dailyMax) {
        showDialog({
            title: '温馨提示',
            message: `每位用户每日最多可投${activityDetail.value.voteInfo.dailyMax || 0}票,明日再来吧~`,
            confirmButtonText: '我知道了',
        })
        return
    }
    if (item.todayCount >= activityDetail.value.voteInfo.dailySingleCount) {
        showDialog({
            title: '温馨提示',
            message: `每个作品每天只能投${activityDetail.value.voteInfo.dailySingleCount || 0}票,继续选择其他作品投票吧~`,
            confirmButtonText: '我知道了',
        })
        return
    }
    clickState.value = true;
    const { code } = await opusesAddRecord({
        activityId: activityDetail.value.activityId,
        opusInfoId: item.opusInfoId,
        platform: "app"
    })
    if (code === 200) {
        item.votesNum++;
        item.todayCount++
        staticObj.value.voteCount++ //总票数++ denglanlan
        showToast({
            message: '投票成功',
            duration: 1000,
            type: 'success',
            forbidClick: true,
        })
    }

    setTimeout(() => {
        clickState.value = false
    }, 1500)
}
const changePage = () => {
    staticObj.value.voteCount++
}
const { refresh } = useRefreshFun()
refresh([
    { name: 'changePage', funsName: [changePage] }
])

onMounted(() => {
    areaList.value = [...dictionary.getDictionaryOBJMap?.['regionCode']];
    if (activityDetail.value.voteInfo.enableRank == 'y') {
        tabs.value.push({ label: '排行榜', value: '1' })
    }
    getStatic();
    getApplyList();
})
</script>
<style lang="scss" scoped>
.vote {
    background: #F7F8F9;
    min-height: 100vh;

    .headers {
        :deep(.van-cell) {
            background-color: transparent;
            padding: 0;
        }

        :deep(.van-tabs--line .van-tabs__wrap) {
            height: 110px;
        }

        :deep(.van-tab) {
            line-height: 1;
            height: 100%;
        }

        :deep(.van-tabs__nav--line) {
            height: 100%;
            padding-bottom: 15px;
        }

        :deep(.van-tabs__line) {
            width: 36px;
            height: 6px;
            background: linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%);
            border-radius: 3px;
        }

        :deep(.van-tab__text) {
            font-size: 32px;
        }

        :deep(.van-field__body) {
            height: 100%;
            font-size: 24px;
        }
    }

    .search {
        .search-btn {
            width: 104px;
            height: 54px;
            text-align: center;
            background: rgba(90, 164, 255, .1);
            border-radius: 27px;
            line-height: 54px;
            font-size: 26px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #5AA4FF;
        }

        :deep(.van-search) {
            padding: 0px;
            padding-right: 16px;
            background-color: #F6F7F8;
            border-radius: 34px;
            width: 85%;

            .van-search__content {
                border-radius: 34px;
                background-color: #F6F7F8;
            }

            .van-search__action {
                padding-right: 0;
            }

            .van-field__left-icon {
                display: flex;
                align-items: center;

                img {
                    width: 25px;
                    height: 25px;
                }
            }
        }
    }

    .filter-box {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 24px;
        color: #999999;
        margin-left: 26px;

        >img {
            width: 25px;
            margin-right: 6px;
        }
    }

    .total-box {
        border: 2px solid #fff;
    }

    .box-sh {
        box-shadow: 0px 0px 10px 0px #F6F7F8;
    }

    .round {
        border-radius: 20px 20px 0px 0px;
    }

    .bg-round {
        background: linear-gradient(0deg, #A1CEFF 0%, #5AA2FF 100%);
        border-radius: 20px 0px 20px 0px;
        width: 113px;
        height: 47px;
    }

    .van-overlay {
        background: transparent;
    }

    .rank-default {
        background: linear-gradient(34deg, #3EA4F2, #588FFF);
        border-radius: 20px 0px 20px 0px;
    }

    .rank-one {
        background: linear-gradient(34deg, #e5b239, #edcb30);
    }

    .rank-two {
        background: linear-gradient(34deg, #d0d5d9, #b0b7bd);
    }

    .rank-three {
        background: linear-gradient(34deg, #de9e56, #cd8749);
    }
}
</style>