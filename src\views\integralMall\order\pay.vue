<template>
    <!-- 支付 -->
    <div class="pay flex flex-col bg-[#F9F9F9] h-100vh">
        <div class="flex-1 overflow-scroll flex flex-col">
            <!-- 发货地址信息 -->
            <div class="address flex items-center px-30px mt-50px relative pb-56px box-border"
                v-if="productInfo?.goodsInfo?.integralPayment === '1'" @click="selectAddress">
                <div class="left mr-42px">
                    <img loading="lazy" src="@/assets/integralMall/big_address_icon.png" class="w-40px" />
                </div>
                <div class="right flex justify-between items-center flex-1">
                    <div v-if="noAddress">
                        <div class="text-[#333] text-28px">请选择收货地址</div>
                    </div>
                    <div class="text-[#333] text-28px" v-else>
                        <div class="text-[#838383] text-30px">{{ addressInfo?.detailArea }}</div>
                        <div class="text-[#333] text-32px mt-24px">{{ addressInfo?.detailAddress }}</div>
                        <div class="user-info text-[#666] flex items-center text-30px mt-24px">
                            <div>{{ addressInfo?.receiverName }}</div>
                            <div class="ml-44px">{{ addressInfo?.receiverPhone }}</div>
                        </div>
                    </div>
                    <img loading="lazy" src="@/assets/integralMall/small_arrow.png" class="w-13px" />
                </div>
            </div>
            <div class="goods-price-info px-30px bg-#FFF mt-31px flex-1">
                <div class="goods flex pb-34px pt-31px">
                    <div class="w-148px h-148px rounded-20px bg-cover center"
                        :style="`background-image: url(${judgeStaticUrl(productInfo?.goodsInfo?.productCoverImg)})`">
                    </div>
                    <div class="right ml-15px flex flex-col flex-1">
                        <div class="text-[#333] text-30px flex-1 mt-17px">{{ productInfo?.goodsInfo?.productName }}
                        </div>
                        <!-- {{ productInfo?.specifyInfo?.orderNum }} -->
                        <div class="text-30px text-[#666]">x1</div>
                    </div>
                </div>
                <div class="price-text mt-54px">
                    <div class="flex justify-between items-center" v-if="productInfo?.goodsInfo?.consumeType === 'mix'">
                        <div class="label text-#666 text-26px">订单金额(含邮费)</div>
                        <div class="price text-#333">
                            <span class="text-23px">￥</span>
                            <span class="text-28px">{{ productInfo.sourceType === '2' ?
                                productInfo?.specifyInfo?.discountPrice : productInfo?.specifyInfo?.nowPrice }}</span>
                        </div>
                    </div>
                    <div class="flex justify-between items-center mt-27px">
                        <div class="label text-#666 text-30px">消耗积分</div>
                        <div class="price text-#333 text-28px">
                            {{ productInfo?.sourceType === '2' ?
                                productInfo?.specifyInfo?.discountIntegral : productInfo?.specifyInfo?.nowIntegral }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="controll-bottom h-140px flex items-center bg-[#fff] px-30px">
            <div class="left mr-40px flex items-center" style="flex:0.45">
                <div class="text-[#333] text-28px text-medium">合计：</div>
                <div class="text-#FF4344 font-36px ml-10px">
                    {{ productInfo?.sourceType === '2' ?
                        productInfo?.specifyInfo?.discountIntegral : productInfo?.specifyInfo?.nowIntegral }}
                    <span class="text-30px">积分</span>
                    <!-- 未对接 -->
                    <span v-if="productInfo?.goodsInfo?.consumeType === 'mix'">+￥{{ productInfo?.sourceType === '2' ?
                        productInfo?.specifyInfo?.discountPrice : productInfo?.specifyInfo?.nowPrice }}</span>
                </div>
            </div>
            <div class="right h-78px text-34px text-#fff
             bg-#FF4344 rounded-40px leading-none flex items-center justify-center" style="flex:0.55"
                @click="submitOrder">
                立即支付
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { judgeStaticUrl } from '@/utils/utils';
import { useMallStore } from '@/store/modules/mall'
import { integralGoodsExchange } from '@/api/mall/integral'
import { defaultAddress } from '@/api/address'
import { showConfirmDialog, showLoadingToast, closeToast, showDialog } from 'vant';
const router = useRouter()
const mallStore = useMallStore()
const productInfo: any = computed(() => {
    let infos = mallStore.getselectIntegralGoodsInfo
    if (JSON.stringify(infos) === '{}') {
        showConfirmDialog({
            message: '未获取到商品信息，请返回上一页重新操作',
            confirmButtonText: '我知道了'
        }).then(() => {
            router.back()
        })
    }
    console.log(infos, 'infos')

    return infos
})
onMounted(() => {
    if (JSON.stringify(mallStore.getAddressInfo) !== '{}') addressInfo.value = mallStore.getAddressInfo
    else getDefaultAddress()
})

const noAddress = ref(false)
const addressInfo = ref<any>({})
const getDefaultAddress = () => {
    defaultAddress().then((res: any) => {
        if (res.data) {
            addressInfo.value = res.data
        } else {
            noAddress.value = true
        }
    })
}
const selectAddress = () => {
    router.push({
        path: '/address'
    })
}

let btnClick = false
const submitOrder = async () => {
    if (btnClick) return
    btnClick = true
    showLoadingToast('正在兑换中，亲稍等...')
    const { companyId, productId, } = productInfo.value?.goodsInfo
    const { productSubId, birthdayProductInfoId } = productInfo.value?.specifyInfo
    const params: any = {
        companyId,
        productId,
        productSubId,
        exchangeSource: productInfo.value?.sourceType // 1:线上兑换 2：生日福利
    }
    if (params.exchangeSource === '2') params.birthdayProductInfoId = birthdayProductInfoId

    // 线上发货
    if (productInfo.value?.goodsInfo?.integralPayment === '1') {
        if (!addressInfo.value?.receiverName) return showConfirmDialog({
            message: '请选择收货地址',
            confirmButtonText: '选择地址',
            cancelButtonText: '取消'
        }).then(() => {
            selectAddress()
        })

        const { detailArea, detailAddress, receiverName, receiverPhone } = addressInfo.value
        params.detailArea = detailArea
        params.detailAddress = detailAddress
        params.receiverName = receiverName
        params.receiverPhone = receiverPhone
    }

    const { code, data, message } = await integralGoodsExchange(params)
    btnClick = false
    closeToast()
    if (code == 200) {
        router.replace({
            path: '/paySuccess',
            query: {
                recordId: data,
                payType: 'integral',
                integralPayment: productInfo.value?.goodsInfo?.integralPayment,
                index: 2,
                exchangeSource: productInfo.value?.sourceType// 1:线上兑换 2：生日福利
            }
        })
    } else {
        showDialog({
            message,
            showCancelButton: false,
            confirmButtonText: '知道了'
        })
    }
}
</script>

<style lang="scss" scoped>
.address::after {
    content: '';
    display: block;
    position: absolute;
    height: 5px;
    bottom: 0;
    left: 5px;
    right: 5px;
    background-image: linear-gradient(90deg, #418DEA 36%, transparent 36%, transparent 52%, #FF4344 52%, #FF4344 86%, transparent 86%);
    background-size: 60px 5px;
    background-repeat: repeat-x;
    transform: skewX(-20deg);
    overflow: hidden;
}

.goods-price-info {
    .goods {
        border-bottom: 1px solid #E7E7E7;
    }
}

.controll-bottom {
    box-shadow: 0px 4px 43px 0px rgba(61, 23, 23, 0.1);
}
</style>