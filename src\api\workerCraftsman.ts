import { h5Http } from '@/utils/http/axios';

// 劳模工匠列表
export const getwokerCraftList = (params:Recordable) => {
    return h5Http.get({
        url: '/modelWorkerInfo/findVoListH5',
        params,
    });
};

// 劳模工匠类型
export const getwokerCraftType = (modelType:string) => {
    return h5Http.get({
        url: '/modelType/findVoList',
        params:{
            modelType
        }
    })
};

// 劳模工匠详情
export const getwokerCraftDetail = (workerId:string) => {
    return h5Http.get({
        url: '/modelWorkerInfo/getVoByDto',
        params:{
            workerId
        }
    })
}

// 查找劳模工匠认证记录
export  const  checkAuth = (modelType:string) => {
    return h5Http.get({
        url: '/modelWorkerAuditRecord/findVoListByUserId',
        params:{
            modelType
        },
    })
}


export type baseAuthFormType = {
    "companyName":string,
    "userName":string,
    "identityCardNumber" :string,
    "gender":string,
    "phone" :string,
    "avatar" :string,
    "evidentiaryMaterial" :string,
    "typeBizId"?:string,
    "modelType":number,
    "personalStyle":string
}
export type authFormType  = {
    "nationality":string,
    "education":string,
    "politicsState":string,
    "workUnitName" :string,
    "dateOfBirth":string,
    "whenModelWorker":string,
    "certificateNumber" :string,
    "remark"?:string,
} & baseAuthFormType

// 劳模认证
export const savewokerAuth = (params:authFormType) => {
    return h5Http.post({
        url: '/modelWorkerAuditRecord/certificationApplication',
        params     
    })
}

// 工匠认证
export const saveCraftAuth = (params:baseAuthFormType) => {
    return h5Http.post({
        url: '/modelWorkerAuditRecord/certificationApplication',
        params     
    })
}

export const authFlow = () => {
    return h5Http.get({
        url: '/modelWorkerBecome/findVoListH5',
    })
}