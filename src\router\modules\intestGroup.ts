import { KeepAlive } from "vue";

export default [
    {
        path: "/interest",
        name: "interest",
        component: () => import("@/views/interestGroup/interest.vue"),
        meta: {
          title: "兴趣社区",
          isShowTabBar: false,
          isBack:true,
          keepAlive:true,
          updatePath:['/home','/labelInfo']
        }
    },
    {
      path: "/creatGroup",
      name: "creatGroup",
      component: () => import("@/views/interestGroup/creatGroup/index.vue"),
      meta: {
        title: "创建小组",
        isShowTabBar: false,
        isBack:true
      }
    },
    {
      path: "/releaseActivity",
      name: "releaseActivity",
      component: () => import("@/views/interestGroup/releaseActivity/index.vue"),
      meta: {
        title: "发布活动",
        isShowTabBar: false,
        isBack:true
      }
    },
    {
      path: "/createDynamics",
      name: "createDynamics",
      component: () => import("@/views/interestGroup/createDynamics/index.vue"),
      meta: {
        title: "发布动态",
        isShowTabBar: false,
        isBack:true
      }
    },
    {
      path: "/interestList",
      name: "interestList",
      component: () => import("@/views/interestGroup/interestList/interestList.vue"),
      meta: {
        title: "兴趣社区",
        isShowTabBar: false,
        isBack:true,
        keepAlive:true,
        updatePath:['/interest']
      }
    },
    {
      path: "/interestSign",
      name: "interestSign",
      component: () => import("@/views/interestGroup/interestList/sign.vue"),
      meta: {
        title: "报名",
        isShowTabBar: false,
        isBack:true
      }
    },
    {
      path: "/interestComment",
      name: "interestComment",
      component: () => import("@/views/interestGroup/interestList/comment.vue"),
      meta: {
        title: "评论评价",
        isShowTabBar: false,
        isBack:true
      }
    },
    {
      path: "/createTopic",
      name: "createTopic",
      component: () => import("@/views/interestGroup/createTopic/index.vue"),
      meta: {
        title: "创建话题",
        isShowTabBar: false,
        isBack:true
      }
    },
    {
      path: "/topicDetails",
      name: "topicDetails",
      component: () => import("@/views/interestGroup/createTopic/topicDetails.vue"),
      meta: {
        title: "话题详情",
        isShowTabBar: false,
        isBack:true
      }
    },
    {
      path: "/myInterestActivity",
      name: "myInterestActivity",
      component: () => import("@/views/interestGroup/myActivity/index.vue"),
      meta: {
        title: "我的活动",
        isShowTabBar: false,
        isBack:true
      }
    },
    {
      path: "/groupList",
      name: "groupList",
      component: () => import("@/views/interestGroup/groupList/groupList.vue"),
      meta: {
        title: "兴趣小组",
        isShowTabBar: false,
        isBack:true,
        keepAlive:true,
        updatePath:['/interest','/countyDetail']
      }
    },
    {
      path: "/groupExamine",
      name: "examine",
      component: () => import("@/views/interestGroup/examine/examine.vue"),
      meta: {
        title: "审核消息",
        isShowTabBar: false,
        isBack: true,
        keepAlive:true,
        updatePath:['/interest']
      }
    },
    {
      path: "/groupExamine/dynamics",
      name: "examineDynamics",
      component: () => import("@/views/interestGroup/examine/dynamics.vue"),
      meta: {
        title: "审核消息",
        isShowTabBar: false,
        isBack: true
      }
    },
    {
      path: "/groupExamine/activity",
      name: "examineActivity",
      component: () => import("@/views/interestGroup/examine/activity.vue"),
      meta: {
        title: "审核消息",
        isShowTabBar: false,
        isBack: true
      }
    },
    {
      path: "/groupExamine/activityExamine",
      name: "activityExamine",
      component: () => import("@/views/interestGroup/examine/activityExamine.vue"),
      meta: {
        title: "审核消息",
        isShowTabBar: false,
        isBack: true
      }
    },
    {
      path: "/groupExamine/group",
      name: "examineGroup",
      component: () => import("@/views/interestGroup/examine/group.vue"),
      meta: {
        title: "审核消息",
        isShowTabBar: false,
        isBack: true
      }
    },
    {
      path: "/groupExamine/applyGroupInfo",
      name: "applyGroupInfo",
      component: () => import("@/views/interestGroup/examine/applyGroupInfo.vue"),
      meta: {
        title: "审核消息",
        isShowTabBar: false,
        isBack: true
      }
    },
    {
      path: "/groupExamine/groupDetails",
      name: "groupDetails",
      component: () => import("@/views/interestGroup/examine/groupDetails.vue"),
      meta: {
        title: "审核消息",
        isShowTabBar: false,
        isBack: true
      }
    },
    {
      path: "/groupDetail",
      name: "groupDetail",
      component: () => import("@/views/interestGroup/groupList/detail.vue"),
      meta: {
        title: "小组详情",
        isShowTabBar: false,
        isBack: true
      }
    },
    {
      path: "/myInterestGroup",
      name: "myInterestGroup",
      component: () => import("@/views/interestGroup/myInterestGroup/index.vue"),
      meta: {
        title: "我的小组",
        isShowTabBar: false,
        isBack: true
      }
    },
    {
      path: "/labelChoose",
      name: "labelChoose",
      component: () => import("@/views/interestGroup/myInterestGroup/labelChoose.vue"),
      meta: {
        title: "标签选择",
        isShowTabBar: false,
        isBack: true
      }
    },
    {
      path: "/labelInfo",
      name: "labelInfo",
      component: () => import("@/views/interestGroup/myInterestGroup/info.vue"),
      meta: {
        title: "个人信息",
        isShowTabBar: false,
        isBack: true
      }
    },
    {
      path: "/dynamicDetail",
      name: "dynamicDetail",
      component: () => import("@/views/interestGroup/createDynamics/detail.vue"),
      meta: {
        title: "详情",
        isShowTabBar: false,
        isBack: true
      }
    },
]
