<template>
    <van-popup :show="props.show" position="bottom" @click-overlay="close">
        <div class="px-55px py-25px bg-[#fff] popup-content">
            <div class="flex justify-between py-30px" v-for="item, index in refundTypeArr" :key="index"
                @click="selectType(item)" :class="{ 'border-bottom': index < refundTypeArr.length - 1 }">
                <div class="text-center text-28px text-#333">{{ item.label }}</div>
                <div class="w-32px h-32px rounded-50%" :class="{ 'check-circle': item.value !== props.checkedValue }">
                    <img loading="lazy" src="@/assets/public/checked.png" class="w-full h-full"
                        v-if="item.value === props.checkedValue" />
                </div>
            </div>
        </div>
    </van-popup>
</template>
<script setup lang="ts">
import { useDictionary } from '@/store/modules/dictionary'
const dictionaryStore = useDictionary()
const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    checkedValue: {
        type: String,
        default: '1',
    },
    // 是否允许退货退款
    disabledAll: {
        type: Boolean,
        default: false,
    },
})
const refundTypeArr = computed(() => {
    const arr = dictionaryStore.getDictionaryOpt?.['saleServiceType']
    if (props.disabledAll) {
        return arr?.filter((item: any) => item.value !== 'returnAll')
    }
    return arr
})

const emit = defineEmits(['update:show', 'selected'])
const close = () => {
    emit('update:show', false)
}
const selectType = (item) => {
    emit('selected', item)
    close()
}
</script>
<style lang="scss" scoped>
.check-circle {
    border: 2px solid #5AA4FF;
}

.border-bottom {
    border-bottom: 1px solid #EFEFEF;
}

.van-popup {
    background: transparent;
}

.popup-content {
    border-radius: 20px 20px 0px 0px;
}
</style>