
import { h5Http } from '@/utils/http/axios';

//获取学习小组列表
export const findOneJoinRecordVoList = (params) => {
    return h5Http.get({
        url: '/groupsStudy/findOneJoinRecordVoList',
        params
    });
}

//加入学习小组
export const joinGroups = (params) => {
    return h5Http.post({
        url: '/groupsInsertRecord/joinGroups',
        params
    });
}

//退出学习小组
export const exitGroup = (params) => {
    return h5Http.delete({
        url: '/groupsInsertRecord/exitGroup',
        params
    });
}

//创建学习小组
export const addGroupsRequestByDTO = (params) => {
    return h5Http.post({
        url: '/groupsStudy/groupsRequestByDTO',
        params
    });
}


//查询我申请加入的小组
export const findMyGroupVoList = (params) => {
    return h5Http.get({
        url: '/groupsStudy/findMyGroupVoList',
        params
    });
}

//查询我申请创建的小组
export const findMyGroupCreateVoList = (params) => {
    return h5Http.get({
        url: '/groupsStudy/findMyGroupCreateVoList',
        params
    });
}




//小组详情
export const groupsStudyGetVoByDto = (params) => {
    return h5Http.get({
        url: '/groupsStudy/getVoByDto',
        params
    });
}


//小组聊天内容
export const groupsMessageReplyFindVoList = (params) => {
    console.log("params333",params);
    return h5Http.get({
        url: '/groupsMessageReply/findVoList',
        params
    });
}

//发送小组聊天内容
export const groupsMessageReplySaveMessageByDTO = (params) => {
    return h5Http.post({
        url: '/groupsMessageReply/saveMessageByDTO',
        params
    });
}

//查询小组成员
export const groupMembersInfo = (params) => {
    return h5Http.get({
        url: '/groupsInsertRecord/groupMembersInfo',
        params
    });
}

//删除小组成员
export const removeMemberById = (autoId) => {
    return h5Http.delete({
        url: '/groupsInsertRecord/removeMemberById?autoId='+autoId,
    });
}


//是否小组管理员
export const isLeaderFlag = (params) => {
    return h5Http.get({
        url: '/groupsInsertRecord/isLeaderFlag',
        params
    });
}


//分页查询公开的心得分享
export const shareFindVoListH5 = (params) => {
    return h5Http.get({
        url: '/experienceSharing/findVoListH5',
        params
    });
}

//分页查询个人全部心得分享
export const shareFindVoListH5Person = (params) => {
    return h5Http.get({
        url: '/experienceSharing/findVoListH5Person',
        params
    });
}

//新增/修改心得分享内容
export const shareSaveOrUpdateByDTO = (params) => {
    return h5Http.post({
        url: '/experienceSharing/saveOrUpdateByDTO',
        params
    });
}

//新增心得分享评论
export const shareInsertReply = (params) => {
    return h5Http.post({
        url: '/experienceSharingReply/insertReply',
        params
    });
}


//获取心得分享下评论
export const experienceSharingReplyFindVoListH5 = (params) => {
    return h5Http.get({
        url: '/experienceSharingReply/findVoListH5',
        params
    });
}







