<template>
  <div class="updata h-100vh">
    <div class="text-box">
      <div class="name"> <span style="color: #387cff">南充频道</span>升级啦！ </div>
      <div class="text">启程全新体验之旅，拥抱便捷高效新功能！</div>
    </div>
    <img loading="lazy"
      src="@/assets/public/icon_phone.png"
      alt=""
      class="phone"
    />
    <div class="tips-box">
      <p class="title">尊敬的南充职工：</p>
      <p class="text">
        为给广大南充职工带来更好的使用体验，川工之家南充频道将进行停机升级，停机升级期间用户无法访问川工之家南充频道，我们将在停机升级完成后及时恢复，给您带来的不便敬请谅解！
      </p>
    </div>
  </div>
</template>
<style>
  .updata {
    /* width: 100%;
    height: 100%; */
    background: url('@/assets/public/bg.png') no-repeat;
    background-size: 101% 100%;
    background-position: center center;
    display: flex;
    flex-flow: column;
    align-items: center;
    box-sizing: border-box;
    padding-top: 6vh;
    overflow: hidden;
  }
  .text-box {
    display: flex;
    flex-flow: column;
    align-items: center;
  }

  .text-box .name {
    font-weight: 600;
    font-size: 6.4vw;
    /* line-height: 12.5vw; */
    margin-bottom: 3vw;
    color: #333333;
  }

  .text-box .text {
    font-size: 4vw;

    color: #666666;
  }
  .phone {
    height: 33vh;
    margin-top: 10vw;
  }
  .tips-box {
    width: 84.27vw;
    box-sizing: border-box;
    padding: 5.6vw;
    background: url('@/assets/public/bg_text.png') no-repeat;
    background-size: 100% 100%;
    background-position: center center;
    position: fixed;
    bottom: 5vh;
  }
  .tips-box {
    font-family: Source Han Sans CN;
    font-size: 3.466vw;
    line-height: 6.4vw;
    color: #0a246b;
  }
  .tips-box .title {
    font-size: 4vw;
    font-weight: 600;
  }
  .tips-box .text {
    text-indent: 6.933vw;
  }
  .tips-box .text span {
    font-weight: 600;
  }
</style>
