<template>
  <div class="w-full pt-30px px-20px" :class="$style.reserve">
    <div class="w-full mb-50px">
      <div class="h-152px title flex flex-col justify-between px-40px pt-20px pb-20px">
        <div class="text-[#2D2D2D] text-34px font-500"> 户外驿站休息室 </div>
        <div class="text-[#333333] text-30px font-500">
          选择日期 <span class="text-[#F38A23] text-26px">（*只可提前预约一周的时间）</span>
        </div>
      </div>
      <div class="bg-[#fff] h-482px px-30px rounded-b-16px">
        <div class="grid grid-cols-4 gap-4" v-if="venueInfo">
          <div @click="onSelectedDate(item)" v-for="item in weekDates"
            class="w-136px h-118px border-[1px] border-[#BFBFBF] border-solid rounded-[8px] flex flex-col justify-center items-center text-[#333] text-30px relative"
            :class="{
              selected: item.dateTime === selected,
              disabled: item.disabled,
            }">
            <div class="mb-10px">
              {{ item.week }}
            </div>
            <div>
              {{ item.date }}
            </div>
            <img loading="lazy" v-if="item.dateTime === selected" class="w-32px h-32px absolute -bottom-5px -right-5px"
              src="@/assets/position/reserve/selected.png" />
          </div>
        </div>
        <div class="text-[#333333] text-30px font-500 mt-40px mb-20px"> 选择时间 </div>
        <div class="flex justify-between items-center border-[#BFBFBF] border-1 border-solid px-20px py-15px rounded-[8px] 
          text-[#333] text-28px" @click="showTime = true">
          <div>{{ dateTime }} {{ join(dateTimeHHss, '-') }}</div>
          <van-icon name="arrow-down" />
        </div>
      </div>
    </div>
    <div class="h-360px px-40px py-20px bg-[#fff] rounded-16px">
      <div class="text-[#333333] text-30px font-500 mb-20px"> 预留信息 </div>
      <van-form input-align="left">
        <van-cell-group inset>
          <van-field v-model="formData.userName" name="userName" label="姓名"
            class="border-b-1px border-[#E3E3E3] border-b-solid" :left-icon="userIcon" placeholder="姓名" />
          <van-field v-model="formData.phone" name="phone" label="手机号"
            class="border-b-1px border-[#E3E3E3] border-b-solid" :left-icon="phoneIcon" placeholder="手机号" />
          <van-field v-model="formData.idCard" name="idCard" label="身份证号" :left-icon="idCardIcon" placeholder="身份证号" />
        </van-cell-group>
      </van-form>
    </div>
    <div class="flex justify-center items-center mt-50px w-full mb-10">
      <Button name="提交" class="!w-[470px] !h-[70px]" @click="onSubmit" />
    </div>
    <van-popup :show="showPop" round
      class="px-80px box-border w-570px h-630px pt-200px pb-70px flex flex-col justify-end text-center items-center success">
      <div class="text-36px text-[#000] mb-20px">预约成功,等待审核</div>
      <div class="text-30px text-[#999] mb-40px">审核成功将发送短信至您的手机上，请留意短信！</div>
      <Button name="确定" class="w-400px h-68px" @click="onClick" />
    </van-popup>

    <!-- 时间 -->
    <van-popup v-model:show="showTime" round position="bottom">
      <van-picker title="时间选择" :columns="times" @confirm="e => handleConfirm(e, true)" @cancel="showTime = false" />
    </van-popup>
  </div>
</template>

<script lang="ts" setup>
import userIcon from '@/assets/position/reserve/icon_user.png';
import idCardIcon from '@/assets/position/reserve/icon_idcard.png';
import phoneIcon from '@/assets/position/reserve/icon_phone.png';
import Button from '@/components/Button/button.vue';
import dayjs, { Dayjs } from 'dayjs';
import { addRecord, venueInfoDetail, getRecordByRecordId } from '@/api/position.ts';
import { showFailToast, showToast } from 'vant';
import {
  cloneDeep,
  find,
  includes,
  isEmpty,
  isString,
  join,
  map,
  range,
  remove,
  split,
} from 'lodash-es';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isBetween from 'dayjs/plugin/isBetween';
import isoWeek from 'dayjs/plugin/isoWeek';
import { useUserStore } from '@/store/modules/user';

dayjs.extend(isoWeek);

dayjs.extend(isBetween);

dayjs.extend(isSameOrAfter);

const route = useRoute();

const router = useRouter();

const userStore = useUserStore();

const formData = ref<Recordable>({});

const showPop = ref(false);

const venueInfo = ref<Recordable>();

const weeks = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];

const weekDates = ref<Recordable[]>([]);

const detailInfo = ref<Recordable>({});

const originalData = ref<Recordable>({});
// dateTime
const dateTime = ref<string>();

const dateTimeHHss = ref<string[]>([]);

const showTime = ref(false);

const times = ref<Recordable[]>([
  { text: '上午', value: '上午' },
  { text: '下午', value: '下午' },
]);

const originalTimes = ref<Recordable[]>([]);

const selected = ref<string>();

const onSubmit = () => {
  if (!isString(unref(dateTime))) {
    showFailToast('请选择预约日期！');
    return;
  }

  if (isEmpty(unref(dateTimeHHss))) {
    showFailToast('请选择预约时间！');
    return;
  }

  addRecord({
    ...unref(formData),
    userId: userStore.getUserInfo.userId,
    companyId: userStore.getUserInfo.companyId,
    companyName: userStore.getUserInfo.companyName,
    recordType: 'reservation',
    reservationDate: unref(dateTime),
    reservationStartTime: `${unref(dateTimeHHss)[1]}:00`,
    reservationEndTime: `${unref(dateTimeHHss)[2]}:00`,
    venueInfoId: unref(venueInfo)?.venueInfoId,
  }).then(({ code, message }) => {
    if (code === 200) {
      showPop.value = true;
    } else {
      showFailToast(`提交失败！${message || ''}`);
    }
  });
};

const onClick = () => {
  showPop.value = false;
  router.go(-1);
};

const onSelectedDate = (item: Recordable) => {
  if (item.disabled) {
    showToast('未开放日期~');
    return;
  }
  selected.value = item.dateTime;

  dateTime.value =
    unref(selected) === item.dateTime ? dayjs(item.dateTime)?.format('YYYY-MM-DD') : '';
};

function getWeekDates(openWeekDay: any[], afterCloseTime: string) {
  // 获取本周的周一
  const startOfWeek = dayjs(); // 默认周日是0，所以加1天获取周一

  const afterCloseDate = dayjs().format(`YYYY-MM-DD ${afterCloseTime}`);

  let weekDates = [];
  // 获取本周的所有日期
  for (let i = 0; i < 7; i++) {
    const dateTime = startOfWeek.add(i, 'day');

    const weekNum = dateTime.isoWeekday();

    let disabled = !includes(openWeekDay || [], `${weekNum}`);

    // 第一天校验当前时间
    if (i === 0) {
      disabled = dateTime.isAfter(afterCloseDate);
    }

    weekDates.push({
      date: startOfWeek.add(i, 'day').format('MM-DD'),
      dateTime,
      week: weeks[weekNum - 1],
      disabled,
    });
  }
  return weekDates;
}

// 处理时间
function fixDateTime(
  afterOpenTime: string,
  afterCloseTime: string,
  morningOpenTime: string,
  morningCloseTime: string
) {
  // // 比较时间

  const morningOpenDate = dayjs(dayjs().format(`YYYY-MM-DD ${morningOpenTime}`));
  const morningCloseDate = dayjs(dayjs().format(`YYYY-MM-DD ${morningCloseTime}`));

  const afterOpenDate = dayjs(dayjs().format(`YYYY-MM-DD ${afterOpenTime}`));
  const afterCloseDate = dayjs(dayjs().format(`YYYY-MM-DD ${afterCloseTime}`));

  if (morningOpenTime && morningCloseTime) {
    fixTimes(0, morningOpenDate, morningCloseDate);
  }

  if (afterOpenTime && afterCloseTime) {
    fixTimes(1, afterOpenDate, afterCloseDate);
  }

  // 存一份
  originalTimes.value = cloneDeep(unref(times));
}

// 计算区间
function fixTimes(timeIndex: number, openDate: Dayjs, closeDate: Dayjs) {
  const startHour = openDate.hour();
  const endHour = closeDate.hour();

  const hourRange = range(startHour, endHour);

  // 校验区间

  const defaultTime = map(range(startHour, endHour + 1), (v, dIndex) => {
    const time = dIndex === 0 ? openDate.format('HH:mm') : `${v < 10 ? `0${v}` : v}:00`;

    return {
      text: time,
      value: time,
    };
  });

  unref(times)[timeIndex].children = map(defaultTime, v => {
    return {
      ...v,
      children: map([...hourRange, hourRange[hourRange.length - 1] + 1], child => {
        const dateTime = dayjs(dayjs().format(`YYYY-MM-DD ${v.value}`));
        const time =
          child === closeDate.hour()
            ? closeDate.format('HH:mm')
            : `${child < 10 ? `0${child}` : child}:00`;
        const flatTime = dayjs(dayjs().format(`YYYY-MM-DD ${time}`));

        return { text: time, value: time, disabled: !flatTime.isAfter(dateTime) };
      }),
    };
  });
}

// 确认
function handleConfirm({ selectedOptions }: Recordable, flg?: boolean) {
  const hasDisabled = find(selectedOptions, v => v.disabled);
  if (hasDisabled?.disabled) {
    showToast('请选择有效时间预约！');
    return;
  }

  dateTimeHHss.value = map(selectedOptions, v => v.value);
  showTime.value = !flg;
}

// 处理上午下午时间
function fixMAtime(params: type) { }

onMounted(async () => {
  const data = await venueInfoDetail({ autoId: route.query.autoId });
  // 存一份
  originalData.value = data;
  if (route.query?.recordId) {
    detailInfo.value = await getRecordByRecordId({ recordId: route.query.recordId });
    unref(formData).idCard = detailInfo.value?.idCard;
    unref(formData).phone = detailInfo.value?.phone;
    unref(formData).userName = detailInfo.value?.userName;
  }

  const { openWeekDay, afterOpenTime, afterCloseTime, morningOpenTime, morningCloseTime } = data;
  weekDates.value = getWeekDates(split(openWeekDay || [], ',') ?? [], afterCloseTime);

  // 默认选择
  const firstDate = find(unref(weekDates), v => !v.disabled);

  dateTime.value = firstDate?.dateTime?.format('YYYY-MM-DD');
  selected.value = firstDate?.dateTime;

  venueInfo.value = data;
  fixDateTime(afterOpenTime, afterCloseTime, morningOpenTime, morningCloseTime);
});

watch(dateTime, () => {
  dateTimeHHss.value = [];
  // 是否当天
  const ifLocal = unref(dateTime) && dayjs(unref(dateTime)).isSame(dayjs(), 'day');
  if (ifLocal && !isEmpty(unref(originalData)) && !isEmpty(unref(times))) {
    const { afterCloseTime, morningCloseTime } = unref(originalData);
    // 获取当前时间的时刻
    const localHour = dayjs().hour();

    const morningCloseDate = dayjs(dayjs().format(`YYYY-MM-DD ${morningCloseTime}`));
    const afterCloseDate = dayjs(dayjs().format(`YYYY-MM-DD ${afterCloseTime}`));

    // 下午时间段处理
    if (dayjs().isSameOrAfter(morningCloseDate)) {
      const onlyAfter = remove(cloneDeep(unref(times)), v => v.value === '下午');

      onlyAfter[0].children = map(onlyAfter[0]?.children, v => {
        const afterHour = dayjs(dayjs().format(`YYYY-MM-DD ${v.value}`)).hour();

        return {
          ...v,
          disabled: localHour > afterHour,
          children: map(v.children, child => {
            const childDate = dayjs(dayjs().format(`YYYY-MM-DD ${child.value}`));

            return { ...child, disabled: dayjs().isSameOrAfter(childDate) || child.disabled };
          }),
        };
      });
      // 重新赋值，保证响应式
      times.value = onlyAfter;
    } else {
      // 上午午时间段处理
      // 重新赋值，保证响应式
      unref(times)[0].children = map(unref(times)[0]?.children, v => {
        const hour = dayjs(dayjs().format(`YYYY-MM-DD ${v.value}`)).hour();

        return {
          ...v,
          disabled: localHour > hour,
          children: map(v.children, child => {
            const childDate = dayjs(dayjs().format(`YYYY-MM-DD ${child.value}`));

            return { ...child, disabled: dayjs().isSameOrAfter(childDate) || child.disabled };
          }),
        };
      });
    }
  } else {
    times.value = unref(originalTimes);
  }
});
</script>

<style lang="less" module>
.reserve {
  :global {
    height: 100vh;
    box-sizing: border-box;
    background-image: url('@/assets/position/reserve/bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    overflow-y: scroll;

    .success {
      background-image: url('@/assets/position/reserve/success.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }

    .title {
      background-image: url('@/assets/position/reserve/title-bg.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }

    .selected {
      border-color: #5ba4ff;
      background-color: #eff7ff;
    }

    .disabled {
      background-color: #f2f2f2;
      border: none;
    }

    .van-cell-group {
      margin: 0;

      .van-cell.van-field {
        align-items: center;
      }
    }

    .van-cell {
      font-size: 28px;
      padding-top: 18px;
      padding-bottom: 18px;
    }

    .van-icon__image {
      width: 28px;
    }
  }
}
</style>
