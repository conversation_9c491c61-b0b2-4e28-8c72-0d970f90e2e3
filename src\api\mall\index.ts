import { openHttp } from '@/utils/http/axios';

// ==== 商品公共接口 ====
// 商品主体详情
export const integralGoodsDetail = (productId:any) => {
  return openHttp.get({
        url:'/customProductInfo/getProductInfoByProductId',
        params:{
            productId
        }
    })
}
// 商品规格详情
export const integralSpecifyGoodsDetail = (params:any) => {
    return openHttp.get({
          url:'/customProductInfo/getProductPriceInfoByProductId',
          params
      })
}
// 商品介绍文本
export const integralGoodsDetailText = (productId:any) => {
  return openHttp.get({
    url:'/customProductInfo/getProductIntroduce',
    params:{
        productId
    }
 })
}
// ===== end =====