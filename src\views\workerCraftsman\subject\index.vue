<template>
    <div class="worker_story">
        <div class="banner w-full">
            <img loading="lazy" :src="bannerUrl" class="w-full" />
        </div>
        <div class="content w-full box-border rounded-t-16px bg-[#fff] -translate-y-20px px-28px">
            <!-- 故事/政策/礼遇 -->
            <template v-if="pageType !== '3'">
                <refreshList @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
                    <newsCell v-for="(item, index) in list" :key="index" :content="item"></newsCell>
                </refreshList>
            </template>
            <!-- 申报流程导航 -->
            <template v-else>
                <div class="apply_flow py-36px">
                    <div class="apply_flow_title">
                        <img loading="lazy" src="@/assets/workerCraftsman/apply_title.png" class="w-157px" />
                    </div>
                    <div class="apply_flow_lists">
                        <div class="list mt-28px h-160px bg-cover bg-left-top text-[32px] flex justify-end"
                            :style="{ backgroundImage: `url(${item.background})` }" v-for="item, index in flowList"
                            :key="index" @click="toPage(item)">
                            <div class="flex items-center justify-between flex-[0.7_0.7_0%]">
                                <span :style="{ color: item.color }">{{ item.title }}</span>
                                <img loading="lazy" :src="item.icon" class="w-34px mr-52px" />
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>
</template>

<script setup lang="ts">
import newsCell from '@/components/Cell/newsCell.vue';
import refreshList from '@/components/refreshList/index.vue';
import { showFailToast } from 'vant';
import { authFlow } from '@/api/workerCraftsman'

import banner_1 from '@/assets/workerCraftsman/story_banner.jpg'
import banner_2 from '@/assets/workerCraftsman/policy_banner.jpg'
import banner_3 from '@/assets/workerCraftsman/apply_banner.jpg'
import banner_4 from '@/assets/workerCraftsman/gift_banner.jpg'

import flow_bg_country from '@/assets/workerCraftsman/flow_bg_country.png';
import flow_bg_province from '@/assets/workerCraftsman/flow_bg_province.png';
import flow_bg_city from '@/assets/workerCraftsman/flow_bg_city.png';

import more_country from '@/assets/workerCraftsman/more_country.png';
import more_province from '@/assets/workerCraftsman/more_province.png';
import more_city from '@/assets/workerCraftsman/more_city.png';
import { getCategoryInfo, getNewsList } from '@/api/news/index';
onMounted(() => {
    initalPage();
    if (pageType.value === '3') getAuthFlow()
})
const list = ref([]);
let page = 1;//分页
const loadMoreRef = ref(null)
const route = useRoute();
const bannerUrl = ref(banner_1)
const pageType = ref('1');
const code = ref('lao_mo_gu_shi');
const tabData = ref([])

// ====新闻列表====
const initalPage = () => {
    pageType.value = route.params.type as string || '1'
    switch (pageType.value) {
        case '1':
            document.title = '劳模故事';
            bannerUrl.value = banner_1;
            code.value = 'lao_mo_gu_shi'
            break;
        case '2':
            document.title = '劳模政策';
            bannerUrl.value = banner_2;
            code.value = 'lao_mo_zheng_ce'
            break;
        case '3':
            document.title = '劳模申报';
            bannerUrl.value = banner_3;
            break;
        case '4':
            document.title = '劳模礼遇';
            bannerUrl.value = banner_4;
            code.value = 'lao_mo_li_yu'
            break;
        default:
            document.title = '劳模故事';
            bannerUrl.value = banner_1;
            code.value = 'lao_mo_gu_shi'
    }
    list.value = [];
    page = 1;
    getColumn()
}
//获取栏目
async function getColumn() {
    getCategoryInfo({
        categoryCode: code.value,
        platformType: 30,
    }).then(res => {
        tabData.value = res.data;
        getLists();
    });
}
// 刷新
const onRefreshList = () => {
    page = 1
    getLists()
}
// 加载更多
const onLoadMore = () => {
    page++
    getLists()
}
//新闻列表
async function getLists() {
    let res = await getNewsList({
        categoryCode: tabData.value?.categoryCode,
        platformType: 30,
        pageNum: page,
        pageSize: 10,
    });
    if (page === 1) list.value = [];
    list.value = list.value.concat(res.data);
    //重置刷新状态及 判断是否加载完成
    if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(list.value.length, res.total);
    }
}

// ====end====


// ====申报流程====
const flowList = ref<any[]>([])//接口请求
// 获取申报流程
const getAuthFlow = () => {
    authFlow().then((res: any) => {
        if (res.code === 200) {
            res.data.forEach((item: any, index: number) => {
                let tag = (index + 1) % 3
                if (tag === 0) tag = 3
                res.data[index] = { ...res.data[index], ...matchImage(tag) }
            })
            flowList.value = res.data
        } else {
            showFailToast(res.message)
        }
    })
}
const matchImage = (index: number) => {
    let background = ''
    let icon = ''
    let color = ''
    switch (index) {
        case 1:
            background = flow_bg_country
            icon = more_country
            color = '#973500'
            break;
        case 2:
            background = flow_bg_province
            icon = more_province
            color = '#016EBA'
            break;
        case 3:
            background = flow_bg_city
            icon = more_city
            color = '#6C48C2'
            break
    }
    return { background, icon, color }
}

// ====end====
// 页面跳转
const router = useRouter();
const toPage = (item: Recordable) => {
    let obj = flowList.value.find((it: any) => item.title === it.title)
    router.push({
        path: '/workerCraftsman/subject/applyFlow',
        query: {
            title: item.title,
            url: obj?.flowChart,
        }
    })
}
</script>

<style scoped lang="scss"></style>