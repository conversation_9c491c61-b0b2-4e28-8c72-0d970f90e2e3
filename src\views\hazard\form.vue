<template>
  <div class="hazard-form">
    <div class="hazard-form__title relative z-1">
      <img src="@/assets/hazard/form/default_banner.png" alt="" class="w-full" />
    </div>
    <div class="form bg-#fff -mt-40px relative z-10 py-40px px-20px">
      <div class="title flex items-center">
        <img src="@/assets/hazard/form/icon_info.png" alt="" class="w-50px h-50px" />
        <div class="title-text text-34px text-#5AA4FF ml-15px">举报信息</div>
      </div>
      <van-form ref="formRef" label-align="left" @submit="onSubmit">
        <van-field v-model="form.userName" name="userName" label="举报人姓名" placeholder="请输入您的姓名"
          :rules="[{ required: true, message: '请输入您的姓名' }]" required />
      </van-form>
    </div>
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'hazardForm',
})
//判断新增还是查看详情

const form = reactive({
  userName: '',
})
const onSubmit = () => {
  console.log('submit')
}
</script>
<style scoped lang="scss">
.form {
  border-radius: 20px 20px 0px 0px;

  :deep(.van-field__label) {
    font-weight: 500;
    font-size: 30px;
    color: #333333;
    margin-bottom: 27px;
  }

  :deep(.van-field__body) {
    font-size: 30px;
  }

  :deep(.van-cell) {
    padding: 0px 0px 27px 0px;

  }
}
</style>