export default [
  {
    path: '/joinUnion',
    name: 'JoinUnio<PERSON>',
    component: () => import('@/views/joinUnion/index.vue'),
    meta: {
      title: '一键入会',
      isShowTabBar: false,
      isBack: false,
    },
  },
  {
    path: '/join',
    name: 'Join',
    component: () => import('@/views/joinUnion/join.vue'),
    meta: {
      title: '一键入会',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/joinHistory',
    name: 'JoinHistory',
    component: () => import('@/views/joinUnion/joinHistory/index.vue'),
    meta: {
      title: '申请记录',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/joinHistoryDetail',
    name: 'JoinHistoryDetail',
    component: () => import('@/views/joinUnion/joinHistory/joinHistoryDetail.vue'),
    meta: {
      title: '申请记录详情',
      isShowTabBar: false,
      isBack: true,
    },
  },
];
