<template>
    <!-- 消息提示弹窗 -->
    <van-popup v-model:show="props.show" position="center" round class="popup box-border" @click-overlay="close">
        <div class="popup_content w-65% text-center relative box-border mx-auto" @click="close">
            <img loading="lazy" src="@/assets/tree/tips_popup.png" class="w-full">
            <div class="text_content absolute top-23% bottom-0 left-0% right-0% flex flex-col overflow-scroll">
                <div class="title text-[#343432] font-bold text-2.2vh">{{ props.tipsTitle }}</div>
                <div class="tips_text text-[#486B00] text-2vh mt-5% mx-10% leading-3vh 
                flex-1 flex items-center flex-col justify-center" v-html="props.tipsContent">
                </div>
                <div class="my-10%" @click.stop="confirm">
                    <img loading="lazy" src="@/assets/tree/tips_btn.png" class="w-60%" />
                </div>
            </div>
        </div>
    </van-popup>
</template>
<script lang="ts" setup>
const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    tipsTitle: {
        type: String,
        default: '温馨提示',
    },
    tipsContent: {
        type: String,
        default: '树苗陆续发放中，敬请期待！'
    }
});
const emit = defineEmits(['update:show']);
const confirm = () => {
    emit('update:show', false);
};
const close = () => {
    emit('update:show', false);
};
</script>

<style scoped lang="scss">
.van-popup {
    background: transparent;
    width: fit-content;
    height: fit-content;
}
</style>