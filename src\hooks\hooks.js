import './hooks.css'
let timer = true
export function usePopup({ state, userInfo, message }) {
  const app = document.querySelector('#app')
  if (state) {
    const dom = document.createElement('div')
    dom.setAttribute('class', 'timetips-container')
    dom.innerHTML = `
    <div class="timetips-body">
        <div>尊敬的${userInfo.nickname.slice(0, 18) || '川工之家'}会员</div>
        <div class="timetips-message">${message}</div>
        <div>感谢您的支持</div>
        <div id="iknow-btn"></div>
    </div>
    `
    app?.appendChild(dom)
    document.querySelector('#iknow-btn').onclick = () => {
      if (timer) {
        usePopup({ state: false })
      }
    }
  } else {
    const dom = document.querySelector('.timetips-container')
    dom.style.opacity = '0'
    timer = false
    setTimeout(() => {
      if (dom && app) {
        app.removeChild(dom)
      }
      timer = true
    }, 300)
  }
}
