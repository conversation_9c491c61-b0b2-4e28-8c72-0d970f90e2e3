<template>
  <div class="fixed right-[20px] top-[200px]" ref="phoneRef" :class="$style.phone">
    <van-floating-bubble v-model:offset="offset" axis="xy" magnetic="x" :icon="iconPhone" class="floating-bubble-phone"
      :gap="5" @click="showCenter = true" />

    <van-popup v-model:show="showCenter" round :style="{ padding: '64px' }" :close-on-click-overlay="false"
      class="phone-popup">
      <h3 class="text-[#333333] text-[46px] pt-[120px]">{{ phone }}</h3>
      <div class="flex justify-center items-center">
        <div
          class="w-[200px] border border-[#65AAFF] border-solid h-[78px] rounded-[39px] flex justify-center items-center text-[#65AAFF] mr-[16px]"
          @click="showCenter = false">
          取消
        </div>
        <div class="w-[200px] submit-btn h-[78px] rounded-[39px] flex justify-center items-center text-[#fff]"
          @click="handleCall">
          立即呼出
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script lang="ts" setup>
import iconPhone from '@/assets/soulStation/icon-phone.png';
import { psychologicalHotline } from '@/api/soulStation';
import utils from '@/utils/utils';
const showCenter = ref<boolean>(false);

const phoneRef = ref<HTMLElement | null>(null);
const phone = ref(null)
const offset = ref({ x: -window.innerWidth, y: 400 });
//获取电话
function getPhone() {
  psychologicalHotline({
    pageNum: 1,
    pageSize: 1
  }).then(res => {
    if (res.code == 200) {
      if (res?.data) {
        phone.value = res?.data[0]?.phone
      }

    }
  })
}
function handleCall() {
  // TODO
  utils.getTel({ number: phone.value })
}
onMounted(() => {
  getPhone()
})
</script>

<style lang="less" module>
.phone {
  :global {
    .phone-popup {
      overflow: hidden;
      background-color: transparent;
      background-image: url('@/assets/soulStation/call-bg.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: center;
      width: 560px;
      height: 495px;
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: center;
      padding: unset;

    }

    .submit-btn {
      background: linear-gradient(to bottom, #5AA4FF, #A1CBFF);
    }
  }
}
</style>

<style lang="less">
.floating-bubble-phone {
  background-color: transparent !important;

  .van-icon__image {
    width: 85px;
    height: 106px;
  }
}
</style>
