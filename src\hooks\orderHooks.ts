import {orderCancel,orderConfirm,inclusiveGoodsComment,afterSaleCancel,uploadLogistics} from '@/api/mall/inclusive'
import { showToast, showConfirmDialog, showSuccessToast} from 'vant'
export default function () {
    // 取消订单函数
    const orderCancelFn = async(orderId:any,callback:any) => {
        showConfirmDialog({
            title: '取消订单',
            message: '确定要取消订单吗？',
            confirmButtonText: "确认",
            cancelButtonText: "取消",
            className: "close",
        }).then(async () => {
            const res:any = await orderCancel(orderId)
            if (res?.code == 200) {
                showToast('取消成功')
                callback()
            }
            else showToast(res?.message || '取消失败')
        })
        
    }
    // 确认收货函数
    const orderConfirmFn = (orderId:any,callback:any) => {
        orderConfirm(orderId).then((res:any) => {
            if (res?.code == 200) {
                showSuccessToast('收货成功')
                callback()
            }
            else showToast(res?.message || '确认失败')
        })
    }
    // 提交评价函数
    const submitCommentFn = (params:any,callback:any) => {
        inclusiveGoodsComment(params).then((res:any) => {
            if (res?.code == 200) {
                showSuccessToast('评价成功')
                callback()
            }
            else showToast(res?.message || '评价失败')
        })
    }
    // 取消退款申请
    const cancelAfterSaleFn = (params:any,callback:any) => {
        afterSaleCancel(params).then((res:any) => {
            if (res?.code == 200) {
                showSuccessToast('取消成功')
                callback()
            }
            else showToast(res?.message || '取消失败')
        })
    }

    // 上传物流单号
    const uploadLogisticsFn = (params:any,callback:any) => {
        uploadLogistics(params).then((res:any) => {
            if (res?.code == 200) {
                showSuccessToast('上传成功')
                callback()
            }
            else showToast(res?.message || '上传失败')
        })
    }

    return {
        orderCancelFn,
        orderConfirmFn,
        submitCommentFn,
        cancelAfterSaleFn,
        uploadLogisticsFn
    }
}