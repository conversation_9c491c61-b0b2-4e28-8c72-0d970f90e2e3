import { useAppStore } from '../store/modules/app'
import { computed, onActivated, nextTick } from 'vue'
import { useRoute } from 'vue-router'


export default function () {
  const refresh = (paramsList:any) => {
    const store = useAppStore()
    const refreshApiList = computed(() => store.$state.refreshApiList || [])
    const refreshApiFun = (pageName:any) => {
      //当前缓存页面没有配置刷新接口
      if (!paramsList) return
      //获取该缓存页面中的vuex的api列表
      const current = refreshApiList.value.find(
        (item) => item.pageName.toLocaleUpperCase() == pageName.toLocaleUpperCase()
      )
      // 无需要刷新的api
      if (!current) return
      current.funsName.forEach((funName:any) => {
        //vuex中的当前页面缓存的api集合
        const refitem = paramsList.find((refitem:any) => funName === refitem.name)
        //如果方法存在则刷新
        if (refitem !== undefined) refitem.funsName.forEach((fun:any) => fun())
      })
      nextTick(() => store.removeRefreshApi(pageName))
    }
    // 组件被激活
    onActivated(() => {
      const route = useRoute()
      const pageName = route.name
      refreshApiFun(pageName)
    })
  }

  // 添加刷新api
  const addRefreshList = (apiParams:any) => {
    const store = useAppStore()
    store.addRefreshApi(apiParams)
  }

  // 删除缓存组件
  const removeIncludesList = (pageName:any) => {
      const store = useAppStore()
      store.removeIncludes(pageName)
  }
  // 删除所有缓存组件 
  const removeAllList = () => {
    const store = useAppStore()
    store.removeIncludesAll()
  }
  return {
    refresh,
    addRefreshList,
    removeIncludesList,
    removeAllList
  }
}
