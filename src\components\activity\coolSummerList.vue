<template>
  <div v-for="(item, index) in data" :key="index"
    class="border-b-[1px] pb-30px box-border flex border-b-#EBEBEB border-b-solid mb-30px relative"
    @click="toDetails(item)"
    :style="{ background: bgColor, boxShadow: !septalLine ? '0px 3px 10px 0px rgba(128, 153, 162, 0.1)' : 'none' }"
    :class="septalLine ? 'border-b-[1px] border-b-#EBEBEB border-b-solid ' : 'rounded-20px p-30px box-border'">
    <div class="relative  mr-30px">
      <img loading="lazy" :src="useStore.getPrefix + item.appCover" alt=""
        class="h-220px w-280px rounded-16px cover-img" style="object-fit: cover;" />
      <div class="absolute left-0 top-0 list-status">
        <slot name="status" :item="item"></slot>
      </div>
    </div>
    <div class="item-content flex flex-col justify-between">
      <slot name="subTitle" :item="item">
        <div class="text-30px text-#333  mb-20px truncate w-70%">{{
          item.activityName }}</div>
      </slot>
      <slot name="subContent" :item="item">
        <div class="text-24px text-#999 mb-30px">
          <div class="mb-16px">
            <img loading="lazy" src="@/assets/vitality/volunteerService/icon_bmsj.png" alt=""
              class="w-22px h-22px mr-10px" />
            {{ dayjs(item.activityStartTime).format('YYYY-MM-DD') }} 至 {{
              dayjs(item.activityEndTime).format('YYYY-MM-DD') }}
          </div>
          <div class="mb-10px">
            <img loading="lazy" src="@/assets/interest/icon_num.png" alt="" class="w-22px h-20px mr-10px" />
            {{ item.readCount || 0 }}人参加
          </div>
        </div>
      </slot>
      <slot name="btn" :item="item" style="width: 20%;"></slot>

    </div>

  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs'
import { useUserStore } from "@/store/modules/user";
const useStore = useUserStore();
const props = defineProps({
  data: {
    type: Array,
    default: [],
  },
  bgColor: {
    //item的背景色
    type: String,
    default: "transparent",
  },
  septalLine: {
    //是否有分割线
    type: Boolean,
    default: true,
  },
});
const emit = defineEmits(["toDetails"]);
function toDetails(item: any) {
  emit("toDetails", item);
}
</script>