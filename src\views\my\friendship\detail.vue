<template>
    <div class="myFriendshipAudit px30px py44px pb150px">
        <div class=" bg-#fff rounded-20px box-border px24px pt25px pb55px relative">
            <div class="flex">
                <img loading="lazy" :src="judgeStaticUrl(info.avatar, true)"
                    class="w-160px h-160px rounded-20px font-400 text-28px mr23px" alt="" srcset="">
                <div class="flex-1">
                    <div class="flex items-center mb29px text-28px font-400" v-for="(el, i) in row1">
                        <div class=" text-#666 mr18px">
                            {{ el.name }}:
                        </div>
                        <div class="text-#333">
                            {{ el.value }}
                        </div>
                    </div>
                </div>
            </div>

            <div class="py20px px28px bg-#FFF2F4 text-28px font-400 text-#2B2B2B leading-60px rounded-20px"
                v-if="info.auditStatus == 'refuse' && info.auditRemarks">
                <span class="text-#E72740">驳回原因：</span> {{ info.auditRemarks }}
            </div>
            <img loading="lazy" :src="info.auditStatus == 'pass' ? pass : info.auditStatus == 'wait' ? wait : refuse"
                class="w-128px h-128px absolute right-27px top-40px" alt="" srcset="">

            <div class="flex justify-between absolute bottom-[-60px] w-[calc(100%-50px)] left-50% translate-x-[-50%]">
                <img loading="lazy" src="@/assets/friendShip/audit/left.png" class="h-100px w-auto" alt="" srcset="">
                <img loading="lazy" src="@/assets/friendShip/audit/right.png" class="h-100px w-auto" alt="" srcset="">
            </div>
        </div>
        <div class="bg-#fff rounded-20px box-border px26px pt57px pb38px mt28px">
            <div class="">
                <div class="flex justify-between items-center mb29px font-400 text-28px text-#666"
                    v-for="(el, i) in row2">
                    <div class=" text-#666">
                        {{ el.name }}
                    </div>
                    <div class="text-#333">
                        {{ el.value }}
                    </div>
                </div>
            </div>
            <div class="text-28px">
                <div class=" text-#666 mb-24px">
                    自我介绍
                </div>
                <div class="bg-#f6f7f8 rounded-20px leading-44px text-#444 p28px">
                    {{ info.biography || "" }}
                </div>
            </div>
            <div class="text-28px mt30px">
                <div class=" text-#666 mb-24px">
                    兴趣爱好
                </div>
                <div class="flex flex-wrap">
                    <template v-for="(item, index) in info.singleLabelList" :index="index">
                        <div class="bg-#5AA4Ff rounded-12px text-#fff p12px mr18px mb-20px" v-if="item.labelName">

                            {{ item.labelName }}
                        </div>
                    </template>

                </div>
            </div>
        </div>
        <div class="bg-#fff rounded-20px box-border px26px pt33px pb30px mt28px">
            <div class="font-500 text-#3f95ff text-34px mx-auto flex justify-center items-center">
                <img loading="lazy" src="@/assets/friendship/audit/title.png" alt="" srcset="" class="w-69px h-20px">
                <span class="mx-18px">
                    择偶标准
                </span>
                <img loading="lazy" src="@/assets/friendship/audit/title.png" alt="" srcset="" class="w-69px h-20px">
            </div>
            <div class="">
                <div class="flex justify-between items-center mb29px font-400 text-28px text-#666"
                    v-for="(el, i) in row3">
                    <div class=" text-#666">
                        {{ el.name }}
                    </div>
                    <div class="text-#333">
                        {{ el.value }}
                    </div>
                </div>
            </div>
        </div>
        <div class="bg-#fff rounded-20px box-border px26px pt33px pb36px mt28px">
            <div class="">
                <div class="flex justify-between items-center mb29px font-400 text-28px text-#666">
                    <div class=" text-#666">
                        提交时间
                    </div>
                    <div class="text-#333">
                        {{ info.createTime }}
                    </div>
                </div>
                <div class="flex justify-between items-center font-400 text-28px text-#666"
                    v-if="info.auditStatus != 'wait'">
                    <div class=" text-#666">
                        审核时间
                    </div>
                    <div class="text-#333">
                        {{ info.auditTime }}
                    </div>
                </div>
            </div>
        </div>
        <!--  -->
        <div class="fixed left-0 bottom-0 w-full audit_btn flex items-center p30px justify-center"
            v-if="info.auditStatus == 'wait'">
            <div class="refuse_btn !h-79px !w-280px !rounded-40px mr48px" @click="clickAgreeOrRefuse('refuse')">
                不通过
            </div>
            <div class="agree_btn !h-79px !w-280px !rounded-40px" @click="clickAgreeOrRefuse('pass')">
                通过
            </div>
        </div>
        <popup :showPop="showPop" @close-popup="showPop = false" @submit-popup="clickSubmitPopup"></popup>
    </div>
</template>

<script lang="ts" setup>
import useRefreshFun from '@/hooks/app.ts';
import wait from "@/assets/friendship/audit/wait.png"
import pass from "@/assets/friendship/audit/pass.png"
import refuse from "@/assets/friendship/audit/refuse.png"
import popup from './compontents/popup.vue';
import { singleAudit, findAuditDetail } from "@/api/friendship/audit"
import { useDictionary } from '@/store/modules/dictionary';
import { judgeStaticUrl } from '@/utils/utils';
import { showFailToast, showSuccessToast } from 'vant';
const dictionary = useDictionary()
const route = useRoute()
const router = useRouter()
const row1 = ref([
    { name: "用户姓名", key: 'nickname', value: '' },
    { name: "性别年龄", key: 'gender', key1: 'age', value: '', dictKey: 'gender' },
    { name: "联系电话", key: 'phone', value: '15877492589' }
])

const row2 = ref([
    { name: "身高", key: 'height', unit: 'cm', value: '' },
    { name: "现居住地", key: 'currentAddress', value: '' },
    { name: "学历", key: 'education', dictKey: 'modelEducation', value: '' },
    // { name: "所属单位类别", key: '', value: '' },
    { name: "收入情况", key: 'incomeMonth', dictKey: 'incomeMonth', value: '' },
    {
        name: "购房情况", key: 'houseStatus', dictObj: {
            'n': '已购房',
            'y': '未购房'
        }, value: ''
    },
    {
        name: "购车情况", key: 'carStatus', dictObj: {
            'n': '已购车',
            'y': '未购车'
        }, value: ''
    },
    {
        name: "婚姻状态", key: 'maritalStatus', dictObj: {
            'married': '已婚',
            'unmarried': '未婚'
        }, value: ''
    },
])

const row3 = ref([
    { name: "年龄", key: 'lowerAge', key1: 'upperAge', unit: '岁', value: '' },
    { name: "身高", key: 'lowerHeight', key1: 'upperHeight', unit: 'cm', value: '' },
    { name: "学历", key: 'education', dictKey: 'modelEducation', value: '' },
    { name: "收入情况", key: 'incomeMonth', dictKey: 'incomeMonth', value: '' },
    {
        name: "购房情况", key: 'houseStatus', dictKey: 'selectType', value: ''
    },
    {
        name: "购车情况", key: 'carStatus', dictKey: 'selectType', value: ''
    },
])
const info = ref({})
const formData = ref({
    singleUserId: '',
    auditStatus: '',
    auditRemarks: ''
})

const showPop = ref(false)

onMounted(() => {
    getDetail()
})

// 其他页面触发刷新执行事件
const { addRefreshList } = useRefreshFun()
const refresh = () => {
    addRefreshList({ pageName: 'myFriendship', funsName: ['changePage'] })
}
// 通过按鈕事件
function clickAgreeOrRefuse(status) {
    formData.value.auditStatus = status
    if (status == 'refuse') {
        showPop.value = true
    } else {
        audit()
    }
}

// 审核弹窗提交事件
function clickSubmitPopup({ auditRemarks }) {
    formData.value.auditRemarks = auditRemarks
    audit()
}


function audit() {
    formData.value.singleUserId = info.value.singleUserId
    singleAudit(formData.value).then(res => {
        if (res.code == 200) {
            refresh()
            showSuccessToast("审核成功")
            router.go(-1)
        } else {
            showFailToast(res.message)
        }
        showPop.value = false
    })
}

function getDetail() {
    findAuditDetail({ singleUserId: route.query?.singleUserId }).then(res => {
        if (res.code == 200) {
            info.value = res.data

            row1.value.forEach(item => {
                item.value = info.value[item.key]
                if (item.dictKey && item.dictKey == 'gender') {
                    item.value = dictionary.getDictionaryMap?.[`gender_${info.value[item.key]}`].dictName + "   " + info.value[item.key1] + "岁"
                }
            })

            row2.value.forEach(item => {
                item.value = info.value[item.key]
                if (item.dictKey) {
                    item.value = dictionary.getDictionaryMap?.[`${item.dictKey}_${info.value[item.key]}`]?.dictName || "--"
                }
                if (item.dictObj) {
                    item.value = item.dictObj[info.value[item.key]]
                }
                if (item.unit) {
                    item.value += item.unit
                }
            })

            row3.value.forEach(item => {
                if (info.value?.singleMateChoose) {
                    item.value = info.value?.singleMateChoose[item.key] || "--"
                    if (item.dictKey) {
                        console.log("item", item);
                        item.value = dictionary.getDictionaryMap?.[`${item.dictKey}_${info.value.singleMateChoose[item.key]}`]?.dictName || "--"
                    }
                    if (item.dictObj) {
                        item.value = item.dictObj[info.value.singleMateChoose[item.key]]
                    }

                    if (item.unit) {
                        item.value += item.unit
                    }
                    if (item.key1) {
                        item.value += "-" + info.value.singleMateChoose[item.key1] + item.unit
                    }
                } else {
                    item.value = "--"
                }

            })
        }
    })
}

</script>

<style lang="scss" scoped>
.myFriendshipAudit {
    background-image: linear-gradient(to bottom, #5AA4FF 0, #F6F7F8 260px);

    .audit_btn {
        background: #FFFFFF;
        box-shadow: 0px -2px 10px 0px rgba(183, 192, 204, 0.2);

        >div {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .refuse_btn {
            width: 46%;
            height: 60px;
            background: #FFFFFF;
            border-radius: 30px;
            border: 1px solid #5BA5FF;
            font-weight: 400;
            font-size: 34px;
            color: #4297FF;
        }

        .agree_btn {
            width: 46%;
            height: 60px;
            background: linear-gradient(0deg, #A0CAFF 0%, #5AA3FF 100%);
            border-radius: 30px;
            font-weight: 400;
            font-size: 34px;
            color: #FFFFFF;
        }
    }

}
</style>