<template>
    <div class="rank relative">
        <img loading="lazy" src="@/assets/activity/rank/rank_banner.png" alt="" class="h-250px w-full absolute top-0">
        <div class="content relative bg-#fff p-30px box-border">
            <div class="my-graden bg-#fff rounded-20px mb-15px mt-12px p-34px box-border flex items-center justify-around"
                v-if="user">
                <div class="flex flex-col text-28px text-#F32026">
                    <div>我的</div>
                    <div>成绩</div>
                </div>
                <img loading="lazy"
                    :src="userInfo?.avatar?
                    userInfo?.avatar:
                    userInfo?.gender === '男' ? male :
                    userInfo?.gender === '女' ? female :
                    defaultAvatar"
                    class="avatar w-80px h-80px object-cover" />
                <div class="text-34px text-#333 font-500 ">第{{ user?.userRank }}名</div>
                <div class="text-#666 text-30px ">答对{{ user?.answerCount }}题</div>
            </div>
            <div class="flex items-center pt-35px text-#666666 text-30px mb-35px">
                <div class="w-12% text-center">排名</div>
                <div class="w-48% text-center">姓名</div>
                <div class="w-20% text-center">答题数</div>
                <div class="w-20% text-center">总得分</div>

            </div>
            <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
                <div class="flex items-center py-25px text-#666666 text-30px mb-20px"
                    :class="index == 0 ? 'index1' : index == 1 ? 'index2' : index == 2 ? 'index3' : ''"
                    v-for="(item, index) of Data.rankList" :key="index">
                    <div class="w-12% text-center">
                        <div v-if="index > 2">{{ index + 1 }}</div>
                        <img loading="lazy" :src="index == 0 ? one : index == 1 ? two : three" alt="" v-else class="w-44px h-50px">
                    </div>
                    <div class="w-48% text-center">{{ item.userName }}</div>
                    <div class="w-20% text-center">{{ item.answerCount }}</div>
                    <div class="w-20% text-center">{{ item.totalScore }}</div>
                </div>
            </refreshList>

        </div>
    </div>
</template>
<script lang="ts" setup>
import one from '@/assets/activity/rank/one.png';
import two from '@/assets/activity/rank/two.png';
import three from '@/assets/activity/rank/three.png';
import defaultAvatar from '@/assets/public/head_default.png';
import female from '@/assets/public/female.png';
import male from '@/assets/public/male.png';
import { useUserStore } from '@/store/modules/user';
const useStore = useUserStore();
const userInfo: any = computed(() => useStore.userInfo);
import { recordRank, scoreInfo } from '@/api/activity';
const activityDetail = computed(() => useStore.activityDetail || {});
import refreshList from '@/components/refreshList/index.vue';
//动态列表
const loadMoreRef = ref(null);
const user = ref({})
const Data = ref({
    pageNum: 1,
    rankList: []
})
//获取排行列表
function getRankList() {
    recordRank({
        pageSize: 10,
        pageNum: Data.value.pageNum,
        activityId: activityDetail?.value.activityId
    }).then(res => {
        if (res.code == 200) {
            if (Data.value.pageNum === 1) Data.value.rankList = [];
            Data.value.rankList = Data.value.rankList.concat(res.data);
            //重置刷新状态及 判断是否加载完成
            if (loadMoreRef.value) {
                loadMoreRef.value.onLoadSuc(Data.value.rankList.length, res.total);
            }
        }
    })
}
// 刷新
const onRefreshList = () => {
    Data.value.pageNum = 1;
    getRankList();
};
// 加载更多
const onLoadMore = () => {
    Data.value.pageNum++;
    getRankList();
};
function getScoreInfo() {
    scoreInfo({ activityId: activityDetail?.value.activityId }).then(res => {
        if (res.data) {
            user.value = res.data;
        }
    })
}
onMounted(() => {
    getRankList();
    getScoreInfo();
})
</script>
<style scoped lang="scss">
.rank {
    .content {
        border-radius: 20px 20px 0px 0px;
        margin-top: -10px;
        top: 240px;
        min-height: calc(100vh - 240px);

        .my-graden {
            box-shadow: 0px 0px 9px 0px rgba(112, 22, 17, 0.08);
        }

        .index1 {
            background: linear-gradient(264deg, #FFFFFF 0%, #FFDAA0 100%);
            border-radius: 10px;
        }

        .index2 {
            background: linear-gradient(264deg, #FFFFFF 0%, #D0D3DB 100%);
            border-radius: 10px;
        }

        .index3 {
            background: linear-gradient(264deg, #FFFFFF 0%, #F4D2B8 100%);
            border-radius: 10px;
        }
    }
}
</style>