<template>
  <div class="w-full" :class="$style['worker-voice']">
    <div class="banner w-full h-[318px]" />
    <div class="w-full flex bg-[#fff] rounded-xl relative top-[-40px]">
      <div v-for="item in tabArr" :key="item.value"
        class="w-1/2 relative rounded-lg flex justify-center items-center text-[30px] tab"
        :class="`${active === item.value ? 'tab--active h-[97px]  top-[-20px] font-medium ' : ''} ${item.clazz}`"
        @click="onClickTab(item)">
        <img loading="lazy" :src="item.icon" class="w-[50px] mr-2" />
        <div class="tracking-in-expand">{{ item.title }}</div>
      </div>
    </div>

    <div class="h-[calc(100%-318px-97px)] relative px-[30px]">
      <Transition name="fade" mode="out-in" appear>
        <component :is="Comps[active]" />
      </Transition>
    </div>
  </div>
</template>

<script lang="ts" setup>
import iconWrite from '@/assets/workers-voice/icon-write.png';
import iconInfo from '@/assets/workers-voice/icon-info.png';
import MessageInfo from './messageInfo.vue';
import MessageWrite from './messageWrite.vue';

const active = ref<string>('info');

const Comps: Recordable = {
  info: markRaw(MessageInfo),
  write: markRaw(MessageWrite),
};

const tabArr = [
  { title: '留言信息', value: 'info', clazz: 'message-info slide-in-left', icon: iconInfo },
  { title: '我的留言', value: 'write', clazz: 'message-write', icon: iconWrite },
];

const onClickTab = ({ value }: Recordable) => {
  active.value = value;
};
</script>

<style lang="less" module>
.worker-voice {
  :global {
    .banner {
      background-image: url('@/assets/workers-voice/banner-bg.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    .message-info.tab--active {
      background-image: url('@/assets/workers-voice/change-left.png');
      background-repeat: no-repeat;
      background-size: 100% 97px;
      // -webkit-animation: slide-in-left 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
      // animation: slide-in-left 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    }

    .message-write.tab--active {
      background-image: url('@/assets/workers-voice/change-right.png');
      background-repeat: no-repeat;
      background-size: 100% 97px;
      // -webkit-animation: slide-in-right 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
      // animation: slide-in-right 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    }
  }
}
</style>
