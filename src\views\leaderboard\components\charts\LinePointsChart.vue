<template>
  <div class="echart-box" style="width:100%;height:100%">
    <div ref="echartRef" class="w-full h-full"></div>
  </div>
</template>

<script lang="ts" setup>
// 饼图
import { useECharts } from '@/utils/useECharts'
import { Ref, ref, watch, onMounted } from 'vue'
import * as echarts from 'echarts'
import { getVwSize } from '../../data'
const props = defineProps({
  dataSource: {
    type: Object,
    default: {
      xmonthList: [],
      yGetData: [],
      yUseData: []
    },
  },
})

const echartRef = ref<HTMLDivElement | null>(null)
const { setOptions } = useECharts(echartRef as Ref<HTMLDivElement>)

const initChart = () => {
  let data = { x: [], y1: [], y2: [] }
  data.x = props.dataSource?.xmonthList
  let maxyGetData = 0, maxyUseData = 0

  if (props.dataSource?.yGetData) {
    data.y1 = props.dataSource.yGetData
    maxyGetData = Math.max(...props.dataSource?.yGetData)
    if (maxyGetData >= 10000) data.y1 = props.dataSource.yGetData.map((item: any) => parseFloat((item / 10000).toFixed(2)))
  }
  if (props.dataSource?.yUseData) {
    data.y2 = props.dataSource.yUseData
    maxyUseData = Math.max(...props.dataSource?.yUseData)
    if (maxyUseData >= 10000) data.y2 = props.dataSource.yUseData.map((item: any) => parseFloat((item / 10000).toFixed(2)))
  }


  // 绘制图表
  var option = {
    tooltip: {
      show: true,
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      textStyle: {
        fontSize: getVwSize(22)
      },
      formatter: function (params: any) {
        let result = params[0].name + '<br/>'
        params.forEach((v: any) => {
          let value = v.value;
          if (v.seriesIndex === 0) {
            value = props.dataSource.yGetData[v.dataIndex]
          }
          else if (v.seriesIndex === 1) {
            value = props.dataSource.yUseData[v.dataIndex]
          }
          let name = v.seriesName;
          result += `${name}: ${value}分<br/>`
        })
        return result
      }
    },
    grid: {
      top: '20%',
      left: '0%',
      right: '0%',
      bottom: '0%',
      containLabel: true,
    },
    legend: {
      show: true,
      orient: 'horizontal',
      top: '0%',
      right: '0%',
      itemWidth: getVwSize(20),
      itemHeight: getVwSize(10),
      itemGap: getVwSize(30),
      textStyle: {
        color: '#666666',
        fontSize: getVwSize(24),
      },
    },
    xAxis: [
      {
        type: 'category',
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitArea: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        boundaryGap: true,
        axisLabel: {
          show: true,
          interval: 0,
          rotate: 30,
          fontSize: getVwSize(22),
          lineHeight: getVwSize(22),
          margin: getVwSize(20),
          color: '#999',
        },
        data: data.x,
      },
    ],
    yAxis: [
      {
        name: `新增积分(${maxyGetData >= 10000 ? '万' : ''}分)`,
        nameTextStyle: {
          color: '#999',
          fontSize: getVwSize(22),
        },
        type: 'value',
        min: 0,
        minInterval: 1,
        splitLine: {
          show: true,
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#E6E6E6',
          },
        },
        axisLabel: {
          textStyle: {
            color: '#999',
            fontSize: getVwSize(22)
          },
        },
        axisTick: {
          show: false,
        },
      },
      {
        name: `消耗积分(${maxyUseData >= 10000 ? '万' : ''}分)`,
        nameTextStyle: {
          color: '#999',
          fontSize: getVwSize(22),
          padding: [0, getVwSize(40), 0, 0]
        },
        type: 'value',
        min: 0,
        minInterval: 1,
        splitLine: {
          show: true,
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#E6E6E6',
          },
        },
        axisLabel: {
          textStyle: {
            color: '#999',
            fontSize: getVwSize(22),
          },
        },
        axisTick: {
          show: false,
        },
      },
    ],
    dataZoom: [
      {
        show: false,
        startValue: 0,
        endValue: 6,
      },
      {
        type: 'inside',
        zoomOnMouseWheel: false,// 禁用鼠标滚轮缩放
        moveOnMouseMove: true,// 禁用鼠标拖拽移动
        moveOnMouseWheel: true,
      }
    ],
    series: [
      {
        name: '新增积分',
        type: 'line',
        symbol: 'circle',
        symbolSize: getVwSize(10),
        z: 10,
        label: {
          show: false,
        },
        lineStyle: {
          width: getVwSize(4),
          shadowColor: 'rgba(66, 163, 255, 0.1)',
          shadowBlur: getVwSize(6),
          shadowOffsetY: getVwSize(6)
        },
        itemStyle: {
          normal: {
            color: '#2CB99D',
          },
        },
        data: data.y1,
        yAxisIndex: 0
      },
      {
        name: '消耗积分',
        type: 'bar',
        barWidth: getVwSize(20),
        yAxisIndex: 1,
        z: 2,
        label: {
          show: false,
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [{
            offset: 0,
            color: 'rgba(161, 203, 255, 1)'
          },
          {
            offset: 1,
            color: 'rgba(90, 164, 255, 1)'
          }
          ]),
          borderRadius: getVwSize(16)
        },
        data: data.y2,
      },
    ],
  }

  setOptions(option)
}

onMounted(() => {
  initChart()
})

// 监听dataSource
watch(
  () => props.dataSource,
  (newValue, oldValue) => {
    // console.log('watch', newValue, oldValue);
    initChart()
  },
  { deep: true, immediate: true }
)
</script>
<style scoped lang="scss">
.echart-box {}
</style>
