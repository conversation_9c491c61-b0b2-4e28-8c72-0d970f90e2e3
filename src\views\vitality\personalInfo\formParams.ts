import { type } from "os";

export default[
    {
        name:'用户姓名',
        key:'name',
        itemType:'input',
        type:'text',
        require:true,
        placeholder:'请输入用户姓名',
        value:''
    },
    {
        name:'身份证号',
        key:'idNum',
        itemType:'input',
        type:'text',
        require:true,
        placeholder:'请输入身份证号',
        value:''
    },
    {
        name:'用户性别',
        key:'sex',
        itemType:'switch',
        require:false,
        placeholder:'请选择用户性别',
        value:'2',
        selArr:[
            {dictName:'男',dictCode:'1'},
            {dictName:'女',dictCode:'2'}
        ]
    },
    {
        name:'出生日期',
        key:'date',
        itemType:'date',
        type:'yyyy-MM-dd',
        require:false,
        placeholder:'请选择出生日期',
        value:'',
    },
    {
        name:'联系电话',
        key:'phone',
        itemType:'input',
        type:'number',
        require:true,
        placeholder:'请输入联系电话',
        value:''
    },
    {
        name:'工作单位',
        key:'workUnit',
        itemType:'switch',
        type:'number',
        require:false,
        placeholder:'请输入工作单位',
        value:'1',
        selArr:[
            {dictName:'工会',dictCode:'1'},
            {dictName:'其它',dictCode:'2'}
        ]
    },
    {
        name:'所在区域',
        key:'area',
        itemType:'picker',
        require:false,
        placeholder:'请选择所在区域',
        value:'',
        selArr:[]
    },
    {
        name:'详细地址',
        key:'address',
        itemType:'input',
        type:'text',
        require:false,
        placeholder:'请输入详细地址',
        value:''
    },
    
]
