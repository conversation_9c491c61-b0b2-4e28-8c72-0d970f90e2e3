

export default [
    {
        path: '/integralMall',
        name: 'IntegralMall',
        component: () => import('@/views/integralMall/index.vue'),
        meta: { 
            title: '积分商城',
            isShowTabBar: false,
            isBack:true,
            keepAlive:true,
            updatePath:['/home','/my','/integralTree','/integralMall/integralTask','/integralMall/integralDetails']
        }
    },
    {
        path:'/integralMall/rule',
        name:'integralRule',
        component:() => import('@/views/integralMall/rule.vue'),
        meta:{
            title:'积分规则',
            isShowTabBar: false,
            isBack:true
        }
    },
    {
        path:'/integralMall/integralDetails',
        name:'integralDetails',
        component:() => import('@/views/integralMall/integralDetails.vue'),
        meta:{
            title:'积分明细',
            isShowTabBar: false,
            isBack:true,
            keepAlive:true,
            updatePath:['/integralMall','/my','/integralMall/integralTask']
        }
    },
    {
        path:'/integralMall/activity',
        name:'mallActivity',
        component:() => import('@/views/integralMall/activity.vue'),
        meta:{
            title:'积分活动',
            isShowTabBar: false,
            isBack:true,
            keepAlive:true,
            updatePath:['/integralMall']
        }
    },
    {
        path:'/integralMall/treasureHunt',
        name:'integralTreasure',
        component:() => import('@/views/integralMall/treasureHunt.vue'),
        meta:{
            title:'积分夺宝',
            isShowTabBar: false,
            isBack:true
        }
    },
    // 积分任务
    {
        path:'/integralMall/integralTask',
        name:'integralTask',
        component:() => import('@/views/integralMall/integralTask.vue'),
        meta:{
            title:'积分任务',
            isShowTabBar: false,
            isBack:true
        }
    },
    {
        path:'/integralMall/product/detail',
        name:'integralProduct',
        component:() => import('@/views/integralMall/product/detail.vue'),
        meta:{
            title:'商品详情',
            isShowTabBar: false,
            isBack:true
        }
    },
    {
        path:'/integralMall/order/pay',
        name:'integralOrderPay',
        component:() => import('@/views/integralMall/order/pay.vue'),
        meta:{
            title:'商品支付',
            isShowTabBar: false,
            isBack:true
        }
    },
    {
        path:'/integralMall/order/list',
        name:'integralOrderList',
        component:() => import('@/views/integralMall/order/list.vue'),
        meta:{
            title:'订单列表',
            keepAlive:true,
            isShowTabBar: false,
            isBack:true
        }
    },
    {
        path:'/integralMall/order/detail',
        name:'integralOrderDetail',
        component:() => import('@/views/integralMall/order/detail.vue'),
        meta:{
            title:'订单详情',
            isShowTabBar: false,
            isBack:true
        }
    },
    {
        path:'/integralMall/order/writeOff',
        name:'integralOrderWriteOff',
        component:() => import('@/views/integralMall/order/writeOff.vue'),
        meta:{
            title:'核销详情',
            isShowTabBar: false,
            isBack:true
        }
    },
    // 生日商品专区
    {
        path:'/integralMall/birthdayGoods',
        name:'birthdayGoods',
        component:() => import('@/views/integralMall/birthdayGoods.vue'),
        meta:{
            title:'商品兑换专区',
            isShowTabBar: false,
            isBack:true,
            keepAlive:true,
            updatePath:['/yearGift']
        }
    }
]