<template>
  <div class="w-full relative max-h-100vh" :class="$style['soul-station']">
    <div class="w-full h-[360px] banner-bg"></div>
    <div class="rounded-[16px] w-full p-[30px] rounded-[16px] relative top-[-49px] bg-[#fff]">
      <div class="flex justify-between items-center py-[10px]">
        <div class="text-[#333333] font-medium text-[32px]">解压放松</div>
        <div
          class="border border-1px border-solid border-#5AA4FF w-77px h-40px rounded-20px flex items-center justify-center input-container"
          @click="handleRouter('/searchList')">
          <img loading="lazy" src="@/assets/public/icon_s.png" alt="" class="w-20px h-20px mr-5px" />
          <span class="text-20px text-#999">搜</span>
        </div>
      </div>

      <!-- list -->
      <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
        <newsCell v-for="(item, index) in Data.list" :key="index" :content="item" :showborderBottom="false"></newsCell>
      </refreshList>
    </div>
  </div>
</template>

<script lang="ts" setup>
import refreshList from '@/components/refreshList/index.vue';
import defaultList from '@/assets/soulStation/default-list.png';
import { concat } from 'lodash-es';
import newsCell from "@/components/Cell/newsCell.vue";
const router = useRouter();

import { getCategoryInfo, getNewsList } from '@/api/news/index';
const loadMoreRef = ref();

const Data = ref({
  pageNum: 1,
  pageSize: 10,
  list: [],
  tabData: []
});


function handleRouter(path: string) {
  router.push({ path });
}

//获取栏目
async function getColumn() {
  getCategoryInfo({
    categoryCode: 'jie_ya_fang_song',
    platformType: 30,
  }).then(res => {
    Data.value.tabData = res.data;
    if (Data.value.tabData) {
      getLists();
    }

  });
}
// // 刷新
const onRefreshList = () => {
  Data.value.pageNum = 1
  Data.value.list = [];
  getLists()
}
// 加载更多
const onLoadMore = () => {
  Data.value.pageNum++
  getLists()
}
// //新闻列表
async function getLists() {
  let res = await getNewsList({
    categoryCode: Data.value.tabData?.categoryCode,
    platformType: 30,
    pageNum: Data.value.pageNum,
    pageSize: 10,
  });
  if (Data.value.pageNum === 1) Data.value.list = [];
  Data.value.list = Data.value.list.concat(res.data);
  //重置刷新状态及 判断是否加载完成
  if (loadMoreRef.value) {
    loadMoreRef.value.onLoadSuc(Data.value.list.length, res.total);
  }
}
onMounted(() => {
  getColumn()
})
</script>

<style module lang="less">
.soul-station {
  :global {
    .banner-bg {
      background-image: url('@/assets/soulStation/relax-bg.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }

    .van-cell {
      .van-cell__value {
        display: flex;
        align-items: center;
        text-align: left;
      }

      &::after {
        left: 0;
        right: 0;
        border-color: #eeeeee !important;
      }
    }
  }
}
</style>
