<template>
    <div class="county box-border w-full h-100vh relative overflow-y-scroll">
        <div class="w-100% relative">
            <img loading="lazy" src="@/assets/county/bg_banner.png" alt="" class="w-full h-80%">
            <div class="absolute top-6% w-full">
                <div class="w-100% flex items-center justify-center">
                    <img loading="lazy" src="@/assets/county/title.png" alt="" class="w-75%">
                </div>
                <div class="w-100% flex items-center justify-center mt-6% jz-box">
                    <img loading="lazy" src="@/assets/county/jz.png" alt="" class="w-85% ">
                </div>
            </div>
        </div>
        <div class="county-box absolute top-27% flex w-full pb-200px flex-wrap px-20px box-border">
            <div class="w-33.3% h-223px relative flex flex-col justify-center mb-20px"
                v-for="(item, index) of Data.list" :key="index" @click="toPage(item)">
                <div class="img-bg w-65% h-223px absolute left-1/2 -translate-x-1/2 flex items-center justify-center">
                    <img loading="lazy" :src="useStore.getPrefix + item.cityPicUrlOne" alt=""
                        class="w-100% h-82% rounded-10px">
                </div>
                <div
                    class="name-bg h-60px flex items-center justify-center text-#fff text-30px w-90% absolute left-1/2 -translate-x-1/2 bottom-0">
                    <div class="mt-8px">{{ item.subscribeCityName }}</div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import router from '@/router';
import { findH5XQBannerList } from '@/api/news';
import { useUserStore } from '@/store/modules/user';
const useStore = useUserStore();
const Data = ref({
    list: []
})
function toPage(item) {
    router.push({ path: '/countyDetail', query: { id: item.subscribeCityCode } })
}
//获取列表
function getList() {
    // 有缓存直接取缓存的
    if (useStore?.getCityList) {
        Data.value.list = useStore?.getCityList;
        return
    }
    findH5XQBannerList({
        userId: useStore.getUserInfo?.userId
    }).then(res => {
        if (res.code == 200) {
            Data.value.list = res.data;
        }
    })
}
onMounted(() => {
    getList()
})
</script>
<style lang="scss" scoped>
.county {
    background: url('@/assets/county/county_bg.png') no-repeat;
    // background-size: cover,cover;
    background-size: 100% 100%;
    height: 100vh;

    .img-bg {
        // background: url('@/assets/county/bg.png') no-repeat;
        // background-size: 100% 100%;
    }

    .name-bg {
        background: url('@/assets/county/label.png') no-repeat;
        background-size: 100% 100%;
    }

    // .county-box {
    //     top: calc(29% - 110px);
    // }
}
</style>