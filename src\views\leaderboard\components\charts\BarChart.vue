<template>
  <div class="echart-box" style="width:100%;height:100%">
    <div ref="echartRef" class="w-full h-full"></div>
  </div>
</template>

<script lang="ts" setup>
// 饼图
import { useECharts } from '@/utils/useECharts'
import { Ref, ref, watch, onMounted } from 'vue'
import * as echarts from 'echarts'
import { LabelBg ,getVwSize} from '../../data'

const props = defineProps({
  dataSource: {
    type: Object,
    default: {
      areaNameList: [],
      countByAreaName: [],
    },
  },
})

const echartRef = ref<HTMLDivElement | null>(null)
const { setOptions } = useECharts(echartRef as Ref<HTMLDivElement>)

const initChart = () => {
  let data = { x: [], y: []}
  data.x = props.dataSource?.areaNameList
  data.y = props.dataSource?.countByAreaName

  // 绘制图表
  var option = {
    tooltip: {
      show: true,
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      textStyle: {
        color: '#666',
        fontSize: getVwSize(20),
      },
    },
    grid: {
      top: '15%',
      left: '0%',
      right: '0%',
      bottom: '0%',
      containLabel: true,
    },
    legend: {
      show: false,
      icon: 'circle',
      orient: 'horizontal',
      top: '0%',
      right: '0%',
      itemWidth: getVwSize(20),
      itemHeight: getVwSize(20),
      itemGap: getVwSize(60),

      textStyle: {
        // color: '#FFFFFF'
        color: '#666666',
        fontSize: getVwSize(22),
      },
    },
    xAxis: [
      {
        type: 'category',
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitArea: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        boundaryGap: true,
        axisLabel: {
          show: true,
          interval: 0,
          rotate: 30,
          fontSize: getVwSize(22),
          lineHeight: getVwSize(30),
          margin: getVwSize(20),
          color: '#999',
        },
        data: data.x,
      },
    ],
    yAxis: [
      {
        name: '入驻量(家)',
        nameTextStyle: {
          color: '#666',
          fontSize: getVwSize(20),
          padding: [0, 0, 0, getVwSize(40)],

        },
        type: 'value',
        min: 0,
        minInterval: 1,
        splitLine: {
          show: true,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#E6E6E6',
          },
        },
        axisLabel: {
          textStyle: {
            color: '#666',
            fontSize: getVwSize(20),
          },
        },
        axisTick: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: '数量',//柱子
        type: 'bar',
        barWidth: getVwSize(30),
        label: {
          show: false,
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [{
            offset: 0,
            color: 'rgba(66, 201, 250, 1)'
          },
          {
            offset: 1,
            color: 'rgba(55, 131, 250, 1)'
          }
          ]),
          borderRadius: [0, 0, getVwSize(6), getVwSize(6)]
        },
        data: data.y,
      },
      {
        name: "",
        type: "pictorialBar",
        symbolSize: [getVwSize(28), getVwSize(16)],
        symbolPosition: "end",
        symbolOffset: [0, -getVwSize(6)],
        z: 12,
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [{
            offset: 0,
            color: 'rgba(66, 201, 250, 1)'
          },
          {
            offset: 1,
            color: 'rgba(55, 131, 250, 1)'
          }
          ]) //圆柱顶部颜色
          }
        },
        tooltip: {
          show: false,
        },
        data: data.y,
      }

    ],
  }

  // if (data.x.length > 5) {
  //   option.dataZoom = dataZoom
  // }

  setOptions(option)
}

onMounted(() => {
  initChart()
})

// 监听dataSource
watch(
  () => props.dataSource,
  (newValue, oldValue) => {
    // console.log('watch', newValue, oldValue);
    initChart()
  },
  { deep: true, immediate: true }
)
</script>
<style scoped lang="scss">
.echart-box {}
</style>
