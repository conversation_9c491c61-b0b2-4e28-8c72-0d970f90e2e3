<template>
  <refreshList @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
    <van-cell v-for="(item, index) of Data.listData" class="!flex items-center px-0 w-full">
      <div>
        <img loading="lazy" :src="item.avatar" class="w-[140px] h-[140px] rounded-1/2" />
      </div>
      <div class="flex flex-col justify-between pl-[37px] w-full">
        <div class="flex justify-between items-center">
          <div class="flex">
            <span class="text-[#333333] text-[32px] mr-[22px]"> {{ item.userName || '' }}</span>
            <span class="text-[#333333] text-[24px] truncate w-40%">
              {{ item.affiliatedHospital || '' }}</span>
          </div>
          <div class="border-[#4899FF] border border-solid rounded-[20px] text-[#4899FF] px-[15px]"
            @click="handleAsk(item)">
            问医生
          </div>
        </div>
        <!-- <div class="flex items-center">
          <van-tag
            color="rgba(120, 180, 255, .1)"
            text-color="#4899FF"
            class="mr-[22px]"
          >
            {{ item.hospitalLevel }}
          </van-tag>
          <img loading="lazy"
            :src="belong"
            class="w-[32px] h-[32px] mr-[15px]"
          />
          <span>{{ item.hospital || '' }}</span>
        </div> -->
        <div class="text-[24px] text-[#333333] flex">
          擅长领域：<div class="text-[#666666] truncate max-w-[310px]" v-html="item.expertiseArea || ''"></div>
        </div>
        <div class="text-[24px] text-[#333333]">
          咨询时段：<span class="text-[#666666]">{{ formatTimeWithoutSeconds(item.startTime) }}-{{
            formatTimeWithoutSeconds(item.endTime)
          }}</span>
        </div>
      </div>
    </van-cell>
  </refreshList>
</template>

<script lang="ts" setup>
import refreshList from '@/components/refreshList/index.vue';
import belong from '@/assets/soulStation/belong.png';
import defaultImg from '@/assets/public/head_default.png';
import { psychologicalExpert } from '@/api/soulStation';
import { judgeStaticUrl } from '@/utils/utils';
const router = useRouter();
const Data = ref({
  pageNum: 1,
  listData: [],
});
function formatTimeWithoutSeconds(timeStr) {
  // 正则表达式匹配 "时:分:秒" 并将秒替换为空字符串
  return timeStr?.replace(/(\d{2}):(\d{2}):(\d{2})$/, '$1:$2');
}
//跳转详情
function handleAsk(item) {
  router.push({
    path: '/chatDoctor',
    query: { psychologicalExpertId: item.psychologicalExpertId, showIcon: 1 },
  });
}
// 刷新
const onRefreshList = () => {
  Data.value.pageNum = 1;
  getList();
};
// 加载更多
const onLoadMore = () => {
  Data.value.pageNum++;
  getList();
};
//搜索列表
const loadMoreRef = ref(null);
//获取专家列表
function getList() {
  psychologicalExpert({
    pageNum: Data.value.pageNum,
    pageSize: 10,
    state: true,
  }).then(res => {
    if (res.code == 200) {
      res.data.forEach((item: Recordable) => {
        item.avatar = judgeStaticUrl(item.avatar) || defaultImg;
      });
      if (Data.value.pageNum === 1) Data.value.listData = [];
      Data.value.listData = Data.value.listData.concat(res.data);
      //重置刷新状态及 判断是否加载完成
      if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(Data.value.listData.length, res.total);
      }
    }
  });
}
onMounted(() => {
  getList();
});
</script>

<style lang="less" module></style>
