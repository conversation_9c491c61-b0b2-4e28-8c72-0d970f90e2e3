import { defineStore } from 'pinia';
import { store } from '@/store';

interface VideoState {
    collectionData?: Array<any>;
    zoneData?: Array<any>;
    personalWorks?: any;
    params?: Recordable;
}

export const useVideoStore = defineStore({
    id: 'app-video',
    persist: false,
    state: (): VideoState => ({
        zoneData: [],
        personalWorks: {},
        collectionData: [],
        params: {},
    }),
    getters: {
        getZoneList(state) {
            return state.zoneData || [];
        },
        getPersonalWorks(state) {
            return state.personalWorks || {};
        },
        getCollectionList(state) {
            return state.collectionData || [];
        },
        getParams(state) {
            return state.params || {}
        }
    },
    actions: {
        /**
         * 存储视频
         * @param list 视频列表
         * @param type 存储类型 1:分区列表  2：个人作品  3：收藏列表
         * @param userId 用户id
         */
        setVideoList(list: Array<any> = [], type: 1 | 2 | 3, userId: string = 'default', params: Recordable = {}) {
            if (type === 1) {
                this.zoneData = list;
            }
            if (type === 2) {
                this.personalWorks[userId] = list;
            }
            if (type === 3) {
                this.collectionData = list;
            }
            this.params = params || {};
        },
    },
});

// Need to be used outside the setup
export function useVideoStoreWithOut() {
    return useVideoStore(store);
}
