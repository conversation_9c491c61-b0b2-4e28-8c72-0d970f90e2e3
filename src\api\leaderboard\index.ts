// 首页统计
import { cloudManageHttp,h5Http,dataCenterHttp } from '@/utils/http/axios';

// 工会统计信息
export function getGuildStatistics(params:any) {
  return cloudManageHttp.get({
    url:'/unionAndUserCount/kbByArea',
    params,
  })
}
export function getGuildDetail() {
  return cloudManageHttp.get({
    url:'/unionAndUserCount/kbAllData',
  })
}


// 近一个月数据统计
// 频道积分
export function getMonthIntegral(params:any) {
  return dataCenterHttp.get({
    url:'/customIntegral/dataSummaryMonth',
    params
  })
}
// 思想引领
export function getMonthNews(params:any) {
  return h5Http.get({
    url:'/dataSummary/ideologicalGuidanceStatisticsMonth',
    params
  })
}
// 普惠/工会活动
export function getMonthActivity(params:any) {
  return h5Http.get({
    url:'/dataSummary/activity/index',
    params
  })
}
// end