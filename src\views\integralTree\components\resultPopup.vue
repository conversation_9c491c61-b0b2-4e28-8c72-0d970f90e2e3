<template>
    <!-- 兑换肥料弹窗 -->
    <van-popup v-model:show="props.show" position="center" round class="popup box-border" @click-overlay="close">
        <!-- 兑换商品 -->
        <div class="text-center relative box-border mx-auto w-100%" v-if="exchangeType === '3'" @click="close">
            <img loading="lazy" src="@/assets/tree/friut_popup.png" class="w-full">
            <!-- 商品图片 -->
            <img loading="lazy" :src="useStore.getPrefix + props.resultInfo?.prizeImg"
                class="w-50% absolute top-45% left-50% -translate-50%">

            <div class="text_content absolute top-74% bottom-0 left-0 right-0 flex flex-col items-center">
                <div class="goods_text relative w-full z-1">
                    <!-- 避免描边影响字重 -->
                    <div class="flex-1 text-3.5vh text-[#fff] font-bold absolute 
                    z-2 left-50% top-50% -translate-50%">
                        {{ props.resultInfo?.prizeName }}
                    </div>
                    <!-- 描边 -->
                    <div class="text_stroke flex-1 text-3.5vh text-[#fff] font-bold absolute 
                        z-1 left-50% top-50% -translate-50%">
                        {{ props.resultInfo?.prizeName }}
                    </div>
                </div>
                <div class="mt-11% z-10" @click.stop="confirm">
                    <img loading="lazy" src="@/assets/tree/furit_btn.png" class="w-40%" />
                    <div class="text-[#CD9F81] text-1.3vh">*兑换成功后不支持重新兑换奖励</div>
                </div>
            </div>
        </div>
        <!-- 兑换优惠券 -->
        <div class="text-center relative box-border mx-auto w-85%" v-else-if="exchangeType === '7'">
            <img loading="lazy" src="@/assets/tree/coupons_popup.png" class="w-full" />
            <div class="text_content absolute top-24% bottom-0 left-13% right-10% flex flex-col items-center">
                <div class="flex items-end justify-center -skew-x-10">
                    <!-- <span class="text-discount text-[#FFE955] flex items-end">
                        <span class="num text-4.2vh font-bold leading-3.5vh">8</span>
                        <span class="unit text-2.8vh font-bold">折</span>
                    </span> -->
                    <span class="text-[#fff] text-2.8vh font-bold">{{ props.resultInfo?.couponInfo?.couponName }}</span>
                </div>

                <!-- 优惠券信息 -->
                <div class="text_info mt-22% w-full h-full flex items-center border-box">
                    <div class="left_text pr-1vh flex-1">
                        <div class="">
                            <!-- 折扣券 -->
                            <span class="text-[#DA332C]" v-if="props.resultInfo?.couponInfo?.couponType === 'discount'">
                                <span class="text-3.4vh leading-3.4vh">{{ props.resultInfo?.couponInfo?.discountPercent
                                    }}</span>
                                <span class="text-1.8vh leading-3.4vh">折</span>

                                <div class="text-[#EDACA9] text-1.6vh">折扣券</div>
                            </span>

                            <!-- 满减券 -->
                            <span class="text-[#DA332C]"
                                v-else-if="props.resultInfo?.couponInfo?.couponType === 'fullDecrement'">
                                <span class="text-2vh leading-2.5vh flex items-end flex-wrap justify-center">
                                    满
                                    <span class="text-2.4vh leading-2.5vh">
                                        {{ props.resultInfo?.couponInfo?.amountLimit }}
                                    </span>
                                    减
                                    <span class="text-2.4vh leading-2.5vh">{{
                                        props.resultInfo?.couponInfo?.discountAmount
                                        }}</span>
                                </span>

                                <div class="text-[#EDACA9] text-1.6vh mt-1vh">满减券</div>
                            </span>
                            <!-- 无门槛 -->
                            <span class="text-[#DA332C]"
                                v-else-if="props.resultInfo?.couponInfo?.couponType === 'noLimit'">
                                <span class="text-2.5vh">{{ props.resultInfo?.couponInfo?.discountAmount }}</span>
                                <div class="text-[#EDACA9] text-1.6vh">无门槛券</div>
                            </span>
                        </div>
                    </div>
                    <div class="right_text text-center flex-1">
                        <div class="desc text-[#584844] text-1.5vh">全场满返专项享</div>
                        <div class="tips text-[#604D48] text-1.4vh mt-1vh">特殊商品除外</div>
                    </div>
                </div>

                <!-- 兑换按钮 -->
                <div class="z-10 mt-6vh" @click.stop="confirm">
                    <img loading="lazy" src="@/assets/tree/coupons_btn.png" class="w-72%" />
                    <div class="text-[#B87771] text-1.3vh">*兑换成功后不支持重新兑换奖励</div>
                </div>
            </div>
        </div>

    </van-popup>
</template>
<script lang="ts" setup>
import { useUserStore } from '@/store/modules/user';
import { exchangePrize } from '@/api/integralTree';
import { showLoadingToast, closeToast, showToast } from 'vant';
const useStore = useUserStore();

const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    resultInfo: {
        type: Object,
        default: () => ({}),
        required: true,
    },
    activityId: {
        type: String,
        default: "",
    },

});
const exchangeType = computed(() => {
    return props.resultInfo.prizeType
})
const emit = defineEmits(['update:show', 'refresh']);
let handleClick = false
// 确认执行操作
const confirm = async () => {
    if(handleClick) return;
    handleClick = true
    showLoadingToast({
        message: '奖品兑换中,请耐心等待...',
        forbidClick: true,
        duration: 0
    })
    const { code, message } = await exchangePrize({
        activityId: props.activityId,
        platform: "app",
        prizeInfoId: props.resultInfo.prizeInfoId //奖品id
    });
    closeToast();
    if (code === 200) {
        showToast('兑换成功')
        setTimeout(() => {
            emit('update:show', false);
            emit('refresh', {
                type: 'exchangeResult',
            })
        }, 2000)

    } else {
        showToast(message)
        setTimeout(() => {
            emit('update:show', false)
        }, 2000)
    }
    handleClick = false;
};
// 关闭弹窗
const close = () => {
    emit('update:show', false);
}
</script>

<style scoped lang="scss">
.van-popup {
    background: transparent;
}

.text_stroke {
    font-family: Source Han Sans CN;
    -webkit-text-stroke: 0.5vh #F38900;
    -moz-text-stroke: 0.5vh #F38900;
    -ms-text-stroke: 0.5vh #F38900;
    -o-text-stroke: 0.5vh #F38900;
}

.text_info {
    .left_text {
        border-right: 0.3vh dashed #EB928C;
    }
}
</style>