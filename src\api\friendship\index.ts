
import { h5Http,fileHttp,cloudManageHttp } from '@/utils/http/axios';
import { BasicResponse } from '.';
// 智能推荐
export const getRecommendPerson = (params) => {
    return h5Http.get({
        url:'/singleInfo/smartRecommend',
        params
    });
}
//缘分墙列表
export const getSingleInfoList = (params) => {
    return h5Http.get({
        url: '/singleInfo/h5FindVoList',
        params,
    });
};
//缘分墙-详情
export const queryOthersDetailApi = (params) => {
    return h5Http.get({
        url: '/singleInfo/queryOthersDetail',
        params,
    });
};

// 查询个人档案
export const queryMyDetail = () => {
    return h5Http.get({
        url: '/singleInfo/queryMyDetail',
    })
};

// 关注
export const followUserApi = (params) => {
    return h5Http.get({
        url: '/singleInfo/like',
        params
    });
};
// 查询黑名单列表
export const myBlacklist = (params) => {
    return h5Http.get({
        url: '/singleBlack/myBlacklist',
        params,
    });
};

// 关注列表
export const followListApi = (params) => {
    return h5Http.get({
        url: '/singleInfo/queryLikeList',
        params,
    });
};

// 加入或者删除黑名单
export const addOrCancelBlack = (params) => {
    return h5Http.post({
        url: '/singleBlack/addOrCancelBlack',
        params,
    });
};
// 申请列表
export const applyListApi = (params) => {
    return h5Http.get({
        url: '/singleFriend/queryApplyRecord',
        params,
    });
};

// 动态列表
export const getDynamicsList = (params:any) => {
    return h5Http.get({
        url: '/singlePost/h5FindList',
        params
    });
}
// 个人动态
export const getPersonDynamics = (params:any) => {
    return h5Http.get({
        url: '/singlePost/h5FindMyList',
        params
    });
}

// 申请联系方式
export const applyContact = (params:any) => {
    return h5Http.post({
        url: '/singleFriend/applyFriends',
        params
    })
}
// 删除动态
export const deleteDynamic = (postId:any) => {
    return h5Http.delete({
        url: '/singlePost/removeByPostId?postId='+postId,
        
    })
}
// 申请获取联系方式
export const applyPhonenum = (params:any) => {
    return h5Http.post({
        url: '/singleFriend/applyFriends',
        params
    });
}
// 好友审核
export const friendReview = (params:any) => {
    return h5Http.post({
        url: '/singleFriend/friendReview',
        params
    });
}
// 发评论
export const dynamicSendComment =(params:any) => {
    return h5Http.post({
        url: '/singlePostComment/addComment',
        params
    })
}
// 删除评论
export const deleteComment = (autoId:any) => {
    return h5Http.delete({
        url: '/singlePostComment/removeById?autoId='+autoId,
    })
}
// 修改个人标签
export const updateLabelApi = (params:any) => {
    return h5Http.post({
        url: '/singleInfo/updateLabel',
        params
    })
}
// 消息推送列表
export const queryMessageListApi = (params:any) => {
    return h5Http.get({
        url: '/singleMessage/queryMessageList',
        params
    })
}
// 已读消息
export const readMessageApi = (params:any) => {
    return h5Http.post({
        url: '/singleMessage/read',
        params
    })
}
// 未读消息
export const unreadMessageCountApi = () => {
    return h5Http.get({
        url: '/singleMessage/messageCount', 
    })
}
// 脱单
export const offSingleApi = () => {
    return h5Http.get({
        url: '/singleInfo/offSingle',
      
    })
}
// 资讯列表
export const findCategoryList = (params:any) => {
    return h5Http.get({
        url: '/singleCategory/findVoList',
        params
    })
}


