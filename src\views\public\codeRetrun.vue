<template>
    <div></div>
</template>
<script setup lang="ts">
import { showToast } from 'vant';
import { useRouter, useRoute } from "vue-router";
import { destroyQrCode, signIn } from '@/api/activity';
import { integralGoodsExchangeQrCode } from '@/api/mall/integral'
import { useUserStore } from '@/store/modules/user';
import utils from '@/utils/utils';
const router = useRouter();
const route = useRoute();
const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);
function getCodeContent() {
    let obj = route.query;
    console.log('扫码参数', obj)

    // //扫码入会
    if (obj.QREventCode == 'UNION') {
        router.replace({
            path: '/joinUnion',
            query: {
                data: obj?.QRCode
            }
        })
    }
    //阵地扫码签到
    else if (obj.QREventCode == 'MAP') {
        router.replace({
            path: '/scanQRcode',
            query: {
                data: obj?.QRBusinessParam
            }
        })
    }
    //普惠票券核销
    else if (obj.QREventCode == 'coupon') {
        let params = JSON.parse(obj?.QRBusinessParam)
        destroyQrCode({
            recordId: params?.recordId,
            couponId: params?.couponId
        }).then(res => {
            if (res.code == 200) {
                showToast('核销成功');

            } else {
                showToast(res.message)
            }
            setTimeout(() => {
                utils.citySercive(route.fullPath, '二维码跳转', 'close', 'qrpage');
            }, 1500)
        })

    }
    // 积分商城订单核销
    else if (obj.QREventCode == 'ORDER') {
        // let params = JSON.parse(obj?.QRBusinessParam)
        integralGoodsExchangeQrCode({
            recordId: obj?.QRBusinessParam,
            writeOffUser: userInfo.value?.userId,//核销人 userId
            writeOffUserName: userInfo.value?.nickname,//核销人姓名
            writeOffUserPhone: userInfo.value?.phone//核销人手机号码
        }).then(res => {
            if (res.code == 200) {
                showToast('核销成功');

            } else {
                showToast(res.message)
            }
            // 不确定跳转
            setTimeout(() => {
                utils.citySercive(route.fullPath, '二维码跳转', 'close', 'qrpage');
            }, 1500)
        })

    }
    //活动扫码签到
    else if (obj.QREventCode == 'activity') {
        signIn({ activityId: obj?.QRBusinessParam }).then(res => {
            if (res.code == 200) {
                showToast('签到成功');
            } else {
                showToast(res.message)
            }
            setTimeout(() => {
                utils.citySercive(route.fullPath, '二维码跳转', 'close', 'qrpage');
            }, 1500)
        })
    }
}
onMounted(() => {
    getCodeContent()
})
</script>