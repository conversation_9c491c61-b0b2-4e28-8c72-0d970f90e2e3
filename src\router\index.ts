import { createRouter, createWebHistory } from 'vue-router';
import type { RouteRecordRaw } from 'vue-router'; //路由ts类型
import Layout from '@/layout/layout.vue';
import { isApp,isLoginDialog } from '@/utils/utils';
import { useUserStore } from '@/store/modules/user';
import {useAppStore} from '@/store/modules/app'

const modules = import.meta.glob('./modules/**/*.ts', { eager: true });
const routeModuleList: RouteRecordRaw[] = [];
// 加入到路由集合中
Object.keys(modules).forEach(key => {
  const mod = (modules[key] as Recordable).default || {};
  const modList = Array.isArray(mod) ? [...mod] : [mod];

  routeModuleList.push(...modList);
});

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    component: Layout,
    redirect: '/welcome',
    children: [
      {
        path: '/welcome',
        name:'welcome',
        component: () => import('@/views/welcome.vue'),
        meta: {
          isShowTabBar: false,
          isBack: false,
          isWelcome: true,
          keepAlive:true,
          updatePath:['/'],
          preload:true
        },
      },
      {
        path: '/home',
        name:'home',
        component: () => import('@/views/home/<USER>'),
        meta: {
          isShowTabBar: true,
          isBack: false,
          title: '首页',
          isWelcome: true,
          keepAlive:true,
          updatePath:['/welcome'],
          preload:true
        },
      },
      {
        path:'/guide',
        name:'guide',
        component: () => import('@/views/public/guide.vue'),
        meta: {
          isBack: false,
          title: '川工之家-南充频道 指南',
          keepAlive:true
        }
      },
      {
        path:'/updata',
        name:'updata',
        component: () => import('@/views/public/updata.vue'),
        meta: {
          isBack: false,
          title: '川工之家-南充频道',
          keepAlive:true
        }
      },
      {
        path:'/login',
        name:'login',
        component: () => import('@/views/public/login.vue'),
        meta: {
          isBack: false,
          title: '登录',
          keepAlive:false
        }
      },
      ...routeModuleList,
    ],
  },
];

const router = createRouter({
  history: createWebHistory(), //import.meta.env.BASE_URL
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { x: 0, y: 0 } //期望滚动到哪个的位置
    }
  },
});

window.h5LocationBackReturn = h5LocationBackReturn;
function h5LocationBackReturn(top: number) {
  if (top == 0) {
    router.go(-1);
  } else {
    router.replace('/home');
  }
}
// App登录返回处理
window.h5UserLoginReturn = h5UserLoginReturn;
function h5UserLoginReturn(code:any, token:string) {
  if (code == 200) {
    useUserStore()?.login({token})
  }
}


//判断是否含有用户信息，增加游客访问白名单限制
const whiteList = ['/my','/integralMall','/friendship/activity','/interest','/digitalSchool',
  '/soulStation','/joinHistory','/integralTree','/yearGift','/groupDetail','/workerCraftsman/auth/form','/groupList','/video-personalCenter','/activityHome/myWorks'];
router.beforeEach((to, from, next) => {
  const store = useAppStore();
  if(from.name=='detail'&&to.name=='activity'){
    to.meta.isShowTabBar=false;
    to.meta.isBack=true;
  }
  if(to.name=='activity'||to.name=='home'||to.name=='inclusive'||to.name=='countyDetail'){
    window.scrollTo(0, 0);
  }
  // 存储缓存的页面
  if (to.meta.keepAlive && to.name) {
    store.setincludes({ name: to.name })
  }
  if ((to.meta.updatePath as string[] | undefined)?.includes(from.path)) {
    store.removeIncludes(to.name as string)
    requestAnimationFrame(() => {
      store.setincludes({ name: to.name })
    })
  }
  //特殊处理 跳转页面的地方不是活动，或者是优惠券详情页，地址选择页面  暂不需要
  // if(to.path.indexOf('/activityHome') < 0 && !['/couponRecordDetail','/address'].includes(to.path)) {
  //   store.removeIncludes('activityHome')
  // }
  let token= useUserStore()?.getUserInfo?.userId;
  if (token) {
     window.document.title = (to.meta.title as string) || '南充频道';
     next()
  }
  // 不是白名单判断是否登录

  else if(whiteList.indexOf(to.path) !== -1){
    isLoginDialog(()=>{})
  }
  else {
    window.document.title = (to.meta.title as string) || '南充频道';
    next()
  }
});


let position = window.history.state.position
let delta = 0 // delta > 0: 前进，delta < 0: 后退
router.afterEach((to,from)=>{
    // 在路由切换之后执行的逻辑
    const nextPosition = window.history.state.position
    delta = nextPosition - position
    position = nextPosition
    if (isApp()) {
      if (delta > 0) {
        //  前进
        let param:any = {
          name: router.currentRoute.value.meta.title,
          url: router.currentRoute.value.fullPath,
          action: 'jump',
          win: 'public_nav'
        };
        try {  
          api.sendEvent({
            name: 'h5citysub',
            extra: param,
          });
        } catch(error) {
          console.log(JSON.stringify(error),'h5citysub error');
        }
        
      } else if(delta < 0) {
        //  后退
      }    
    }
})


export default router;
