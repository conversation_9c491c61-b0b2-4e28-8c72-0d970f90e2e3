
a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}
body {
  margin: 0;
  padding: 0;
}
/* 全局滚动条样式 */
.scrollable {
  -webkit-overflow-scrolling: touch;
}

::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(50, 50, 50, 0.3);
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(50, 50, 50, 0.6);
}

::-webkit-scrollbar-track {
  background-color: rgba(50, 50, 50, 0.1);
}

::-webkit-scrollbar-track:hover {
  background-color: rgba(50, 50, 50, 0.2);
}
@supports (bottom: env(safe-area-inset-bottom)){
  #app {
      padding-bottom: constant(safe-area-inset-bottom);
      padding-bottom: env(safe-area-inset-bottom);
  }
}