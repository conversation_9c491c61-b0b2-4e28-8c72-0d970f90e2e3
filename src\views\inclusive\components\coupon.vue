<template>

    <div class="coupons">
        <van-popup v-model:show="props.show" position="bottom" @click-overlay="close">
            <div class="coupons-content pb-50px">
                <div class="coupons-header text-34px text-#333 h-104px 
                flex items-center justify-center">优惠
                </div>
                <div class="coupons-list mt-37px">
                    <!-- style="filter:grayscale(100%);opacity: 0.65;" -->
                    <div v-for="item in 3" :key="item"
                        class="coupons-item bg-#FFF0F0 mx-30px flex justify-between px-38px py-30px mb-30px rounded-20px">
                        <div class="coupons-item-left text-#FF4344 flex">
                            <div class="coupons-item-left-price text-40px">￥<span class="text-60px font-blod">10</span>
                            </div>
                            <div class="coupons-item-left-info ml-54px">
                                <div class="coupons-item-left-info-title text-28px">无门槛普惠券</div>
                                <div class="coupons-item-left-info-time text-20px">距离到期仅剩3天</div>
                            </div>
                        </div>
                        <div class="coupons-item-right">
                            <div class="coupons-item-right-btn text-30px bg-#FF4344 
                                w-150px h-60px rounded-12px text-#fff flex items-center justify-center">
                                立即使用
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </van-popup>
    </div>

</template>
<script lang="ts" setup>
const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    }
})
const emit = defineEmits(["update:show"]);
const close = () => {
    emit("update:show", false);
};
</script>
<style scoped lang="scss">
.coupons-content {
    border-radius: 20px 20px 0px 0px;

    .coupons-header {
        border-bottom: 1px solid #EBEBEB;
    }
}
</style>