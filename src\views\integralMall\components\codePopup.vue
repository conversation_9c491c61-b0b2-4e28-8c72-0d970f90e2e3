<template>
    <van-popup v-model:show="props.show" position="center" @click-overlay="close">
        <div class="popup-content relative ">
            <img loading="lazy" src="@/assets/public/code_bg.png" class="w-100%" />
            <img loading="lazy" :src="url" class="absolute w-80% top-50% 
            left-50% right-50% -translate-50%" />
        </div>
    </van-popup>
</template>
<script setup lang="ts">
const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    url: {
        type: String,
        default: '',
        required: true
    },
})
const emit = defineEmits(['update:show'])
const close = () => {
    emit('update:show', false)
}
</script>
<style lang="scss" scoped>
.van-popup {
    background: transparent;
    width: 80%;
}
</style>