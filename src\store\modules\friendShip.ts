import { defineStore } from 'pinia';
import { store } from '@/store';

interface FriendShipStore {
    dynamicsPageNum: number,
    MyFollowed: any[],
}

  
export const useFriendShipStore = defineStore({
    id: 'app-firendship',
    state: ():FriendShipStore => ({
        dynamicsPageNum: 0,
        MyFollowed:[],//我的关注
    }),
    getters: {
        getDynamicsPageNum: (state) => state.dynamicsPageNum,
        getMyFollowed: (state) => state.MyFollowed,
    },
    actions: {
        setdynamicPageNum(page:number) {
            this.dynamicsPageNum = page;
        },
       setMyFollowed(list:any[]) {
            this.MyFollowed = list;
        },
    },
})
export function useMallStoreWithOut() {
    return useFriendShipStore(store);
}