export default [
  {
    path: '/soulStation',
    name: 'SoulStation',
    component: () => import('@/views/soulStation/index.vue'),
    meta: {
      title: '心灵驿站',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/psychologicalCounseling',
    name: 'PsychologicalCounseling',
    component: () => import('@/views/soulStation/counseling.vue'),
    meta: {
      title: '心理专家',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/relaxUnwind',
    name: 'RelaxUnwind',
    component: () => import('@/views/soulStation/relaxUnwind.vue'),
    meta: {
      title: '解压放松',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/chatDoctor',
    name: 'ChatDoctor',
    component: () => import('@/views/soulStation/chat.vue'),
    meta: {
      title: '咨询医生',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/expertDoctor',
    name: 'expertDoctor',
    component: () => import('@/views/soulStation/expertAnswe.vue'),
    meta: {
      title: '专家列表',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/DoctorAnswer',
    name: 'DoctorAnswer',
    component: () => import('@/views/soulStation/doctorAnswer.vue'),
    meta: {
      title: '医生回答',
      isShowTabBar: false,
      isBack: true,
    },
  },
];
