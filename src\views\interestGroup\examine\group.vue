<template>
  <div class="bg-[#f6f7f8] min-h-[100vh] p-[30px] box-border">
    <interestGroupList :data="Data.groupList" @details="toDatails"/>
    <!-- <div class="bg-[#fff] rounded-[20px] px-[21px] pt-[26px] pb-[1px] box-border mt-[20px]">
        <div v-for="(item,index) in Data.applyInfoList" :key="index" >
            <div class="flex items-center  text-[28px] mb-[25px]" v-if="item.type==='text'">
              <div class="text-[#666]">{{item.name}}</div>
              <div class="text-[#333] pl-12px">{{item.value=='wait'?'待审核':item.value=='pass'?'审核通过':item.value=='refuse'?'审核不通过':item.value?item.value:'--'}}</div>
            </div>
            <div class="flex flex-col text-[28px]" v-else-if="item.type==='textarea'">
              <div class="text-[#666]">{{item.name}}</div>
              <div class="text-[#333] bg-[#F6F7F8] p-[28px] box-border mt-[35px] rounded-[10px]">{{item.value}}</div>
            </div>
        </div> -->
      <!-- </div> -->
    <!-- </div> -->

    
    
  </div>
</template>
    <script lang="ts" setup>
import { useRoute } from "vue-router";
const route = useRoute();
import interestGroupList from "@/components/List/interestGroupList.vue";
import detailImg from "@/assets/interest/banner.png";
import router from "@/router";
import { getAuditDetails,detailByGroupId,groupAudit } from "@/api/interestGroup";
import { showFailToast } from "vant";

const Data = ref({
  groupList: [],
  detailInfo:[
    {name:'创建人',value:'',type:'text'},
    {name:'小组名称',value:'',type:'text'},
    {name:'最大成员数量限制',value:'',type:'text'},
    {name:'加入要求',value:'',type:'text'},
    {name:'所在地区',value:'',type:'text'},
    {name:'标签',value:'',type:'text'},
    {name:'加入理由',value:'',type:'textarea'},
  ],
  applyInfoList:[
    // 未通过和已通过  加对应审核参数即可
    {name:'申请时间',value:'',type:'text'},
 
  ],
});

//详情
function getDetail() {
  getAuditDetails({autoId:route.query.autoId}).then(res=>{
    if(res.code==200){
      Data.value.groupList=[res.data]
     
    }else{
      showFailToast(res.message)
    }
   
  })
}
function toDatails(item:any) {
  router.push({
    path:route.query.isExamine ==='1'?'/groupExamine/groupDetails?isExamine=1':'/groupExamine/groupDetails',
    query:{
      autoId:route.query.autoId
    }
  });
}
onMounted(()=>{
  getDetail()
})
</script>
    <style lang="scss" scoped>
</style>