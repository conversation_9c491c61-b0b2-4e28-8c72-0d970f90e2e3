import { h5Http } from '@/utils/http/axios';
//获取兴趣小组用户信息
export const getCurrentUserInfo = (params) => {
    return h5Http.get({
        url: '/interestGroupH5/currentUserInfo',
        params
    });
}
//获取用户标签
export const getCurrentUserLabel = () => {
    return h5Http.get({
        url: '/interestGroupLabel/findList',
        params:{
            pageSize:0
        }
    });
}
//用户更新自己的标签
export const interestGroupLabelRelation = (params) => {
    return h5Http.post({
        url: '/interestGroupLabelRelation/saveOrUpdateByDTO',
        params
    });
}
//兴趣小组列表
export const getGroupH5 = (params) => {
    return h5Http.get({
        url: '/interestGroupH5/findGroupList',
        params
    });
}
//加入小组
export const joinGroup = (params) => {
    return h5Http.post({
        url: '/interestGroupH5/joinGroup',
        params
    });
}
//创建小组
export const interestGroupAudit = (params) => {
    return h5Http.post({
        url: '/interestGroupAudit/saveOrUpdateByDTO',
        params
    });
}
//我的小组
export const myGroupList = (params) => {
    return h5Http.get({
        url: '/interestGroupH5/myGroupList',
        params
    });
}
//我的小组信息更新
export const interestGroupUserInfo = (params) => {
    return h5Http.post({
        url: '/interestGroupUserInfo/saveOrUpdateByDTO',
        params
    });
}
//我的小组详情
export const detailByGroupId = (params) => {
    return h5Http.get({
        url: '/interestGroupInfo/detailByGroupId',
        params
    });
}
///动态评论列表
export const commentsList = (params) => {
    return h5Http.get({
        url: '/interestGroupH5/commentsList',
        params
    });
}
///小组成员
export const h5GroupUserList = (params) => {
    return h5Http.get({
        url: '/interestGroupUserInfo/h5GroupUserList',
        params
    });
}
//退出小组
export const exitGroup = (params) => {
    return h5Http.delete({
        url: '/interestGroupUserRelation/exitGroup',
        params
    });
}
//禁言、解除禁言
export const h5UpdateState = (params) => {
    return h5Http.post({
        url: '/interestGroupUserRelation/h5UpdateState',
        params
    });
}
//移除人员
export const h5Remove = (params) => {
    return h5Http.delete({
        url: '/interestGroupUserRelation/h5Remove',
        params
    });
}
//话题列表
export const interestGroupTopic = (params) => {
    return h5Http.get({
        url: '/interestGroupTopic/findList',
        params
    });
}

//发布动态、评论
export const interestGroupComments = (params) => {
    return h5Http.post({
        url: '/interestGroupComments',
        params
    });
}
//审核列表
export const h5FindAuditList = (params) => {
    return h5Http.get({
        url: '/interestGroupH5/h5FindAuditList',
        params
    });
}
//我提交列表
export const h5FindMyApplyList = (params) => {
    return h5Http.get({
        url: '/interestGroupH5/h5FindMyApplyList',
        params
    });
}
//审核
export const groupAudit = (params) => {
    return h5Http.post({
        url: '/interestGroupH5/audit',
        params
    });
}
//创建活动
export const createActivity = (params) => {
    return h5Http.post({
        url: '/interestGroupH5/createActivity',
        params
    });
}
//动态评论点赞
export const CommentsLikeOperate = (params) => {
    return h5Http.post({
        url: '/interestGroupComments/likeOperate',
        params
    });
}
//审核详情
export const getAuditDetails = (params) => {
    return h5Http.get({
        url: '/interestGroupH5/getAuditDetails',
        params
    });
}
//审核详情
export const getactivityInfoDetails = (params) => {
    return h5Http.get({
        url: '/activityInfo/getDetails',
        params
    });
}
//活动评论
export const interestComments = (params) => {
    return h5Http.post({
        url: '/interestGroupComments/saveOrUpdateByDTO',
        params
    });
}
//动态评价详情
export const commentDetail = (params) => {
    return h5Http.get({
        url: '/interestGroupH5/commentDetail',
        params
    });
}
//解散小组
export const interestGroupInfo = (params) => {
    return h5Http.delete({
        url: '/interestGroupInfo?groupId=',
        params
    });
}
