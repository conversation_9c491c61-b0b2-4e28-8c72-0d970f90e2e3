<template>
    <div class="my-ticket p-30px box-border w-full">
        <div class="tab-box">
            <van-tabs v-model:active="Data.tab.active" sticky line-width="20" @click-tab="onClickTab">
                <van-tab :title="item.name" v-for="(item, index) in Data.tab.nav" :key="index"
                title-class="tab-title"></van-tab>
            </van-tabs>
        </div>
        <div class="two-tab-box">
            <van-tabs type="card" v-model:active="Data.twoTab.active" sticky line-width="20" @click-tab="onClickTwoTab">
                <van-tab :title="item.name" v-for="(item, index) in Data.twoTab.nav" :key="index"
                title-class="tab-title"></van-tab>
            </van-tabs>
        </div>
        
    </div>
</template>
<script lang="ts" setup>
const Data=ref({
    tab: {
        active: 0,
        nav: [
            {name: "优惠券",},
            {name: "兑换券",},
        ],
  },
  twoTab:{
    active:0,
    nav:[
        {name: "待使用", ticketStatus:'waitUsed'},
        {name: "快过期",ticketStatus:'expiring'},
        {name: "已失效",ticketStatus:'expired'},
        {name: "已使用",ticketStatus:'used'}
    ]
  },
})
function onClickTab(val:{name:number}){
    Data.value.tab.active = val.name;
}
function onClickTwoTab(val:{name:number}){
  Data.value.twoTab.active = val.name;
}
</script>
<style lang="scss" scoped>
.my-ticket{
    .tab-box {
        :deep(.van-tab){
            padding-bottom: 20px;
        }
    :deep(.van-tabs__line) {
        background: linear-gradient(86deg, #5aa4ff  0%, #c7e0ff 100%);
        border-radius: 3px;
        bottom: 50px;
    }

    :deep(.tab-title) {
        font-weight: 400;
        font-size: 30px;
        color: #808080;
    }

    :deep(.van-tab--active) {
        font-weight: 500;
        font-size: 32px;
        color: #333333;
    }
    }
    .two-tab-box{
        :deep(.van-tabs__nav--card){
            border-radius: 29px !important;
            margin: 0  !important;
            border: none !important;
            box-sizing: border-box;


        }
        :deep(.van-tab--card ){
            border-right: none !important;
        }
        :deep(.van-tab--active){
            border-radius: 29px !important;
            color: #5aa4ff !important;
            background: #EEF6FF !important;

        }
        :deep(.tab-title) {
            font-weight: 400;
            font-size: 26px;
            color: #666666;
        }
    }
}
</style>