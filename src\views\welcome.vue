<template>
  <!-- <div class="nancon-welcome h-100vh w-100 overflow-hidden  bg-#fff">
    <div class="relative">
      <img loading="lazy" :src="loadingBg" class="w-full min-h-full" />
      <div class="absolute top-10% left-1/2 -translate-x-1/2 w-100% h-45% flex items-start justify-center">
        <div class="w-6% mr-5% small-text mt-32%">
        </div>
        <div class="w-13% bigText"></div>
      </div>
    </div>
  </div> -->
</template>
<script lang="ts" setup>
// import loadingBg from '@/assets/public/loading_bg.png';
// import { h5Login } from '@/api/login';
import { useUserStore } from '@/store/modules/user';
import { useDictionary } from '@/store/modules/dictionary';
const dictionary = useDictionary()
import utils from '@/utils/utils';
import router from '@/router';
const useStore = useUserStore();
import { asyncAddWebVisitSummary } from '@/api/public';
/**
 * 初
 * JTdiJTIydHlwJTIyJTNhJTIySldUJTIyJTJjJTIyYWxnJTIyJTNhJTIySFMyNTYlMjIlN2Q=.JTdiJTIydGltZXN0YW1wJTIyJTNhMTczMjk2MDMzNzc5MCUyYyUyMnN1YiUyMiUzYSUyMjMwMTFlOTU5YjI1OTRkMjFiMjQ0ZGRlMDFkNGM5NzJlJTIyJTdk.5297d54052b15893f1f7a9b3145eec05b84aedf345df51481a30ff0550f5b8da
 * 义
 * JTdiJTIydHlwJTIyJTNhJTIySldUJTIyJTJjJTIyYWxnJTIyJTNhJTIySFMyNTYlMjIlN2Q=.JTdiJTIydGltZXN0YW1wJTIyJTNhMTczMDUyNzI0NjA5NyUyYyUyMnN1YiUyMiUzYSUyMjU1YmY0ZDM0MTkzZTQwYzliYTA3ZTZjM2I5M2Q1MWNjJTIyJTdk.dfec5bad49f05f125873c87c51dc7ed471538c1965473abd86fe496f41b94028
 * 158****6475
 * JTdiJTIydHlwJTIyJTNhJTIySldUJTIyJTJjJTIyYWxnJTIyJTNhJTIySFMyNTYlMjIlN2Q=.JTdiJTIydGltZXN0YW1wJTIyJTNhMTczMzM2NTA3MzkyNSUyYyUyMnN1YiUyMiUzYSUyMmE1NzBmOGIxYWY2ZjRhYWE5M2NhOThmMWQxNjJmMGJiJTIyJTdk.9e43b69e3fd5335a939ead90100a2583561a5b9bda4d36faff8e772cd3b02dad
 * 初
 * 'JTdiJTIydHlwJTIyJTNhJTIySldUJTIyJTJjJTIyYWxnJTIyJTNhJTIySFMyNTYlMjIlN2Q=.JTdiJTIydGltZXN0YW1wJTIyJTNhMTczMzI3NjQ4NzU3NCUyYyUyMnN1YiUyMiUzYSUyMmE1NzBmOGIxYWY2ZjRhYWE5M2NhOThmMWQxNjJmMGJiJTIyJTdk.bfe13ab5112e5f77ec62571527578e42f8a737ff7f780d52570294204cade119';
 * JTdiJTIydHlwJTIyJTNhJTIySldUJTIyJTJjJTIyYWxnJTIyJTNhJTIySFMyNTYlMjIlN2Q=.JTdiJTIydGltZXN0YW1wJTIyJTNhMTczMzM2NTA3MzkyNSUyYyUyMnN1YiUyMiUzYSUyMmE1NzBmOGIxYWY2ZjRhYWE5M2NhOThmMWQxNjJmMGJiJTIyJTdk.9e43b69e3fd5335a939ead90100a2583561a5b9bda4d36faff8e772cd3b02dad
 * JTdiJTIydHlwJTIyJTNhJTIySldUJTIyJTJjJTIyYWxnJTIyJTNhJTIySFMyNTYlMjIlN2Q=.JTdiJTIydGltZXN0YW1wJTIyJTNhMTczMzczODI2MTE5NiUyYyUyMnN1YiUyMiUzYSUyMjBjMDc1MzBjZTZmMTRiYjJhYjQ1NWVjOTA0NWEzOTMxJTIyJTdk.f83e67c40ff616af76eb2c143295b4e7bd38a8762250945cab3c8a6307d08971
 * */
const token =
  'JTdiJTIydHlwJTIyJTNhJTIySldUJTIyJTJjJTIyYWxnJTIyJTNhJTIySFMyNTYlMjIlN2Q=.JTdiJTIydGltZXN0YW1wJTIyJTNhMTczMzM2NTA3MzkyNSUyYyUyMnN1YiUyMiUzYSUyMmE1NzBmOGIxYWY2ZjRhYWE5M2NhOThmMWQxNjJmMGJiJTIyJTdk.9e43b69e3fd5335a939ead90100a2583561a5b9bda4d36faff8e772cd3b02dad';
async function getH5Login() {
  useStore.login({ token: utils.getUrlKey('token') ? utils.getUrlKey('token') : '' })
  dictionary.getQueryDictionary()
  // const res = await h5Login({
  //   token: utils.getUrlKey('token') ? utils.getUrlKey('token') : '',
  // })
  // useStore.setUserInfo(res.data);
  // // 如果是干部 获取干部信息
  // if (res.data?.cadreInfo?.id) {
  //   useStore.getCadreInfoAction({
  //     id: res.data?.cadreInfo?.id
  //   })
  // }
  // setTimeout(() => {
  //   if (useStore.getUserInfo?.userId) {
  //     getBannerList()
  //   }
  // }, 1000)
}


//新增访问量
function getAddWebVisitSummary() {
  asyncAddWebVisitSummary({ systemTypeCode: 'h5', companyId: '6650f8e054af46e7a415be50597a99d5' }).then(res => { })
}
onBeforeMount(() => {
  useStore.setCityData({})
  useStore.resetState();//清除用户信息
  getH5Login()
  getAddWebVisitSummary();
  useStore.setPrefix();
  router.replace('/home')
});
</script>
<style lang="scss" scoped>
.bigText {
  height: 100%;
  opacity: 0;
  background-image: url('@/assets/public/loading_big_text.png');
  background-size: 100% auto;
  background-repeat: no-repeat;
  background-position: center top;
  animation: expandFolding .4s forwards;
  animation-delay: .1s;
}

.small-text {
  opacity: 0;
  height: 100%;
  background-image: url('@/assets/public/loading_small_text.png');
  background-size: 100% auto;
  background-repeat: no-repeat;
  background-position: center top;
  animation-delay: .6s;
  animation-name: expandFolding;
  animation-duration: .4s;
  animation-fill-mode: forwards;

}

// 展开动画
@keyframes expandFolding {
  to {
    opacity: 1;
  }
}
</style>
