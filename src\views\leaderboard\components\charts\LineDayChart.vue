<template>
  <div class="echart-box" style="width:100%;height:100%">
    <div ref="echartRef" class="w-full h-full"></div>
  </div>
</template>

<script lang="ts" setup>
// 饼图
import { useECharts } from '@/utils/useECharts'
import { Ref, ref, watch, onMounted } from 'vue'
import { getVwSize, dataZoomIndexSet } from '../../data'
const props = defineProps({
  dataSource: {
    type: Object,
    default: {
      columnList: [],
      joinCountList: [],//参与量
      lotteryCountList: [],//抽奖
      readCountList: [],//访问量
      vieAnswerCountList: []//答题次数
    },
  }, //color
})

const echartRef = ref<HTMLDivElement | null>(null)
const { setOptions } = useECharts(echartRef as Ref<HTMLDivElement>)

const initChart = () => {
  // 获取当前日期 -设置默认滚动位置到当前日期
  let data = { x: [], y1: <any[]>[], y2: <any[]>[], y3: <any[]>[], y4: <any[]>[] }
  data.x = props.dataSource?.columnList
  const { startValue, endValue } = dataZoomIndexSet(data.x, 5)
  // end
  let maxNumRead = 0, maxNumJoin = 0, maxNumLottery = 0, maxNumAnswer = 0
  let isFormated = false//Y轴1


  if (props.dataSource?.readCountList) {
    maxNumRead = Math.max(...props.dataSource?.readCountList)
    data.y1 = props.dataSource?.readCountList
  }
  if (props.dataSource?.vieAnswerCountList) {
    maxNumAnswer = Math.max(...props.dataSource?.vieAnswerCountList)
    data.y3 = props.dataSource?.vieAnswerCountList
  }
  if (props.dataSource?.lotteryCountList) {
    maxNumLottery = Math.max(...props.dataSource?.lotteryCountList)
    data.y4 = props.dataSource?.lotteryCountList
  }
  // 第二个Y轴
  if (props.dataSource?.joinCountList) {
    maxNumJoin = Math.max(...props.dataSource?.joinCountList)
    data.y2 = props.dataSource?.joinCountList
  }
  // 第一个Y轴
  if ([maxNumRead, maxNumLottery, maxNumAnswer].some(item => item >= 10000)) {
    isFormated = true
    data.y1 = data.y1.map((item: any) => parseFloat((item / 10000).toFixed(2)))
    data.y2 = data.y2.map((item: any) => parseFloat((item / 10000).toFixed(2)))
    data.y3 = data.y3.map((item: any) => parseFloat((item / 10000).toFixed(2)))
    data.y4 = data.y4.map((item: any) => parseFloat((item / 10000).toFixed(2)))
  }
  if (maxNumJoin >= 10000) {
    data.y2 = data.y2.map((item: any) => parseFloat((item / 10000).toFixed(2)))
  }
  // 绘制图表
  var option = {
    tooltip: {
      show: true,
      trigger: 'axis',
      textStyle: {
        fontSize: getVwSize(20),
      },
      formatter: function (params: any) {
        let result = ''
        if (params?.length) {
          result = params[0].name + '<br/>'
          params.forEach((item: any) => {
            let value = item.value
            if (item.seriesIndex === 0) value = props.dataSource?.readCountList[item.dataIndex];
            else if (item.seriesIndex === 1) value = props.dataSource?.joinCountList[item.dataIndex];
            else if (item.seriesIndex === 2) value = props.dataSource?.vieAnswerCountList[item.dataIndex];
            else if (item.seriesIndex === 3) value = props.dataSource?.lotteryCountList[item.dataIndex];

            result += item.marker + item.seriesName + ': ' + value + '<br/>';
          })
          return result;
        }
        return result
      }
    },
    grid: {
      top: '22%',
      left: '0%',
      right: '3%',
      bottom: '0%',
      containLabel: true,
    },
    legend: {
      show: true,
      icon: 'square',
      orient: 'horizontal',
      top: '0%',
      right: 'center',
      width: '100%',
      itemWidth: getVwSize(20),
      itemHeight: getVwSize(20),
      itemGap: getVwSize(30),
      textStyle: {
        color: '#666666',
        fontSize: getVwSize(22),
      },
    },
    xAxis: [
      {
        type: 'category',
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitArea: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        boundaryGap: true,
        axisLabel: {
          show: true,
          interval: 0,
          rotate: 30,
          fontSize: getVwSize(22),
          lineHeight: getVwSize(30),
          margin: getVwSize(20),
          color: '#999',
        },
        data: data.x,
      },
    ],
    yAxis: [
      {
        name: `人数${isFormated ? '(万)' : ''}`,
        nameTextStyle: {
          color: '#999',
          fontSize: getVwSize(20)

        },
        type: 'value',
        min: 0,
        minInterval: 1,
        splitLine: {
          show: true,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#E6E6E6',
          },
        },
        axisLabel: {
          textStyle: {
            color: '#999',
            fontSize: getVwSize(20)
          },
        },
        axisTick: {
          show: false,
        },
      },
      {
        name: `参与人数${maxNumJoin >= 10000 ? '(万)' : ''}`,
        nameTextStyle: {
          color: '#999',
          fontSize: getVwSize(20)
        },
        type: 'value',
        min: 0,
        minInterval: 1,
        splitLine: {
          show: true,
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#E6E6E6',
          },
        },
        axisLabel: {
          textStyle: {
            color: '#999',
            fontSize: getVwSize(20)
          },
        },
        axisTick: {
          show: false,
        },
      },
    ],
    dataZoom: [
      {
        // 设置滚动条的隐藏与显示
        show: false,
        // 设置滚动条类型
        type: 'slider',
        // 设置背景颜色
        backgroundColor: '#4898FB',
        // 设置选中范围的填充颜色
        fillerColor: '#4898FB',
        // 设置边框颜色
        borderColor: '#fff',
        // 是否显示detail，即拖拽时候显示详细数值信息
        showDetail: false,
        // 数据窗口范围的起始数值
        // startValue: 0,
        // 数据窗口范围的结束数值（一页显示多少条数据）
        // endValue: 5,
        startValue,  // 显示最后5条（假设数组长度20）
        endValue,
        handleSize: data.x?.length || 0,// 控制手柄的尺寸
        // empty：当前数据窗口外的数据，被设置为空。
        // 即不会影响其他轴的数据范围
        filterMode: 'empty',
        // 设置滚动条宽度，相对于盒子宽度
        width: '90%',
        // 设置滚动条高度
        height: getVwSize(16),
        // 设置滚动条显示位置
        left: 'center',
        // 是否锁定选择区域（或叫做数据窗口）的大小
        zoomLoxk: true,
        // dataZoom-slider组件离容器下侧的距离
        bottom: getVwSize(30),
      },
      {
        // 没有下面这块的话，只能拖动滚动条，
        // 鼠标滚轮在区域内不能控制外部滚动条
        type: 'inside',
        // 滚轮是否触发缩放
        zoomOnMouseWheel: false,
        // 鼠标滚轮触发滚动
        moveOnMouseMove: true,
        moveOnMouseWheel: true,
      },
    ],
    series: [
      {
        name: '访问量',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        showSymbol: false,
        yAxisIndex: 0,
        z: 1,
        label: {
          show: false,
        },
        lineStyle: {
          width: getVwSize(4),
          shadowColor: 'rgba(48,224,189,0.3)',
          shadowBlur: getVwSize(4),
          shadowOffsetY: getVwSize(6)
        },
        itemStyle: {
          normal: {
            color: '#30E0BD',
          },
        },
        data: data.y1,
      },
      {
        name: '参与人数',
        type: 'line',
        yAxisIndex: 1,
        smooth: true,
        symbol: 'circle',
        showSymbol: false,
        z: 3,
        label: {
          show: false,
          position: 'top',
        },

        lineStyle: {
          width: getVwSize(4),
          shadowColor: 'rgba(255, 111, 76, 0.6)',
          shadowBlur: getVwSize(4),
          shadowOffsetY: getVwSize(4)
        },
        itemStyle: {
          normal: {
            color: '#FF6F4C',
          },
        },
        data: data.y2,
      },
      {
        name: '答题次数',
        type: 'line',
        yAxisIndex: 0,
        smooth: true,
        symbol: 'circle',
        showSymbol: false,
        z: 3,
        label: {
          show: false,
          position: 'top',
        },

        lineStyle: {
          width: getVwSize(4),
          shadowColor: '#FADB89',
          shadowBlur: getVwSize(4),
          shadowOffsetY: getVwSize(4)
        },
        itemStyle: {
          normal: {
            color: '#FFCB40',
          },
        },
        data: data.y3,
      },
      {
        name: '抽奖次数',
        type: 'line',
        yAxisIndex: 0,
        smooth: true,
        symbol: 'circle',
        showSymbol: false,
        z: 3,
        label: {
          show: false,
          position: 'top',
        },

        lineStyle: {
          width: getVwSize(4),
          shadowColor: 'rgba(244,152,250,0.6)',
          shadowBlur: getVwSize(4),
          shadowOffsetY: getVwSize(4)
        },
        itemStyle: {
          normal: {
            color: '#896AFB',
          },
        },
        data: data.y4,
      },

    ],
  }
  setOptions(option)
}

onMounted(() => {
  initChart()
})

// 监听dataSource
watch(
  () => props.dataSource,
  (newValue, oldValue) => {
    // console.log('watch', newValue, oldValue);
    initChart()
  },
  { deep: true, immediate: true }
)
</script>
<style scoped lang="scss">
.echart-box {}
</style>
