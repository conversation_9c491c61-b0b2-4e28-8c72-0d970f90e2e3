<template>
  <div class="vieAnswerActivity w-full  h-100vh relative"
    :style="{ backgroundImage: `url(${activityDetail.appDetailsCover})` }">
    <div class="absolute bottom-190px w-full flex flex-col items-center">
      <div class="w-400px h-100px btn flex justify-center items-center text-[#fff] text-40px pb-10px mb-40px"
        v-if="activityDetail.questionnaireInfo" @click="toSignUp">
        <img loading="lazy" src="@/assets/activity/investigation_icon.png" alt="" class="w-82px mr-10px"> 参与问卷调查
      </div>
      <div class="flex btns w-75/100 justify-around">
        <div class="" @click="toDetail"> <img loading="lazy" src="@/assets/activity/investigation_info.png" alt="">活动详情
        </div>
        <div @click="toLotteryRecord" v-if="activityDetail.luckDraw === 'y'"> <img loading="lazy"
            src="@/assets/activity/record.png" alt="">中奖记录
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { showDialog } from 'vant'

import { useRouter } from 'vue-router'
import { activityValidator, defaultValidator, activityDetailValidator, checkIntegral } from '@/hooks/useValidator'
import { useUserStore } from '@/store/modules/user';
import { questionCount } from '@/api/activity';
import backgroundImg from '@/assets/activity/background.jpg';


const useStore = useUserStore();
const router = useRouter();

// Reactive state
const count = ref(0)

// 计算属性
const activityDetail = computed(() => useStore.activityDetail || {})

watch(() => activityDetail.value, () => {
  init()
})
onMounted(() => {
  init();
})

const init = () => {
  if (activityDetail.value.activityId) {
    activityDetail.value.appDetailsCover = activityDetail.value.appDetailsCover ? activityDetail.value.appDetailsCover : backgroundImg
    checkQuestionCount()
  }
}

// Methods
const toSignUp = async () => {
  if (!activityValidator()) {
    return
  }
  if (count.value !== null && count.value > 0) {
    showDialog({
      title: '温馨提示',
      message: `您已完成本次问卷调查,感谢您的参与~`,
      confirmButtonText: '我知道了',
    })
  } else {
    if (! await checkIntegral()) {
      return;
    }
    router.push('/activityHome/questionnaireActivity/question')
  }
}

const toDetail = () => {
  if (!activityDetailValidator()) return
  router.push('/activityHome/activityDetail')
}

const toLotteryRecord = () => {
  if (!defaultValidator()) return
  router.push('/activityHome/lotteryRecord')
}

const checkQuestionCount = async () => {
  const { data } = await questionCount({
    userId: useStore.userInfo.userId,
    activityId: activityDetail.value.activityId,
  })
  count.value = data
}
</script>

<style lang="scss" scoped>
.vieAnswerActivity {
  background-color: #9bbefe;
  background-size: 100% 100%;

  .btn {
    // background-image: url('@/assets/activity/stater_button.png');
    width: 507px;
    height: 104px;
    background: linear-gradient(-12deg, #EA4E33 0%, #FA897F 100%);
    border-radius: 52px;
    border: 3px solid;
    // border-image: linear-gradient(0deg, #FF984C, #FFE3A6) 10 10;
  }

  .btns {
    >div {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 220px;
      height: 60px;
      font-size: 29px;
      color: #FFFFFF;
      // background: #FFFFFF;
      // background: linear-gradient(-12deg, #EA4E33 0%, #FA897F 100%);
      border-radius: 14px;
      border: 1px solid #FFFFFF;

      img {
        width: 30px;
        margin-right: 5px;
      }
    }
  }
}
</style>
