<template>
  <div class="my-groups-page bg-#f6f7f8">
    <div class="tab-box">
      <van-tabs v-model:active="Data.tab.active" sticky color="#5AA4FF" title-active-color="#5AA4FF"
        title-inactive-color="#333333" line-width="30" @click-tab="onClickTab">
        <van-tab :title="item.name" v-for="(item, index) in Data.tab.nav" :key="index"
          title-class="tab-title"></van-tab>
      </van-tabs>
    </div>
    <div class="px my-30px">
      <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
        <CourseList :data="Data.courseList" bgColor="#fff" :septalLine="false" @toDetails="toDetails">
          <template #subTitle="{ item }">
            <div class="text-30px text-#333 mb-20px text-ellipsis line-clamp-3 whitespace-nowrap text-wrap">{{ item.name
              }}</div>
          </template>
          <template #subContent="{ item }">
            <div class="flex items-center text-#999 text-24px">
              <van-icon name="eye-o" size="16" color="#999" class="mr-15px" />{{ item.clickNum || 0 }}
            </div>
          </template>
          <!-- <template #status="{ item }">
          <div
            class="list-status absolute top-0 bg-#e4d4d2 text-24px text-#999 px-15px py-6px box-border rounded-tr-20px rounded-bl-20px">
            文章
          </div>
        </template> -->
        </CourseList>
      </refreshList>

    </div>
  </div>
</template>
<script lang="ts" setup>
import CourseList from "@/components/activity/coolSummerList.vue";
import router from "@/router";
import { useUserStore } from "@/store/modules/user";
import cover from "@/assets/vitality/volunteerService/cover.jpg";
import icon_zbjs from "@/assets/digitalSchool/icon_zbjs.png";
import { curriculumUserInfoFindVoListH5, curriculumInfoMyCollect } from "@/api/digitalSchools/";
import refreshList from '@/components/refreshList/index.vue';
const useStore = useUserStore()
const Data = ref({
  tab: {
    active: 0,
    nav: [
      { name: "我的课堂", code: "my", api: curriculumUserInfoFindVoListH5 },
      { name: "收藏课堂", code: "collect", api: curriculumInfoMyCollect },
    ],
  },
  courseList: [
  ],
  pageNum: 1
});
const loadMoreRef = ref('')


//课堂列表
function getList() {
  let tabActiveObj = Data.value.tab.nav[Data.value.tab.active]
  tabActiveObj['api']({
    pageNum: Data.value.pageNum,
    pageSize: 10
  }).then(res => {
    if (res.code == 200) {
      if (Data.value.pageNum == 1) {
        Data.value.courseList = res.data || []
      } else {
        Data.value.courseList = Data.value.courseList.concat(res.data);
      }
      Data.value.courseList.forEach(item => {
        item.name = item.curriculumName
        item.appCover = item.curriculumCover
      })
      //重置刷新状态及 判断是否加载完成
      if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(Data.value.courseList.length, res.total);
      }
    }
  })
}

/**
 * 点击状态标签页的处理函数
 *
 * @param item 被点击的标签页对象
 */
function onClickTab(item: any) {
  Data.value.tab.active = item.name;
  Data.value.pageNum = 1;
  if (loadMoreRef.value) loadMoreRef.value.resetStatus();
  onRefreshList();
}

function toDetails(item: any) {
  router.push({
    path: "/digitalSchool/course/courseDetails", query: {
      autoId: item.autoId,
      type: item.catalogueType
    }
  });
}
// 刷新
const onRefreshList = () => {
  Data.value.pageNum = 1;
  getList();
};
// 加载更多
const onLoadMore = () => {
  Data.value.pageNum++;
  getList();
};

onMounted(() => {
  getList()
})
</script>
<style lang="scss" scoped>
.my-groups-page {
  background: #f6f7f8;
  min-height: 100vh;
  padding-bottom: 30px;
  box-sizing: border-box;

  .tab-box {
    :deep(.tab-title) {
      font-weight: 400;
      font-size: 32px;
      color: #333333;
    }

    :deep(.van-tab--active) {
      font-weight: 400;
      font-size: 32px;
      color: #5aa4ff;
    }
  }

  :deep(.cover-img) {
    width: 260px;
    height: 176px;
  }

  :deep(.list-status) {
    right: 0;
  }
}
</style>