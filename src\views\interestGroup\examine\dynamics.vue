<template>
  <div class="bg-[#f6f7f8] min-h-[100vh]">
    <div
      v-for="(item, index) in Data.topicList"
      :key="index"
      class="mb-[20px] bg-[#fff] px-29px box-border pt-[39px] pb-[19px]"
    >
      <topicList :data="[item]" type="examine" />
    </div>
    <div class="px-29px box-border" v-if="route.query.type === 'dtpj'">
      <div class="bg-[#fff] rounded-[20px] p-[22px] box-border">
        <evaluateList :data="Data.evaluateList">
          <template #shortContent="{ item, index }">
            <div class="text-28px mt-20px">{{ item.content }}</div>
          </template>
        </evaluateList>
      </div>
    </div>
    <div class="px-29px box-border mt-20px">
      <div class="bg-[#fff] rounded-[20px] p-[22px] box-border">
        <div class="text-28px">审核状态：{{ Data.evaluateList[0]?.state=='wait'?'待审核':Data.evaluateList[0]?.state=='pass'?'审核通过':'审核不通过' }}</div>
        <div class="text-28px mt-20px" v-if="Data.evaluateList[0] && Data.evaluateList[0].auditOpinion">审核原因：{{ Data.evaluateList[0]?.auditOpinion||'--' }}</div>
      </div>

    </div>
    <div
      class="flex items-center justify-between w-[100%] px-[90px] box-border fixed bottom-[84px]"
      v-if="route.query.isExamine === '1'&&Data.evaluateList[0]?.state=='wait'"
    >
      <van-button
        block  @click.stop="onRefuse(Data.evaluateList[0],'refuse')"
        class="w-[260px] h-[76px] rounded-[39px] justify-center flex items-center text-[30px] text-[#5AA4FF] border-[1px] border-[#5AA4FF] border-solid mr-[30px]"
      >
        拒绝
      </van-button>
      <van-button
        block  @click.stop="onPass(Data.evaluateList[0],'pass')"
        class="w-[260px] h-[78px] rounded-[39px] justify-center flex items-center text-[30px] text-[#fff]"
        style="background: linear-gradient(0deg, #a1cbff 0%, #5aa4ff 100%)"
      >
        通过
      </van-button>
    </div>
    <Popup
      :show="Data.showPop"
      :titleName="Data.titleName"
      :placeHolder="'请输入'+Data.titleName"
      @submit-content="submitContent"
      @close-popup="closePopup"
    />
  </div>
</template>
<script lang="ts" setup>
import { useRoute } from "vue-router";
const route = useRoute();
import evaluateList from "@/components/List/evaluateList.vue";
import topicList from "@/components/List/topicList.vue";
import { getAuditDetails,groupAudit } from "@/api/interestGroup";
import Popup from "@/components/Popup/popup.vue";
import { showToast } from "vant";
const Data = ref({
  topicList: [],
  evaluateList: [],
  showPop: false,
  titleName:'',
  objInfo:{},
});
onMounted(() => {
  getDetail()
});
//获取详情
function getDetail() {
  getAuditDetails({autoId:route.query.autoId}).then(res=>{
    if(res.code==200){
      Data.value.topicList=[res.data];
      Data.value.evaluateList=[res.data.replyInfo ?? res.data];
    }

  })
}
/**
 * 点击拒绝  处理函数
 *
 * @param item 请求项，可以是任意类型的值
 */
 function onRefuse(item: any,type) {
  Data.value.showPop = true;
  item.type=type;
  Data.value.titleName='失败原因'
  Data.value.objInfo=item;

}
//通过
function onPass(item,type) {
  item.type=type;
  Data.value.objInfo=item;
  Data.value.titleName='通过原因'
  Data.value.showPop = true;
}
//确认提交  拒绝
function submitContent(val: string) {
  // if (!val&&Data.value.objInfo.type=='refuse') {
  //   showToast({
  //     message: "请输入拒绝原因",
  //     icon: "none",
  //   });
  //   return;
  // }
  groupAudit({
    autoId:route.query.autoId,
    state:Data.value.objInfo.type,
    auditOpinion:val
  }).then(res=>{
    if(res.code==200){
      showToast("审核成功");
      getDetail()
      Data.value.showPop = false;
    }
  })

}
//关闭拒绝弹窗
function closePopup() {
  Data.value.showPop = false;
}

</script>
<style lang="scss" scoped>
</style>
