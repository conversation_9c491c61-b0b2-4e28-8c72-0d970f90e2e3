<template>
  <div class="video_cell bg-[#fff] w-48% mb-20px">
    <div class="video_cover relative w-full h-410px rounded-xl overflow-hidden">
      <!-- 删除 -->
      <van-icon class="absolute left-22px top-26px" size="18" name="delete" color="#fff" v-if="showDelete"
        @click.stop="emit('deleteItem', content)"></van-icon>
      <!-- 播放图标 -->
      <img loading="lazy" class="play_icon absolute top-26px right-22px w-42px h-42px"
        src="@/assets/video/play_icon.png" />
      <!-- 视频类型 -->
      <img loading="lazy" :src="judgeStaticUrl(content.pictures || content.cover)" class="w-full h-full video_cover" />
      <!-- 直播 -->
      <div class="absolute top-0" v-if="content.dataType == 'LIVEID'">
        <div class="relative text-20px text-#fff text-center top-0 left-0 w-122px leading-5 rounded-ee-lg rounded-ss-lg"
          :style="{
            background:
              content.dataStatus == 'onGoing'
                ? `linear-gradient(#66A8FE,#3A85EC)`
                : `linear-gradient(#A1A4A3,#C5CAC8)`,
          }">
          <!-- 直播结束 -->
          {{
            content.dataStatus == 'notStart'
              ? '直播未开始'
              : content.dataStatus == 'onGoing'
                ? '正在直播'
                : content.dataStatus == 'hasEnd'
                  ? '直播结束'
                  : '直播结束'
          }}
        </div>
      </div>
    </div>
    <div class="content px-10px">
      <div
        class="title text-[#333] font-medium text-28px mt-16px overflow-hidden text-ellipsis line-clamp-2 whitespace-nowrap text-wrap">
        {{ content.title }}
      </div>
      <div class="info_bottom my-14px flex justify-between items-center">
        <div class="user flex items-center">
          <!-- 性别判断 -->
          <img loading="lazy" :src="avatar" class="avatar w-30px h-30px rounded-50% object-cover" />
          <span class="text-#999 text-20px ml-15px">{{ content.userName }}</span>
        </div>
        <div class="looks flex items-center">
          <img loading="lazy" src="@/assets/public/look_icon.png" class="icon w-32px" />
          <span class="text-[#999] text-24px ml-9px">{{ content.viewVolume }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import defaultAvatar from '@/assets/public/head_default.png';
import female from '@/assets/public/female.png';
import male from '@/assets/public/male.png';
import { useUserStore } from '@/store/modules/user';
const emit = defineEmits(['deleteItem']);
const useStore = useUserStore();
import { judgeStaticUrl } from '@/utils/utils';
const props = defineProps({
  content: {
    type: Object,
    default: () => { },
  },
  cellWidth: {
    type: String,
    default: '335px', //具体值/full
  },
  showDelete: {
    type: Boolean,
    default: false,
  },
});
const avatar = computed(() => {
  if (props.content?.userId && props.content?.userId === useStore.getUserInfo?.userId) {
    if (useStore.getUserInfo?.avatar) return judgeStaticUrl(useStore.getUserInfo?.avatar)
    if (useStore.getUserInfo?.gender === '男') return male
    if (useStore.getUserInfo?.gender === '女') return female
    return defaultAvatar
  }
  else return defaultAvatar;
})
</script>
<style scoped lang="scss">
.video_cell {
  border-radius: 0px 0px 10px 10px;

  .video_cover {
    border-radius: 10px 10px 0px 0px;
    object-fit: cover;
  }
}
</style>
