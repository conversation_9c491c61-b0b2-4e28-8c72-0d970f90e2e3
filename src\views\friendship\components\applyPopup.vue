<template>
    <div>
        <van-popup :show="showPop" class="flex" :class="textObj[type].bgClass" position="center"
            :style="{ width: '80%', height: '50%' }">
            <div class="flex flex-col items-center w-full mt-333px">
                <slot name="popupTitle">
                    <div class="font-500 text-#333 text-36px mb-24px ">
                        {{ props.title==''? textObj[type].title: props.title}}
                    </div>
                </slot>
                <slot name="popupMsg">
                    <div class="text-28px leading-47px font-400 text-#666 w-[calc(100%-126px)] text-center max-h-8vh
                    overflow-y-scroll">
                        {{ props.msg }}
                    </div>
                </slot>
                <div class="confirm_btn flex items-center justify-center mt40px" :class="textObj[type].btnClass"
                    @click="close">
                    我知道了
                </div>
            </div>
        </van-popup>
    </div>

</template>
<script lang="ts" setup>
const emit = defineEmits(['closePopup']);
import router from "@/router";
import applySucess from "@/assets/friendship/apply_success.png"
import applyError from "@/assets/friendship/apply_error.png"
const show = ref(false)
const textObj = ref({
    'error': {
        title: '提交失败',
        bgClass: 'bg_error',
        btnClass: 'btn_pink'
    },
    'checkError': {
        title: '审核失败',
        bgClass: 'bg_error',
        btnClass: 'btn_pink'
    },
    'success': {
        title: '提交成功，等待审核',
        bgClass: 'bg_success',
        btnClass: 'btn_blue'

    },
    'editSuccess': {
        title: '修改成功，等待审核',
        bgClass: 'bg_success',
        btnClass: 'btn_blue'
    },
    'wait': {
        title: '审核中...',
        bgClass: 'bg_wait',
        btnClass: 'btn_yellow'
    },
})

const props = defineProps({
    showPop: {
        type: Boolean,
        default: false
    },
    type: {
        type: String,
        default: 'error'
    },
    title:{
            type: String,
        default: ''
    },
    msg: {
        type: String,
        default: '请您仔细阅读并同意签订《单身承诺书》和《单身告知书》'
    }
})
function close() {
    show.value = false;
    emit('closePopup', false)
}

</script>
<style lang="scss" scoped>
.van-overlay {
    background: rgba(0, 0, 0, .4);
}

.van-popup {
    border-radius: 40px;
    background: transparent;

    .confirm_btn {
        width: 342px;
        height: 83px;

        border-radius: 41px;
        font-weight: 500;
        font-size: 34px;
        color: #FFFEFE;
    }

    .btn_pink {
        background: linear-gradient(0deg, #FE779A 0%, #FA9CAB 100%);
    }

    .btn_blue {
        background: linear-gradient(0deg, #4499FF 0%, #91CDFD 100%);
    }
    .btn_yellow{
        background: linear-gradient(0deg, #FF8921 0%, #FBB74D 100%);
    }

}

.bg_success {
    background-image: url("@/assets/friendship/apply_success.png");
    background-size: contain;
    background-repeat: no-repeat;
}
.bg_wait {
    background-image: url("@/assets/friendship/profile/wait_check_poppup.png");
    background-size: contain;
    background-repeat: no-repeat;
}
.bg_error {
    background-image: url("@/assets/friendship/apply_error.png");
    background-size: contain;
    background-repeat: no-repeat;

}
</style>