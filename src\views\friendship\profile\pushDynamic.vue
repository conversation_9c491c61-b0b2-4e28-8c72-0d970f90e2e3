<template>
    <div class=" push_dynamic " :class="route.query?.type == 'edit' ? ' bg-#f2f2f2' : 'bg-#fff'">
        <div class="flex justify-between px30px pt27px " v-if="route.query?.type != 'edit'">
            <div class="text-#333 text-32px font-400" @click="backFn">
                <!-- 取消 -->
            </div>
            <div class="rounded-27px w-145px h-54px flex items-center justify-center text-#fff text-30px font-400"
                :class="formData.content ? 'push_btn' : 'push_btn_gray'" @click="submit">
                <img loading="lazy" :src="formData.content ? iconPush : iconPushGray" alt="" srcset=""
                    class="w-25px mr-10px">
                发布
            </div>
        </div>
        <div class="overflow-auto h-[calc(100vh-146px)]"
            :class="route.query?.type == 'edit' ? 'h-[calc(100vh-166px)]' : ''">
            <van-form ref="formDataRef">
                <van-field v-model="formData.content" placeholder="分享新鲜事..." name="内容"
                    :rules="[{ required: true, message: `请输入内容` }]" input-align="left" error-message-align="left"
                    label-align="top" type="textarea" rows="4" autosize class="leading-55px"
                    :class="route.query?.type == 'edit' ? ' bg-#f2f2f2' : 'bg-#fff'" />
            </van-form>
            <div class="mt138px px28px">
                <!-- reupload -->
                <van-uploader class="" v-model="imgList"  max-count="9" accept="image/*"
                    :after-read="afterRead">
                    <div class="w-214px h-151px bg-#ebebeb rounded-20px flex items-center justify-center">
                        <van-icon name="plus" size="2.5rem" color="#ABABAB" />
                    </div>
                    <template #preview-delete>
                        <img loading="lazy" :src="iconDel" class="w-32px h-32px" alt="" srcset="">
                    </template>
                </van-uploader>
                <div class="text-31px text-#333 mt48px">
                    可上传图片 <span class="text-#999 text-24px">（注：请勿上传违规内容）</span>
                </div>
            </div>
        </div>

        <div class="bottom_btn flex justify-center px28px pt18px pb46px fixed bottom-0 left-0 w-full"
            v-if="route.query?.type == 'edit'" @click="submit">
            <div class="w-full flex items-center justify-center">
                确认修改
            </div>
        </div>

    </div>
</template>

<script lang="ts" setup>
import iconDel from "@/assets/friendship/profile/icon_del.png"
import iconPush from "@/assets/friendship/profile/icon_push.png"
import iconPushGray from "@/assets/friendship/profile/icon_push_gray.png"
import { uploadFile } from '@/api/public';
import { pushPost } from "@/api/friendship/profile"
import { showFailToast, showSuccessToast } from "vant";
import { useUserStore } from "@/store/modules/user";
import { judgeStaticUrl } from "@/utils/utils";
const formData = ref({
    autoId: '',
    content: '',
    imgUrl: '',
    videoUrl: ''
})
const userStore = useUserStore()
const imgList = ref([])
const videoList = ref([])
const route = useRoute()
const router = useRouter()
const formDataRef = ref(null)
const updataObj = ref(null)
// // 用于测试修改动态 
// const updataObj = ref(
//     {
//         "autoId": 25,
//         "content": "今天是个好日子",
//         "imgUrl": "/union-nanchong/videoStream/f9958342197b5fb55a7d4e4925a44938_160.png,/union-nanchong/videoStream/e81aaea2e92a31fef2a8e2f6f615f2a2_160.png,/union-nanchong/videoStream/f9958342197b5fb55a7d4e4925a44938_160.png",
//     }
// )

onMounted(() => {
    // 修改动态-（暂时取消这个功能）
    if (route.query?.type == 'edit' && route.query?.data) {
        updataObj.value = JSON.parse(route.query?.data)
        const { autoId, content, imgUrl } = updataObj.value
        formData.value = { autoId, content, imgUrl }

        let arr = imgUrl && imgUrl.split(",")
        if (arr) {
            imgList.value = arr.map((item:any) => {
               return {
                    url: judgeStaticUrl(item),
                    objectUrl: judgeStaticUrl(item),
                    originUrl: item
               }
            })
        }
    }
})

function afterRead(file) {
    let filedata = {
        operateType: "160", //操作模块类型
        file: file.file,
    };
    file.status = "uploading"
    uploadFile(filedata).then((res) => {
        if (res.code == 200 && res.data) {
            file.status = "success";
            file.url =judgeStaticUrl(res.data[0]);
            file.originUrl = res.data[0]
            formData.value.imgUrl = imgList.value.map((item:any) => item.originUrl).join(",")
        } else {
            file.status = "failed";
            showFailToast(res.message);
        }
    });
}

async function submit() {
    if (!formData.value.content) return
    formDataRef.value.validate().then(res => {
        pushPost(formData.value).then(res => {
            if (res.code == 200) {
                showSuccessToast("提交成功,请等待审核~")
                router.go(-1)
            } else {
                showFailToast(res.message)

            }
        })
    }).catch(err => {
        console.log("err", err);
    })
}

const backFn = () => {
    router.back()
}
</script>

<style lang="scss" scoped>
.push_dynamic {
    :deep(.van-uploader__preview-image) {
        width: 214px;
        height: 151px;
        border-radius: 20px;

        >img {
            // object-fit: fill !important;

        }
    }

    .push_btn {
        background: linear-gradient(90deg, #FF8635, #FF4055);
    }

    .push_btn_gray {
        background: #efefef;
        color: #999;
    }

    .bottom_btn {
        background: #FFFFFF;
        box-shadow: 0px -6px 9px 0px rgba(186, 194, 201, 0.16);

        >div {
            height: 82px;
            background: linear-gradient(-90deg, #FA9CAB, #FE769A);
            border-radius: 41px;
            font-weight: 500;
            font-size: 32px;
            color: #FFFFFF;
        }
    }

}
</style>