<template>
    <!-- 创建订单 -->
    <div class="pay flex flex-col bg-[#F9F9F9] h-100vh">
        <div class="flex-1 overflow-scroll flex flex-col">
            <!-- 收货地址信息 -->
            <div class="address flex items-center px-30px mt-50px relative pb-56px box-border" @click="selectAddress">
                <div class="left mr-42px">
                    <img loading="lazy" src="@/assets/integralMall/big_address_icon.png" class="w-40px" />
                </div>
                <div class="right flex justify-between items-center flex-1">
                    <div v-if="noAddress">
                        <div class="text-[#333] text-28px">请选择收货地址</div>
                    </div>
                    <div class="text-[#333] text-28px" v-else>
                        <div class="text-[#838383] text-30px">{{ addressInfo?.detailArea }}</div>
                        <div class="text-[#333] text-32px mt-24px">{{ addressInfo?.detailAddress }}</div>
                        <div class="user-info text-[#666] flex items-center text-30px mt-24px">
                            <div>{{ addressInfo?.receiverName }}</div>
                            <div class="ml-44px">{{ addressInfo?.receiverPhone }}</div>
                        </div>
                    </div>
                    <img loading="lazy" src="@/assets/integralMall/small_arrow.png" class="w-13px" />
                </div>
            </div>
            <!-- end -->

            <div class="goods-price-info px-30px bg-#FFF pt-30px flex-1 box-border">
                <div class="merchants pb-24px" v-for="item, index in productList" :key="index">
                    <div class="name-info flex items-center mb-20px">
                        <img loading="lazy" src="@/assets/inclusive/shop/icon-store.png" class="w-32px h-32px block" />
                        <span class="text-#333 text-30px ml-16px">{{ item.companyName }}</span>
                    </div>
                    <!-- 加购商品列表 -->
                    <div class="goods-list-view" v-for="it, index1 in item.productInfoList" :key="index1">
                        <div class="flex mb-30px" v-for="(info, info_i) of it.priceListInfo" :key="info_i">
                            <div class="goods-cover w-140px h-140px rounded-18px bg-blue">
                                <img :src="judgeStaticUrl(info.productSubImg)" alt=""
                                    class="object-cover w-full h-full rounded-18px" />
                            </div>
                            <div class="goods-info ml-26px">
                                <div class="name text-28px textOverflow">{{ it.productName }}</div>
                                <div class="specifications text-#999 text-24px leading-2.2em">
                                    {{ info.productSubName }}</div>
                                <div class="price flex justify-between">
                                    <span class="text-#FF4344 text-26px font-bold flex-1">
                                        ￥<span class="text-30px">{{ info.nowPrice }}</span>
                                    </span>

                                    <div class="flex items-center w-40% h-35px">
                                        <img src="@/assets/inclusive/icon_reduce.png" alt="" class="w-35px h-35px"
                                            @click.stop="handleCount(info, 'decrement', item)" />
                                        <div
                                            class="text-28px ml-20px mr-20px leading-none input-line rounded-10px flex-1">
                                            <van-field v-model="info.currentProductCount" type="number"
                                                @change="handleCount(info, 'update', item)" class="" />
                                        </div>
                                        <img src="@/assets/inclusive/icon_add.png" alt="" class="w-35px h-35px"
                                            @click.stop="handleCount(info, 'increment', item)" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="price-text mt-54px">
                        <div class="flex justify-between items-center">
                            <div class="label text-#666 text-26px">订单金额(含邮费)</div>
                            <div class="price text-#333">
                                <span class="text-23px">￥</span>
                                <span class="text-30px">{{ item.priceNum }}</span>
                            </div>
                        </div>
                        <div class="flex justify-between items-center mt-27px">
                            <div class="label text-#666 text-28px">优惠券</div>
                            <div class="price text-#333 text-28px text-#FF4344">
                                <!-- <span class="text-24px">-￥<span class="text-30px">2</span></span> -->
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>
        <div class="controll-bottom h-140px flex items-center bg-[#fff] px-30px">
            <div class="left mr-40px flex items-center" style="flex:0.45">
                <div class="text-[#333] text-28px text-medium">合计：</div>
                <div class="text-#FF4344 font-36px ml-10px">
                    <span>￥{{ totalPrice }}</span>
                </div>
            </div>
            <div class="right h-78px text-34px text-#fff
             bg-#FF4344 rounded-40px leading-none flex items-center justify-center" style="flex:0.55"
                @click="submitOrder">
                立即支付
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { shoppingCreateOrder } from '@/api/mall/inclusive';
import { judgeStaticUrl } from '@/utils/utils';
import { useMallStore } from '@/store/modules/mall'
import { defaultAddress } from '@/api/address'
import { showConfirmDialog, showLoadingToast, showToast, closeToast } from 'vant';
const router = useRouter()
const route = useRoute()

const mallStore = useMallStore()
const productList: any = computed(() => {
    const list = mallStore.getInclusiveGoodsList || []
    if (!list.length) {
        showConfirmDialog({
            message: '未获取到商品信息，请返回上一页重新操作',
            confirmButtonText: '我知道了'
        }).then(() => {
            router.back()
        })
        return []
    }
    return list
})
// 总金额
const totalPrice = computed(() => {
    let total = 0
    productList.value.map((item: any) => {
        total += parseFloat(item.priceNum)
    })
    return total.toFixed(2)
})

const noAddress = ref(false)
const addressInfo = ref<any>({})
const getDefaultAddress = () => {
    defaultAddress().then((res: any) => {
        if (res.data) {
            addressInfo.value = res.data
        } else {
            noAddress.value = true
        }
    })
}
const selectAddress = () => {
    router.push({
        path: '/address'
    })
}
const handleCount = (item: any, type: string, merchants: any) => {
    if (type === 'decrement') {
        if (item.currentProductCount === 1) return
    }
    else if (type === 'increment') {
        if (item.currentProductCount >= item.purchaseLimit) return showToast("当前购买数量已达到最大限制")
    }
    else if (type === 'update') {
        if (item.currentProductCount < 1) {
            item.currentProductCount = 1
            return
        }
        if (item.currentProductCount > item.purchaseLimit) {
            item.currentProductCount = item.purchaseLimit
            return showToast("当前购买数量已达到最大限制")
        }
    }
    if (type === 'decrement') item.currentProductCount--
    else if (type === 'increment') item.currentProductCount++

    // 重新计算商户商品总价
    merchants.priceNum = 0
    merchants.productInfoList.forEach((it: any) => {
        merchants.priceNum += parseFloat(it.priceListInfo.reduce((pre: any, cur: any) => (parseFloat(pre) + parseFloat(cur.nowPrice) * cur.currentProductCount).toFixed(2), 0))
    })
    merchants.priceNum = parseFloat(merchants.priceNum).toFixed(2)
    // end
}


let btnClick = false
const submitOrder = async () => {
    if (btnClick) return
    btnClick = true
    showLoadingToast('正在创建订单，亲稍等...')
    const params = {
        purchaseType: route.query.type === '1' ? 'shopCart' : 'direct',//购买类型 1:购物车 2:商品详情
        receiveInfo: addressInfo.value,//收件信息
        companyShopList: unref(productList),//商品信息
    }
    shoppingCreateOrder(params).then((res: any) => {
        closeToast()
        if (res.code === 200) {
            console.log(res.data, 'data')
            // 支付逻辑未对接
            showToast({
                message: '创建订单成功',
                duration: 1000
            })
            setTimeout(() => {
                router.replace({
                    path: '/my/orders'
                })
            }, 1000)

        } else {
            showToast(res.message || '创建订单失败')
        }
        btnClick = false
    }).catch(() => {
        btnClick = false
    })
}

onMounted(() => {
    if (JSON.stringify(mallStore.getAddressInfo) !== '{}') addressInfo.value = mallStore.getAddressInfo
    else getDefaultAddress()
})
</script>

<style lang="scss" scoped>
.address::after {
    content: '';
    display: block;
    position: absolute;
    height: 5px;
    bottom: 0;
    left: 5px;
    right: 5px;
    background-image: linear-gradient(90deg, #418DEA 36%, transparent 36%, transparent 52%, #FF4344 52%, #FF4344 86%, transparent 86%);
    background-size: 60px 5px;
    background-repeat: repeat-x;
    transform: skewX(-20deg);
    overflow: hidden;
}

.goods-price-info {
    .goods {
        border-bottom: 1px solid #E7E7E7;
    }
}

.controll-bottom {
    box-shadow: 0px 4px 43px 0px rgba(61, 23, 23, 0.1);
}

.input-line {
    border: 1px solid #CCC;

    :deep(.van-cell) {
        padding: 0px 10px;
        background: transparent;

        .van-cell__value {
            color: #333;
            font-size: 26px;
        }

        .van-field__control {
            text-align: center;
        }
    }
}

.merchants {
    border-bottom: 1px solid #E7E7E7;
}
</style>