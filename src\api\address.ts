import { openHttp } from '@/utils/http/axios';

// 获取地址列表
export const getAddressList = (params:any) => {
    return openHttp.get({
        url: '/userPostAddress/findList',
        params
    })
}
// 添加/修改地址
export const addOrUpdateAddress = (params:any) => {
    return openHttp.post({
        url: '/userPostAddress',
        params
    })
}
// 地址详情内容
export const getAddressDetail = (autoId:string) => {
    return openHttp.get({
        url: '/userPostAddress',
        params:{autoId}
    })

}

// 删除地址
export const deleteAddress = (todoValueList:string[]) => {
    return openHttp.post({
        url: '/userPostAddress/removeBatch',
        params:{todoValueList}
    })
}

// 获取默认地址
export  const defaultAddress = () => {
    return openHttp.get({
        url: '/userPostAddress/selectDefaultAddresses'
    })
}