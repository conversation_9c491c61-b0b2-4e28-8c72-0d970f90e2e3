<template>
    <div class="pb-20px">
        <van-form ref="formRef" @submit="submit">
            <van-cell-group>
                <van-field required v-model="Data.formatData.groupName" label="小组名称"
                    :rules="[{ required: true, message: '请填写小组名称' }]" label-width="25%" placeholder="请填写小组名称"
                    input-align="right" error-message-align="right"></van-field>
                <van-field required v-model="Data.formatData.memberMax" label="成员数量"
                    :rules="[{ required: true, message: '请输入成员数量' }]" label-width="25%" placeholder="请输入成员数量"
                    input-align="right" error-message-align="right"></van-field>
                <van-field required label="小组标签" @click="showPickerFn(Data.labelList, 'Industry')"
                    v-model="Data.labelName" :rules="[{ required: true, message: '请选择' }]" label-width="25%"
                    placeholder="请选择" right-icon="arrow" readonly input-align="right"
                    error-message-align="right"></van-field>
                <div class="textarea">
                    <van-field required v-model="Data.formatData.groupDesc" label="详情介绍" type="textarea"
                        :rules="[{ required: true, message: '请输入详情介绍' }]" label-width="100%"
                        placeholder="请输入小组介绍"></van-field>
                </div>
                <div class="textarea">
                    <van-field required v-model="Data.formatData.requirement" label="加入要求" type="textarea"
                        :rules="[{ required: true, message: '请输入加入要求' }]" label-width="100%"
                        placeholder="请输入加入要求"></van-field>
                </div>
                <div class="p-28px">
                    <div class="text-32px text-#333 mb-20px"><span class="text-#CC3333 ">*</span>封面图片</div>
                    <van-uploader v-model="Data.imgList" reupload max-count="1" accept="image/*"
                        :after-read="afterRead" />
                </div>

            </van-cell-group>
            <van-button type="primary" block
                class="btn w-65/100 mt-40px m-auto text-34px text-#fff py-34px text-center border-none"
                native-type="submit">确定</van-button>
        </van-form>
        <van-popup v-model:show="Data.showPicker" position="bottom">
            <!-- <van-picker :columns="Data.columns" @confirm="onConfirm" @cancel="Data.showPicker = false" /> -->
            <div class="p-28px ">
                <div class="max-h-60vh overflow-scroll">
                    <van-checkbox-group v-model="Data.labelId" direction="horizontal">
                        <van-checkbox :name="item.autoId" v-for="(item, index) in Data.labelList">{{ item.labelName
                            }}</van-checkbox>
                    </van-checkbox-group>
                </div>

                <div
                    class="flex items-center pt-[20px] mt-[20px] box-border border-t-[1px] border-t-[#EBEBEB] border-t-solid W-100%">
                    <div class="flex items-center justify-around w-[100%]">
                        <van-button block @click.stop="Data.showPicker = false"
                            class="w-[48%] h-[78px] rounded-[39px] justify-center flex items-center text-[30px] text-[#5AA4FF] border-[1px] border-[#5AA4FF] border-solid mr-[30px]">
                            取消
                        </van-button>
                        <van-button block
                            class="w-[48%] h-[78px] rounded-[39px] justify-center flex items-center text-[30px] text-[#fff]"
                            style="background: linear-gradient(0deg, #a1cbff 0%, #5aa4ff 100%); " @click="submitLabel">
                            确定
                        </van-button>
                    </div>
                </div>
            </div>
        </van-popup>
    </div>
</template>
<script lang="ts" setup>
import { useUserStore } from '@/store/modules/user';
const useStore = useUserStore();
import { interestGroupAudit } from '@/api/interestGroup';
import { uploadFile } from '@/api/public';
import { showFailToast, showSuccessToast, showToast } from 'vant';
import router from '@/router';
const Data = ref({
    formatData: {
        groupName: '',
        memberMax: '',
        groupDesc: '',
        requirement: '',

    },
    labelId: [],
    labelName: '',
    columns: [],
    showPicker: false,
    type: '',
    labelList: [],
    imgList: [],
    checkboxRefs: null
})
//选择
const submitLabel = () => {
    let res2 = Data.value.labelList.filter(item => Data.value.labelId.some(ele => ele === item.autoId));
    if (res2?.length > 5) {
        showToast("标签最大能选择5个");
        return
    }
    Data.value.showPicker = false;
    Data.value.labelName = res2.map(item => item.labelName).join(',')
}
const showPickerFn = (list, type) => {
    Data.value.columns = list
    Data.value.showPicker = true
    Data.value.type = type
}
function afterRead(file) {
    let filedata = {
        operateType: "160", //操作模块类型
        file: file.file,
    };
    uploadFile(filedata).then((res) => {
        if (res.code == 200 && res.data) {
            file.status = "success";
            file.url = res.data[0];
            let arr = [];
            Data.value.imgList.forEach((item) => {
                arr.push(item.url);
            });
            Data.value.formatData.logo = arr.join(",");
        } else {
            file.status = "failed";
            showFailToast(res.message);
        }
    });
}
//提交
function submit() {
    Data.value.formatData.labelId = Data.value.labelId.join(',');
    interestGroupAudit(Data.value.formatData).then(res => {
        if (res.code == 200) {
            showSuccessToast("提交成功,等待审核");
            router.go(-1)
        }
    })
}
onMounted(() => {
    Data.value.labelList = useStore.getLabelData;
})
</script>
<style lang="scss" scoped>
.textarea {
    :deep(.van-field__control) {
        border-radius: 8px;
        background: #F6F7F8;
        padding: 20px;
    }

    :deep(.van-field__body) {
        margin-top: 15px;
    }
}

:deep(.van-field__label) {
    font-size: 32px !important;
}

:deep(.van-checkbox) {
    margin-right: 15px;
    margin-bottom: 20px;
}

:deep(.van-overlay) {
    background: rgba(0, 0, 0, 0.5)
}

:deep(.van-cell) {
    padding-top: 26px;
    padding-bottom: 26px;
}

:deep(.van-field__body) {
    font-size: 28px;
}

:deep(.van-uploader__upload) {
    width: 120px;
    height: 120px;
}

.btn {
    background: url("@/assets/public/butto.png") no-repeat;
    background-size: 100% 100%;
}
</style>