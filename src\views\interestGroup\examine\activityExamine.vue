<template>
  <div class="bg-[#f6f7f8] min-h-[100vh] px-[30px] box-border py-[40px]">
    <div class="bg-[#fff] rounded-[20px] px-[21px] pt-[39px] pb-[1px] box-border">
      <div v-for="(item, index) in Data.infoList" :key="index">
        <div class="flex items-center justify-between text-[28px] mb-[49px]" v-if="item.type === 'text'">
          <div class="text-[#666]">{{ item.name }}</div>
          <div class="text-[#333]">{{ item.value }}</div>
        </div>
        <div class="flex flex-col text-[28px]" v-else-if="item.type === 'textarea'">
          <div class="text-[#666]">{{ item.name }}</div>
          <div class="text-[#333] bg-[#F6F7F8] p-[28px] box-border mt-[35px] mb-[25px] rounded-[10px]">{{ item.value }}
          </div>
        </div>
        <div class="flex flex-col text-[28px]" v-else-if="item.type === 'img'">
          <div class="text-[#666]">{{ item.name }}</div>
          <div class="flex items-center mb-[25px] flex-wrap">
            <img loading="lazy" :src="e.src" v-for="e, i in item.value" :key="i"
              class="w-[160px] h-[160px] rounded-[4px] mr-[20px] mt-[25px]" alt="">
          </div>
        </div>
      </div>
    </div>
    <div class=" box-border mt-20px">
      <div class="bg-[#fff] rounded-[20px] p-[22px] box-border">
        <div class="text-28px">审核状态：{{
          Data.objInfo?.auditState == 'wait' ? '待审核' : Data.objInfo?.auditState == 'pass' ? '审核通过' :'审核不通过' }}</div>
        <div class="text-28px mt-20px" v-if="Data.objInfo && Data.objInfo?.auditOpinion">审核原因：{{
          Data.objInfo?.auditOpinion ||'--' }}</div>
      </div>

    </div>
    <div class="flex items-center justify-between w-[100%] px-[90px] box-border fixed bottom-[84px]"
      v-if="route.query.isExamine === '1' && Data.evaluateList[0]?.state == 'wait'">
      <van-button block @click.stop="onRefuse(Data.evaluateList[0], 'refuse')"
        class="w-[260px] h-[76px] rounded-[39px] justify-center flex items-center text-[30px] text-[#5AA4FF] border-[1px] border-[#5AA4FF] border-solid mr-[30px]">
        拒绝
      </van-button>
      <van-button block @click.stop="onPass(Data.evaluateList[0], 'pass')"
        class="w-[260px] h-[78px] rounded-[39px] justify-center flex items-center text-[30px] text-[#fff]"
        style="background: linear-gradient(0deg, #a1cbff 0%, #5aa4ff 100%)">
        通过
      </van-button>
    </div>
    <Popup :show="Data.showPop" :titleName="Data.titleName" :placeHolder="'请输入' + Data.titleName"
      @submit-content="submitContent" @close-popup="closePopup" />
  </div>
</template>
<script lang="ts" setup>
import { useRoute } from "vue-router";
const route = useRoute();
import { detailByGroupId, groupAudit, getactivityInfoDetails } from "@/api/interestGroup";
import Popup from "@/components/Popup/popup.vue";
import { showFailToast, showToast } from "vant";
import utils from "@/utils/utils";
import { useUserStore } from '@/store/modules/user';
const useStore = useUserStore();
const Data = ref({
  activityList: [],
  evaluateList: [],
  showPop: false,
  titleName: '',
  objInfo: {},
  infoList: [
    { name: '活动名称', value: '', type: 'text' },
    { name: '小组名称', value: '', type: 'text' },
    { name: '报名人数上限', value: '', type: 'text' },
    { name: '活动类别', value: '', type: 'text' },
    { name: '报名所需分值', value: '', type: 'text' },
    { name: '报名开始时间', value: '', type: 'text' },
    { name: '报名结束时间', value: '', type: 'text' },
    { name: '活动开始时间', value: '', type: 'text' },
    { name: '活动结束时间', value: '', type: 'text' },
    { name: '活动地址', value: '', type: 'textarea' },
    { name: '活动详情', value: '', type: 'textarea' },
    { name: '活动封面', value: [], type: 'img' },
  ],
});

onMounted(() => {
  //   getDetail();
  Data.value.labelList = useStore.getLabelData;
  getActDetails()
});
//详情
function getGroupDetail(groupId) {
  detailByGroupId({
    groupId: groupId
  }).then(res => {
    if (res.code == 200) {
      Data.value.infoList[1].value = res.data.groupName
    }
  })
}
//获取详情
function getActDetails() {
  getactivityInfoDetails({
    activityId: route.query.activityId
  }).then(res => {
    if (res.code == 200) {
      getGroupDetail(res.data.groupId)
      Data.value.infoList[0].value = res.data.activityName;
      Data.value.infoList[2].value = res.data.signUpInfo?.maxCount;
      Data.value.infoList[4].value = res.data.signUpInfo?.enrollmentScore;
      let labelObj = useStore.getLabelData.filter(item => (item.autoId == res.data.activityType))
      Data.value.infoList[3].value = labelObj[0].labelName
      Data.value.infoList[5].value = utils.formatTimeWithoutSeconds(res.data.signUpInfo?.signUpStartTime);
      Data.value.infoList[6].value = utils.formatTimeWithoutSeconds(res.data.signUpInfo?.signUpEndTime);
      Data.value.infoList[7].value = utils.formatTimeWithoutSeconds(res.data.activityEndTime);
      Data.value.infoList[8].value = utils.formatTimeWithoutSeconds(res.data.activityEndTime);
      Data.value.infoList[9].value = res.data.activityAddress;
      Data.value.infoList[10].value = res.data.activityContent;
      Data.value.infoList[11].value = [{ src: utils.judgeStaticUrl(res.data?.appCover) }];
      Data.value.objInfo = res.data;
    } else {
      showToast(res.message)
    }
  })
}
/**
 * 点击拒绝  处理函数
 *
 * @param item 请求项，可以是任意类型的值
 */
function onRefuse(item: any, type) {
  Data.value.showPop = true;
  item.type = type;
  Data.value.titleName = '失败原因'
  Data.value.objInfo = item;

}
//通过
function onPass(item, type) {
  item.type = type;
  Data.value.objInfo = item;
  Data.value.titleName = '通过原因'
  Data.value.showPop = true;
}
//确认提交  拒绝
function submitContent(val: string) {

  if (!val && Data.value.objInfo.type == 'refuse') {
    showToast({
      message: "请输入拒绝原因",
      icon: "none",
    });
    return;
  }
  groupAudit({
    autoId: route.query.autoId,
    state: Data.value.objInfo.type,
    auditOpinion: val
  }).then(res => {
    if (res.code == 200) {
      showToast("审核成功");
      getDetail()
      Data.value.showPop = false;
    }
  })

}
//关闭拒绝弹窗
function closePopup() {
  Data.value.showPop = false;
}

</script>
<style lang="scss" scoped></style>
