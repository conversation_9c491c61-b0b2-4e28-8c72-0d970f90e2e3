<template>
    <div>
        <div v-for="item, index in data" :key="index" class="mb-40px">
            <!-- type 1-左  2-右 -->
            <div v-if="item.type === '1'" class="flex items-start justify-start">
                <img loading="lazy" :src="item.pic ? item.pic : defaultHead" alt=""
                    class="w-66px h-66px rounded-50% mr-20px">
                <div class="flex flex-col  items-start ">
                    <div class="text-28px text-#666  mb-[10px]">{{ item.userName }}</div>
                    <div class="text-28px text-#333 bg-#F6F7F8 rounded-10px p-28px box-border">{{ item.cont }}</div>
                </div>
            </div>
            <div v-if="item.type === '2'" class="flex items-start justify-end">
                <div class="flex flex-col  items-end ">
                    <div class="text-28px text-#666  mb-[10px]">{{ item.userName }}</div>
                    <div class="text-28px text-#333 bg-#F6F7F8 rounded-10px p-28px box-border">{{ item.cont }}</div>
                </div>
                <img loading="lazy" :src="item.pic ? item.pic : defaultHead" alt=""
                    class="w-66px h-66px rounded-50% ml-20px">
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
// import male from "@/assets/public/male.png";
import defaultHead from "@/assets/public/head_default.png"
const props = defineProps({
    data: {
        type: Array,
        default: [],
    },
})
</script>