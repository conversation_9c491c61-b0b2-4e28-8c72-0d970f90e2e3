<template>
    <div class="workerCraftsman pb-72px box-border">
        <div class="banner w-full pt-24px pl-30px pr-114px box-border">
            <img loading="lazy" src="@/assets/workerCraftsman/home_title.png" class="w-full" alt="" />
        </div>
        <div class="content px-30px">
            <!-- 菜单导航 -->
            <div class="nav_menus mt-160px px-46px py-33px bg-white rounded-[16px]">
                <!-- 主导航 -->
                <div class="flex justify-between">
                    <div class="nav text-center" v-for="item, index in menus" :key="index"
                        @click="navPageTo(item.type)">
                        <img loading="lazy" :src="item.icon" class="w-80px" />
                        <div class="name text-[28px] text-[#666666] mt-10px">{{ item.name }}</div>
                    </div>
                </div>
                <!-- 认证入口 -->
                <div class="extrance flex justify-between mt-38px">
                    <div class="nav flex-1 bg-contain bg-no-repeat h-140px pt-32px pl-26px  box-border"
                        :class="{ 'mr-20px': index < extranceList.length - 1 }"
                        :style="{ backgroundImage: `url(${item.bg})` }" v-for="item, index in extranceList" :key="index"
                        @click="workerAuth(item.type)">
                        <div class="text">
                            <img loading="lazy" :src="item.titleImage" class="w-125px" />
                            <div class="des text-[24px] text-[#803A2C]">{{ item.des }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 劳模/工匠/五一奖章内容区域 -->
            <div class="scroll_content bg-white mt-20px rounded-[16px]">
                <div class="tabs h-80px flex flex-col justify-center sticky top-0 z-99">
                    <van-tabs v-model:active="activetab" background="transparent" title-inactive-color="#666666"
                        title-active-color="#333333" @click-tab="onClickTab">
                        <van-tab class="flex-1" v-for="item, index in tabs" :name="item.value" :title="item.name"
                            :key="index">
                        </van-tab>
                    </van-tabs>
                </div>
                <div class="scroll_list px-28px pb-30px pt-30px">
                    <!-- 国家级/省级选择区域 -->
                    <div class="header" v-show="activetab !== '2'">
                        <!-- <div class="tips text-[#999999] text-[24px] flex justify-end mb-10px">超出区域可滑动</div> -->
                        <div class="types flex-1 flex overflow-x-scroll  bg-white text-[#FF864C] pb-10px">
                            <div class="tab flex-1 h-50px flex items-center z-1 box-border
                             border-[1px] border-[#FF864C] border-solid  rounded-[26px]
                            justify-center text-[28px] whitespace-nowrap mr-20px px-20px bg-#FFF9F2" :class="{
                                ' text-[#fff] z-2  rounded-[26px] active_tab':
                                    currentType === item.typeBizId, '!mr-0px': index === typeList.length - 1
                            }" @click="changeType(item.typeBizId)" v-for="item, index in typeList" :key="index">
                                {{ item.typeName }}
                            </div>
                        </div>
                    </div>

                    <div class="tips mt-15px text-[#999999] text-[24px] flex justify-end" v-show="activetab !== '2'">
                        注：排名不分先后
                    </div>
                    <!-- 列表展示区域 -->
                    <div class="lists pb-55px">
                        <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore"
                            ref="loadMoreRef">
                            <!-- 劳模工匠展示模板 -->
                            <template v-if="activetab !== '2'">
                                <div class="list mt-27px flex bg-[#FFF8F3]" v-for="item, index in list" :key="index">
                                    <div class="left_avator w-250px h-300px rounded-[24px] bg-[#D8C9C4] bg-cover bg-center"
                                        :style="{ backgroundImage: `url(${item?.avatar})` }">
                                    </div>
                                    <div class="right_content flex-1  px-35px py-26px box-border h-300px
                                         overflow-hidden flex flex-col justify-between">
                                        <div>
                                            <div class="title text-[#333333] text-[32px]">{{ item?.userName }}</div>
                                            <div class="tag text-[#333333] text-[28px] mt-8px
                                            overflow-hidden text-ellipsis  whitespace-nowrap">
                                                {{ item?.whenModelWorker }}
                                            </div>
                                            <div
                                                class="detail text-[#666] text-[24px] mt-10px text-ellipsis line-clamp-3 whitespace-nowrap text-wrap">
                                                {{ item?.personalStyle }}
                                            </div>
                                        </div>
                                        <div class="look_detail text-[#FF8F44] text-[24px] text-right mt-10px"
                                            @click="toDetail(item.workerId)">
                                            查看详情>>
                                        </div>
                                    </div>
                                </div>
                            </template>
                            <!-- 新闻展示模板 -->
                            <template v-else-if="activetab === '2'">
                                <newsCell v-for="(item, index) in list" :key="index" :content="item"></newsCell>
                            </template>
                        </refreshList>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import newsCell from '@/components/Cell/newsCell.vue'
import meunIcon1 from '@/assets/workerCraftsman/nav_icon_1.png'
import meunIcon2 from '@/assets/workerCraftsman/nav_icon_2.png'
import meunIcon3 from '@/assets/workerCraftsman/nav_icon_3.png'
import meunIcon4 from '@/assets/workerCraftsman/nav_icon_4.png'
import workerBg from '@/assets/workerCraftsman/woker_bg.png'
import workerTitle from '@/assets/workerCraftsman/woker_text.png'
import craftsmanBg from '@/assets/workerCraftsman/craftsman_bg.png'
import craftsmanTitle from '@/assets/workerCraftsman/craftsman_text.png'
import refreshList from '@/components/refreshList/index.vue';
import { judgeStaticUrl } from '@/utils/utils'
import { getwokerCraftList, getwokerCraftType } from '@/api/workerCraftsman.ts';
import { getCategoryInfo, getNewsList } from '@/api/news/index';
defineOptions({
    name: 'workerCraftsman',
})
// 菜单列表
const menus = [
    {
        name: '劳模故事',
        icon: meunIcon1,
        type: '1',
    },
    {
        name: '劳模政策',
        icon: meunIcon2,
        type: '2',
    },
    {
        name: '劳模礼遇',
        icon: meunIcon4,
        type: '4',
    },
    {
        name: '劳模申报',
        icon: meunIcon3,
        type: '3',
    },
]
// 认证入口
const extranceList = [
    {
        name: "劳模认证",
        titleImage: workerTitle,
        des: '点击立即认证',
        bg: workerBg,
        type: '0'
    },
    {
        name: "工匠认证",
        titleImage: craftsmanTitle,
        des: '点击立即认证',
        bg: craftsmanBg,
        type: '1'
    },
]
// 内容列表选择tab
const tabs = ref([
    {
        name: '南充劳模',
        value: '0'
    },
    {
        name: '南充工匠',
        value: '1'
    },
    {
        name: '五一奖章',
        value: '2'
    }
])
const activetab = ref(tabs.value[0].value)
// 类型列表
const typeList = ref<any[]>([])
const currentType = ref('')
const getTypes = async () => {
    const res = await getwokerCraftType(activetab.value)
    typeList.value = res?.data || []
    currentType.value = typeList?.value[0]?.typeBizId || ''
}
const changeType = (value: string) => {
    currentType.value = value
    onRefreshList()
}
//end

// 路由跳转
const router = useRouter()
// 导航菜单跳转
const navPageTo = (type: string) => {
    router.push({
        name: 'wokerSubject',
        params: {
            type
        }
    })
}
// 劳模入驻跳转
const workerAuth = (type: string) => {
    router.push({
        path: '/workerCraftsman/auth/form',
        query: {
            type
        }
    })

}
// 劳模工匠跳转详情页
const toDetail = (id = '1') => {
    router.push({
        name: 'wokerDetail',
        query: {
            id,
            type: activetab.value
        }
    })

}
onMounted(async () => {
    await getTypes()
    await loadMoreData()

})
const list = ref([]);
const pageNum = ref(1);
const loadMoreRef = ref<any>(null)

// 标签时间点击
const onClickTab = async () => {

    await getTypes()
    if (loadMoreRef.value) loadMoreRef.value.resetStatus()
    await onRefreshList()
}

// 刷新
const onRefreshList = () => {
    pageNum.value = 1
    list.value = []
    loadMoreData()
}
// 加载更多
const onLoadMore = () => {
    pageNum.value++
    loadMoreData()
}
//获取栏目
const tabData = ref([])
async function getColumn() {
    getCategoryInfo({
        categoryCode: 'wu_yi_jiang_zhang',
        platformType: 30,
    }).then(res => {
        tabData.value = res.data;
        getLists();
    });
}
async function getLists() {
    let res = await getNewsList({
        categoryCode: tabData.value?.categoryCode,
        platformType: 30,
        pageNum: pageNum.value,
        pageSize: 10,
    });
    if (pageNum.value === 1) list.value = [];
    list.value = list.value.concat(res.data);
    //重置刷新状态及 判断是否加载完成
    if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(list.value.length, res.total);
    }
}
const loadMoreData = async () => {
    // 劳模工匠列表
    let listTotal = 0 //列表长度，用于判断是否加载完成
    if (activetab.value !== '2') {
        const { code, data, total } = await getwokerCraftList({
            pageNum: pageNum.value,
            pageSize: 10,
            modelType: activetab.value,//0劳模类型1工匠类型
            typeBizId: currentType.value,//类型id
        })
        if (code === 200) {
            data.forEach((item: Recordable) => {
                item.avatar = judgeStaticUrl(item.avatar, true)
            })
            list.value = list.value.concat(data)
            listTotal = total
        }
    } else {
        getColumn();
    }
    // end

    // 五一奖章列表-新闻 ....

    //重置刷新状态及 判断是否加载完成
    if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(list.value.length, listTotal)
    }
}
</script>

<style scoped lang="scss">
.workerCraftsman {
    width: 100%;
    min-height: 100vh;
    height: fit-content;
    background: url('@/assets/workerCraftsman/home_bg.jpg'),
        linear-gradient(0deg, #F56B2B 0%, #ba2a12 100%);
    background-repeat: no-repeat;
    background-position: 0px 0px, 0% 0%;
    background-size: 100% auto, 100% 100%;

    .scroll_content {
        .active_tab {
            background: linear-gradient(to right, #FF4B2F, #FF864C)
        }

        .tabs {
            background-image: url('@/assets/workerCraftsman/new_title_bg.png');
            background-size: cover;
            background-position: top center;

            :deep(.van-tabs--line .van-tabs__wrap) {
                height: 80px;
            }

            :deep(.van-tab) {
                line-height: 1;
                height: 100%;
                font-size: 30px;
            }

            :deep(.van-tabs__nav--line) {
                height: 100%;
                padding-bottom: 15px;
            }

            :deep(.van-tabs__line) {
                background: linear-gradient(86deg, #FFB66E 0%, #DC2E31 100%);
                width: 40px;
                height: 6px;
                bottom: 15px;
            }

            :deep(.van-tab--active) {
                font-size: 32px;
            }
        }
    }
}
</style>