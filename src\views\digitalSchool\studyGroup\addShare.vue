<template>
    <div class="pb-20px">
        <van-form ref="formRef" @submit="submit">
            <van-cell-group>
                <div class="textarea">
                    <van-field required v-model="Data.formatData.describes" label="分享内容" type="textarea"
                        :rules="[{ required: true, message: '请输入分享内容' }]" label-width="100%"
                        placeholder="请输入分享内容"></van-field>
                </div>

                <div class="p-28px">
                    <div class="text-28px text-#333 mb-20px"><span class="text-#CC3333 ">*</span>分享图片</div>
                    <van-uploader v-model="Data.imgList" reupload max-count="3" accept="image/*"
                        :after-read="afterRead" />
                        <div class="text-20px text-#333 mt-40px mb-20px">*请上传JPG/PNG/JPEG格式的图片，最多上传3张</div>
                </div>

            </van-cell-group>
            <van-button type="primary" block
                class="btn w-65/100 mt-40px m-auto text-34px text-#fff py-23px text-center border-none fixed bottom-120px left-[50%] translate-x-[-50%]"
                native-type="submit">确定</van-button>
        </van-form>
    </div>
</template>
<script lang="ts" setup>
import { useUserStore } from '@/store/modules/user';
const useStore = useUserStore();
import { shareSaveOrUpdateByDTO} from "@/api/digitalSchools/group";
import { uploadFile } from '@/api/public';
import { showFailToast, showSuccessToast } from 'vant';
import { useDictionary } from '@/store/modules/dictionary';
import { useRoute } from "vue-router";
import router from '@/router';
const route = useRoute();
const dictionary = useDictionary()
const Data = ref({
    formatData: {
        groupBizId: "",
        file: "",
        describes: "",
    },
    type: '',
    imgList: [],
})
function afterRead(file) {
    let filedata = {
        operateType: "160", //操作模块类型
        file: file.file,
    };
    uploadFile(filedata).then((res) => {
        if (res.code == 200 && res.data) {
            file.status = "success";
            file.url = res.data[0];
            let arr = [];
            Data.value.imgList.forEach((item) => {
                arr.push(item.url);
            });
            Data.value.formatData.file = arr.join(",");
        } else {
            file.status = "failed";
            showFailToast(res.message);
        }
    });
}
//提交
function submit() {
    Data.value.formatData.groupBizId=route.query?.groupBizId
    shareSaveOrUpdateByDTO(Data.value.formatData).then(res => {
        if (res.code == 200) {
            showSuccessToast("提交成功,等待审核");
            router.go(-1)
        } else {
            showFailToast(res.message)
        }
    })
}
onMounted(() => {
    console.log('Data.value.labelList', Data.value.columns);
})
</script>
<style lang="scss" scoped>
.textarea {
    :deep(.van-field__control) {
        border-radius: 8px;
        background: #F6F7F8;
        padding: 20px;
    }

    :deep(.van-field__body) {
        margin-top: 15px;
    }
}

:deep(.van-checkbox) {
    margin-right: 15px;
    margin-bottom: 20px;
}

:deep(.van-overlay) {
    background: rgba(0, 0, 0, 0.5)
}

.btn {
    background: url("@/assets/public/butto.png") no-repeat;
    background-size: 100% 100%;
}
</style>