<script setup lang="ts">
import refreshList from '@/components/refreshList/index.vue';
import activity from '@/assets/friendShip/message_file_icon.png'
import follow from '@/assets/friendShip/message_follow_icon.png'
import like from '@/assets/friendShip/message_zan_icon.png'
import send from '@/assets/friendShip/message_pub_icon.png'
import foot from '@/assets/friendShip/message_foot_icon.png'
import router from '@/router';
import { queryMessageListApi, readMessageApi } from '@/api/friendship/index';
const loadMoreRef = ref(null)
// 加载更多
const onLoadMore = () => {
  list.value.pageIndex++;
  getList();
};
const LuckDynamicList = async () => {
  await queryMessageListApi({
    pageNum: list.value.pageIndex,
    pageSize: 10,
  }).then(res => {

    list.value.loading = false
    list.value.isLoad = false
    list.value.pageIndex == 1 ? list.value.List = res.data
      : list.value.List = [...list.value.List, ...res.data]
    if (loadMoreRef.value) {
      loadMoreRef.value.onLoadSuc(list.value.length, res.total);
    }
  }).catch(err => {
    list.value.loading = false
    list.value.isLoad = false
    list.value.finished = true
    list.value.finishedText = ''
  })
}
// 刷新
const onRefresh = () => {
  list.value.pageIndex = 1
  list.value.loading = true
  list.value.isLoad = true
  list.value.finished = false
  list.value.List = []
  LuckDynamicList()
}
const list = ref({
  pageIndex: 1,
  List: [],
  isLoad: false,
  loading: false,
  finished: false,
  finishedText: '',
  RecordCount: 0
})
const toDetail = (item) => {
  let list = []
  list.push(item.autoId)
  readMessageApi({
    batchIdList: list
  })
  switch (item.sourceFlag) {
    case 'apply'://申请获取您的联系方式
      toPage('/friendship/MyApply', {})
      break
    case 'auditRefuse'://同意了您的申请
      toPage('/friendship/MyApply', {})
      break
    case 'comment'://评论您的动态
      toPage('/friendship/dynamicDetail', { postId: item.businessId })
      break
    case 'push'://发布了一条动态
      toPage('/friendship/dynamicDetail', { postId: item.businessId })
      break
    case 'reply'://回复您的评论
      toPage('/friendship/dynamicDetail', { postId: item.businessId })
      break
    default:
      toPage('/friendship/detail', { singleUserId: item.operatorUserId })
      break


  }
}
//详情
function toPage(path, query) {
  router.push({
    path,
    query
  })
}
onMounted(() => {
  // detailId.value = $route.query.Id;
  LuckDynamicList()

})

</script>
<template>
  <div class="index">
    <div class="list">

      <refreshList key="relist" @onRefreshList="onRefresh" @onLoadMore="onLoadMore" ref="loadMoreRef">
        <div class="item" v-for="(item, index) in list.List" :key="index">
          <div class="item-left relative">
            <img loading="lazy" class="state" :src="item.sourceFlag == 'apply' ? activity : item.sourceFlag == 'like' ?
              like : item.sourceFlag == 'push' ? send : item.sourceFlag == 'remark' ? follow : item.sourceFlag == 'reply' ? activity :
                item.sourceFlag == 'browse' ? foot : item.sourceFlag == 'comment' ? activity : item.sourceFlag == 'auditPass' ? activity :
                  item.sourceFlag == 'auditRefuse' ? activity : ''" alt="">
            <span v-if="item.mesReadFlag == 'n'"
              class="w-14px h-14px bg-red absolute rounded-50% left-[40px] top-[5px]"></span>
            <div>
              <div>
                <span class="name">{{ item.operatorUserName }}</span>
                <span>{{ item.messageContent }}</span>
              </div>
              <div class="time mt-10px">{{ item.createTime }}</div>
            </div>

          </div>
          <div class="detail" @click="toDetail(item)">查看详情</div>
        </div>
      </refreshList>

    </div>
  </div>
</template>
<style lang="scss" scoped>
.index {
  box-sizing: border-box;
  padding: 0 30px;

  .list {
    .item {
      box-sizing: border-box;
      padding: 32px 0 24px;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #EBEBEB;
      align-items: center;

      .item-left {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 28px;
        color: #333333;

        .name {
          font-weight: 600;
          margin-right: 10px;
        }

        >img {
          width: 56px;
          height: 56px;
          margin-right: 20px;
        }
      }

      .time {
        font-weight: 400;
        font-size: 24px;
        color: #B3B3B3;
      }

      .detail {
        width: 133px;
        height: 50px;
        background: #FFFFFF;
        border-radius: 24px;
        border: 1px solid #5AA4FF;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: 24px;
        color: #5AA4FF;
      }
    }
  }
}
</style>