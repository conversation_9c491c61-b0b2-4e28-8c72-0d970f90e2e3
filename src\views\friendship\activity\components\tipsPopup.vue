<template>
    <div>
        <van-popup :show="showPop" class="popup1" position="center" :style="{ width: '80%', height: '50%' }">
            <div class="w-full h-full pt68px px30px pb30px">
                <div class="top">
                    <div class="font-500 text-43px text-#333">—温馨提示—</div>
                    <div class="font-400 text-21px text-#83a9f3 mt26px w-268px h-46px flex items-center justify-center bg-no-repeat" :style="{
                        backgroundImage:`url(${titleBg})`,
                        backgroundSize: '100% 100%',
                    }">
                        FRIENDLY REMINDER
                    </div>
                </div>
                <div class="content mx-auto mt-66px leading-53px">
                    1.活动仅限“川工之家”App南充频道单身联谊入驻会员参加。若非“单身联谊”入驻会员，请及时在首页“单身认证”完善个人资料，提交入驻申请。
                </div>
                <div class="bottom_box flex mt73px">
                    <div class="cancel_btn flex items-center justify-center" @click="close">
                        取消
                    </div>
                    <div class="confirm_btn flex items-center justify-center" @click="toWrite">
                        完善资料
                    </div>
                </div>
            </div>
        </van-popup>
    </div>

</template>
<script lang="ts" setup>
const emit = defineEmits(['closePopup', 'toWrite']);
import router from "@/router";
import bg from "@/assets/friendship/bg_tip.png"
import titleBg from "@/assets/friendship/title_bg.png"
const show = ref(false)

const props = defineProps({
    showPop: {
        type: Boolean,
        default: false
    },
    data: {
        type: Array,
        default: [

        ]
    }
})

function close() {
    show.value = false;
    emit('closePopup', false)
}
function toWrite(){
    emit('toWrite', false)
}

</script>
<style lang="scss" scoped>
.van-overlay {
    background: rgba(0, 0, 0, .4);
}

.van-popup {
    // border-radius: 36px 36px 0px 0px;
    background-image: url("@/assets/friendship/bg_tip.png");
    background-size: 100%;
    background-color: transparent;
    background-repeat: no-repeat;
    overflow-y: auto;


}

.bottom_box {
    .cancel_btn {
        width: 209px;
        height: 77px;
        background: #F3F3F3;
        border-radius: 38px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        font-size: 33px;
        color: #333333;
        margin-right: 34px;
    }

    .confirm_btn {
        width: 292px;
        height: 77px;
        background: #FE779A;
        border-radius: 38px;
        font-weight: 500;
        font-size: 33px;
        color: #FFFFFF;
    }
}
</style>