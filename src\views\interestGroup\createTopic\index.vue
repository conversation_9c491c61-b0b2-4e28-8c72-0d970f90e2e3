<template>
    <div class="creatTopic">
        <van-tabs v-model:active="Data.tab.active" swipeable title-active-color="#5AA4FF">
            <van-tab v-for="(item,index) in Data.tab.nav" :title="item.name" class="text-34px" :key="index">
            </van-tab>
        </van-tabs>
        <div class="px-30px py-20px">
            <div class="bg-#fff rounded-10px px-20px py-24px mb-20px text-28px" v-for="(item,index) of Data.list" :key="index" @click="ToDetails(item)">
                <div class="truncate pb-24px" :class="item.status=='on'?'text-#666':'text-#333'">{{ item.title }}</div>
                <div class="flex hr pt-29px justify-between w-full">
                    <div class=" text-22px" :class="item.status=='on'?'text-#666':'text-#333'">{{ item.time }}</div>
                    <div class="flex ">
                        <div class="w-90px py-4px  rounded-19px text-#fff text-center text-22px mr-15px" :class="item.status=='on'?'bg-#FFA66A':'bg-#8EDEC8'">{{ item.status=='on'?'禁用':'启用' }}</div>
                        <div class="w-90px py-4px bg-#5AA4FF rounded-19px text-#fff text-center text-22px">删除</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="btn w-80/100  m-auto text-34px text-#fff py-23px text-center fixed bottom-40px left-1/2 -translate-x-1/2" @click="createTopic()">+ 创建话题</div>
        <Popup :show="Data.showPop" :titleName="'创建话题'" :placeHolder="'请输入创建话题内容'" @submit-content="submitContent" @close-popup="closePopup"/>
    </div>
</template>
<script lang="ts" setup>
import Popup from '@/components/Popup/popup.vue';
import { showToast } from 'vant';
import router from "@/router";
const Data=ref({
    tab:{active:0,nav:[{name:'全部'},{name:'已启用'},{name:'已禁用'}]},
    list:[
        {title:'#打卡最美南充景区',time:'2024-10-20  10:23',status:'off'},
        {title:'#打卡最美南充景区',time:'2024-10-20  10:23',status:'on'},
    ],
    showPop:false,
})
//创建话题
function createTopic() {
    Data.value.showPop=true;
}
//确认提交
function submitContent(val) {
    if(!val){
        showToast({
            message:"请输入创建话题",
            icon: 'none',
        })
        return
    }
    Data.value.showPop=false;
    console.log(val);
    
}
//关闭
function closePopup(){
    Data.value.showPop=false;
}
function ToDetails(item:any) {
    router.push('/topicDetails')

 }
</script>
<style lang="scss">
.creatTopic{
    background: #F6F7F8;
    min-height: 100vh;
    .van-tab{
        font-size: 32px;
    }
    .hr{
        border-top:1px solid #EBEBEB;
    }
    .btn{
        background: url("@/assets/public/button.png") no-repeat;
        background-size: 100% 100%;
    }
}

</style>