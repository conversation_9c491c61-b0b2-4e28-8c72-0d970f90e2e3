<template>
    <div class="worker_story box-border">
        <div class="p-30px">
            <div class="banner w-full">
                <img loading="lazy" :src="useStore.getPrefix + Data.info?.appBannerPath"
                    class="w-full rounded-16px h-240px" />
            </div>
        </div>
        <van-tabs v-model:active="Data.activetab" background="transparent" title-inactive-color="#666666"
            title-active-color="#333333" @change="onClickTab" v-if="Data.tabs.length > 1" :swipe-threshold="3">
            <van-tab class="flex-1" v-for="(item, index) in Data.tabs" :id="item.categoryId" :title="item.categoryName"
                :key="index">
            </van-tab>
        </van-tabs>

        <div class="content w-full box-border rounded-t-16px bg-[#fff] p-30px">
            <refreshList @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
                <newsCell v-for="(item, index) in list" :key="index" :content="item" :showborderBottom="false">
                </newsCell>
            </refreshList>

        </div>
    </div>
</template>

<script setup lang="ts">
defineOptions({
    name: 'subjectList'
})
import newsCell from '@/components/Cell/newsCell.vue';
import refreshList from '@/components/refreshList/index.vue';
import { showFailToast } from 'vant';
import { getNewsList, getCategoryInfoListBySpecialId } from '@/api/news/index';
import { useUserStore } from '@/store/modules/user';
import { useRoute, useRouter } from 'vue-router';
import router from '@/router';
const useStore = useUserStore();
onMounted(() => {
    initalPage();
})
const Data = ref({
    activetab: 0,
    tabs: [],
    pageNum: 1,
    info: {}
})
const list = ref([]);
let page = 1;//分页
const loadMoreRef = ref(null)
const route = useRoute();
function onClickTab(val) {
    Data.value.activetab = val;
    Data.value.pageNum = 1;
    if (loadMoreRef.value) loadMoreRef.value.resetStatus();
    onRefreshList();
}
// ====新闻列表====
const initalPage = () => {
    list.value = [];
    page = 1;
    getColumn()
}
//获取栏目
async function getColumn() {
    getCategoryInfoListBySpecialId({
        specialId: route.query.specialId,
        platformType: 30,
    }).then(res => {
        Data.value.tabs = res.data?.categoryInfoList;
        Data.value.info = res.data;
        getLists();
    });
}
// 刷新
const onRefreshList = () => {
    Data.value.pageNum = 1
    list.value = [];
    getLists()
}
// 加载更多
const onLoadMore = () => {
    Data.value.pageNum++
    getLists()
}
//新闻列表
async function getLists() {
    let res = await getNewsList({
        categoryCode: Data.value.tabs[Data.value?.activetab]?.categoryCode,
        platformType: 30,
        pageNum: Data.value.pageNum,
        pageSize: 10,
    });
    if (Data.value.pageNum === 1) list.value = [];
    list.value = list.value.concat(res.data);
    //重置刷新状态及 判断是否加载完成
    if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(list.value.length, res.total);
    }
}

</script>

<style scoped lang="scss">
.worker_story {
    :deep(.van-tab) {
        font-size: 30px;
    }
}
</style>