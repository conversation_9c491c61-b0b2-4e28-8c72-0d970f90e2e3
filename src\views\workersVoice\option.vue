<template>
  <div class="w-full" :class="$style.option">
    <div class="banner w-full h-[318px]" />

    <div class="h-[calc(100%-318px)] bg-[#fff] relative top-[-40px] rounded-xl">
      <van-form @submit="onSubmit">
        <van-cell-group inset>
          <van-field v-model="result" is-link readonly required name="employeeMessageType" label="留言类型"
            @click="showPicker = true" />

          <van-field label-align="top" class="message-clazz" v-model="message" name="describes" rows="4" autosize
            label="留言内容" type="textarea" required maxlength="250" placeholder="请输入留言内容..." show-word-limit />

          <van-field label-align="top" label="上传图片">
            <template #input>
              <van-uploader v-model="photos" :after-read="afterRead" max-count="3" accept="image/*"></van-uploader>
            </template>
          </van-field>
        </van-cell-group>
        <div style="margin: 16px" class="absolute bottom-0 left-1/2 -translate-x-1/2">
          <van-button round block type="primary" native-type="submit"
            class="bg-transparent border-transparent w-[400px] h-[79px]" :style="{
              backgroundImage: `url(${bgButtonSubmit})`,
              backgroundRepeat: 'no-repeat',
              backgroundSize: '100% 100%',
            }">
            提交
          </van-button>
        </div>
      </van-form>
    </div>

    <van-popup v-model:show="showPicker" position="bottom">
      <van-picker :columns="columns" @confirm="onConfirm" @cancel="showPicker = false" />
    </van-popup>

    <van-popup v-model:show="showCenter" round :style="{ padding: '64px' }" :close-on-click-overlay="false"
      class="success-msg">
      <div class="text-[#333333] text-[36px] mt-120px">提交成功</div>
      <p class="text-[#666666] text-[26px] text-center">感谢您的留言，我们会尽快查看！</p>
      <div>
        <div
          class="w-[400px] submitbtn h-[78px] rounded-[39px] flex justify-center items-center text-[#fff] absolute left-1/2 -translate-x-1/2"
          @click="handleClick">
          确认
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script lang="ts" setup>
import { questionTypeList, saveOrUpdate } from '@/api/workerVoice';
import bgButtonSubmit from '@/assets/workers-voice/bg-button-submit.png';
import { map } from 'lodash-es';
import { showFailToast, showSuccessToast } from 'vant';
import { uploadFile } from '@/api/public';
import { judgeStaticUrl } from '@/utils/utils';
const router = useRouter();

const showCenter = ref<boolean>(false);

const message = ref<string>('');

const result = ref<string>('');

const resultId = ref<string>('');

const showPicker = ref(false);

const columns = ref<Recordable[]>([]);

const photos = ref<any>([]);

const onConfirm = ({ selectedOptions }: Recordable) => {
  result.value = selectedOptions[0]?.text;
  resultId.value = selectedOptions[0]?.value;
  showPicker.value = false;
};

const onSubmit = (values: any) => {
  // showCenter.value = true;
  saveOrUpdate({
    ...values,
    employeeMessageTypeId: unref(resultId),
    img: photos.value.map((v: any) => v.originUrl).join(','),//增加图片上传功能
  }).then(({ message, code }) => {
    if (code === 200) {
      showCenter.value = true;
    } else {
      showFailToast(`提交失败！${message || ''}`);
    }
  });
};

function handleClick() {
  showCenter.value = false;
  router.push({ path: '/workersVoice' });
}

onMounted(async () => {
  columns.value = map(
    (await questionTypeList({ typeState: true, pageSize: 0 })) || [],
    (v: Recordable) => ({
      text: v.typeName,
      value: v.employeeMessageTypeId,
    })
  );
});
function afterRead(file) {
  let filedata = {
    operateType: "166", //操作模块类型
    file: file.file,
  };
  uploadFile(filedata).then((res) => {
    if (res.code == 200 && res.data) {
      file.status = "success";
      file.url = judgeStaticUrl(res.data[0])
      file.originUrl = res.data[0]
      showSuccessToast('上传成功')
    } else {
      file.status = "failed";
      showFailToast(res.message);
    }
  });
}
</script>

<style lang="less" module>
.option {
  :global {
    .banner {
      background-image: url('@/assets/workers-voice/banner-bg-2.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    .message-clazz {
      .van-cell__value {
        background-color: #f6f7f8;
        padding: 10px;
        border-radius: 15px;
      }
    }

    .success-msg {
      background-color: transparent;
      background-image: url('@/assets/workers-voice/bg-success.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: center;
      width: 509px;
      height: 628px;
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: center;
    }

    .submitbtn {
      background: linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%) !important;
    }
  }


}
</style>
