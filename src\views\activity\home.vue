<template>
  <div class="w-full h-100vh">
    <RouterView v-slot="{ Component, route }">
      <keep-alive :include="includes">
        <component :is="Component" :key="route.fullPath"></component>
        </keep-alive>
      <!-- <keep-alive >
        <component v-if="route.meta.keepAlive" :is="Component" class="" :key="route.path" />
      </keep-alive>
      <component v-if="!route.meta.keepAlive" :is="Component" class="" :key="route.path" /> -->
    </RouterView>
  </div>
</template>

<script setup lang="ts">
  defineOptions({
    name:'activityHome'
  })
  import { onMounted, onBeforeMount } from 'vue'
  import { useRouter, useRoute } from "vue-router";
  import { useUserStore } from "@/store/modules/user";
  import {useAppStore} from '@/store/modules/app'

  const useStore = useUserStore();
  const router = useRouter();
  const route = useRoute();
  const activityDetail = computed(() => useStore.activityDetail || {});

onBeforeMount(() => {
  if(!activityDetail.value.activityId) {
    useUserStore().setActivityDetail(route.query.activityId || sessionStorage.getItem('activityId'))
  }else{
    // 存在活动id，则设置当前活动详情信息
    if(route.query.activityId &&route.query.activityId !== activityDetail.value.activityId) {
      useUserStore().setActivityDetail(route.query.activityId)
    }
  }
})

const appStore = useAppStore();
const includes = computed(() => {
  return appStore.includes;
});

onMounted(() => {
  // console.log('进入页面');
  // 缓存
  // appStore.setincludes({name:'activityHome'});
})
onDeactivated(() => {
  // console.log('离开页面');
  // console.log(includes.value);
  // appStore.removeIncludes('activityHome');
})

</script>

<style lang="scss" scoped></style>
