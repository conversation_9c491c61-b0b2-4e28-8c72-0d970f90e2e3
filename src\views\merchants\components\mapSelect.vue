<template>
    <div class="relative">
        <BMap width="100%" height="100vh" ak="DbJrhrxGlSAoIsnUnNcWNnL9VPHBXcNO" :enableScrollWheelZoom="true"
            :enableDragging="true" :enableInertialDragging="true" :center="lngLat || location.point || undefined"
            @initd="initMap" :mapType="mapType" :zoom="15" :enablePinchToZoom="true" ref="mapRef" @click="getLocation">
            <BMarker :position="resultOp?.point" key="bm-marker-target"></BMarker>
            <BLabel :content="resultOp?.address" :position="resultOp?.point"></BLabel>
        </BMap>
        <div class="control-btn absolute flex justify-around items-center 
        px-30px text-#333 text-32px
        h-140px bottom-0 bg-#fff w-full z-9999">
            <div class="bg-#E6F2FF text-center px-20px py-15px text-#5AA4FF rounded-25px flex-1 mr-20px"
                @click="cancel">取消</div>
            <div class="bg-#3D92FF text-center text-#fff px-20px py-15px rounded-25px flex-1" @click="confirm">确定</div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import {
    BMap, BMarker, BLabel,
    Point, MapType, useBrowserLocation, usePointGeocoder, PointGeocoderResult
} from 'vue3-baidu-map-gl';
import utils from '@/utils/utils';

const lngLat: Point = { lng: 106.089957, lat: 30.800315 };
const mapRef = ref();
const lnglat = ref<Recordable>({ point: { lng: '104.054638', lat: '30.681797' } });
const mapType = ref<MapType>('BMAP_NORMAL_MAP')
const { get: getPoint, result: resultOp } = usePointGeocoder<PointGeocoderResult>({})

const initMap = (args) => {
    if (utils.isApp()) {
        utils.getPosition('public_nav', (code: any, content: any) => {
            if (code == 200) {
                const position = JSON.parse(content);
                lnglat.value.point.lng = position.longitude
                lnglat.value.point.lat = position.latitude
                unref(mapRef).resetCenter();
            }
        })
    } else {
        get(args);
    }
}

const { get, location } = useBrowserLocation(null, () => {
    lnglat.value = location;
    getPoint(location.value.point)
    unref(mapRef).resetCenter();
});
const emit = defineEmits(['returnLocation']);
const getLocation = (e: any) => {
    getPoint({
        lng: e.latlng?.lng,
        lat: e.latlng?.lat
    })
}
const confirm = () => {
    emit('returnLocation', {
        longitude: resultOp.value?.point?.lng,
        latitude: resultOp.value?.point?.lat,
        address: resultOp.value?.address
    })
}
const cancel = () => {
    emit('returnLocation', null)
}
</script>