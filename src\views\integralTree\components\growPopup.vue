<template>
    <!-- 树成长弹窗 -->
    <van-popup v-model:show="props.show" position="top" round class="popup box-border flex flex-col items-center">
        <div class="h-65% relative w-fit">
            <img loading="lazy" :src="currenType.imgUrl" class="h-full" />

            <div class="text absolute text-[#fff] top-34% text-center left-0 right-0 
             whitespace-nowrap overflow-hidden text-ellipsis text-1.8vh">
                {{ currenType.tips }}
            </div>
        </div>
        <div class="flex justify-center w-full">
            <div class="comfirm_btn text-center px-3vh py-1.4vh flex items-center justify-center
            text-[#fff] text-2vh  rounded-3vh leading-1" @click="confirm">
                <span v-if="props.confirmText">{{ props.confirmText }}</span>
                <slot name="right_btn"></slot>
            </div>
        </div>
    </van-popup>
</template>
<script lang="ts" setup>
import treePopup1 from '@/assets/tree/tree_1_popup.png'
import treePopup2 from '@/assets/tree/tree_2_popup.png'
import treePopup3 from '@/assets/tree/tree_3_popup.png'
import treePopup4 from '@/assets/tree/tree_4_popup.png'
import treePopup5 from '@/assets/tree/tree_5_popup.png'
import treePopup6 from '@/assets/tree/tree_6_popup.png'

const props = defineProps({
    show: {
        type: Boolean,
        default: false
    },
    popupType: {
        type: String,
        default: '1',
    },
    confirmText: {
        type: String,
        default: ''
    }
})
const currenType = computed(() => {
    return popupType.find((item) => item.type === props.popupType)
})
const popupType = [
    {
        imgUrl: treePopup1,
        type: '1',
        tips: '多浇水长得更快哦~'
    },
    {
        imgUrl: treePopup2,
        type: '2',
        tips: '多浇水长得更快哦~'
    },
    {
        imgUrl: treePopup3,
        type: '3',
        tips: '多浇水更快开花哦~'
    },
    {
        imgUrl: treePopup4,
        type: '4',
        tips: '多浇水更快结果哦~'
    },
    {
        imgUrl: treePopup5,
        type: '5',
        tips: '多浇水结出的果实更甜哦~'
    },
    {
        imgUrl: treePopup6,
        type: '6',
        tips: '快去兑换奖励吧！'
    }
]


const emit = defineEmits(['update:show', 'cancel', 'confirm'])
const confirm = () => {
    emit('confirm')
}
</script>
<style lang="scss" scoped>
.popup {
    .comfirm_btn {
        background: linear-gradient(4deg, #FD8301 0%, #F5BF3F 100%);
    }
}

.van-popup {
    background: transparent;
    width: 100vw;
    height: 100vh;
}
</style>