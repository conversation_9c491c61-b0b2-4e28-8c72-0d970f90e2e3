<template>
  <div class="echart-box" style="width:100%;height:100%">
    <div ref="echartRef" class="w-full h-full"></div>
  </div>
</template>

<script lang="ts" setup>
// 饼图
import { useECharts } from '@/utils/useECharts'
import { Ref, ref, watch, onMounted } from 'vue'
import * as echarts from 'echarts'
import { getVwSize } from '../../data'
const props = defineProps({
  dataSource: {
    type: Object,
    default: {
      xData: [
        '0-99',
        '100-499',
        '500-999',
        '1000-2999',
        '3000-9999',
        '10000-19999',
        '20000-39999',
        '40000以上',
      ],
      yData: [],
      totalPoints: []

    },
  }, //color
})

const echartRef = ref<HTMLDivElement | null>(null)
const { setOptions } = useECharts(echartRef as Ref<HTMLDivElement>)

const initChart = () => {
  let data = { x: [], y1: [], y2: [] }
  let yDataMax = 0, totalPointsMax = 0
  data.x = props.dataSource?.xData

  if (props.dataSource?.yData) {
    data.y1 = props.dataSource.yData
    yDataMax = Math.max(...props.dataSource.yData)
    if (yDataMax >= 10000) {
      data.y1 = props.dataSource.yData.map((item: any) => parseFloat((item / 10000).toFixed(2)))
    }
  }
  if (props.dataSource?.totalPoints) {
    data.y2 = props.dataSource.totalPoints
    totalPointsMax = Math.max(...props.dataSource.totalPoints)
    if (totalPointsMax >= 10000) {
      data.y2 = props.dataSource.totalPoints.map((item: any) => parseFloat((item / 10000).toFixed(2)))
    }
  }

  // 绘制图表
  var option = {
    tooltip: {
      show: true,
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      textStyle: {
        fontSize: getVwSize(22),
      },
      formatter: function (params: any) {
        let result = params[0].name + '<br>'
        params.forEach((item: any) => {
          let value = item.value
          if (item.seriesIndex === 0) value = props.dataSource?.yData[item.dataIndex];
          if (item.seriesIndex === 1) value = props.dataSource?.totalPoints[item.dataIndex];
          result += item.marker + item.seriesName + '：' + value + '<br>'
        })
        return result
      }
    },
    grid: {
      top: '20%',
      left: '0%',
      right: '0%',
      bottom: '0%',
      containLabel: true,
    },
    legend: {
      show: true,
      icon: 'square',
      orient: 'horizontal',
      top: '0%',
      right: '0%',
      itemWidth: getVwSize(20),
      itemHeight: getVwSize(20),
      itemGap: getVwSize(60),
      textStyle: {
        color: '#666666',
        fontSize: getVwSize(22),
      },
    },
    xAxis: [
      {
        type: 'category',
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitArea: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        boundaryGap: true,
        axisLabel: {
          show: true,
          interval: 0,
          rotate: 20,
          fontSize: getVwSize(22),
          lineHeight: getVwSize(30),
          color: '#999',
          formatter: (value: any) => {
            const maxChars = 7; // 每行最大字符数
            return value.split('').reduce((str, char, idx) =>
              (idx % maxChars === 0) ? str + '\n' + char : str + char, ''
            ).trim();
          },
          margin: getVwSize(40)
        },
        data: data.x,
      },
    ],
    yAxis: [
      {
        name: `人数${yDataMax >= 10000 ? '(万)' : ''}`,
        nameTextStyle: {
          color: '#999',
          fontSize: getVwSize(22),
          padding: [0, 0, 0, getVwSize(40)]
        },
        type: 'value',
        minInterval: 1,
        splitLine: {
          show: true,
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#E6E6E6',
          },
        },
        axisLabel: {
          textStyle: {
            color: '#999',
            fontSize: getVwSize(22),
          },
        },
        axisTick: {
          show: false,
        },
        min: 0,  // 取数据最小值
        max: 'dataMax'   // 取数据最大值
      },
      {
        name: `积分总数${totalPointsMax >= 10000 ? '(万分)' : '(分)'}`,
        nameTextStyle: {
          color: '#999',
          fontSize: getVwSize(22),
          padding: [0, getVwSize(50), 0, 0]
        },
        type: 'value',
        minInterval: 1,
        splitLine: {
          show: true,
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#E6E6E6',
          },
        },
        axisLabel: {
          textStyle: {
            color: '#999',
            fontSize: getVwSize(22),
          },
        },
        axisTick: {
          show: false,
        },
        min: 0,  // 取数据最小值
        max: 'dataMax'   // 取数据最大值
      },
    ],
    series: [
      {
        name: '人数',
        type: 'bar',
        barWidth: getVwSize(20),
        label: {
          show: false,
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [{
            offset: 0,
            color: 'rgba(190, 251, 245, 1)'
          },
          {
            offset: 1,
            color: 'rgba(141, 226, 218, 1)'
          }
          ]),
          borderRadius: getVwSize(16)
        },
        data: data.y1,
        yAxisIndex: 0,
      },
      {
        name: '积分总数',
        type: 'bar',
        barWidth: getVwSize(20),
        label: {
          show: false,
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [{
            offset: 0,
            color: 'rgba(161, 203, 255, 1)'
          },
          {
            offset: 1,
            color: 'rgba(90, 164, 255, 1)'
          }
          ]),
          borderRadius: getVwSize(16)
        },
        data: data.y2,
        yAxisIndex: 1,
      },
    ],
    dataZoom: [{
      show: false,
      startValue: 0,
      endValue: 4,
    },
    {
      type: 'inside',
      zoomOnMouseWheel: false,// 禁用鼠标滚轮缩放
      moveOnMouseMove: true,// 禁用鼠标拖拽移动
      moveOnMouseWheel: true,
    }]
  }

  setOptions(option)
}

onMounted(() => {
  initChart()
})

// 监听dataSource
watch(
  () => props.dataSource,
  (newValue, oldValue) => {
    initChart()
  },
  { deep: true, immediate: true }
)
</script>
<style scoped lang="scss">
.echart-box {}
</style>
