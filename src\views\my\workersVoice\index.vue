<template>
  <div class="w-full min-h-full message-info bg-#fff">
    <!-- <div class="tabs h-100px bg-white sticky top-0 z-99">
      <van-tabs v-model:active="activeValue" class="myTabs" @click-tab="changeActive">
        <van-tab :title="item.label" :name="item.value" v-for="(item, index) in tabs" :key="index"
          title-class="tab-title">
        </van-tab>
      </van-tabs>
    </div> -->
    <div class="flex bg-[#F6F7F8] rounded-[10px] mx-[75px] mt-20px">
      <div v-for="(item, index) in tabs" class="w-1/2 py-[17px] flex justify-center items-center relative text-28px"
        :class="`${activeValue === item.value ? 'text-[#5CA5FF]' : ''} ${index === 0 ? 'line-after' : ''}`"
        @click="changeActive(item)">
        {{ item.title }}
      </div>
    </div>
    <div class="pb-2 relative px-29px box-border list-box">
      <MessageList :api-list="getPersonalList" :if-nothing="false" :if-del="true" :params="listParams" type="personal"
        @delete-card="handleDelete" ref="messageListRef" />
    </div>
    <van-popup v-model:show="showCenter" round class="p-64px">
      <div class="text-[#333333] text-[36px] text-center">是否删除留言</div>
      <p class="text-[#666666] text-[26px] text-center mt-40px mb-70px">删除后，您可以继续浏览或 添加新的留言。</p>
      <div>
        <div
          class="w-[400px] submit-btn  text-34px h-[78px] rounded-[39px] flex justify-center items-center text-[#fff] absolute left-1/2 -translate-x-1/2"
          @click="handleConfirmDel">
          确认
        </div>
        <div
          class="w-[400px] bg-transparent  h-[78px] text-34px rounded-[39px] mt-81px flex justify-center items-center text-[#333333] absolute left-1/2 -translate-x-1/2"
          @click="handleCancel">
          取消
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script lang="ts" setup>
import MessageList from "@/views/workersVoice/list.vue";
import { getPersonalList, deleteLine } from "@/api/workerVoice";
import { showSuccessToast, showFailToast } from 'vant';
const activeValue = ref<boolean>(true);
const tabs = ref([
  { title: '已回复', value: true },
  { title: '未回复', value: false },
  // { label: "已通过", value: "pass" },
  // { label: "待审核", value: "wait" },
  // { label: "未通过", value: "refuse" },
]);
const showCenter = ref<boolean>(false);
// 删除中间对象
const deleteRecord = ref<Recordable>();
const router = useRouter();

const val = ref<string>();
const messageListRef = ref<{ onRefreshList: Function }>();
const dataTabs = reactive<{ active: number; nav: Recordable[] }>({
  active: 0,
  nav: [],
});

let listParams = reactive<Recordable>({});

function handleTabChange(item: Recordable) {
  dataTabs.active = item.activIndex;
}
const changeActive = (item) => {
  activeValue.value = item.value;
  listParams.replyState = item.value;
};

function handleDelete({ item }: Recordable) {
  showCenter.value = true;
  deleteRecord.value = item;
}
function handleCancel() {
  showCenter.value = false;
}
function handleConfirmDel() {
  deleteLine(`?employeeMessageId=${unref(deleteRecord)?.employeeMessageId}`).then(
    ({ code, message }) => {
      code === 200 ? showSuccessToast('删除成功！') : showFailToast(`删除失败！${message || ''}`);

      showCenter.value = false;

      unref(messageListRef)?.onRefreshList?.();
    }
  );
}


onMounted(async () => {
  listParams.replyState = activeValue.value;
});
</script>

<style lang="scss" scoped>
.message-info {
  min-height: 100vh;

  .line-after {
    &:after {
      content: '';
      width: 1px;
      height: 60%;
      position: absolute;
      background-color: #e5e5e5;
      right: 0;
    }
  }

  .tabs {
    :deep(.van-tabs--line .van-tabs__wrap) {
      height: 100px;
    }

    :deep(.van-tab) {
      line-height: 1;
      height: 100%;
    }

    :deep(.van-tabs__nav--line) {
      height: 100%;
      padding-bottom: 15px;
    }

    :deep(.van-tabs__line) {
      background: linear-gradient(0deg, #a1cbff 0%, #5aa4ff 100%);
      width: 36px;
      border-radius: 3px;
      height: 6px;
      z-index: 1;
    }

    :deep(.van-tab__text) {
      font-size: 32px;
      z-index: 2;
    }

    :deep(.van-tab--active) {
      color: #5aa4ff !important;
    }
  }

  .list-box {
    :deep(.item-box) {
      background: #ffffff;
      box-shadow: 0px 0px 10px 0px rgba(227, 227, 227, 0.5);
      border-radius: 20px;
    }

    :deep(.pb-2) {
      padding-bottom: 26px;
    }
  }

  .van-popup {
    background-color: transparent;
    background-image: url('@/assets/workers-voice/bg-delete.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    width: 500px;
    height: 520px;
  }

  .submit-btn {
    background: linear-gradient(to bottom, #5AA4FF, #A1CBFF)
  }
}
</style>