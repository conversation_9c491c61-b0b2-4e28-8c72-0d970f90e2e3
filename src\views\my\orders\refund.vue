<!-- 退款 -->
<template>
    <div class="refund w-full min-h-100vh h-fit bg-#F9F9F9 px-30px box-border">
        <div class="goods bg-[#fff] rounded-16px mt-27px">
            <div class="refund-company flex items-center text-30px py-20px px-28px">
                <img loading="lazy" src="@/assets/inclusive/shop/icon-store.png" class="w-34px h-32px block" />
                <span class="ml-10px">{{ details?.transProductSnapshot?.companyName }}</span>
            </div>
            <orderDetailCell :content="item" v-for="item, index in details?.transProductSnapshot?.productInfoList"
                :key="index" :showBtn="false" class="goods-cell" />
        </div>
        <div class="refund-info mt-20px">
            <van-form ref="formRef" @submit="submit">
                <div class="bg-[#fff] rounded-16px special">
                    <van-field label="服务类型" v-model="serviceTypeName" name="serviceTypeName" placeholder="请选择服务类型"
                        readonly required right-icon="arrow" :rules="[{ required: true, message: '请选择服务类型' }]"
                        @click="showReceive">
                    </van-field>
                    <van-field label="退款金额" required v-model="refundPrice" name="refundPrice" readonly>
                        <template #input>
                            <div class="w-full text-right text-#333 text-28px text-#5AA4FF">￥{{ refundPrice }}</div>
                        </template>
                    </van-field>
                    <inputSelect :value="form.serviceReason" name="serviceReason" required label="退款原因"
                        :requiredRule="[{ required: true, message: '退款原因' }]" labelWidth="fit-content"
                        :columns="reasonClomun" placeholder="请选择退款原因" rightIcon="arrow"
                        @onConfirm="(val) => onConfirmSelect(val, 'serviceReason')">
                    </inputSelect>
                </div>

                <div class="refund-remark mt-20px">
                    <van-field label="退款备注" label-width="100%" type="textarea" v-model="form.serviceRemark"
                        name="serviceRemark" placeholder="请输入退款备注" rows="3" maxlength="100" show-word-limit>
                    </van-field>
                </div>
            </van-form>
        </div>
        <div class="refund-btn text-34px text-#fff text-center 
        h-78px w-60% flex items-center justify-center mx-auto mt-60px" @click="handleSubmit">
            提交信息
        </div>
        <refundTypePopup :checkedValue="form.saleServiceType"
            :disabledAll="details?.shopOrderRecord?.orderState == 'deliver'" v-model:show="show"
            @selected="selectedType" />
    </div>
</template>

<script setup lang="ts">
import { orderDetails, orderAfterSale } from "@/api/mall/inclusive"
import refundTypePopup from "./components/refundTypePopup.vue";
import inputSelect from "@/components/inputSelect/index.vue"
import orderDetailCell from "./components/orderDetailCell.vue";
import { useDictionary } from "@/store/modules/dictionary";
import { showDialog, showToast } from "vant";
import useRefreshFun from '@/hooks/app.ts';
const route = useRoute()
const router = useRouter()
const dictionary = useDictionary()
const formRef = ref<any>(null)
const form = ref({
    backType: route.query.type || 'backOrder',//退款类型，backOrder整个订单退款；backDetail单笔商品退款
    orderId: route.query.orId,
    saleServiceType: '',
    serviceReason: '',
    serviceRemark: '',
    backAmountTotal: '',
    backIntegralTotal: 0,
    orderProductSubIdList: [],//backType === 'backDetail'时，传入需要退款的商品子单id列表

})
const show = ref(false)
const refundPrice = computed(() => {
    let total = 0
    details.value?.transProductSnapshot?.productInfoList?.forEach(item => {
        total += item.priceListInfo.reduce((total: number, cur) => {
            return parseFloat(total) + parseFloat(cur.nowPrice * cur.currentProductCount)
        }, 0)
    })
    form.value.backAmountTotal = total.toFixed(2)//退款金额总和

    return total.toFixed(2)
})
const reasonClomun = computed(() => dictionary.getDictionaryOpt?.['saleServiceReason'])
const serviceTypeName = ref('')

const selectedType = (val: any) => {
    if (details.value?.shopOrderRecord?.orderState == "deliver" && val.value === 'returnAll') {
        return showToast('待发货商品不支持退货，请重新选择')
    }
    form.value.saleServiceType = val.value
    serviceTypeName.value = val.label

}
const showReceive = () => {
    show.value = true
}

const details = ref<any>({})
const getOrderDetail = async () => {
    const res = await orderDetails(route.query.orId)
    if (res.code === 200) {
        details.value = res.data
        // 待发货 -默认仅退款
        if (details.value?.shopOrderRecord?.orderState === 'deliver') {
            form.value.saleServiceType = 'refund'
            serviceTypeName.value = '仅退款'
        }
        // end

        // 过滤到虚拟商品
        details.value.transProductSnapshot.productInfoList = details.value.transProductSnapshot?.productInfoList?.filter((item: any) => item.productType === 'actual')

        // 单个规格退款时，过滤到对应规格的商品信息
        if (route.query?.type === 'backDetail' && route.query?.psubId as string) {
            form.value.orderProductSubIdList = route.query.psubId?.split(',')

            details.value.transProductSnapshot.productInfoList.forEach((item: any) => {
                item.priceListInfo = item.priceListInfo?.filter((sub: any) => form.value.orderProductSubIdList.includes(sub.orderProductSubId))
            })
        }
    }
}

// 提交
const handleSubmit = async () => {
    formRef.value?.submit()
}
const submit = async (val: any) => {
    if (val) {
        const res = await orderAfterSale(form.value)
        if (res.code === 200) {
            showToast({
                message: '实物商品申请退款成功,预计退款结果将在72小时内完成。',
                duration: 1000,
            })
            refresh()
            setTimeout(() => {
                router.go(-1)
            }, 1000)
        } else {
            showDialog({
                title: '提示',
                message: res.message || '提交失败',
            })
        }
    }
}

const onConfirmSelect = (val: any, name: string) => {
    if (name === 'serviceReason') {
        form.value[name] = val[0].label
    }
}

// 触发列表刷新执行事件
const { addRefreshList } = useRefreshFun()
const refresh = () => {
    addRefreshList({ pageName: 'myOrders', funsName: ['changePage'] })
}

onMounted(() => {
    getOrderDetail()
})
</script>

<style lang="scss" scoped>
.van-field {
    background-color: #fff;
    margin-bottom: 20px;
    border-radius: 16px;
    font-size: 28px;
    line-height: 1;
}

:deep(.van-field__label) {
    color: #333333;
    font-size: 28px;
}

:deep(.van-field__body) {
    display: flex;
    justify-content: flex-end;
}

:deep(.van-cell) {
    line-height: 1.5;
}

.special {
    :deep(.van-field__control) {
        text-align: right;
    }
}

:deep(.van-field__control) {
    color: #333333;
    font-size: 28px;
}

:deep(.van-field__control::placeholder) {
    color: #666;
    font-size: 28px;
}

.refund-remark {
    :deep(.van-field__control::placeholder) {
        color: #999;
    }

    :deep(.van-field__body) {
        margin-top: 10px;
    }
}

:deep(.van-field__error-message) {
    text-align: right;
}

.refund-btn {
    background: linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%);
    border-radius: 40px;

}

:deep(.van-field__right-icon) {
    font-size: 20px;
}

.goods {
    .goods-cell {
        border-bottom: 1px solid #F2F2F2;

        &:last-child {
            border-bottom: none;
        }
    }

}
</style>