<template>
    <!-- 单身联谊修改页面 -->
    <div class="apply-form w-full min-h-100vh h-fit">
        <div class="form px-30px pt-39px pb-50px">
            <!-- 表单 -->
            <div class="auth-form bg-[#fff] pt-40px px-28px pb-66px mt-20px">
                <!-- <div class="text-center">
                    <img loading="lazy" src="@/assets/friendShip/profile/title_icon.png" class="w-284px" />
                </div> -->

                <div class="img-box relative w-full  h-480px ">
                    <!-- <van-popover v-model:show="showPopover" placement="bottom-end" v-if="type != 0">
                
                      <div class="tips_box">
                        <span class="tips_btn" @click="showPopover = false">确定</span>
                      </div>
                      

                    <template #reference>
                        <img loading="lazy" src="@/assets/lovespace/tips.png" class="w-[40px] h-[17px]  top-[10px] right-[-14px]" />
                    </template>
</van-popover> -->
                    <img loading="lazy" class="absolute w-full top-0 left-0 h-full" style="object-fit: cover;"
                        v-if="avatar[0]" :src="avatar[0].url" alt="">
                    <div class="img-list">
                        <van-uploader v-model="album" max-count="8" :after-read="afterRead">
                            <template #preview-cover="item">
                                <div class="preview-coverAll " @touchstart="starTouchs(item)" @touchmove="endTouchs"
                                    @touchend="endTouchs"></div>
                            </template>
                        </van-uploader>
                    </div>
                </div>
                <div class="text-center w-full text-24px text-#aaa">（长按设置头像）</div>
                <!-- 表单内容 -->
                <van-form ref="formRef" label-align="top" @submit="getForm" @failed="onFailed" :disabled="formDisabled">
                    <!-- 头像上传 -->
                    <!-- <van-field :rules="[{ required: true, message: '请上传头像' }]" class="special avatar">
                        <template #input>
                            <div class="uploader_avator w-full mx-auto text-center mt-56px">
                                <van-uploader v-model="avatar" max-count="1" accept="image/*" reupload
                                    :after-read="afterRead" :disabled="formDisabled">
                                    <div class="imgs relative">
                                        <img loading="lazy" src="@/assets/friendShip/profile/default_avatar.png" class="w-156px" />
                                        <div class="absolute w-65px h-65px rounded-50% bottom-10px -right-10px
                            bg-[#D5D5D5] flex items-center justify-center">
                                            <img loading="lazy" src="@/assets/public/camera_icon.png" class="w-30px">
                                        </div>
                                    </div>
                                    <div class="text-[#444] text-24px required">上传头像</div>
                                </van-uploader>
                            </div>
                        </template>
                    </van-field> -->
                    <!-- 照片上传 -->
                    <!-- <van-field :rules="[{ required: true, message: '请上传照片' }]" class="special avatar">
                        <template #input>
                            <div class="uploader_avator w-full mx-auto text-center mt-56px">
                                <van-uploader v-model="album" max-count="5" accept="image/*" reupload
                                    :after-read="afterRead" :disabled="formDisabled">
                                    <div class="imgs relative">
                                        <div class="w-156px h-156px bg-[#eee]"></div>

                                        <div class="absolute w-65px h-65px rounded-50% bottom-10px -right-10px
                            bg-[#D5D5D5] flex items-center justify-center">
                                            <img loading="lazy" src="@/assets/public/camera_icon.png" class="w-30px">
                                        </div>
                                    </div>
                                    <div class="text-[#444] text-24px required">图片上传</div>
                                </van-uploader>
                            </div>
                        </template>
                    </van-field> -->
                    <van-field v-model="form.nickname" required label="昵称" placeholder="请输入昵称"
                        :rules="rules.nickname" />
                    <van-field v-model="form.phone" required label="联系电话" placeholder="请输入联系电话" :rules="rules.phone"
                        readonly />
                    <inputSelect :value="incomeMonthName" name="incomeMonth" required label="月收入" readonly
                        :requiredRule="rules.incomeMonth" :columns="incomeMonthClomun" placeholder="请选择月收入"
                        :disabled="formDisabled" @onConfirm="(val) => onConfirmSelect(val, 'incomeMonth')" />

                    <inputSelect :value="educationName" name="education" required label="学历" placeholder="请选择学历"
                        :filterSearch="true" :requiredRule="rules.education" :columns="modelEducationClomnu"
                        @onConfirm="(val) => onConfirmSelect(val, 'education')" :disabled="formDisabled" />
                    <van-field v-model="form.currentAddress" name="currentAddress" label="现居地址" placeholder="请输入现居地址"
                        required :rules="rules.currentAddress" />
                    <van-field v-model="form.workAddress" name="workAddress" label="工作地址" placeholder="请输入工作地址" required
                        :rules="rules.workAddress" />
                    <inputSelect :value="workPostName" name="workPost" required label="工作岗位" readonly
                        :columns="workPostClomun" placeholder="请选择工作岗位" :requiredRule="rules.workPost"
                        :filterSearch="true" @onConfirm="(val) => onConfirmSelect(val, 'workPost')"
                        :disabled="formDisabled" />

                    <van-field v-model="form.residence" name="residence" label="户籍所在地" placeholder="请输入户籍所在地" required
                        :rules="rules.residence" />

                    <van-field v-model="form.maritalStatus" name="maritalStatus" required label="婚姻状况" class="special"
                        :rules="rules.maritalStatus">
                        <template #input>
                            <van-radio-group v-model="form.maritalStatus" direction="horizontal" shape="square"
                                :disabled="formDisabled">
                                <van-radio :name="i.value" v-for="i in maritalStatusClomun" :key="i.value">{{
                                    i.label
                                    }}</van-radio>
                            </van-radio-group>
                        </template>

                    </van-field>

                    <!-- <van-field v-model="form.singleStatus" name="singleStatus" label="是否脱单" label-align="left" required
                        class="radioBox" :rules="rules.singleStatus">
                        <template #input>
                            <van-radio-group v-model="form.singleStatus" direction="horizontal" shape="square"
                                :disabled="formDisabled">
                                <van-radio :name="i.value" v-for="i in commonYesNo" :key="i.value">{{
                                    i.label
                                    }}</van-radio>
                            </van-radio-group>
                        </template>
                    </van-field> -->

                    <van-field v-model="form.houseStatus" name="houseStatus" label="是否有房" label-align="left" required
                        class="radioBox" :rules="rules.houseStatus">
                        <template #input>
                            <van-radio-group v-model="form.houseStatus" direction="horizontal" shape="square"
                                :disabled="formDisabled">
                                <van-radio :name="i.value" v-for="i in commonYesNo" :key="i.value">{{
                                    i.label
                                    }}</van-radio>
                            </van-radio-group>
                        </template>
                    </van-field>
                    <van-field v-model="form.carStatus" name="carStatus" label="是否有车" label-align="left" required
                        class="radioBox" :rules="rules.carStatus">
                        <template #input>
                            <van-radio-group v-model="form.carStatus" direction="horizontal" shape="square"
                                :disabled="formDisabled">
                                <van-radio :name="i.value" v-for="i in commonYesNo" :key="i.value">{{
                                    i.label
                                    }}</van-radio>
                            </van-radio-group>
                        </template>
                    </van-field>
                    <van-field v-model="form.biography" name="biography" label="自我介绍" placeholder="请输入自我介绍" rows="3"
                        maxlength="200" show-word-limit type="textarea" :rules="rules.biography" required>
                    </van-field>

                    <!-- 择偶标准 -->
                    <div class="mt-64px">
                        <div class="mate-title font-medium text-[#3F95FF] text-34px text-center">择偶标准</div>
                        <van-field name="age" label="年龄" class="special min-field">
                            <template #input>
                                <van-field v-model="form.singleMateChoose.lowerAge" placeholder="请输入" type="number"
                                    class="min-input min-input-left"></van-field>
                                <div class="line w-80px h-2px bg-[#DADADA] mx-22px mt-50px"></div>
                                <van-field v-model="form.singleMateChoose.upperAge" placeholder="请输入" type="number"
                                    class="min-input"></van-field>
                            </template>
                        </van-field>
                        <van-field name="height" label="身高(cm)" class="special min-field">
                            <template #input>
                                <van-field v-model="form.singleMateChoose.lowerHeight" placeholder="请输入" type="number"
                                    class="min-input min-input-left"></van-field>
                                <div class="line w-80px h-2px bg-[#DADADA] mx-22px mt-50px"></div>
                                <van-field v-model="form.singleMateChoose.upperHeight" placeholder="请输入" type="number"
                                    class="min-input"></van-field>
                            </template>
                        </van-field>
                        <inputSelect :value="educationName1" required name="education" label="学历" placeholder="请选择学历"
                            :columns="[{ label: '不限', value: '' }, ...modelEducationClomnu]"
                            :requiredRule="rules.singleMateChoose.education" :filterSearch="true"
                            @onConfirm="(val) => onConfirmSelect(val, 'education1')" :disabled="formDisabled" />

                        <inputSelect :value="incomeMonthName1" required name="incomeMonth" label="月收入"
                            :requiredRule="rules.singleMateChoose.education"
                            :columns="[{ label: '不限', value: '' }, ...incomeMonthClomun]"
                            @onConfirm="(val) => onConfirmSelect(val, 'incomeMonth1')" :disabled="formDisabled">
                        </inputSelect>

                        <van-field v-model="form.singleMateChoose.houseStatus" name="houseStatus" label="是否有房"
                            label-align="left" required class="radioBox" :rules="rules.singleMateChoose.houseStatus">
                            <template #input>
                                <van-radio-group v-model="form.singleMateChoose.houseStatus" direction="horizontal"
                                    shape="square" :disabled="formDisabled">
                                    <van-radio :name="i.value" v-for="i in commonYesNo1" :key="i.value">{{
                                        i.label
                                        }}</van-radio>
                                    <van-radio name="none" key="none"> 不限 </van-radio>
                                </van-radio-group>
                            </template>
                        </van-field>
                        <van-field v-model="form.singleMateChoose.carStatus" name="carStatus" label="是否有车"
                            label-align="left" required class="radioBox" :rules="rules.singleMateChoose.carStatus">
                            <template #input>
                                <van-radio-group v-model="form.singleMateChoose.carStatus" direction="horizontal"
                                    shape="square" :disabled="formDisabled">
                                    <van-radio :name="i.value" v-for="i in commonYesNo1" :key="i.value">{{
                                        i.label
                                        }}</van-radio>
                                    <van-radio name="none" key="none"> 不限 </van-radio>
                                </van-radio-group>
                            </template>
                        </van-field>
                    </div>
                </van-form>
                <div class="controll-btn text-32px text-[#fff] rounded-40px w-70% mx-auto h-83px flex items-center justify-center mt-30px"
                    @click="submithandle" v-if="!formDisabled">
                    提交
                </div>
            </div>
        </div>
        <!-- 协议弹窗 -->
        <singlePopup :showPop="showPop" @closePopup="closeSinglePopup"></singlePopup>
        <!-- 提示弹窗 -->
        <applyPopup :showPop="showApplyPop" :type="popupType" :msg="popupMsg" @closePopup="closeApplyPopup">
        </applyPopup>
    </div>
</template>
<script lang="ts" setup>
import inputSelect from '@/components/inputSelect/index.vue'
import { useUserStore } from '@/store/modules/user'
import { useDictionary } from '@/store/modules/dictionary'
import { updateSingleForm, getSingleRecord } from '@/api/friendship/profile'
import { queryMyDetail, } from '@/api/friendShip/index'

import { uploadFile } from '@/api/public';
import { showFailToast, showSuccessToast } from 'vant';
import singlePopup from '../components/singlePopup.vue'
import applyPopup from '../components/applyPopup.vue'
import { judgeStaticUrl } from '@/utils/utils'
import { showToast } from 'vant'
onMounted(() => {
    initalPage()
})
const userStore = useUserStore()
const dictionary = useDictionary()
const userInfo = computed(() => userStore.userInfo)
const formDisabled = ref(false)
// 初始化页面
const initalPage = () => {
    queryMyDetail().then((res: any) => {
        if (res.data) {
            // 存在表单-回显
            form.value = res.data
            // 流程展示
            if (form.value.auditStatus === 'wait') {
                currentflow.value = '2'
                formDisabled.value = true //审核状态不允许编辑
            }
            else if (form.value.auditStatus === 'refuse') currentflow.value = '3'

            // end

            avatar.value = form.value.avatar?.split(',').map((item: string) => {
                return {
                    Image: item,
                    url: judgeStaticUrl(item)
                }
            })
            album.value = form.value.album ? form.value.album?.split(',').map((item: string) => {
                return {
                    Image: item,
                    url: judgeStaticUrl(item)
                }
            }) : []
            // 下拉回显
            educationName.value = modelEducationClomnu.value.find((item: any) => item.value == form.value.education)?.label
            workPostName.value = workPostClomun.value.find((item: any) => item.value == form.value.workPost)?.label
            incomeMonthName.value = incomeMonthClomun.value.find((item: any) => item.value == form.value.incomeMonth)?.label

            // 择偶标准
            if (!form.value.singleMateChoose) form.value.singleMateChoose = {}
            else {
                educationName1.value = [{ label: '不限', value: null }, ...modelEducationClomnu.value].find((item: any) => item.value == form.value.singleMateChoose?.education)?.label || '不限'
                incomeMonthName1.value = [{ label: '不限', value: null }, ...incomeMonthClomun.value].find((item: any) => item.value == form.value.singleMateChoose?.incomeMonth)?.label || '不限'
            }
            checked.value = true // 编辑状态-已阅读
        }
    })
}


const currentflow = ref('1')
const validatorMaxAge = (val: any) => {
    if (val < form.value.singleMateChoose?.lowerAge) {
        return false
    }
}
const validatorMaxHeight = (val: any) => {
    if (val < form.value.singleMateChoose?.lowerHeight) {
        return false
    }
}
// end
const afterRead = (file: any) => {
    let filedata = {
        operateType: "162", //操作模块类型
        file: file.file,
    }
    file.status = "uploading"
    uploadFile(filedata).then((res) => {
        if (res.code == 200 && res.data) {
            file.status = "success";
            file.Image = res.data[0]
            console.log(avatar.value);
            console.log(album.value);


            showSuccessToast('上传成功')
        } else {
            file.status = "failed";
            showFailToast(res.message);
        }
    })
}
// 长按设置头像
const touchLoop = ref()
const starTouchs = (item) => {
    touchLoop.value = setTimeout(() => {
        avatar.value[0].url = judgeStaticUrl(item.Image)
        avatar.value[0].Image = item.Image
    }, 1000)
}
const endTouchs = () => {
    clearTimeout(touchLoop.value)
}
const rules = ref({
    nickname: [{ required: true, message: '请输入昵称', trigger: 'blur' }],
    phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
    incomeMonth: [{ required: true, message: '请输入月收入', trigger: 'blur' }],
    education: [{ required: true, message: '请选择学历', trigger: 'change' }],
    currentAddress: [{ required: true, message: '请输入现居地址', trigger: 'blur' }],
    workAddress: [{ required: true, message: '请输入工作地址', trigger: 'blur' }],
    workPost: [{ required: true, message: '请选择工作岗位', trigger: 'change' }],
    maritalStatus: [{ required: true, message: '请选择婚姻状况', trigger: 'change' }],
    residence: [{ required: true, message: '请输入居住地', trigger: 'change' }],
    singleStatus: [{ required: true, message: '请选择单身状态', trigger: 'change' }],
    houseStatus: [{ required: true, message: '请选择是否有房', trigger: 'change' }],
    carStatus: [{ required: true, message: '请选择是否有车', trigger: 'change' }],
    biography: [{ required: true, message: '请输入个人简介', trigger: 'blur' }],
    singleMateChoose: {
        lowerAge: [{ required: true, message: '请输入最小年龄', trigger: 'change' }],
        upperAge: [{ required: true, message: '请输入最大年龄', trigger: 'change' }, {
            validator: validatorMaxAge, trigger: 'change', message: '最大年龄不能小于最小年龄'
        }],
        lowerHeight: [{ required: true, message: '请输入最低身高', trigger: 'change' }],
        upperHeight: [{ required: true, message: '请输入最高身高', trigger: 'change' }, {
            validator: validatorMaxHeight, trigger: 'change', message: '最高身高不能小于最低身高'
        }],
        education: [{ required: true, message: '请选择择偶学历', trigger: 'change' }],
        incomeMonth: [{ required: true, message: '请选择择偶月收入', trigger: 'change' }],
        houseStatus: [{ required: true, message: '请选择择偶是否有房', trigger: 'change' }],
        carStatus: [{ required: true, message: '请选择择偶是否有车', trigger: 'change' }]
    }
})
// end
// 学历
const modelEducationClomnu = computed(() => {
    return dictionary.getDictionaryOpt?.['modelEducation']
})
const educationName = ref('')
const educationName1 = ref('不限')//择偶学历
// end

// 月收入
const incomeMonthClomun = computed(() => {
    return dictionary.getDictionaryOpt?.['incomeMonth']
})
const incomeMonthName = ref('')
const incomeMonthName1 = ref('不限') //择偶月收入
// end

// 工作岗位
const workPostClomun = computed(() => {
    return dictionary.getDictionaryOpt?.['workPost']
})
const workPostName = ref('')
// end
// 公共是否
const commonYesNo = computed(() => dictionary.getDictionaryOpt?.['YesOrNo'])
const commonYesNo1 = computed(() => dictionary.getDictionaryOpt?.['YesOrNo'].filter((item: any) => item.value === 'y'))
// end

// 婚姻状况
const maritalStatusClomun = computed(() => {
    return dictionary.getDictionaryOpt?.['maritalStatus']
})
// end

const avatar = ref<any>([])
const album = ref<any>([])

const formRef = ref(null)
const form = ref<any>({
    nickname: userInfo.value?.nickname,
    identityNum: userInfo.value?.userIdCardNo,
    companyId: userInfo.value?.companyId,
    phone: userInfo.value?.phone,
    gender: userInfo.value?.gender === '男' ? 'male' : userInfo.value?.gender === '女' ? 'female' : '',
    birthDate: userInfo.value?.dateOfBirth,
    constellation: "",
    height: 175,
    incomeMonth: "",
    education: "",
    currentAddress: "",
    workAddress: "",
    workPost: "",
    maritalStatus: "",
    residence: "",
    singleStatus: "n",
    houseStatus: "",
    carStatus: "",
    biography: "",
    singleMateChoose: {
        upperAge: '',
        lowerAge: '',
        upperHeight: "",
        lowerHeight: "",
        houseStatus: "",
        carStatus: "",
        education: "",
        incomeMonth: ""
    },//择偶标准
})

const onConfirmSelect = (val: any, name: string) => {
    if (name !== 'education1' && name !== 'incomeMonth1') form.value[name] = val[0]?.value
    else if (name === 'education1') form.value.singleMateChoose.education = val[0]?.value
    else if (name === 'incomeMonth1') form.value.singleMateChoose.incomeMonth = val[0]?.value
    switch (name) {
        case 'education':
            educationName.value = val[0]?.label
            break
        case 'incomeMonth':
            incomeMonthName.value = val[0]?.label
            break
        case 'education1':
            educationName1.value = val[0]?.label
            break
        case 'incomeMonth1':
            incomeMonthName1.value = val[0]?.label
            break
        case 'workPost':
            workPostName.value = val[0]?.label
            break
        default:
            break
    }
}

// 弹窗提示
const showPop = ref(false)
const checked = ref(false)
const showApplyPop = ref(false)
const popupType = ref('error')
const popupMsg = ref('')
function closeSinglePopup() {
    showPop.value = false
    checked.value = true
}

const router = useRouter()
function closeApplyPopup() {
    showApplyPop.value = false
    if (popupType.value === 'success') {
        router.back()
    }
}
const submithandle = () => {
    if (formDisabled.value) { showToast('审核中，请勿再次提交'); return }

    formRef.value?.submit()
}
const onFailed = (errorInfo: any) => {
    if (errorInfo?.errors?.length > 0) return showToast(errorInfo?.errors[0].message)
}
// 提交
const getForm = async (val: any) => {
    const params = {
        ...form.value,
        avatar: avatar.value[0]?.Image,
        album: album.value.map(t => t.Image).join(',')
    }
    delete params.autoId
    let mycode = 0, mymessage = '请先完善资料'
    const { code, message } = await updateSingleForm(params)
    mycode = code
    mymessage = message
    if (mycode == 200) {
        popupType.value = 'success'
        // 或点击缘空间首页-我的资料 处查看审核进度
        popupMsg.value = '修改成功，请耐心等待审核'
        showApplyPop.value = true
    } else {
        popupType.value = 'error'
        popupMsg.value = mymessage
        showApplyPop.value = true
    }
}

</script>
<style scoped lang="scss">
.apply-form {
    background-image: url('@/assets/friendShip/profile/bj.png');
    background-size: 100% 100%;

    .auth-form {
        border-radius: 20px 20px 0px 0px;

    }

    .mate-title::before {
        content: '';
        display: inline-block;
        width: 69px;
        height: 20px;
        background-image: linear-gradient(90deg, #ABD1FF 36%, transparent 36%, transparent 86%, transparent 86%);
        background-size: 23px 20px;
        background-repeat: repeat-x;
        transform: skewX(-40deg);
        margin-right: 26px;
    }

    .mate-title::after {
        content: '';
        display: inline-block;
        width: 69px;
        height: 20px;
        background-image: linear-gradient(90deg, #ABD1FF 36%, transparent 36%, transparent 86%, transparent 86%);
        background-size: 23px 20px;
        background-repeat: repeat-x;
        transform: skewX(-40deg);
        margin-left: 45px;
    }


    :deep(.van-cell) {
        padding: 0;
        margin-bottom: 20px;
    }

    :deep(.van-field__body) {
        font-weight: 400;
        font-size: 28px;
        color: #999;
        padding: 25px;
        background-color: #F1F6FB;
        border-radius: 10px;
        border: 1px solid #5AA4FF;
    }

    :deep(.van-field__label) {
        color: #333;
        margin-bottom: 26px;
        font-size: 32px;
        font-weight: 500;
    }

    // :deep(.van-checkbox-group) {
    //     display: flex;
    //     justify-content: flex-end;
    // }


    .radioBox {
        padding: 15px 0;

        :deep(.van-field__body) {
            border: none;
            background: #fff;
            padding: 0;
            display: flex;
            justify-content: flex-end;
        }

        :deep(.van-field__label) {
            color: #333;

        }

        .van-radio--horizontal {
            margin-right: 40px;
        }

        .van-radio-group--horizontal {
            justify-content: flex-end;
            flex: 1;
        }

        :deep(.van-field__error-message) {
            text-align: right;
        }
    }

    .special {
        .van-radio--horizontal {
            margin-right: 20px;
            margin-top: 15px;
        }

        :deep(.van-field__body) {
            border: none;
            background: #fff;
            padding: 0;
            display: flex;
            justify-content: flex-end;
        }

        :deep(.van-checkbox__icon) {
            height: fit-content;
            font-size: unset;
            line-height: none;
        }

        :deep(.van-checkbox--horizontal) {
            margin-right: 0;
        }

        :deep(.van-checkbox-group--horizontal) {
            gap: 14px;
        }

        .label-btn {
            border: 1px solid #5AA4FF;
        }

        .label-active {
            background-color: #3F95FF;
            color: #fff;
        }
    }

    .min-field {
        :deep(.van-field__control--custom) {
            align-items: flex-start;

        }
    }

    .min-input {
        :deep(.van-field__body) {
            font-weight: 400;
            font-size: 28px;
            color: #999;
            padding: 25px;
            background-color: #F1F6FB;
            border-radius: 10px;
            border: 1px solid #5AA4FF;
        }
    }

    :deep(.van-checkbox__icon .van-icon),
    :deep(.van-radio__icon .van-icon),
    :deep(.van-radio__icon) {
        display: flex;
        align-items: center;
        width: 30px;
        height: 30px;
        line-height: 30px;
        border-radius: 4px;
        border-color: #5AA4FF;
        font-size: 24px;
    }

    :deep(.van-checkbox__icon) {
        display: flex;
        align-items: center;
    }

    .add-label {
        border: 1px solid #565656;
    }

    :deep(.van-uploader__preview-image) {
        border-radius: 50%;
    }

    .controll-btn {
        background: linear-gradient(0deg, #3F95FF 0%, #93CFFD 100%);
    }

    .avatar {
        :deep(.van-field__error-message) {
            text-align: center;
        }
    }

    .required::before {
        margin-right: 2px;
        color: var(--van-field-required-mark-color);
        content: "*";
        font-size: 28px;
    }

    .img-box {

        margin-bottom: 10px;
        box-sizing: border-box;
        padding: 380px 0 0 16px;

        .big-img {
            border-radius: 15px;
        }

        .img-list {
            // display: flex;
            position: relative;
            z-index: 1;
            overflow-x: auto;

            :deep(.van-uploader) {
                .van-uploader__wrapper {
                    flex-flow: nowrap;
                }

                .van-uploader__upload--readonly {
                    display: none
                }

                .van-uploader__preview {
                    margin-right: 24px
                }

                .van-uploader__preview-image {
                    width: 84px;
                    height: 84px;
                    border-radius: 16px;
                    // margin-right: 7px
                }

                .van-uploader__upload {
                    width: 84px;
                    height: 84px;
                    border-radius: 16px;
                    margin: 0;
                }

                .preview-coverAll {
                    width: 100%;
                    height: 100%;
                    background: #000;
                    opacity: 0;
                }
            }
        }
    }
}
</style>
