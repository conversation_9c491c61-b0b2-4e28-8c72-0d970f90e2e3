<template>
    <div class="product-detail bg-[#F9F9F9] w-full">
        <van-skeleton title :row="10" :loading="loading">
            <div class="details pt-27px px-34px pb-30px box-border overflow-y-scroll">
                <div class="info bg-[#fff] pb-30px">
                    <van-swipe class="my-swipe w-full h-517px box-border" :autoplay="3000" indicator-color="white">
                        <van-swipe-item v-for="(item, index) in goodsInfo.productPublicityImg"
                            class="w-full h-full product-img" :key="index">
                            <img loading="lazy" :src="item" v-previewImg="item" class="w-full h-full rounded-28px" />
                        </van-swipe-item>
                    </van-swipe>

                    <div class="title mt-28px mx-26px">{{ goodsInfo.productName }}</div>

                    <div class="price-info flex justify-between mt-34px mx-26px" v-if="specifyInfo.length">
                        <div class="price text-[#FF4344]">
                            <span v-if="goodsInfo.consumeType === 'mix'">
                                <span class="text-19px">￥</span>
                                <span class="text-40px font-medium">
                                    {{ route.query?.sourceType === '2' ?
                                        specifyInfo[currentSpecifyIndex].discountPrice :
                                        specifyInfo[currentSpecifyIndex].nowPrice
                                    }}
                                </span>
                            </span>

                            <span class="flex items-center">
                                <span>
                                    <span class="text-34px font-medium" v-if="goodsInfo.consumeType === 'mix'">+</span>
                                    <span class="text-40px font-medium">
                                        {{
                                            route.query?.sourceType === '2' ?
                                                specifyInfo[currentSpecifyIndex].discountIntegral :
                                                specifyInfo[currentSpecifyIndex].nowIntegral
                                        }}
                                    </span>
                                </span>
                                <span class="unit text-22px text-26px">积分</span>
                            </span>
                        </div>
                        <div class="ex-text text-#999 text-26px">{{
                            specifyInfo[currentSpecifyIndex].reserveType === 'limited' ?
                                `剩余${specifyInfo[currentSpecifyIndex].reserve}` : '剩余不限'
                        }}</div>
                    </div>
                </div>
                <div class="uses bg-[#fff] rounded-28px px-25px py-30px mt-22px" v-if="goodsInfo.integralPayment">
                    <div class="second-title text-32px text-#333 flex items-center leading-none">
                        使用方法
                    </div>
                    <!-- 购买流程 -->
                    <div class="buy-flow mx-auto flex justify-between mt-20px">
                        <div class="flow-item mt-27px flex-1 text-center relative " :class="{ 'mr-15px': index < Payment[goodsInfo.integralPayment]?.length - 1 }
                            " v-for="item, index in Payment[goodsInfo.integralPayment]" :key="index">
                            <img loading="lazy" :src="item.icon" class="w-50px">
                            <div class="text-24px text-[#666] mt-14px whitespace-nowrap">{{ item.title }}</div>

                            <div class="line bg-[#CCC] h-1px w-50% absolute -right-45px top-25px"
                                v-if="index < Payment[goodsInfo.integralPayment].length - 1"></div>
                        </div>
                    </div>
                </div>
                <!-- 规格 -->
                <div class="specify-goods rounded-28px py-30px px-25px bg-[#fff] mt-22px flex justify-between"
                    v-if="specifyInfo.length" @click="showSpecify = true">
                    <div class="name text-32px text-#333">规格</div>
                    <div class="flex items-center">
                        <div class="text-28px text-#333">x{{ specifyInfo[currentSpecifyIndex].orderNum }}</div>
                        <img loading="lazy" src="@/assets/integralMall/small_arrow.png" class="w-14px ml-20px">
                    </div>
                </div>
                <!-- 线下核销 -->
                <div class="merchant rounded-28px py-30px px-25px bg-[#fff] mt-22px"
                    v-if="goodsInfo.integralPayment === '2'">
                    <div class="second-title text-32px text-#333 flex items-center leading-none">
                        使用商家
                    </div>
                    <div class="cell flex items-center mt-30px">
                        <img loading="lazy" src="@/assets/integralMall/offonline_icon.png" class="w-30px" />
                        <div class="text-28px ml-15px text-#333">{{ goodsInfo.companyName }}</div>
                    </div>
                    <!-- 地址 -->
                    <div class="cell flex items-center mt-30px" @click="locationAdd">
                        <img loading="lazy" src="@/assets/integralMall/address_re_icon.png" class="w-30px" />
                        <div class="text-28px ml-15px text-#333">{{ goodsInfo.address }}</div>
                    </div>
                    <!-- 电话 -->
                    <div class="cell flex items-center mt-30px" @click="toCall">
                        <img loading="lazy" src="@/assets/integralMall/phone_icon.png" class="w-25px" />
                        <div class="text-28px ml-15px text-#333">{{ goodsInfo.contractPhone }}</div>
                    </div>
                </div>
                <!-- 线上发货 -->
                <div class="ex-tips rounded-28px py-30px px-25px bg-[#fff] mt-22px"
                    v-else-if="goodsInfo.integralPayment === '1'">
                    <div class="second-title text-32px text-#333 flex items-center leading-none">
                        兑换须知
                    </div>
                    <div class="text-28px text-[#666] mt-16px leading-40px">
                        兑后48小时内发货，确认兑换过后积分不退港澳台、海外、受其他外在影响或突发情况地区暂不支持发货。
                    </div>
                </div>
                <div class="intro rounded-28px py-30px px-25px bg-[#fff] mt-22px">
                    <div class="second-title text-32px text-#333 flex items-center leading-none">
                        商品详情
                    </div>
                    <div class="text-28px text-[#666] mt-30px" v-html="goodsRichText">
                    </div>
                </div>
            </div>
            <div class="controll-bottom h-140px flex items-center bg-[#fff] px-30px safe_area_bottom">
                <div class="left flex justify-between items-center mr-40px">
                    <div class="share text-24px text-[#666] text-center mr-45px" @click="handleShare">
                        <img loading="lazy" src="@/assets/integralMall/share_icon.png" class="w-35px">
                        <div>分享</div>
                    </div>
                    <div class="collect text-24px text-[#666] text-center" @click="handleCollect">
                        <img loading="lazy" :src="goodsInfo.logicallyDelete === 'n' ? collectIconActive : collectIcon"
                            class="w-35px">
                        <div :class="{ 'text-#ff4344': goodsInfo.logicallyDelete === 'n' }">收藏</div>
                    </div>
                </div>
                <div class="flex-1 right h-78px text-34px text-#fff
             bg-#FF4344 rounded-40px leading-none flex items-center justify-center"
                    :class="{ '!bg-#bdbdbd': disAbledBug }" @click="confirmExchange">
                    立即兑换
                </div>
            </div>
        </van-skeleton>
        <!-- 规格弹窗 -->
        <specifyGoodsPopup v-model:show="showSpecify" :sourceType="route.query?.sourceType || '1'" :list="specifyInfo"
            @selected="selecedSpeficy" />
        <!-- 气泡样式 -->
        <div class="fixed bottom-140px z-100" :style="`left:${bubbleX}%`">
            <waterIntergral v-model:show="showBubble" :score="scoreNum"></waterIntergral>
        </div>
    </div>
</template>
<script setup lang="ts">
import utils from '@/utils/utils';
// 线下领取流程
import exchangIcon from '@/assets/integralMall/coupon_ex_icon.png'
import offOnlineIcon from '@/assets/integralMall/offonline_icon.png'
import exSuccessIcon from '@/assets/integralMall/ex_success_icon.png'
// 线上领取流程
import chooseGoodsIcon from '@/assets/integralMall/choose_icon.png'
import addressIcon from '@/assets/integralMall/address_re_icon.png'
import storeStateIcon from '@/assets/integralMall/store_state_icon.png'
import payRecIcon from '@/assets/integralMall/pay_rec_icon.png'
// 收藏icon
import collectIcon from '@/assets/integralMall/collect_icon.png'
import collectIconActive from '@/assets/integralMall/collect_icon_a.png'

import { collectOperate, shareOperate } from "@/api/public"
import { integralGoodsDetail, integralSpecifyGoodsDetail, integralGoodsDetailText } from '@/api/mall'
import { useMallStore } from '@/store/modules/mall'
import { showConfirmDialog, showDialog, showToast } from 'vant'
const specifyGoodsPopup = defineAsyncComponent(() => import('@/components/Popup/specifyGoodsPopup.vue'))
const waterIntergral = defineAsyncComponent(() => import('@/components/Bubble/waterIntergral.vue'))
onMounted(() => {
    initalPage()
})
const route = useRoute()
const productId = ref(route.query?.productId || '')
const Payment = {
    // 线上发货
    '1': [
        {
            title: '选择商品',
            icon: chooseGoodsIcon,
        },
        {
            title: '填写收货地址',
            icon: addressIcon,
        },
        {
            title: '确认库存状态',
            icon: storeStateIcon,
        },
        {
            title: '支付收货',
            icon: payRecIcon,
        },
    ],
    // 线下核销
    '2': [
        {
            title: '积分兑换',
            icon: exchangIcon,
        },
        {
            title: '线下领取',
            icon: offOnlineIcon,
        },
        {
            title: '兑换成功',
            icon: exSuccessIcon,
        },
    ]
}
const loading = ref(true);//页面加载
const initalPage = () => {
    if (productId.value) {
        getProductInfo()
        getProductSpecify()
        getProductRichtext()
    } else {
        showToast('请传入商品ID')
    }
}
// 商品信息
const goodsInfo = ref<any>({})
const getProductInfo = async () => {
    const { data, code } = await integralGoodsDetail(productId.value)
    loading.value = false;
    if (code === 200) goodsInfo.value = data
    if (!Array.isArray(goodsInfo.value?.productPublicityImg)) {
        goodsInfo.value.productPublicityImg = goodsInfo.value?.productPublicityImg.split(',').map((item: any) => utils.judgeStaticUrl(item))
        if (!goodsInfo.value?.integralPayment) goodsInfo.value.integralPayment = '1'
    }
}
// 商品规格
const showSpecify = ref(false)
const currentSpecifyIndex = ref(0)
const selecedSpeficy = ({ index }) => {
    currentSpecifyIndex.value = index
}
const specifyInfo = ref<any>([])
const getProductSpecify = async () => {
    const { data, code } = await integralSpecifyGoodsDetail(
        {
            productId: productId.value,
            birthdayQueryFlag: route.query?.sourceType === '2',//2为生日福利
            systemQueryType: 'h5'
        })
    if (code === 200) specifyInfo.value = data
}
// 商品详情富文本
const goodsRichText = ref('')
const getProductRichtext = async () => {
    const { data, code } = await integralGoodsDetailText(productId.value)
    if (code === 200) goodsRichText.value = data.productIntroduce
}

// 立即兑换
const mallStore = useMallStore()
const router = useRouter()
const confirmExchange = () => {
    if (disAbledBug.value) return showDialog({
        title: '提示',
        message: '库存不足，请选择其他商品'
    })
    const productinfo = {
        goodsInfo: goodsInfo.value,//商品信息
        specifyInfo: specifyInfo.value[currentSpecifyIndex.value],//规格信息
        sourceType: route.query?.sourceType || '1',//来源类型 1积分商城 2生日福利
    }
    mallStore.setIntegralGoodsInfo(productinfo)
    router.push({ path: '/integralMall/order/pay' })
}

// 导航
const locationAdd = () => {
    showConfirmDialog({
        message: '确定开启导航吗？',
    }).then(() => {
        utils.mapNav({
            win: route.fullPath,
            lon: goodsInfo.value?.addressCoordinate?.split(',')[0] || '',
            lat: goodsInfo.value?.addressCoordinate?.split(',')[1] || '',
            name: goodsInfo.value?.address,
        })
    })
}
// 拨打商家电话
const toCall = () => {
    showConfirmDialog({
        message: '确定拨打商家电话吗？',
    }).then(() => {
        utils.getTel({ number: goodsInfo.value?.contractPhone })
    })
}
// 气泡提示框参数设置
const bubbleX = ref(2);//2 15
const showBubble = ref(false);
const scoreNum = ref(0);
let isReq = false;

// 收藏
const handleCollect = async () => {
    if (isReq) return
    isReq = true
    const { code, data } = await collectOperate({ sourceId: productId.value })
    isReq = false

    if (code !== 200) return showToast('收藏失败')
    const { statefulFlowState, score } = data
    if (statefulFlowState === true) goodsInfo.value.logicallyDelete = 'n'
    else if (statefulFlowState === false) goodsInfo.value.logicallyDelete = 'y'

    if (score) {
        scoreNum.value = score
        showBubble.value = true;
        bubbleX.value = 15;
    }
}
// 分享
const handleShare = () => {

    // 分享成功后
    utils.SharedWorker(goodsInfo.value?.productName, window.location.href, async (isCan: boolean) => {
        if (isCan) {
            if (isReq) return
            isReq = true
            const { code, data: { score } } = await shareOperate({ sourceId: productId.value })
            isReq = false
            if (code !== 200) return showToast('分享失败')
            if (score) {
                scoreNum.value = score
                showBubble.value = true;
                bubbleX.value = 2;
            }
        }
    })
}
const disAbledBug = computed(() => {
    if (specifyInfo.value[currentSpecifyIndex.value]?.reserveType === 'limited' && specifyInfo.value[currentSpecifyIndex.value]?.reserve < 1) return true
    return false
})
</script>

<style lang="scss" scoped>
.product-detail {
    .details {
        height: calc(100vh - 140px);

        .info {
            border-radius: 0 0 28px 28px;
        }

        .product-img {
            border-radius: 28px 28px 0 0;

        }
    }

    .second-title::before {
        display: block;
        content: '';
        width: 10px;
        height: 35px;
        background-color: #FF4344;
        border-radius: 5px;
        margin-right: 10px;
    }
}

.van-swipe-item {
    >img {
        width: 100%;
        height: 100%;
    }
}
</style>