<template>
  <refreshList @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
    <div v-for="item in messageData" :key="item.autoId"
      class="my-[30px] mx-1 rounded-20px shadow-drop-2-center p-5 pb-2 item-box" @click="handleClick(item)">
      <div class="flex items-center mb-2 justify-between">
        <div class="flex items-center">
          <img loading="lazy" :src="item.icon" class="w-[40px] h-[40px] mr-2" />
          <div class="text-[#333333] text-[24px]">
            <div>{{ item.userName || '-' }}</div>
            <div class="mt-11px">{{ utils.formatTimeWithoutSeconds(item.createTime) || '-' }}</div>
          </div>
        </div>
        <div class="flex items-center text-[24px] text-[#333333]" v-if="ifDel && !item.replyState">
          <van-button round type="primary" size="mini" class="w-[90px] text-[24px]" color="#5AA4FF"
            @click.stop="handleDelete(item)">
            删除
          </van-button>
        </div>
      </div>
      <div class="text-[#333333] text-[32px] font-500">{{ item.describes || '-' }}</div>
      <!-- 图片 -->
      <div class="imgs" v-if="item.img">
        <img loading="lazy" v-for="v, index in item.img.split(',')" :src="utils.judgeStaticUrl(v)"
          @click.stop="previewImg(v)" class="w-[30%] h-200px mr-16px rounded-15px mt-20px object-cover" :key="index" />
      </div>

      <div class="flex items-center my-2"
        v-if="(!ifNothing && !type) || (type === 'personal' && item.employeeMessageReplyVO?.content)">
        <img loading="lazy" :src="ghIcon" class="w-[40px] h-[40px] mr-2" />
        <div class="text-[#5AA4FF] text-24px"> 工会回复：</div>
      </div>
      <div class="p-3 bg-[#F6F7F8] rounded-lg text-28px"
        v-if="(!ifNothing && !type) || (type === 'personal' && item.employeeMessageReplyVO?.content)">
        {{ item.employeeMessageReplyVO?.content || '-' }}
      </div>
      <div class="p-3 bg-[#F6F7F8] rounded-lg text-28px mt-30px" v-if="item.auditState == 'refuse' && item.remark">
        未通过原因： <span class="text-#FF4344">{{ item.remark || '-' }}</span>
      </div>
    </div>
  </refreshList>
</template>

<script lang="ts" setup>
import ghIcon from '@/assets/workers-voice/32.png';
import boy from '@/assets/workers-voice/boy.png';
import girl from '@/assets/workers-voice/girl.png';
import refreshList from '@/components/refreshList/index.vue';
import { concat, isEmpty, map } from 'lodash-es';
import utils from '@/utils/utils'
const props = withDefaults(
  defineProps<{
    apiList: Function;
    ifNothing: boolean;
    ifDel: boolean;
    params: Recordable;
    type?: string;
  }>(),
  {
    dataList: () => [],
    ifNothing: false,
    ifDel: false,
    params: () => ({}),
    type: '',
  }
);

const { apiList, ifNothing, ifDel, params } = toRefs(props);

const emit = defineEmits(['click-card', 'delete-card']);

const messageData = ref<Recordable[]>([]);

const pageNum = ref<number>(1);

//获取列表
const loadMoreRef = ref<any>();

function handleClick(item: Recordable) {
  emit('click-card', { item });
}

// 删除
function handleDelete(item: Recordable) {
  emit('delete-card', { item });
}

const onLoad = () => {
  if (isEmpty(unref(params))) {
    return;
  }
  unref(apiList)?.({
    pageSize: 10,
    pageNum: unref(pageNum),
    ...unref(params),
  }).then(({ data, total = 0 }: Recordable) => {
    if (unref(pageNum) === 1) messageData.value = [];

    messageData.value = concat(
      unref(messageData),
      map(data || [], (v: Recordable) => ({ ...v, icon: v.genderCn === '男' ? boy : girl }))
    );

    unref(loadMoreRef)?.onLoadSuc(unref(messageData)?.length, total);
  });
};

// 加载更多
const onLoadMore = () => {
  pageNum.value++;
  onLoad();
};

const onRefreshList = () => {
  pageNum.value = 1;
  onLoad();
};

const previewImg = (url: string) => {
  utils.imagePreview([utils.judgeStaticUrl(url)]);
};

defineExpose({ onRefreshList });

watchEffect(() => {
  pageNum.value = 1;
  onLoad();
});
</script>
