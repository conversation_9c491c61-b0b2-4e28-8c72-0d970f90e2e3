<template>
  <div class="voteActivity-rankList pb-30px pt-60px flex flex-col justify-between">
    <div class="w-full flex justify-center">
      <img loading="lazy" src="@/assets/activity/rank-title.png" alt="" class="w-65/100" />
    </div>
    <div class="w-full list-container">
      <div class="flex justify-between rank-header" v-if="rankList && rankList.length">
        <div class="w-25/100">
          <div>排名</div>
        </div>
        <div class="w-25/100">
          <div>名称</div>
        </div>
        <div class="w-25/100">
          <div>编号</div>
        </div>
        <div class="w-25/100">
          <div>票数</div>
        </div>
      </div>
      <div class="rank-list">
        <template v-if="rankList && rankList.length">
          <div class="flex items-center mb-25px rank-card" v-for="(item, index) in rankList" :key="item.opusInfoId"
            @click.stop="toDetail(item)">
            <div class="w-25/100">
              <img loading="lazy" src="@/assets/activity/rank-one.png" alt="" v-if="item.rank == 1" class="w-60px">
              <img loading="lazy" src="@/assets/activity/rank-two.png" alt="" v-else-if="item.rank == 2" class="w-60px">
              <img loading="lazy" src="@/assets/activity/rank-three.png" alt="" v-else-if="item.rank == 3"
                class="w-60px">
              <template v-else>
                {{ item.rank || '-' }}
              </template>

            </div>
            <div class="w-25/100 text-center flex">{{ item.opusName }}</div>
            <div class="w-25/100">{{ item.opusNo || '-' }}</div>
            <div class="w-25/100"><span style="color: #0382F2;">{{ item.votesNum || '0' }}</span></div>
          </div>
        </template>
        <van-empty description="暂无排行榜数据~" v-else class="w-full" />
      </div>
    </div>

  </div>
</template>

<script>
export default {
  data() {
    return {
      rankList: [],
    }
  },
  computed: {
    visitPrefix() {
      return this.$store.state.visitPrefix
    },
  },
  mounted() {
    this.getRankList()
  },
  methods: {
    // 排行榜
    async getRankList() {
      const { code, data } = await this.$api.common.rankList({
        activityId: this.$store.state.activityId,
        pageSize: 0,
      })
      if (code === 200) {
        this.rankList = data ? data : []
      }
    },
    // 作品详情
    toDetail(item) {
      this.$router.push({
        path: '/voteActivity/workDetail',
        query: {
          opusInfoId: item.opusInfoId,
        },
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.voteActivity-rankList {
  width: 100%;
  min-height: 100%;
  background-image: url('@/assets/activity/rank-bg.jpg');
  background-repeat: no-repeat;
  background-size: 100%;
  background-color: #f98e0f;

  .list-container {
    background-image: url('@/assets/activity/rank-list-bg.png');
    background-repeat: no-repeat;
    background-size: 100%;
    padding: 60px;
    padding-top: 350px;
    margin-bottom: 20px;

    .rank-header {
      background: #eee;
      font-size: 26px;
      font-family: Source Han Sans CN;
      font-weight: 500;
      margin-top: 10px;
      padding: 10px 0;

      div {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
      }
    }

    .rank-list {
      overflow: auto;
      height: 600px;

      .rank-card {
        border-bottom: 2px solid #fbe5da;

        div {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 30px 10px;
          font-size: 26px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #333333;
          flex-shrink: 0;
        }
      }

    }
  }
}
</style>
