<template>
    <van-popup v-model:show="props.show" position="center" round class="popup box-border">
        <div class="popup_content relative z-1 max-w-screen-md" @click="cancel">
            <img loading="lazy" :src="props.surpiseType === 'integral' ? integralbg : giftbg" alt="" class="w-full">
            <div class="content absolute left-12% right-12% top-0 h-full z-2 flex flex-col">
                <div class="title text-48px font-extrabold text-#fff  flex items-end"
                    :style="props.surpiseType === 'integral' ? 'flex:0.25' : 'flex:0.25'">
                    {{ props.title }}
                </div>
                <div class="inner_content flex justify-center items-end mb-30px"
                    :class="{ 'items-center !mb-0px': surpiseType !== 'integral' }">
                    <!-- 插槽页面传具体内容 -->
                    <slot></slot>
                </div>
                <div class="btn px-93px relative w-fit mx-auto" @click.stop="confirm">
                    <img loading="lazy" src="@/assets/integralMall/receive_btn.png" class="w-full">
                    <div class="text-[#fff] text-34px absolute left-50% top-45% -translate-50%">
                        {{ props.btnText }}
                    </div>
                </div>
            </div>
        </div>
    </van-popup>
</template>
<script lang="ts" setup>
import giftbg from '@/assets/integralMall/gift_popup_bg.png'
import integralbg from '@/assets/integralMall/integral_popup_bg.png'
const props = defineProps({
    title: {
        type: String,
        default: '打卡惊喜'
    },
    show: {
        type: Boolean,
        default: false
    },
    btnText: {
        type: String,
        default: '立即领取'
    },
    // integral积分，goods 商品
    surpiseType: {
        type: String,
        default: 'integral'
    }
})
const emit = defineEmits(['update:show', 'cancel', 'confirm'])
const cancel = () => {
    emit('update:show', false)
    emit('cancel')
}
const confirm = () => {
    emit('update:show', false)
    emit('confirm')
}
</script>
<style lang="scss" scoped>
.popup {
    width: calc(100% - 86px);

    .title {
        text-shadow: 0px 3px 10px rgba(196, 0, 15, 0.69);
    }

    .inner_content {
        flex: 0.6;
    }
}

.van-popup {
    background: transparent;
}
</style>