<template>
  <div class="w-100% pb-150px">
    <img loading="lazy" :src="useStore.getPrefix + Data.info.curriculumCover" alt="" class="w-100% h-400px" />
    <div class="px border-b-20px border-b-solid border-b-#F6F7F8">
      <div class="text-36px text-#333 mt-30px mb-37px">
        {{ Data.info.curriculumName }}
      </div>
      <div class="flex w-100% justify-between mb-30px">
        <div class="text-#666 text-28px">来源：{{ Data.info.companyName }}</div>
        <div class="flex items-center text-#999 text-26px">
          <van-icon name="eye-o" color="#999" class="mr-10px" />{{ Data.info.clickNum || 0 }}
        </div>
      </div>
    </div>
    <div class="">
      <div class="tab-box">
        <van-tabs v-model:active="Data.tab.active" sticky color="#5AA4FF" title-active-color="#5AA4FF"
          title-inactive-color="#333333" line-width="18">
          <van-tab :title="item.name" v-for="(item, index) in Data.tab.nav" :key="index"
            title-class="tab-title"></van-tab>
        </van-tabs>
      </div>
      <div class="px" v-show="Data.tab.active === 0">
        <div class="text-#333 text-28px mt-38px mb-80px" v-html="Data.info.curriculumIntroduce">
        </div>

      </div>
      <div v-show="Data.tab.active === 1">
        <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
          <div v-for="(item, index) in Data.listings" :key="index"
            class="px text-32px text-#666 py-40px border-b-1px border-b-solid border-b-#ECECEC"
            @click="toDetail('/digitalSchool/course/liveDetails', { catalogueId: item.catalogueId, type: item.catalogueType })">
            {{ item.catalogueName }}
          </div>
        </refreshList>
      </div>
    </div>
    <div class="fixed left-0 bottom-0 w-full flex items-center py-28px box-border pr-28px bg-#fff"
      style="box-shadow: 4px -3px 4px 0px #F6F7F8; ">
      <div class="flex items-center justify-between px-28px box-border w-45%">
        <div v-for="(item, index) in Data.threeOperate" :key="item.name" class="relative text-55px"
          @click="handleControl(item.type)">
          <van-badge :content="Data.info.likeNum || 0" max="99" color="#5CA5FF" v-if="item.name === '点赞'">
            <div class="flex flex-col justify-center items-center ">
              <van-icon :name="item.whether ? item.iconActive : item.icon"
                :color="item.whether ? 'rgb(92, 165, 255)' : '#999'" />
              <div class="text-24px text-#999 mt-10px">{{ item.name }}</div>
            </div>
          </van-badge>
          <div class="flex flex-col justify-center items-center " v-else>
            <van-icon :name="item.whether ? item.iconActive : item.icon"
              :color="item.whether ? 'rgb(92, 165, 255)' : '#999'" />
            <div class="text-24px text-#999 mt-10px">{{ item.name }}</div>
          </div>
        </div>
      </div>
      <Button :name="Data.info.joinFlag ? '退出课堂' : '加入课堂'" type="small" :disable="Data.info.joinFlag"
        class="!w-55% !h-78px mx-auto" @click="joinOrQuitClass" />
    </div>
    <!-- 气泡样式 -->
    <div class="fixed bottom-140px z-100" :style="`left:${bubbleX}%`">
      <waterIntergral v-model:show="showBubble" :score="scoreNum"></waterIntergral>
    </div>
  </div>
</template>
<script lang="ts" setup>
import Button from "@/components/Button/button.vue";
import { curriculumInfoGetVoByDtoH5, curriculumInfoSaveOrUpdateByDTO, curriculumCatalogueFindVoListH5, curriculumUserInfoDelete } from "@/api/digitalSchools/index"
import { useRoute } from "vue-router";
import router from "@/router";
import { useUserStore } from "@/store/modules/user";
import refreshList from '@/components/refreshList/index.vue';
import { likeOperate, collectOperate, shareOperate, ChainShare, unionIntegralH5 } from "@/api/public"
import { showFailToast, showSuccessToast, showToast, showConfirmDialog } from "vant";
import utils from "@/utils/utils";

const waterIntergral = defineAsyncComponent(() => import('@/components/Bubble/waterIntergral.vue'))
const route = useRoute();
const useStore = useUserStore()
const Data = ref({
  type: '2',//1-文章  2-非文章
  threeOperate: [
    { name: "点赞", icon: "good-job-o", iconActive: 'good-job', type: 'like' },
    { name: "收藏", icon: "star-o", iconActive: 'star', type: 'collect' },
    { name: "分享", icon: "share-o", type: 'share' },
  ],
  tab: {
    active: 0,
    nav: [{ name: "介绍" }, { name: "目录" }],
  },
  listings: [],
  info: {},
  pageNum: 1
});
const loadMoreRef = ref('')

// 气泡提示框参数设置
const bubbleX = ref(2);//2 15 30
const showBubble = ref(false);
const scoreNum = ref(0);
let isReq = false;
function toDetail(path: string, query: object) {
  if (!Data.value.info.joinFlag) {
    showToast('请先加入课堂')
    return
  }
  if (!path) return;
  router.push({ path, query });
}

//课堂介绍详情
function getDetail() {
  curriculumInfoGetVoByDtoH5({
    autoId: route.query?.autoId || ""
  }).then(res => {
    if (res.code == 200) {
      Data.value.info = res.data
      Data.value.threeOperate.forEach(item => {
        if (item.type == 'like') {
          item.whether = Data.value.info.likeStatus
        }
        if (item.type == 'collect') {
          item.whether = Data.value.info.collectStatus
        }
      })
      getCourseList()
    }
  })
}
// 课程列表
function getCourseList() {
  curriculumCatalogueFindVoListH5({
    curriculumBizId: Data.value.info.curriculumBizId,
    pageNum: Data.value.pageNum,
    pageSize: 10
  }).then(res => {
    if (res.code == 200) {
      if (Data.value.pageNum == 1) {
        Data.value.listings = res.data || []
      } else {
        Data.value.listings = Data.value.listings.concat(res.data);
      }
      //重置刷新状态及 判断是否加载完成
      if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(Data.value.listings.length, res.total);
      }

    }
  })
}
// 加入/退出课堂
function joinOrQuitClass() {
  // 退出
  if (Data.value.info.joinFlag) {
    showConfirmDialog({
      title: "提示",
      message: `确认退出${Data.value.info.curriculumName}课堂?`,
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      className: "close",
    }).then(async () => {
      curriculumUserInfoDelete(Data.value.info.curriculumBizId).then(res => {
        if (res.code == 200) {
          showToast("退出成功")
          Data.value.info.joinFlag = !Data.value.info.joinFlag
        } else {
          showFailToast(res.message)
        }
      })
    });
  } else {
    // 加入
    curriculumInfoSaveOrUpdateByDTO({
      curriculumBizId: Data.value.info.curriculumBizId || ""
    }).then(res => {
      if (res.code == 200) {
        showToast("加入成功")
        Data.value.info.joinFlag = !Data.value.info.joinFlag
      } else {
        showFailToast(res.message)
      }
    })
  }

}

// 刷新
const onRefreshList = () => {
  Data.value.pageNum = 1;
  getCourseList();
};
// 加载更多
const onLoadMore = () => {
  Data.value.pageNum++;
  getCourseList();
};

// 点赞、收藏、分享

const handleControl = (type: string) => {
  switch (type) {
    case "like":
      getLike();
      break;
    case "collect":
      getCollect();
      break;
    case "share":
      utils.SharedWorker(Data.info?.curriculumName, window.location.href, (isCan: boolean) => {
        if (isCan) getShare()
      })
      break
  }
}
//点赞
const getLike = (dataid, code) => {
  if (isReq) return
  isReq = true
  likeOperate({
    sourceId: Data.value.info.curriculumBizId,
    userId: useStore.getUserInfo?.userId,
  }).then(res => {
    isReq = false
    if (res.code == 200) {
      let obj = Data.value.threeOperate.find(item => item.type == 'like')
      if (obj?.whether) {
        obj.whether = !obj.whether;
        showSuccessToast("取消成功");
        Data.value.info.likeNum--;
      } else {
        obj.whether = !obj.whether;
        showSuccessToast("点赞成功");
        Data.value.info.likeNum++;
      }
      if (res.data?.score) {
        scoreNum.value = res.data.score
        bubbleX.value = 2
        showBubble.value = true;
      }
    }
  })
    .catch(err => {
      isReq = false
    })
}
//收藏
const getCollect = (dataid, code) => {
  if (isReq) return
  isReq = true
  collectOperate({
    sourceId: Data.value.info.curriculumBizId,
    userId: useStore.getUserInfo?.userId,
  }).then(res => {
    isReq = false

    if (res.code == 200) {
      let obj = Data.value.threeOperate.find(item => item.type == 'collect')
      if (obj?.whether) {
        obj.whether = !obj.whether;
        showSuccessToast("取消成功");

      } else {
        obj.whether = !obj.whether;
        showSuccessToast("收藏成功");
      }

      if (res.data?.score) {
        scoreNum.value = res.data.score
        bubbleX.value = 30
        showBubble.value = true;
      }
    }
  })
    .catch(err => {
      isReq = false
    })

}
//分享
const getShare = (dataid, code) => {
  if (isReq) return
  isReq = true
  shareOperate({
    sourceId: Data.value.info.curriculumBizId,
    userId: useStore.getUserInfo?.userId,
  }).then(res => {
    isReq = false
    if (res.code == 200) {
      showSuccessToast("分享成功");
      if (res.data?.score) {
        scoreNum.value = res.data.score
        bubbleX.value = 15
        showBubble.value = true;
      }
    }
  })
    .catch(err => {
      isReq = false
    })
}


onMounted(() => {
  if (route.query.type === 'article') {
    Data.value.type = '1'
  } else {
    getDetail()
  }
});
</script>
<style lang="scss" scoped>
.tab-box {
  border-bottom: 1px solid #ececec;

  :deep(.van-tabs__nav--card) {
    border: none !important;
    margin: 0 !important;
  }

  :deep(.van-tabs__line) {
    background: linear-gradient(0deg, #a1cbff 0%, #5aa4ff 100%);
    border-radius: 3px;
  }

  :deep(.tab-title) {
    font-weight: 400;
    font-size: 28px;
    color: #666;
  }

  :deep(.van-tab--active) {
    font-weight: 500;
    font-size: 32px;
    color: #333;
  }
}
</style>
