<template>
    <div class="my-collection bg-[#F6F7F8] box-border flex flex-col w-full min-h-100vh">
        <div class="headers h-110px bg-#fff  sticky top-0 z-99">
            <van-tabs v-model:active="tabActive" title-active-color="#5AA4FF" @change="tabChange"
                title-inactive-color="#333">
                <van-tab :title="item.label" v-for="item in tabs" :key="item.value" :name="item.value"
                    title-class="tab-title">
                </van-tab>
            </van-tabs>
        </div>
        <div class="headers bg-#fff pb-26px">
            <div class="flex items-center bg-[#F9F9F9] rounded-26px mx-30px mt-20px pl-30px pr-10px">
                <img loading="lazy" src="@/assets/public/icon_s.png" class="w-24px">
                <van-field v-model="searchValue" placeholder="请输入关键字" class="flex-1 ml-20px h-60px" clearable
                    @clear="searchFn" clickable />
                <div class="text-#5AA4FF text-24px py-9px px-20px bg-[#E6EFF9] 
                rounded-20px leading-none" @click="searchFn">搜索</div>
            </div>
        </div>
        <div class="list mt-30px bg-#F6F7F8 px-30px flex-1"
            :class="{ '!bg-#fff': ['1', '3', '5'].includes(tabActive) }">
            <refreshList ref="loadMoreRef" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" :immeCheck="true"
                :slotEmpty="true" class="w-full">
                <template v-if="tabActive === '1'">
                    <newsCell v-for="item, index in list" :key="index" :content="item" :showTag="false">
                        <template #rightView>
                            <div class="text-[#999] text-[24px]">
                                <van-icon name="star" color="#5AA4FF" />
                                <span class="ml-5px">已收藏</span>
                            </div>
                        </template>
                    </newsCell>
                </template>

                <template v-else-if="tabActive === '2'">
                    <CourseList :data="list" bgColor="#fff" :septalLine="false"
                        @toDetails="(val) => toDetails('course', val)">
                        <template #subTitle="{ item }">
                            <div
                                class="text-30px text-#333 mb-20px text-ellipsis line-clamp-3 whitespace-nowrap text-wrap">
                                {{ item.curriculumName }}</div>
                        </template>
                        <template #subContent="{ item }">
                            <div class="flex items-center text-#999 text-24px">
                                <van-icon name="eye-o" size="16" color="#999" class="mr-15px" />{{ item.clickNum || 0 }}
                            </div>
                        </template>
                    </CourseList>
                </template>
                <div v-else-if="tabActive === '3'" class="flex flex-wrap justify-between">
                    <videoListCell v-for="item, index in list" :key="item.autoId" :content="item"
                        @click="onVideoClick(index)">
                    </videoListCell>
                </div>

                <!-- 阵地 -->
                <div v-else-if="tabActive === '5'">
                    <positionList :list="list">
                        <template #left-view>
                            <div class="text-[#999] text-[24px] mr-15px">
                                <van-icon name="star" color="#5AA4FF" />
                                <span class="ml-5px">已收藏</span>
                            </div>
                        </template>
                    </positionList>
                </div>

                <template #noData>
                    <van-empty :image="emptyImg" description="收藏夹空空" :image-size="['60%', 'auto']">
                    </van-empty>
                </template>
            </refreshList>
        </div>
    </div>
</template>
<script lang="ts" setup>
defineOptions({
    name: 'myCollection'
})
// 资讯
import { getUserCollectionList } from '@/api/news/index'
import newsCell from '@/components/Cell/newsCell.vue';
// 课堂
import CourseList from "@/components/activity/coolSummerList.vue";
import { curriculumInfoMyCollect } from '@/api/digitalSchools/index'
// 视频
import videoListCell from '@/components/Cell/videoListCell.vue';
import { getMyFavoritesVideos } from '@/api/video/index'
// 阵地
import { collectVenueInfo } from '@/api/position'
import positionList from '@/views/position/components/positionList.vue';

import refreshList from '@/components/refreshList/index.vue';
import emptyImg from '@/assets/my/no_collect.png'
import { useVideoStore } from '@/store/modules/video';
const route = useRoute()
const router = useRouter()
watch(route, (val) => {
    if (val.path === '/my/collection') {
        searchFn()
    }
})

const tabs = ref([
    {
        label: '资讯',
        value: '1',
    },
    {
        label: '课堂',
        value: '2',
    },
    {
        label: '视频',
        value: '3',
    },
    // {
    //     label: '普惠',
    //     value: '4',
    // },
    {
        label: '阵地',
        value: '5',
    },
])
import { useUserStore } from '@/store/modules/user';
const tabActive = ref('1')
const searchValue = ref('')
const loadMoreRef = ref<any>(null)
let pageNum = 1
const list = ref<any>([])
const searchFn = () => {
    if (loadMoreRef.value) loadMoreRef.value.resetStatus()
    onRefreshList()
}
const onRefreshList = () => {
    pageNum = 1;
    getList();
}
const onLoadMore = () => {
    pageNum++;
    getList();
}
const tabChange = () => {
    if (loadMoreRef.value) loadMoreRef.value.resetStatus()
    onRefreshList();
}
const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo)
const getLocationInfo = computed(() => userStore.locationInfo)
const getList = async () => {
    let res: any = null;
    switch (tabActive.value) {
        case '1':
            res = await getUserCollectionList({
                pageNum,
                pageSize: 10,
                searchTitle: searchValue.value
            })
            break
        case '2':
            // 课堂
            res = await curriculumInfoMyCollect({
                pageNum,
                pageSize: 10,
                curriculumName: searchValue.value
            })
            res.data.forEach((item: any) => {
                item.appCover = item.curriculumCover
            })
            break
        case '3':
            res = await getMyFavoritesVideos({
                pageNum,
                pageSize: 10,
                title: searchValue.value
            })
            break
        case '4':
            // 普惠 -暂无
            break
        case '5':
            let userCoordinate = getLocationInfo.value?.longitude ?
                getLocationInfo.value?.longitude + ',' + getLocationInfo.value?.latitude : ''
            // 阵地
            res = await collectVenueInfo({
                pageNum,
                pageSize: 10,
                userCoordinate,//用户坐标系
                positionName: searchValue.value
            })
            break
    }
    if (res.code === 200) {
        if (pageNum === 1) list.value = res.data
        else list.value = list.value.concat(res.data)
    }
    if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(list.value, res.total);
    }
}
const toDetails = (from: string, item: any) => {
    switch (from) {
        case 'course':
            router.push({
                path: "/digitalSchool/course/courseDetails",
                query: {
                    autoId: item.autoId,
                    type: item.catalogueType
                }
            })
            return
    }
}
// 视频跳转
const videoStore = useVideoStore();
function onVideoClick(listnumber: number) {
    videoStore.setVideoList(list.value, 3);
    router.push({
        path: '/video-playback',
        query: { entertype: 3, listnumber },
    })
}
</script>
<style scoped lang="scss">
.my-collection {
    .headers {
        :deep(.van-cell) {
            background-color: transparent;
            padding: 0;
        }

        :deep(.van-tabs--line .van-tabs__wrap) {
            height: 110px;
        }

        :deep(.van-tab) {
            line-height: 1;
            height: 100%;
        }

        :deep(.van-tabs__nav--line) {
            height: 100%;
            padding-bottom: 15px;
        }

        :deep(.van-tabs__line) {
            width: 36px;
            height: 6px;
            background: linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%);
            border-radius: 3px;
        }

        :deep(.van-tab__text) {
            font-size: 32px;
        }

        :deep(.van-field__body) {
            height: 100%;
            font-size: 24px;
        }
    }

    :deep(.cover-img) {
        width: 260px;
        height: 176px;
    }

    :deep(.self-style) {
        width: calc((100% - 20px) * 0.5);
    }

    :deep(.van-cell:after) {
        border: none !important;
    }
}
</style>