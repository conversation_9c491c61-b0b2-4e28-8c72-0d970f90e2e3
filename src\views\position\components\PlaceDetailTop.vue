<template>
  <div class="max-w-100vw px-[30px] relative">
    <div class="bg-[#fff] p-[20px] rounded-[16px]">
      <div class="flex-col flex">
        <van-skeleton title :row="3" :loading="loading">
          <div class="text-[#333333] text-[30px] font-bold text-left w-full truncate">
            {{ placeRecord?.venueName || '' }}
          </div>
          <div class="block items-center text-[24px]">
            <img loading="lazy" :src="icon_time" class="w-[24px] h-[24px] mr-1" />
            <span class="text-[#808080] my-0.5">开放时间：</span>
            <span class="text-[#333333]">{{ fixOpenWeekDay(placeRecord) }}</span>

            &nbsp;&nbsp;
            <span v-if="placeRecord?.morningOpenTime && placeRecord?.morningCloseTime" class="text-[#333333]">
              {{ exchangeTime(placeRecord.morningOpenTime) }} -
              {{ exchangeTime(placeRecord.morningCloseTime) }}
            </span>
            &nbsp;
            <span v-if="placeRecord?.afterOpenTime && placeRecord?.afterCloseTime" class="text-[#333333]">
              {{ exchangeTime(placeRecord.afterOpenTime) }} -
              {{ exchangeTime(placeRecord.afterCloseTime) }}
            </span>
          </div>

          <div class="flex items-center text-[24px]">
            <img loading="lazy" :src="icon_fzr" class="w-[24px] h-[24px] mr-1" />
            <span class="text-[#808080]">负责人：</span>
            <span class="text-[#333333] mx-1 my-0.5">{{ placeRecord?.manager }}</span>
            <span class="text-[#5BA5FF]">{{ placeRecord?.phone }}</span>
          </div>
          <div class="flex items-center text-[24px]">
            <img loading="lazy" :src="icon_people" class="w-[24px] h-[24px] mr-1" />
            <span class="text-[#808080]">容纳人数：</span>
            <span class="text-[#333333] mt-0.5">{{ placeRecord?.capacity }} 人</span>
          </div>
        </van-skeleton>
      </div>
      <div class="line my-2" :style="{ borderBottom: '1px solid #EBEBEB' }" />
      <div class="flex items-center justify-between">
        <div>
          <div class="text-[#333333] text-[30px] font-bold text-left w-full">
            {{ query?.address || '' }}
          </div>
          <div class="flex items-center text-[24px]">
            <span class="text-[#808080]">距您{{ fixDistance(query.actualDistance) }}</span>
          </div>
        </div>
        <img loading="lazy" :src="navigation"
          class="w-[60px] h-[60px] relative right-3 active:shadow-[0_0_5px_0_#5ba5ff] active:rounded-1/2" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import icon_people from '@/assets/position/icon_people.png';
import icon_time from '@/assets/position/icon_time.png';
import icon_fzr from '@/assets/position/icon_fzr.png';
import navigation from '@/assets/position/navigation.png';
import { fixOpenWeekDay, fixDistance, exchangeTime } from '../utils';
import { isEmpty } from 'lodash-es';

const route = useRoute();

const placeRecord = inject<Recordable>('placeRecord', {});

const query = computed<Recordable>(() => route.query);

const loading = computed(() => isEmpty(unref(placeRecord)));
</script>
