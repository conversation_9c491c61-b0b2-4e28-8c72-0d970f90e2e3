<template>
  <div class="">
    <div v-for="(item, index) in list" :key="index" @click="handleClick(item)" class="flex box-border py-30px"
      :class="{ 'border-bottom': index < list.length - 1 }">
      <div class="w-[200px] h-[244px]">
        <img loading="lazy" :src="item.positionCoverImage ? exchangeImg(item.positionCoverImage) : defaultPositionLittle
          " class="w-full h-full rounded-20px object-contain" />
      </div>

      <div class="flex-1 pl-2 flex flex-col overflow-hidden justify-between">
        <div class="text-[#333333] text-[30px] font-550 text-left w-full truncate leading-none">
          {{ item.positionName || '' }}
        </div>
        <div class="flex items-center text-[24px] text-#999 mt-12px">
          <van-icon name="clock-o" />
          <div class="line-right ml-9px leading-none"
            :class="`${item.openState ? 'text-[#5AA4FF]' : 'text-[#f25f20]'}`">
            {{ item.openState ? '开放中' : '暂未开放' }}</div>
          <div class="ml-10px leading-none">{{ fixOpenWeekDay(item) }}</div>
        </div>
        <div class="text-left text-[24px] text-#999 mt-15px leading-none">
          <span v-if="item.morningOpenTime && item.morningCloseTime">
            {{ exchangeTime(item.morningOpenTime) }} -
            {{ exchangeTime(item.morningCloseTime) }}
          </span>
          &nbsp;
          <span v-if="item.afterOpenTime && item.afterCloseTime">
            {{ exchangeTime(item.afterOpenTime) }} -
            {{ exchangeTime(item.afterCloseTime) }}
          </span>
        </div>
        <div class="flex items-center text-left text-[24px] truncate text-#999 mt-15px">
          <van-icon name="location-o" />
          <div class="line-right leading-none">{{
            item.actualDistance ? fixDistance(item.actualDistance) : '定位失败'
          }}</div>
          <div class="w-ful ml-5px flex-1 overflow-hidden text-ellipsis leading-none">{{
            item.address
          }}</div>
        </div>
        <div class="text-[22px] block text-left w-full multiline-text mt-10px">
          <div v-for="tag in item.venueServiceTypes" class="box-border pr-[4px] inline-block">
            <van-tag color="#FCF0E2" class="text-[#A94800] px-[2px] text-24px">
              {{ tag.serviceTypeName }}
            </van-tag>
          </div>
        </div>
        <div class="flex justify-start items-center mt-12px text-28px flex-1">
          <slot name="left-view"></slot>
          <div
            class="active:shadow-[0_0_10px_0_#5ba5ff] text-[#4297FF] mr-2 flex items-center leading-none cursor-pointer border border-[#4297FF] border-solid px-[54px] rounded-xl box-border h-50px"
            @click.stop="handlePhoneClick(item)">电话</div>

          <div
            class="active:shadow-[0_0_10px_0_#5ba5ff] text-[#fff] flex items-center leading-none box-border h-50px cursor-pointer px-[54px] rounded-xl active-btn"
            @click.stop="handleNavigationClick(item)">导航
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import utils from '@/utils/utils';
import defaultPositionLittle from '@/assets/position/default-position-little.jpg';
import { fixDistance, fixOpenWeekDay, exchangeImg, exchangeTime } from '../utils';

withDefaults(defineProps<{ list: Recordable[] }>(), { list: () => [] });

const router = useRouter();

// 详情
function handleClick(item: Recordable) {
  router.push({
    path: '/positionDetail',
    query: { positionInfoId: item.positionInfoId, userCoordinate: item.userCoordinate },
  });
}
// phone
function handlePhoneClick(item: Recordable) {
  utils.getTel({ number: item?.phone });
}
// navigation
function handleNavigationClick(item: Recordable) {
  let marker = item?.coordinate.split(',');
  utils.mapNav({
    win: item.autoId,
    name: item.positionName,
    lat: marker[1],
    lon: marker[0],
  });
}
</script>
<style lang="scss" scoped>
.active-btn {
  background: linear-gradient(90deg, #5ba5ff 0%, #5cb6ff 100%);
}

.border-bottom {
  border-bottom: 1px solid #ebebeb;
}

.multiline-text {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  /* 设置n行，也包括1 */
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
</style>
