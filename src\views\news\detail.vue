<template>
    <div class="news_detail flex flex-col min-h-[100vh] max-w-100% bg-#F6F7F8">
        <van-skeleton title :row="10" :loading="loading">
            <div v-if="detailInfo" class="pb-100px">
                <div class="content flex-1 pt-50px box-border overflow-scroll">
                    <div class="news_title px-50px text-36px text-[#2D2D2D] font-bold">
                        {{ detailInfo?.newsTitle }}
                    </div>
                    <div class="px-50px text-24px text-#333 mt-36px">
                        <div class="flex justify-between  pb-18px items-center" style="border-bottom: 1px solid #999;">
                            <div>{{ detailInfo?.publishTime ? dayjs(detailInfo?.publishTime).format('YYYY-MM-DD HH:mm')
                                :
                                '--' }}
                            </div>
                            <div class="text-#999" v-if="detailInfo?.newsSource"> {{ detailInfo?.newsSource || '--' }}
                            </div>
                        </div>
                    </div>

                    <div class="rich_text mt-20px px-50px text-[#333] min-h-350px" @click="handleClick"
                        v-html="detailInfo?.newsDetailsContent" v-if="!detailInfo?.whetherExternalLink">
                    </div>
                    <!-- 外链新闻在内部打开 -->
                    <nestedWindow :url="detailInfo?.externalLinkAddress" v-else />

                    <div v-if="detailInfo?.whetherLinkResources" class="p-30px">
                        <!-- 资源类型（内部资源--internal-活动业务，外部资源{外链}--external） -->
                        <div v-if="detailInfo?.resourceType == 'internal' && actDetail" @click="toDetails()">
                            <img loading="lazy" :src="useStore.getPrefix + actDetail.appCover" alt=""
                                class="h-300px w-100% rounded-15px">
                        </div>
                        <div v-if="detailInfo?.resourceType == 'external'" @click="toDetails()">
                            <img loading="lazy" :src="useStore.getPrefix + detailInfo?.externalCoverUrl" alt=""
                                class="h-300px w-100% rounded-15px">
                        </div>

                    </div>
                    <!-- 推荐新闻 -->
                    <div class="recomend mt-20px py-30px px-35px border_top_20px bg-#fff">
                        <div class="header flex justify-between items-center">
                            <div class="title text-32px font-semibold">推荐新闻</div>
                            <div class="refresh flex border border-[#5ca5ff] py-5px px-15px rounded-16px"
                                @click="refresh()">
                                <img loading="lazy" src="@/assets/public/refresh_icon.png" class="w-20px" />
                                <span class="text-24px text-[#676] ml-10px">刷新</span>
                            </div>
                        </div>
                        <div class="content">
                            <newsCell v-for="(item, index) in recommenList" :key="index" :content="item"
                                :showborderBottom="index !== 1"></newsCell>
                            <Empty v-if="!recommenList?.length" />
                        </div>
                    </div>
                    <!-- end -->
                    <!-- 评论区 -->
                    <div class="coment_list px-35px py-30px border_top_20px bg-#fff"
                        v-if="detailInfo?.newsIsopenComment">
                        <div class="header flex items-center pb-15px border_bottom_1px">
                            <div class="title text-32px font-semibold leading-none">评论</div>
                            <div class="text-[#999] text-28px leading-none ml-10px">
                                ({{ total }})
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-#fff px-20px" v-if="detailInfo?.newsIsopenComment">
                    <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
                        <div class="cell flex py-20px border_bottom_1px" v-for="(item, index) of commentList"
                            :key="index">
                            <div class="avator  w-86px h-86px rounded-50%">
                                <img loading="lazy" src="@/assets/public/head_default.png" alt="" class="w-full h-full">
                            </div>
                            <div class="content flex-1 ml-24px">
                                <div class="c1 flex justify-between">
                                    <div class="name text-[#666666] text-[30px]">{{ item.nickName }}</div>
                                    <div class="zan_text" @click="getCommentLike(item)">
                                        <span class="text-[#333] text-[32px]">{{ item.likeVolume }}</span>
                                        <img loading="lazy" :src="item?.whetherLike ? zanActiveIcon : zanIcon"
                                            class="w-34px ml-10px" />
                                    </div>
                                </div>
                                <div class="c2 text-[#333] mt-15px text-[26px]">
                                    {{ item.content }}
                                </div>
                            </div>
                        </div>
                    </refreshList>
                </div>
                <div class="control_btn flex items-end justify-between h-fit z-99
                    bg-[#fff] fixed bottom-0 w-100%
                    shadow-[0px 0px 21px_rgba(0,0,0,0.3)] px-30px box-border safe_area_bottom">
                    <div class="input flex items-center bg-[#F1F1F1] my-20px flex-1 rounded-37px min-h-full  relative"
                        v-if="detailInfo?.newsIsopenComment">
                        <div class="w-33px ml-40px" v-show="!conment">
                            <img loading="lazy" src="@/assets/public/comment_icon.png" class="w-full" />
                        </div>
                        <van-field v-model="conment" placeholder="写评论" type="textarea" rows="1" autosize
                            :class="{ 'mb-55px': conment }"></van-field>
                        <div class="send absolute right-17px text-center leading-49px
                        w-104px h-49px rounded-23px bg-[#fff] text-26px text-[#999]"
                            :class="{ 'active bottom-10px': conment }" @click="sendText">发送</div>
                    </div>
                    <div class="right-control flex items-center my-20px"
                        :class="detailInfo?.newsIsopenComment ? 'justify-end ml-38px' : 'justify-between !mx-30px w-100%'">
                        <van-badge :content="detailInfo?.likeVolume" color="#5ca5ff" class="mr-30px">
                            <div class="item  text-center text-[#333] text-22px" @click="handleControl('like')">
                                <img loading="lazy" :src="detailInfo?.whetherLike ? zanActiveIcon : zanIcon"
                                    class="w-43px" />
                                <div class="mt-10px">点赞</div>
                            </div>
                        </van-badge>
                        <van-badge :content="detailInfo?.collectVolume" color="#5ca5ff" class="mr-30px">
                            <div class="item text-center text-[#333] text-22px" @click="handleControl('collect')">
                                <img loading="lazy" :src="detailInfo?.whetherCollect ? collectActiveIcon : collectIcon"
                                    class="w-43px" />
                                <div class="mt-10px">收藏</div>
                            </div>
                        </van-badge>
                        <van-badge :content="detailInfo?.shareVolume" color="#5ca5ff">
                            <div class="item text-center text-[#333] text-22px" @click="handleControl('share')">
                                <img loading="lazy" :src="detailInfo?.whetherShare ? shareActiveIcon : shareIcon"
                                    class="w-43px" />
                                <div class="mt-10px">分享</div>
                            </div>
                        </van-badge>

                    </div>
                </div>
            </div>
            <Empty v-else />

            <!-- 气泡样式 -->
            <div class="fixed z-100" :class="isComment ? `left-50% top-50% -translate-50% z-100` : `bottom-140px`"
                :style="isComment ? `` : `left:${bubbleX}%`">
                <waterIntergral v-model:show="showBubble" :bigSize="isComment ? '150px' : '100px'"
                    :midSize="isComment ? '62px' : '30px'" :smallSize="isComment ? '50px' : '20px'"
                    :scorefontSize="isComment ? '40px' : '32px'" :score="scoreNum">
                </waterIntergral>
            </div>
        </van-skeleton>
    </div>
</template>

<script lang="ts" setup>
const waterIntergral = defineAsyncComponent(() => import('@/components/Bubble/waterIntergral.vue'))
import nestedWindow from "@/views/public/nestedWindow.vue"
import zanIcon from "@/assets/public/dianzan_icon.png";
import zanActiveIcon from "@/assets/public/dianzan_active_icon.png";
import collectIcon from "@/assets/public/collect_icon.png";
import collectActiveIcon from "@/assets/public/collect_active_icon.png";
import shareIcon from "@/assets/public/share_icon.png";
import shareActiveIcon from "@/assets/public/share_active_icon.png";
import { showConfirmDialog, showToast, showFailToast, showSuccessToast } from "vant";
import newsCell from "@/components/Cell/newsCell.vue";
import refreshList from "@/components/refreshList/index.vue";
import { getNewsDetails, getRecommendNewsList, getRecommendNewsListByKeywords, getUserCommentList, addUserComment } from "@/api/news/index";
import { likeOperate, collectOperate, shareOperate, ChainShare, unionIntegralH5 } from "@/api/public"
import { useRoute } from "vue-router";
const route = useRoute();
import { useUserStore } from '@/store/modules/user';
import Empty from '@/components/Empty/index.vue';
import utils from "@/utils/utils";
import dayjs from "dayjs";
import { getDetails } from '@/api/activity';
import router from "@/router";
const useStore = useUserStore();
const conment = ref("");//评论
import { encryptUserInfo } from '@/api/news';
const detailInfo = ref<any>(null);
const pageNum = ref(1);
const recommenList = ref([]);
const hasNextPage = ref(false);
const commentList = ref([]);
const total = ref(0);
const loading = ref(true);//页面加载
const showTool = ref(true)
const bubbleX = ref(6);
const showBubble = ref(false);
const scoreNum = ref(0);
let isReq = false;
const isComment = ref(false);
//发送
const sendText = () => {
    addUserComment({
        newsId: route.query.newsId,
        content: conment.value,
        userId: useStore.getUserInfo?.userId,
    }).then(res => {
        if (res.code == 200) {
            showSuccessToast("评论成功，等待管理员审核！");
        } else {
            showFailToast(res.message)
        }

    })

    conment.value = "";
}

// 处理富文本中存在文件的情况 denglanlan 2024-10-12
function handleClick(event) {
    const target = event.target.closest('a') || event.target.closest('img');
    if (target) {
        let type = event.target.closest('a') ? 'file' : event.target.closest('img') ? 'img' : ''
        // 阻止默认行为，例如链接跳转
        let url = target.href || target.src
        event.preventDefault();
        // 处理a标签点击事件
        if (!url) {
            showToast('路径错误或不存在')
            return
        }
        let n = url.lastIndexOf('.')
        let Suffix = url.substring(n + 1)
        // 证明是文件
        if (Suffix) {
            if (type === 'file') utils.filePreview(url, target.title || '')
            else utils.imagePreview([url])
        } else {
            // 不是文件
            utils.openActLink({
                title: target.title,
                url: url,
                type: 1,
                win: Math.floor(Math.random() * 100),
                isExternal: 'y',
                dataid: Math.floor(Math.random() * 100)
            })
        }
    }
}

// 点赞、收藏、分享
const handleControl = (type: string) => {
    if (!showTool.value) {
        return
    }
    // if (!useStore.getUserInfo?.userId) {
    //     showToast("请先在川工之家登录后重试");
    //     return
    // }
    isComment.value = false

    utils.isLoginDialog(() => {
        switch (type) {
            case "like":
                if (utils.isApp()) {
                    utils.newsAction({ title: detailInfo.value?.newsTitle, dataId: detailInfo.value?.newsId }, detailInfo.value?.whetherLike ? "unlike" : "like")
                } else {
                    getLike();
                }
                break;
            case "collect":
                if (utils.isApp()) {
                    utils.newsAction({ title: detailInfo.value?.newsTitle, dataId: detailInfo.value?.newsId }, detailInfo.value?.whetherCollect ? "uncollect" : "collect")
                } else {
                    getCollect();
                }
                break;
            case "share":
                if (utils.isApp()) {
                    utils.newsAction({ title: detailInfo.value?.newsTitle, dataId: detailInfo.value?.newsId }, "share")
                } else {
                    showToast("请在川工之家app分享")
                }
                break
        }
    })

}
//评论点赞
const getCommentLike = (item) => {
    if (isReq) return
    isReq = true
    likeOperate({
        sourceId: item.recordId,
        userId: useStore.getUserInfo?.userId,
    }).then(res => {
        isReq = false
        if (res.code == 200) {
            if (item?.whetherLike) {
                item.whetherLike = !item.whetherLike;
                showSuccessToast("取消成功");
                item.likeVolume--;
            } else {
                item.whetherLike = !item.whetherLike;
                showToast({ type: 'success', message: '点赞成功', duration: 1500 })
                item.likeVolume++;
            }
            // 气泡展示
            if (res.data?.score) {
                scoreNum.value = res.data.score
                isComment.value = true
                // 气泡展示
                setTimeout(() => { showBubble.value = true }, 1500)
            }
        }
    })
        .catch(err => {
            isReq = false
        })

}
//点赞
const getLike = () => {
    if (isReq) return
    isReq = true
    likeOperate({
        sourceId: route.query.newsId,
        userId: useStore.getUserInfo?.userId,
    }).then(res => {
        isReq = false
        if (res.code == 200) {
            if (detailInfo.value?.whetherLike) {
                detailInfo.value.whetherLike = !detailInfo.value.whetherLike;
                showSuccessToast("取消成功");
                detailInfo.value.likeVolume--;
            } else {
                chainShares(7, detailInfo.value.newsDetailsTitle, detailInfo.value.newsId, 1);
                detailInfo.value.whetherLike = !detailInfo.value.whetherLike;
                showSuccessToast("点赞成功");
                detailInfo.value.likeVolume++;
                incIntegralH5("thumbs");
            }
            // 气泡展示
            if (res.data?.score) {
                scoreNum.value = res.data.score
                if (detailInfo.value?.newsIsopenComment) {
                    bubbleX.value = 65
                }
                else bubbleX.value = 6
                // 气泡展示
                showBubble.value = true;
            }
        }
    })
        .catch(err => {
            isReq = false
        })
}
//收藏
const getCollect = () => {
    if (isReq) return
    isReq = true
    collectOperate({
        sourceId: route.query.newsId,
        userId: useStore.getUserInfo?.userId,
    }).then(res => {
        isReq = false
        if (res.code == 200) {
            if (detailInfo.value?.whetherCollect) {
                detailInfo.value.whetherCollect = !detailInfo.value.whetherCollect;
                showSuccessToast("取消成功");
                detailInfo.value.collectVolume--;
            } else {
                detailInfo.value.whetherCollect = !detailInfo.value.whetherCollect;
                showSuccessToast("收藏成功");
                chainShares(6, detailInfo.value.newsDetailsTitle, detailInfo.value.newsId, 1);
                detailInfo.value.collectVolume++;
                incIntegralH5("collection");
            }

            if (res.data?.score) {
                scoreNum.value = res.data.score
                if (detailInfo.value?.newsIsopenComment) bubbleX.value = 70
                else bubbleX.value = 40
                showBubble.value = true;
            }
        }
    })
        .catch(err => {
            isReq = false
        })
}
//分享
const getShare = () => {
    if (isReq) return
    isReq = true
    shareOperate({
        sourceId: route.query.newsId,
        userId: useStore.getUserInfo?.userId,
    }).then(res => {
        isReq = false
        if (res.code == 200) {
            // 分享不激活状态
            // detailInfo.value.whetherShare = !detailInfo.value.whetherShare;
            showSuccessToast("分享成功");
            incIntegralH5("share");
            detailInfo.value.shareVolume++;
            if (res.data?.score) {
                scoreNum.value = res.data.score
                // 气泡展示
                bubbleX.value = 80;
                showBubble.value = true;

            }
        }
    })
        .catch(err => {
            isReq = false
        })
}
// 评论列表
// 刷新
const loadMoreRef = ref(null)
const onRefreshList = () => {
    pageNum.value = 1
    getCommendList()
}
//新闻详情
const getDetail = async () => {
    const res = await getNewsDetails({ newsInfoId: route.query.newsId, platformType: 30 })
    if (res.data) {
        detailInfo.value = res.data;
        chainShares(3, detailInfo.value.newsDetailsTitle, detailInfo.value.newsId, 1);
        incIntegralH5("read");
        //查询活动详情
        if (detailInfo.value?.resourceType == 'internal') {
            getActDetails()
        }
        if (detailInfo.value?.newsIsopenComment) {
            getCommendList();
        } else {
            commentList.value = []
        }
    }
    loading.value = false;
}
const actDetail = ref(null)
//获取详情
function getActDetails() {
    getDetails({
        activityId: detailInfo.value?.internalBusinessId
    }).then(res => {
        if (res.code == 200) {
            actDetail.value = res.data;
        }
    })
}
//详情
function toDetails() {
    //内置链接跳转活动
    if (detailInfo.value?.resourceType == 'internal' && actDetail.value) {
        if (actDetail.value?.externalLink == 'n') {
            useUserStore().setActivityDetail(actDetail.value?.activityId)
            router.push({
                path: '/activityHome/activityDetail',
                query: { activityId: actDetail.value?.activityId }
            })
        } else {
            if (actDetail.value?.skipType == "builtIn") {
                getUserInfo(actDetail.value, 1)
            }
            else {
                if (actDetail.value?.skipNoticeFlag == 'y') {
                    showConfirmDialog({
                        title: '提示',
                        message: `是否跳转外链`,
                        confirmButtonText: '确认',
                        lockScroll: false,
                        showConfirmButton: true,
                    }).then(() => {
                        getUserInfo(actDetail.value)
                    })
                } else {
                    getUserInfo(actDetail.value)
                }

            }
        }
    }
    else if (detailInfo.value?.resourceType == 'external') {
        if (detailInfo.value?.detailsWhetherPrompt) {
            showConfirmDialog({
                title: '提示',
                message: `是否跳转外链`,
                confirmButtonText: '确认',
                lockScroll: false,
                showConfirmButton: true,
            }).then(() => {
                utils.openActLink({
                    win: Math.floor(Math.random() * 100),
                    title: detailInfo.value?.newsTitle,
                    url: detailInfo.value?.externalAddress,//window.location.origin+'/newsDetail?newsId='+detailInfo.value??.newsId+'&categoryCode='+props?.categoryCode,
                    shareName: detailInfo.value?.newsTitle,
                    shareurl: detailInfo.value?.externalAddress,//window.location.origin+'/newsDetail?newsId='+detailInfo.value??.newsId+'&categoryCode='+props?.categoryCode,
                    dataid: detailInfo.value?.newsId,
                    isExternal: 'y'
                })
            }).catch(() => { })
        } else {
            utils.openActLink({
                win: Math.floor(Math.random() * 100),
                title: detailInfo.value?.newsTitle,
                url: detailInfo.value?.externalAddress,//window.location.origin+'/newsDetail?newsId='+detailInfo.value??.newsId+'&categoryCode='+props?.categoryCode,
                shareName: detailInfo.value?.newsTitle,
                shareurl: detailInfo.value?.externalAddress,//window.location.origin+'/newsDetail?newsId='+detailInfo.value??.newsId+'&categoryCode='+props?.categoryCode,
                dataid: detailInfo.value?.newsId,
                isExternal: 'y'
            })
        }
    }
}
//用户信息加密
function getUserInfo(item) {
    //是否过期
    if (item.whetherExpire) {
        showToast(("授权已过期！"));
    } else {
        getEncryption(item);//获取登录返回的用户信息
    }
}
function getEncryption(item) {
    if (!useStore.getUserInfo?.userId) {
        showToast("请先登录！")
    } else {
        encryptUserInfo({
            recordId: actDetail.value?.activityId,
            userInfo: useStore?.getUserInfo,
        }).then(res => {
            if (res.code == 200 && res.data) {
                let url = item.externalLinkUrl; //返回的url
                url += item.externalLinkUrl.indexOf("?") == -1 ? "?" : "&"; //判断是否有问号
                utils.openActLink({
                    title: item.activityName,
                    url: url + "token=" + res.data,
                    shareName: item.activityName,//'分享标题'
                    shareurl: url,//'分享地址'
                    dataid: actDetail.value?.activityId,
                    type: 1,//类型 1-工会活动，2-普惠活动
                    uid: item.companyId,
                    win: item?.activityId,
                    isExternal: 'y'
                })

            } else {
                utils.openActLink({
                    title: item.activityName,
                    url: item.externalLinkUrl,
                    shareName: item.activityName,//'分享标题'
                    shareurl: item.externalLinkUrl,//'分享地址'
                    dataid: actDetail.value?.activityId,
                    type: 1,//类型 1-工会活动，2-普惠活动
                    uid: item.companyId,
                    win: item?.activityId,
                    isExternal: 'y'
                })

            }
        })
    }
}
//数据上链
const chainShares = (type, title, newsId, eventType) => {
    if (!utils.isApp()) {
        let params = {
            account: useStore.getUserInfo?.userId || "", //用户id
            eventId: newsId, //新闻主键id
            id: "",
            district: "511800",
            eventName: title,
            eventType: eventType, //1新闻2工会活动3普惠
            operationType: type, //1发布2展现3阅读4参与5离开6收藏7点赞8分享
        };
        ChainShare(params).then(function () { });
    }
}
//推送工分
const incIntegralH5 = (type) => {
    if (!utils.isApp()) {
        unionIntegralH5({
            userId: useStore.getUserInfo?.userId || "",
            increase_type: type,
        }).then((res) => { });
    }

}
//评论列表
const getCommendList = () => {
    getUserCommentList({
        newsId: route.query.newsId,
        pageSize: 10,
        pageNum: pageNum.value,
    }).then(res => {
        total.value = res.total;
        if (pageNum.value === 1) commentList.value = []
        commentList.value = commentList.value.concat(res.data)
        //重置刷新状态及 判断是否加载完成
        if (loadMoreRef.value) {
            loadMoreRef.value.onLoadSuc(commentList.value.length, res.total)
        }
    })
}
// 加载更多
const onLoadMore = () => {
    pageNum.value++;
    getCommendList();
}
//推荐
const getRecommendList = () => {
    recommenList.value = []
    getRecommendNewsListByKeywords({
        platformType: 30,
        excludeNewsId: route.query.newsId,
        pageSize: 2,
        pageNum: pageNum.value,
    }).then(res => {
        recommenList.value = res.data;
        hasNextPage.value = res.hasNextPage;
    })
}
//刷新
const refresh = () => {
    if (!hasNextPage.value) {
        pageNum.value = 1;
    } else {
        pageNum.value++;
    }
    getRecommendList();
}
onMounted(() => {
    window.h5NewsLikeReturn = (dataid, code) => {
        if (code == 200) getLike();
    }

    window.h5NewsUnLikeReturn = (dataid, code) => {
        if (code == 200) getLike();
    }
    window.h5NewsCollectReturn = (dataid, code) => {
        if (code == 200) getCollect();
    };
    window.h5NewsUnCollectReturn = (dataid, code) => {
        if (code == 200) getCollect();
    }
    window.h5NewsShareReturn = getShare


    if (!useStore.getPrefix) {
        useStore.setPrefix({});
    }
    if (route.query.showTool == '0') {
        showTool.value = false
    } else {
        showTool.value = true
    }
    getDetail();
    getRecommendList();
})
watch(route, (New, Old) => {
    loading.value = true
    getDetail();
    getRecommendList()
})
</script>

<style lang="scss" scoped>
.rich_text {
    width: 100%;

    :deep(img, image) {
        max-width: 100% !important;
        height: 100% !important;
    }
}

.control_btn {
    box-shadow: 0px 0px 21px 0px rgba(195, 194, 194, 0.31);

    image,
    img {
        max-width: 100% !important;
        max-height: 100% !important;
    }

    // :deep(.rich_text) {
    //     width:100%;
    //     // 处理富文本样式


    //     &::after {
    //         content: "";
    //         display: table;
    //         clear: both;
    //     }
    // }

    .input {
        :deep(.van-cell) {
            background-color: transparent;
        }

        :deep(.van-field__control) {
            font-size: 26px;
        }

        .active {
            background-color: #5ca5ff;
            color: #fff;
        }
    }


}

.recomend {
    .refresh {
        border: 1px solid #5CA5FF;
    }
}

.header {
    .title {
        position: relative;
        padding-left: 10px;
    }

    .title::before {
        width: 5px;
        height: 30px;
        background-color: #5CA5FF;
        border-radius: 3px;
        content: "";
        display: block;
        margin-right: 10px;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
    }
}

.border_top_20px {
    border-top: 20px solid #F2F2F2;
}

.border_bottom_1px {
    border-bottom: 1px solid #F2F2F2;
}

:deep(.van-skeleton) {
    background-color: #fff !important;
    height: 100%;
}
</style>