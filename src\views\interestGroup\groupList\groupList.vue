<template>
    <div class=" groupList w-100%">
        <div class="bg">
            <div class="p-20px">
                <Search @btn-publish="publish" @on-search="searchVal" :title="'创建小组'" />
                <Tab :data="Data.tab" @choose-index="chooseIndex" :showArea="true" @choose-area="chooseArea" />
            </div>

        </div>
        <div class="p-30px">
            <div class="min-h-80vh">
                <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
                    <interestGroupList :data="Data.groupList" :showBtn="true" @details="toDetail">
                        <template #showBtn="{ item }">
                            <div class="w-90px py-6px rounded-19px text-#fff text-center text-24px "
                                @click.stop="join(item)"
                                :class="item.joinState || item.memberCount == item.memberMax ? 'bg-#999' : 'bg-#5AA4FF'">
                                {{ item.joinState ? '退出' : item.memberCount == item.memberMax ? '已满' : '加入' }}</div>
                        </template>
                    </interestGroupList>
                </refreshList>
            </div>
        </div>
        <Popup :show="Data.showPop" :titleName="'加入原因'" :placeHolder="'请输入加入原因'" @submit-content="submitContent"
            @close-popup="closePopup" />
    </div>
</template>
<script lang="ts" setup>
import Search from "@/components/Search/index.vue";
import Tab from "@/components/Tab/tab.vue";
import router from "@/router";
import interestGroupList from "@/components/List/interestGroupList.vue";
import Popup from '@/components/Popup/popup.vue';
import { showToast, showConfirmDialog, showFailToast } from 'vant';
import { useUserStore } from '@/store/modules/user';
import { getGroupH5, joinGroup, exitGroup, getCurrentUserLabel } from '@/api/interestGroup';
import refreshList from '@/components/refreshList/index.vue';
import { useRoute, useRouter } from 'vue-router';
const route = useRoute();
const useStore = useUserStore();
const Data = ref({
    tab: {
        active: 0,
        nav: [
            { labelName: '全部' }
        ]
    },
    groupList: [],
    showPop: false,
    joinObj: {},
    searchVal: null,
    labelId: null,
    pageNum: 1,
    companyClassicIds: '',
    navData: []
})
const loadMoreRef = ref(null)
//获取兴趣小组列表
const getGroupList = () => {
    getGroupH5({
        groupName: Data.value.searchVal,
        searchLabelId: Data.value.labelId,
        pageNum: Data.value.pageNum,
        pageSize: 10,
        companyClassicIds: route.query?.companyId ? route.query?.companyId : Data.value.companyClassicIds
    }).then((res) => {
        if (Data.value.pageNum === 1) Data.value.groupList = []
        Data.value.groupList = Data.value.groupList.concat(res.data)
        //重置刷新状态及 判断是否加载完成
        if (loadMoreRef.value) {
            loadMoreRef.value.onLoadSuc(Data.value.groupList.length, res.total)
        }
    })
}
// 刷新
const onRefreshList = () => {
    Data.value.pageNum = 1
    getGroupList()
}
// 加载更多
const onLoadMore = () => {
    Data.value.pageNum++
    getGroupList()
}
//按钮
function publish() {
    router.push('/creatGroup')
}
//搜索
function searchVal(val) {
    Data.value.searchVal = val;
    Data.value.pageNum = 1
    getGroupList();
}
//筛选
function chooseIndex(item) {
    Data.value.tab.active = item.activIndex;
    Data.value.labelId = item.autoId;
    Data.value.pageNum = 1
    getGroupList();
}
//区域筛选
function chooseArea(item) {
    console.log(item);
    Data.value.companyClassicIds = item.remark;
    Data.value.pageNum = 1
    getGroupList();
}
//加入
function join(item) {
    Data.value.joinObj = item;
    if (item.memberCount == item.memberMax) {
        return
    }
    if (!item.joinState) {
        Data.value.showPop = true;
    }
    else {
        showConfirmDialog({
            title: "提示",
            message: `确认退出${item.groupName}小组?`,
            confirmButtonText: "确认",
            cancelButtonText: "取消",
            className: "close",
        }).then(async () => {
            // 自定义代码
            exitGroup({ groupId: Data.value.joinObj.groupId }).then(res => {
                if (res.code == 200) {
                    showToast("退出成功");
                    item.joinState = !item.joinState
                } else {
                    showFailToast(res.message)
                }
            })
        });

    }
}
//确认提交
function submitContent(val) {
    if (!val) {
        showToast({
            message: "请输入加入原因",
            icon: 'none',
        })
        return
    }
    joinGroup({
        groupName: Data.value.joinObj.groupName,
        groupId: Data.value.joinObj.groupId,
        reason: val
    }).then(res => {
        if (res.code == 200) {
            showToast("加入成功，等待管理员审核~")
        } else {
            showFailToast(res.message)
        }

    })
    Data.value.showPop = false;
}
//关闭
function closePopup() {
    Data.value.showPop = false;
}
//跳转详情
function toDetail(item) {
    router.push({
        path: '/groupDetail',
        query: {
            groupId: item.groupId
        }
    })
}
//获取兴趣小组用户标签
const getUserLabel = () => {
    getCurrentUserLabel().then((res) => {
        Data.value.navData = res.data;
        Data.value.navData?.map(el => {
            el.isChoose = false
        })
        Data.value.tab.nav = Data.value.tab.nav.concat(Data.value.navData);
        useStore.setLabelData(Data.value.navData)
    })
}
onMounted(() => {
    if (useStore.getLabelData?.length) {
        Data.value.tab.nav = Data.value.tab.nav.concat(useStore.getLabelData)
    } else {
        getUserLabel()
    }

    getGroupList()
})
</script>
<style lang="scss">
.groupList {
    min-height: 100vh;
    background: #F6F7F8;

    .van-tabs__nav {
        background: transparent;
    }

    .bg {
        background: url("@/assets/interest/bg_cover.png") no-repeat;
        background-size: 100% 100%;
    }
}
</style>
