import { h5Http,dataCenterHttp } from '@/utils/http/axios';

// 活动详情
export const treeAcDetail = () => {
    return h5Http.get({
        url: '/activityInfo/h5/integralTreeDetail',
    })
}
// 个人积分树信息
export const personalTreeInfo = (activityId:string) => {
    return h5Http.get({
        url: '/integralTree/user',
        params:{activityId}
    })
}
// 领取树苗
export type applyTreeType = {
    "platform": string,
    "activityId": string
}
export const applyTree = (params:applyTreeType) => {
    return h5Http.post({
        url: '/integralTree/activeTree',
        params
    })
}

// 每日兑换 
export type dailyExchangeType = {
    "platform": string,
    "exchangeType":string,
    "exchangeCount":number,
    "activityId": string
}
export const dailyExchange = (params:dailyExchangeType) => {
    return h5Http.post({
        url: '/integralTree/exchange',
        params
    })
}
// end

// 每日浇水/施肥
export type dailyWaterFertilizerType = {
    "platform": string,
    "exchangeType":string, //操作类型：water：浇水 fertilizer：施肥
    "activityId": string
}
export const dailyWaterFertilizer = (params:dailyWaterFertilizerType) => {
    return h5Http.post({
        url: '/integralTree/operate',
        params
    })
}
// end

// 个人积分
export const personIntegral = (userId:string) => {
    return dataCenterHttp.get({
        url: '/customIntegral/getIntegralByUserId',
        params: { userId}
    })
}

// 兑换奖品
export type exchangePrizeType = {
    "activityId": string,
    "platform":string,
    "prizeInfoId":string //奖品id
}

export const exchangePrize = (params:exchangePrizeType) => {
    return h5Http.post({
        url: '/activityInfo/h5/luckDraw/receive',
        params
    })
}