<template>
  <div class="filter-pop relative w-100vw flex bg-white items-center mt-1px">
    <div class="w-[calc(100%-80px)] h-76px">
      <van-tabs v-model:active="active" :line-height="0" :line-width="0" @click-tab="clickTab">
        <van-tab :title="item.dictName" v-for="(item, index) in areaList">
          <template #title>
            <span class="bg-#F6F6F6 px-30px py-10px rounded-30px text-#9B9B9B" :class="{
              '!bg-#EEF6FF !text-#5AA4FF': active == index,
            }">
              {{ item.dictName }}
            </span>
          </template>
        </van-tab>
      </van-tabs>
    </div>
    <img loading="lazy" src="@/assets/public/icon_sx.png" alt="" class="w-29px h-29px ml-20px" @click="showOverLay" />

    <van-overlay :show="overlayShow" @click="overlayShow = false" lock-scroll :z-index="100"
      class="my-overlay absolute top-76px">
      <div class="bg-[rgba(0,0,0,.3)] w-full relative mt-1px !h-[calc(100vh-186px)]" @click.stop="showOverLay">
        <div class="bg-#fff p-30px pr-10px max-h-28vh overflow-scroll">
          <div class="text-28px mb-20px">所属区域：</div>
          <div class="flex flex-wrap">
            <div v-for="(item, index) in areaList" :key="index"
              :class="active == index ? 'bg-#EEF6FF text-#5AA4FF' : 'bg-#F6F7F8'"
              class="kind rounded-10px text-center py-17px px-17px mb-20px text-28px mr-20px"
              @click.stop="chooseArea(index,item,'area')">
              {{ item.dictName }}
            </div>
          </div>
          <div class="text-28px mb-20px">所属栏目：</div>
          <div class="flex flex-wrap">
            <div v-for="(item, index) in typeList" :key="index"
              :class="type_active == index ? 'bg-#EEF6FF text-#5AA4FF' : 'bg-#F6F7F8'"
              class="kind rounded-10px text-center py-17px px-17px mb-20px text-28px mr-20px"
              @click.stop="chooseArea(index,item,'type')">
              {{ item.dictName }}
            </div>
          </div>
          
        </div>
      </div>
    </van-overlay>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useDictionary } from '@/store/modules/dictionary';
const dictionary = useDictionary();

const emit = defineEmits(['update:modelValue', 'success']);

const areaList = ref<any>([]);
const typeList = ref<any>([]);
const active = ref(0);
const type_active =ref(0)
const overlayShow = ref(false);

const clickTab = () => {
  let obj= {
    areaId:areaList.value[active.value]?.remark,shortVideoType:typeList.value[type_active.value]?.dictCode
  }
  emit('success',obj)
}

function chooseArea(index: number,item: Object,type:String) {
  if(type=="area"){
    active.value = index;
    // obj.areaId=item.remark
  }else{
    type_active.value = index;
    // obj.shortVideoType=item.dictCode
  }
  let obj= {
    areaId:areaList.value[active.value]?.remark,shortVideoType:typeList.value[type_active.value]?.dictCode
  }
  // emit('update:modelValue', obj);
  emit('success',obj);
  overlayShow.value = false;
}

function showOverLay() {
  overlayShow.value = !overlayShow.value;
}

onMounted(() => {
  areaList.value = [
    ...dictionary.getDictionaryOBJMap?.['regionCode']?.filter(
      (item: any) => item.dictCode !== '511399'
    ),
  ];
  typeList.value = [
    ...dictionary.getDictionaryOBJMap?.['shortVideoType'],
  ];
  // emit('update:modelValue', areaList.value[0].remark);
  emit('success',{areaId:areaList.value[0].remark,shortVideoType:typeList.value[0].dictCode});
});
</script>

<style lang="less" scoped>
.filter-pop {
  --van-tabs-bottom-bar-height: 0px;
  --van-tab-font-size: 28px;
  --van-tabs-line-height: 76px;
  --van-padding-sm: 12px;

  .van-overlay {
    background: transparent;
  }
}
</style>
