<template>
    <div class="pb-20px">
        <van-form ref="formRef" @submit="submit">
            <van-cell-group>
                <div v-for="(item,index) of activityDetail?.signUpInfo?.topicInfoList" :key="index">
                    <van-field v-model="item.answer"
                            :label="item.topicContent"
                            :placeholder="item.topicContent"
                            :rules="[{ required: item.ifMust === 'y'?true:false, message: item.topicContent }]"
                            :required="item.ifMust === 'y'" input-align="right" error-message-align="right"
                            v-if="item.optionType === 'single-text'"/>
                    <van-field v-model="item.answer"
                            :label="item.topicContent"
                            :placeholder="item.topicContent"
                            :required="item.ifMust === 'y'"
                            :rules="[{ required: item.ifMust === 'y'?true:false, message: item.topicContent  }]"
                            v-if="item.optionType === 'radio'"
                            is-link readonly input-align="right" error-message-align="right"
                            @click="selectEvet(item,index)" ></van-field>
                    <div v-if="item.optionType === 'many-text'" class="textarea">
                        <div class="special_lable text-28px pl-14px pt-14px">{{ item.topicContent }}</div>
                        <van-field  v-model="item.answer" input-align="left"  :required="item.ifMust === 'y'"  :rules="[{ required: item.ifMust === 'y'?true:false, message: item.topicContent  }]"  :placeholder="item.topicContent" type="textarea"/>
                    </div>
                    <div v-if="item.optionType === 'select'">
                        <van-field
                                readonly
                                clickable
                                :label="item.topicContent"
                                :required="item.ifMust === 'y'"
                                :rules="[{ required: item.ifMust === 'y'?true:false, message: item.topicContent  }]"
                                name="type"
                                v-model="item.answer"
                                :placeholder="item.topicContent"
                                is-link
                                @click="chooseSelect(item,index)"
                                input-align="right" error-message-align="right"/>
                    </div>
                </div>
            </van-cell-group>
            <van-button  type="primary" block class="btn w-65/100 mt-40px m-auto text-34px text-#fff py-23px text-center border-none" native-type="submit" >确定</van-button>
        </van-form>
        <!-- <van-popup v-model:show="Data.showPicker" position="bottom">
            <van-picker :columns="Data.columns" @confirm="onConfirm" @cancel="Data.showPicker = false" />
        </van-popup> -->
          <!-- 单选类别弹窗 -->
          <van-popup v-model:show="Data.showPickerkindType" position="bottom">
            <van-picker :columns="Data.columns" @cancel="Data.showPickerkindType = false" @confirm="chooseType" :show-toolbar="true">
                <template #option="option">
                    {{ option.optionContent }}
                </template>
            </van-picker>
        </van-popup>
        <!--多选-->
        <div v-if="Data.showType" class="popup-bg" @click="cancel">
            <div class="popup"  :class="Data.showType?'popupChoose':''">
                <div class="flex justify-between">
                    <div @click="cancel" class="cancle">取消</div>
                    <div @click="confirmOk()" class="confirm">确定</div>
                </div>
                <van-checkbox-group v-model="Data.topicInfo.result">
                    <van-cell-group>
                        <van-cell
                                v-for="(v,k) in Data.topicInfo.options"
                                clickable
                                :key="v.optionContent"
                                :title="v.optionContent"
                                @click.stop="v.checked = !v.checked"
                        >
                            <template #right-icon>
                                <van-checkbox :name="k" ref="checkboxes" />
                            </template>
                        </van-cell>
                    </van-cell-group>
                </van-checkbox-group>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { useUserStore } from '@/store/modules/user';
const useStore = useUserStore();
import {useRoute} from "vue-router";
const route= useRoute();
import { getDetails,signUp } from '@/api/activity';
import router from '@/router';
import { showFailToast, showSuccessToast } from 'vant';
const Data=ref({
    detail:{},
    columns:[],
    selectIndex:'',
    showPickerkindType:false,
    showType:false,
    topicInfo:{},
})
//选择
// const showPickerFn = (list, type) => {
//     Data.value.columns = list
//     Data.value.showPicker = true
//     Data.value.type = type
// }
// const onConfirm = ({ selectedOptions }) => {
//     switch (Data.value.type) {
//         case 'education':
//         Data.value.formatData.educationName = selectedOptions[0]?.text;
//         Data.value.formatData.education = selectedOptions[0]?.value;
//             break;

//     }
//     Data.value.showPicker= false

// }
//获取详情
function getActDetails() {
    getDetails({
        activityId:route.query.activityId
    }).then(res=>{
        if(res.code==200){
            activityDetail=res.data;
        }
    })
}
 // 开启选择
 function selectEvet(arr,index) {
    Data.value.columns = arr.options;
    Data.value.selectIndex = index
    Data.value.showPickerkindType = true;

}
// 开启选择-多选
function chooseSelect(item,index) {
    Data.value.topicInfo = item
    Data.value.selectIndex = index
    Data.value.showType = true;
}
 //类别
function chooseType(value) {
    activityDetail.signUpInfo.topicInfoList[Data.value.selectIndex].answer = value.selectedOptions[0].optionContent;
    Data.value.showPickerkindType = false;
}
//确定
function  confirmOk(){
    Data.value.showType=false
    activityDetail.signUpInfo.topicInfoList[Data.value.selectIndex].answer = Data.value.topicInfo.options.filter(t=>t.checked).map(t=>t.optionContent)?.join(",")||'' //多选框显示选中值
}
//多选取消
function cancel(){
    Data.value.showType=false
}
//提交
function submit() {
    console.log(1);

    let answerRecords = {};
    activityDetail.signUpInfo.topicInfoList.map(itm=>{
        answerRecords[itm.topicInfoId] = itm.answer
    })
    signUp({
        activityId: activityDetail.activityId,
	    platform: "app",
        topicAnswers: answerRecords
    }).then(res=>{
        if(res.code==200){
            showSuccessToast('提交成功')
            setTimeout(()=>{
                router.go(-1)
            },1500)
        }else{
            showFailToast(res.message)
        }
    })
}
onMounted(()=>{
    getActDetails();
})
</script>
<style lang="scss">
.textarea{
    .van-field__control{
        background: #F6F7F8;
        border-radius: 8px;
        padding: 20px;
    }
    .van-field__body{
        margin-top: 15px;
    }
}
.btn{
    background: url("@/assets/public/button.png") no-repeat;
    background-size: 100% 100%;
}
.popup-bg{
    background: rgba(51, 51, 51, 0.5);
    width: 100%;
    height:100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 2;
    .van-cell__title{
        text-align: left;
    }
    .cancle{
        padding:28px 0 28px 28px;
        font-size:28px;
        color: #969799;
    }
    .confirm{
        padding:28px  28px 28px 0;
        font-size:28px;
        color: #576b95;
    }
    .popup{
        position: absolute;
        bottom: 0;
        left: 0;
        z-index: 100;
        width: 100%;
        height: 45%;
        overflow: auto;
        background: #fff;
        transition: all 3s;
    }
}
</style>
