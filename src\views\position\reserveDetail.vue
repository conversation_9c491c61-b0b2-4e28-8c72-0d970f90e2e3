<template>
  <div class="w-full relative"
    :class="`${$style['reserve-detail']} ${detailRecord?.state === 'pass' ? 'bg-[#f5f5f5]' : 'bg-[#fff]'}`">
    <div class="absolute left-60px top-70px">
      <img loading="lazy" alt="" class="h-43px mb-10px" :src="BtnStr[detailRecord?.state]?.icon" />
      <div class="!text-[#fff] !text-28px">{{ BtnStr[detailRecord?.state]?.tip }}</div>
    </div>
    <img loading="lazy" alt="" class="w-full" :src="BtnStr[detailRecord?.state]?.bg" />

    <div v-if="detailRecord?.state === 'pass'" class="h-[500px] relative top-[-19px] px-[30px]">
      <div class="bg-[#fff] w-full h-full rounded-[16px] flex flex-col justify-evenly items-center">
        <div class="text-[32px] text-[#333] text-center relative w-full">
          <div class="font-semibold mb-1 w-full">
            <div>当前定位</div>
            <div
              class="absolute right-2 top-0 text-[24px] rounded-[16px] text-[#5ea6ff] px-1 active:shadow-[0_0_10px_0_#5ba5ff] active:rounded-[36px]"
              :style="{ border: '1px solid #5ea6ff' }" @click="initPosition">
              <van-icon name="replay" color="#5ea6ff" />
              刷新
            </div>
          </div>
          <div class="text-[28px] text-[#888888]">{{ position?.locality }}</div>
        </div>

        <div
          class="relative w-[300px] h-[300px] flex justify-center flex-col items-center text-[#fff] active:shadow-[0_0_10px_0_#5ba5ff] active:rounded-full"
          @click="handleSign">
          <div class="z-10"> 立即签到 </div>
          <div class="z-10 mt-1"> {{ dateTime }} </div>
          <img loading="lazy" :src="circleSuccess" class="w-full h-full absolute top-0" />
        </div>
      </div>
    </div>

    <div class="px-30px pt-10px bg-[#fff] rounded-[16px]" :class="detailRecord?.state === 'pass' ? 'mx-[30px]' : ''">
      <ReserveDetailCenter :detailRecord="detailRecord" />
      <div class="flex justify-center items-center mt-100px w-full mb-10" v-if="ifShowBtn">
        <Button :name="buttonStr" class="!w-[470px] !h-[70px] active:shadow-[0_0_10px_0_#5ba5ff] active:rounded-[36px]"
          @click="handleClick" />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { cancelRecord, getRecordByRecordId, punchSign } from '@/api/position';
import Button from '@/components/Button/button.vue';
import ReserveDetailCenter from './components/ReserveDetailCenter.vue';
import { includes, isEmpty } from 'lodash-es';
import useExpireMsg from '@/assets/position/reserve/useExpire-msg.png';
import expireMsg from '@/assets/position/reserve/expire-msg.png';
import cancelMsg from '@/assets/position/reserve/cancel-msg.png';
import passMsg from '@/assets/position/reserve/pass-msg.png';
import refuseMsg from '@/assets/position/reserve/refuse-msg.png';
import reviewMsg from '@/assets/position/reserve/review-msg.png';
import usedMsg from '@/assets/position/reserve/used-msg.png';
import reviewBg from '@/assets/position/reserve/review-bg.jpg';
import usedBg from '@/assets/position/reserve/used-bg.jpg';
import cancelBg from '@/assets/position/reserve/cancel-bg.jpg';
import expireBg from '@/assets/position/reserve/expire-bg.jpg';
import passBg from '@/assets/position/reserve/pass-bg.jpg';
import refuseBg from '@/assets/position/reserve/refuse-bg.jpg';
import circleSuccess from '@/assets/position/reserve/circle-success.png';
import { useDateFormat, useNow } from '@vueuse/core';
import { showConfirmDialog, showFailToast, showSuccessToast, showToast } from 'vant';
import utils from '@/utils/utils';
import { checkReservation } from './utils';
import { useUserStore } from '@/store/modules/user';

const route = useRoute();

const router = useRouter();

const userStore = useUserStore();

const dateTime = useDateFormat(useNow(), 'MM-DD HH:mm:ss');

const BtnStr: Recordable = {
  review: {
    text: '取消预约',
    icon: reviewMsg,
    tip: '请耐心等待~',
    bg: reviewBg,
    fn: function () {
      cancelRecord({ recordId: unref(detailRecord)?.recordId }).then(({ code, message }) => {
        if (code === 200) {
          router.go(-1);
          showSuccessToast(`取消成功！`);
        } else {
          showFailToast(`取消失败！${message || ''}`);
        }
      });
    },
  },
  pass: { text: '', icon: passMsg, bg: passBg, tip: '使用时请定位打卡核销' },
  refuse: {
    text: '重新预约',
    icon: refuseMsg,
    tip: '请重新预约',
    bg: refuseBg,
    fn: function () {
      checkReservation(
        {
          venueInfoId: unref(detailRecord)?.venueInfoId,
          userId: userStore.getUserInfo.userId,
        },
        unref(detailRecord)?.venueInfoAutoId,
        unref(detailRecord)?.recordId,
        router
      );
    },
  },
  cancel: { text: '', icon: cancelMsg, bg: cancelBg, tip: '您已取消预约' },
  expire: { text: '', icon: expireMsg, bg: expireBg, tip: '您的预约已过期' },
  used: {
    text: '去评价',
    icon: usedMsg,
    bg: usedBg,
    tip: '分享一下您的使用体验吧',
    fn: function () {
      // 评价
      router.push({
        path: '/evaluationOption',
        query: {
          positionInfoId: unref(detailRecord)?.positionInfoId,
        },
      });
    },
  },
  useExpire: {
    text: '',
    icon: useExpireMsg,
    bg: expireBg,
    tip: '您的预约已过期',
  },
};

const detailRecord = ref<Recordable>();

const buttonStr = ref('');

const position = ref<Recordable>();

const ifShowBtn = ref<boolean>(false);

function handleSign() {
  if (isEmpty(unref(position))) {
    showFailToast('未获取到当前为止信息，请重试！');
    return;
  }

  punchSign({
    recordId: unref(detailRecord)?.recordId,
    useCoordinate: `${unref(position)?.longitude},${unref(position)?.latitude}`,
    useAddress: `${unref(position)?.placeName}`,
  }).then(({ code, message }) => {
    if (code === 200) {
      showSuccessToast(`签到成功！`);
      getDetail();
    } else {
      showFailToast(`签到失败！${message || ''}`);
    }
  });
}

function handleClick() {
  BtnStr[unref(detailRecord)?.state]?.fn?.();
}

function initPosition() {
  if (unref(detailRecord)?.state === 'pass') {
    utils.getPosition('position', (code: any, content: any) => {
      if (code == 200) {
        position.value = JSON.parse(content);
      }
    })
  }
}

async function getDetail() {
  detailRecord.value = await getRecordByRecordId(route.query);
  buttonStr.value = BtnStr[unref(detailRecord)?.state]?.text;
  ifShowBtn.value = includes(['review', 'refuse', 'used'], unref(detailRecord)?.state);
  initPosition();
}
onMounted(async () => {

  getDetail()

});
</script>

<style module lang="less">
.reserve-detail {
  :global {
    font-weight: 400;
    font-size: 30px;
    color: #666666;

    span {
      font-size: 30px;
      color: #333333;
    }
  }
}
</style>
