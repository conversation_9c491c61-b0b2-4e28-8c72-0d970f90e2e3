<template>
  <div class="w-full relative" :class="$style['reserve-detail']">
    <div class="absolute left-60px top-70px">
      <img loading="lazy" alt="" class="h-43px mb-10px" :src="BtnStr[detailRecord?.state]?.icon" />
      <div class="!text-[#fff] !text-28px">{{ BtnStr[detailRecord?.state]?.tip }}</div>
    </div>
    <img loading="lazy" alt="" class="w-full" :src="BtnStr[detailRecord?.state]?.bg" />

    <div class="px-30px pt-10px bg-[#fff] rounded-[16px]">
      <ReserveDetailCenter :detailRecord="detailRecord" class="detail">
        <template #black="{ item }">
          <div class="w-160px h-[50px] rounded-[21px] justify-center flex items-center text-[22px] "
            :class="detailRecord?.whetherBlackList == 'n' ? '!text-[#5AA4FF] border-[1px] border-[#5AA4FF] border-solid ' : 'text-[22px] !text-[#fff] bg-[#5BA5FF]'"
            @click="addBlack()">
            {{ detailRecord?.whetherBlackList == 'n' ? '加入黑名单' : '已加入黑名单' }}</div>
        </template>
      </ReserveDetailCenter>
      <div v-if="detailRecord?.state === 'review'">
        <div class="flex items-center justify-between w-[100%] px-[90px] box-border fixed bottom-[84px]">
          <van-button block @click.stop="onRefuse(detailRecord, 'refuse')"
            class="w-[260px] h-[76px] rounded-[39px] justify-center flex items-center text-[30px] !text-[#5AA4FF] border-[1px] border-[#5AA4FF] border-solid mr-[30px]">
            拒绝
          </van-button>
          <van-button block @click.stop="onPass(detailRecord, 'pass')"
            class="w-[260px] h-[78px] rounded-[39px] justify-center flex items-center text-[30px] !text-[#fff]"
            style="background: linear-gradient(0deg, #a1cbff 0%, #5aa4ff 100%)">
            通过
          </van-button>
        </div>
      </div>
    </div>

    <Popup :show="Data.showPop" :titleName="Data.titleName" :placeHolder="'请输入' + Data.titleName"
      @submit-content="submitContent" @close-popup="closePopup" />
    <van-popup v-model:show="Data.show" position="bottom" round :style="{ height: '25%' }">
      <div class="text-#444 text-28px text-center px-100px py-35px border-#EEEEEE border-1px border-solid">
        加入黑名单后，该用户不可再使用数字阵地 预约功能，请确定是否将该用户拉黑</div>
      <div class="text-#F43F31 text-32px py-30px text-center" style="border-bottom:6px solid #F5F5F5"
        @click="chooseOk()">确定
      </div>
      <div class="text-#333 text-32px py-30px text-center" @click="Data.show = false">取消</div>
    </van-popup>
  </div>
</template>
<script setup lang="ts">
import { administratorsGetRecordByRecordId, administratorsAuditRecord, administratorsSaveVenueBlacklist } from '@/api/position';
import ReserveDetailCenter from '../../position/components/ReserveDetailCenter.vue';
import { includes } from 'lodash-es';
import useExpireMsg from '@/assets/position/reserve/useExpire-msg.png';
import expireMsg from '@/assets/position/reserve/expire-msg.png';
import cancelMsg from '@/assets/position/reserve/cancel-msg.png';
import passMsg from '@/assets/position/reserve/audit-pass.png';
import refuseMsg from '@/assets/position/reserve/audit-refuse.png';
import reviewMsg from '@/assets/position/reserve/wait-msg.png';
import usedMsg from '@/assets/position/reserve/used-msg.png';
import reviewBg from '@/assets/position/reserve/review-bg.jpg';
import usedBg from '@/assets/position/reserve/used-bg.jpg';
import cancelBg from '@/assets/position/reserve/cancel-bg.jpg';
import expireBg from '@/assets/position/reserve/expire-bg.jpg';
import passBg from '@/assets/position/reserve/pass-bg.jpg';
import refuseBg from '@/assets/position/reserve/refuse-bg.jpg';
// import circleSuccess from '@/assets/position/reserve/circle-success.png';
// import { useDateFormat, useNow } from '@vueuse/core';
import { showFailToast, showToast } from 'vant';
import { useUserStore } from '@/store/modules/user';

import Popup from "@/components/Popup/popup.vue";
const route = useRoute();

// const router = useRouter();

const userStore = useUserStore();

// const dateTime = useDateFormat(useNow(), 'MM-DD HH:mm:ss');

const BtnStr: Recordable = {
  review: {
    icon: reviewMsg,
    tip: '请您尽快完成审核流程',
    bg: reviewBg,
  },
  pass: { text: '', icon: passMsg, bg: passBg, tip: '您已通过此次阵地预约' },
  refuse: {
    tip: '您已拒绝此次阵地预约',
    icon: refuseMsg,
    bg: refuseBg,

  },
  cancel: { text: '', icon: cancelMsg, bg: cancelBg, tip: '用户已取消预约' },
  expire: { text: '', icon: expireMsg, bg: expireBg, tip: '用户预约已过期' },
  used: {
    icon: usedMsg,
    bg: usedBg,
    tip: '用户已完成此次预约签到',

  },
  useExpire: {
    text: '',
    icon: useExpireMsg,
    bg: expireBg,
    tip: '用户未使用此次预约',
  },
};

const detailRecord = ref<Recordable>();

const buttonStr = ref('');

const position = ref<Recordable>();

const ifShowBtn = ref<boolean>(false);
const Data = ref<any>({
  showPop: false,
  pageNum: 1,
  objInfo: {},
  titleName: '',
  show: false
})

function handleClick() {
  BtnStr[unref(detailRecord)?.state]?.fn?.();
}

async function getDetail() {
  detailRecord.value = await administratorsGetRecordByRecordId(route.query);

  buttonStr.value = BtnStr[unref(detailRecord)?.state]?.text;
  ifShowBtn.value = includes(['review', 'refuse', 'used'], unref(detailRecord)?.state);
}
/**
* 点击拒绝  处理函数
*
* @param item 请求项，可以是任意类型的值
*/
function onRefuse(item: any, type) {
  Data.value.showPop = true;
  item.type = type;
  Data.value.titleName = '审核不通过原因'
  Data.value.objInfo = item;

}
//通过
function onPass(item, type) {
  item.type = type;
  Data.value.objInfo = item;
  Data.value.titleName = '审核通过原因'
  Data.value.showPop = true;
}
//确认提交  拒绝
function submitContent(val: string) {
  // if (!val&&Data.value.objInfo.type=='refuse') {
  //   showToast({
  //     message: "请输入拒绝原因",
  //     icon: "none",
  //   });
  //   return;
  // }
  administratorsAuditRecord({
    recordId: Data.value.objInfo.recordId,
    state: Data.value.objInfo.type,
    auditOpinion: val,
    phone: userStore.getUserInfo?.phone
  }).then(res => {
    if (res.code == 200) {
      showToast("审核成功");
      Data.value.showPop = false;
      getDetail()
    }
  })

}
//关闭拒绝弹窗
function closePopup() {
  Data.value.showPop = false;
}
function addBlack() {
  if (detailRecord?.value?.whetherBlackList == 'n') {
    Data.value.show = true
  }
}
//确定
function chooseOk() {
  administratorsSaveVenueBlacklist({
    recordId: detailRecord.value?.recordId,
    phone: userStore.getUserInfo?.phone,
  }).then(res => {
    if (res.code == 200) {
      getDetail();
      Data.value.show = false;
    } else {
      showFailToast(res.message)
    }
  })
}
onMounted(async () => {

  getDetail()

});
</script>

<style module lang="less">
.reserve-detail {
  :global {
    font-weight: 400;
    font-size: 30px;
    color: #666666;

    .detail span {
      font-size: 30px;
      color: #333333;
    }
  }
}
</style>