<template>
  <div class="relative top-[-80px] px-[30px] py-2" :class="$style['detail-center']">
    <div class="bg-[#fff] w-full rounded-[16px] px-2 pt-2 pb-[140px]">
      <van-tabs v-model:active="active">
        <van-tab v-for="item in dataTabs" :title="item.labelName">
        </van-tab>
      </van-tabs>
      <component :is="CompArr[active]" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import CenterList from './CenterList.vue';
import FieldService from './FieldService.vue';
import PositionEvaluation from './PositionEvaluation.vue';

const CompArr = [markRaw(CenterList), markRaw(FieldService), markRaw(PositionEvaluation)];

const dataTabs = ref([
  { labelName: '场所预约', value: 0 },
  { labelName: '阵地服务', value: 1 },
  { labelName: '阵地评价', value: 2 },
]);

const active = ref(0);

onMounted(() => {
  // onLoad();
});
</script>

<style lang="less" module>
.detail-center {
  :global {
    .van-tab {
      border-bottom: 1px solid #ebebeb;
      line-height: 1;
    }

    .van-tab__text {
      font-size: 32px;
    }

    .van-tabs__line {
      background: linear-gradient(-90deg, #c9ebff 0%, #5eacfd 100%);
      border-radius: 3px;
      width: 50px;
    }

    .van-cell {
      &::after {
        border-color: #cfcfcf !important;
      }
    }

    .van-tabs--line .van-tabs__wrap {
      height: 78px;
    }
  }
}
</style>
