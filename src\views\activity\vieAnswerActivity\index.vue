<template>
  <div class="vieAnswerActivity w-full h-100vh relative" style="background-size: 100% 100%;"
    :style="{ backgroundImage: `url(${activityDetail.appDetailsCover})` }">
    <div class="absolute bottom-190px w-full flex flex-col items-center">
      <div class="w-440px h-106px btn flex justify-center items-center text-[#fff] text-42px pb-12px mb-40px"
        v-if="activityDetail.vieAnswerInfo" @click="onQuestion">
        开始答题
      </div>
      <div class="flex btns w-70/100 justify-around">
        <div class="" @click="toDetail"> <img loading="lazy" src="@/assets/activity/actinfo.png" alt=""
            class="w-26px mr-10px">活动详情</div>
        <div class="mx-10px" @click="toRank"> <img loading="lazy" src="@/assets/activity/rank/rank.png" alt=""
            class="w-26px mr-10px">排行榜</div>
        <div @click="toLotteryRecord" v-if="activityDetail.luckDraw === 'y'"> <img loading="lazy"
            src="@/assets/activity/actjl.png" alt="" class="w-34px mr-8px">中奖记录
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { activityValidator, defaultValidator, activityDetailValidator, checkIntegral } from '@/hooks/useValidator.js'
import { showDialog } from 'vant'
import { ref, computed, onMounted, watch } from 'vue';
import backgroundImg from '@/assets/activity/background.jpg'
import { getVieAnswerState } from '@/api/activity';
import { useRoute, useRouter } from "vue-router";
import { useUserStore } from "@/store/modules/user";
const useStore = useUserStore();
const router = useRouter();
const route = useRoute();
const answerDailyCount = ref(0);
const answerTotal = ref(0);
const activityDetail = computed(() => useStore.activityDetail || {});
watch(activityDetail, () => {
  if (activityDetail.value.activityId) {
    activityDetail.value.appDetailsCover = activityDetail.value.appDetailsCover ? activityDetail.value.appDetailsCover : backgroundImg
    getVieAnswerCount();
  }
})
onMounted(() => {
  if (activityDetail.value.activityId) {
    activityDetail.value.appDetailsCover = activityDetail.value.appDetailsCover ? activityDetail.value.appDetailsCover : backgroundImg
    getVieAnswerCount();
  }
});
const onQuestion = async () => {
  if (!activityValidator()) return;

  const { numberPerDay, totalParticipation } = activityDetail.value?.vieAnswerInfo || {};
  if (answerTotal.value >= totalParticipation) {
    showDialog({
      title: "温馨提示",
      message: `没有答题次数~`,
      confirmButtonText: "我知道了",
    });
    return;
  }
  if (answerDailyCount.value >= numberPerDay) {
    showDialog({
      title: "温馨提示",
      message: `每日可参与答题${numberPerDay}次,您今日已无答题次数,明日再来吧~`,
      confirmButtonText: "我知道了",
    });
    return
  }
  if (! await checkIntegral()) {
    return;
  }
  router.push('/activityHome/vieAnswerActivity/question');
};
const toDetail = () => {
  if (!activityDetailValidator()) return;
  router.push('/activityHome/activityDetail');
};
const toRank = () => {
  if (!activityDetailValidator()) return;
  router.push('/activityHome/vieAnswerActivity/rank');
};

const toLotteryRecord = () => {
  if (!defaultValidator()) return;
  router.push('/activityHome/lotteryRecord');
};
const getVieAnswerCount = async () => {
  if (!useUserStore().userInfo) {
    return
  }
  const res = await getVieAnswerState({
    activityId: activityDetail.value.activityId,
  });
  if (res.code == 200) {
    answerDailyCount.value = res.data?.daily ?? 0
    answerTotal.value = res.data?.total ?? 0
  }
};
</script>

<style lang="scss" scoped>
.vieAnswerActivity {
  background-color: #9bbefe;

  .btn {
    background-image: url('@/assets/activity/draw_btn.png');
    background-size: 100% 100%;
  }

  .btns {
    >div {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 188px;
      height: 56px;
      font-size: 28px;
      color: #FFFFFF;
      background: #D1170B;
      border-radius: 14px;
      border: 1px solid #FFFFFF;

    }
  }
}
</style>
