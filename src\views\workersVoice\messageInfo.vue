<template>
  <div class="w-full min-h-full" :class="$style['message-info']">
    <van-field v-model="val" :center="true" :clearable="true" placeholder="请输入关键字" class="!py-1 !pr-10px">
      <template #button>
        <van-button size="small" @click="handleSearch">搜索</van-button>
      </template>
      <template #left-icon>
        <img loading="lazy" :src="iconSearch" class="w-[24px] h-[24px]" />
      </template>
    </van-field>
    <TabChange :data="dataTabs" wrapperClass="top-580px h-[calc(100%-580px)]" :swipeThreshold="4"
      @choose-index="handleTabChange" />
    <div class="pb-2 relative">
      <MessageList :api-list="selectEmployeeMessageList" :if-nothing="false" :if-del="false" :params="listParams"
        @click-card="handleClick" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import iconSearch from '@/assets/workers-voice/icon-search.png';
import TabChange from '@/components/Tab/tab.vue';
import MessageList from './list.vue';
import { questionTypeList, selectEmployeeMessageList } from '@/api/workerVoice';
import { map } from 'lodash-es';

const router = useRouter();

const val = ref<string>();

const dataTabs = reactive<{ active: number; nav: Recordable[] }>({
  active: 0,
  nav: [],
});

const listParams = reactive<Recordable>({});

function handleTabChange(item: Recordable) {
  dataTabs.active = item.activIndex;
  listParams.employeeMessageTypeId = item.value;
}

function handleSearch() {
  listParams.describes = unref(val);
}

function handleClick({ item }: Recordable) {
  router.push({ path: '/messageInfoDetail', query: { id: item.employeeMessageId } });
}

onMounted(async () => {
  dataTabs.nav = [
    { labelName: '最新咨询', value: undefined },
    ...(map((await questionTypeList({ typeState: true, pageSize: 0 })) || [], (v: Recordable) => ({
      labelName: v.typeName,
      value: v.employeeMessageTypeId,
    })) || []),
  ];

  listParams.employeeMessageTypeId = dataTabs.nav?.[0]?.value;
});
</script>

<style lang="less" module>
.message-info {
  :global {

    .van-field {
      background-color: #f6f7f8;
      border-radius: 50px;

      .van-button {
        border: unset !important;
        color: #5aa4ff;
        background-color: rgba(90, 164, 255, 0.1);
        border-radius: 40px;
        font-size: 24px;
      }
    }
  }
}
</style>
