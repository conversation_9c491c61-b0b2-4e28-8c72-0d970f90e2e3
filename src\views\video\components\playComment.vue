<template>
  <van-popup v-model:show="props.showBottomPopup" position="bottom" :style="{ height: '60%', background: '#ffffff' }"
    closeable close-icon="close" round @close="emit('update:showBottomPopup', false)">
    <div class="flex items-center justify-center pt-3 text-30px">
      评论列表 {{ currentVideo.commentsVolume || 0 }} 条
    </div>
    <div class="videoCommentList relative z-10">
      <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoad" ref="loadMoreRef">
        <div class="flex items-top w-full py-3 justify-between px-3 box-border" v-for="item in commentList"
          :key="item.autoId">
          <img loading="lazy" src="@/assets/public/female.png" class="avatar w-60px h-60px rounded-[50%] mt-1" />
          <div class="flex-1 mx-2 text-#999 text-24px">
            <div class="truncate text-28px text-#666 mb-1 flex items-center">
              {{ item.userName }}
              <span v-if="currentVideo.userId == item.userId"
                class="bg-#dceafb text-24px py-1px px-2 text-#5CA5FF rounded-3 ml-2">
                作者
              </span>
              <div class="flex-1 text-right">
                <span @click="likeClick(item)" :class="item.whetherLike ? 'text-#5CA5FF' : ''">
                  <van-icon name="good-job-o" size="15" />
                  {{ item.likeVolume }}
                </span>
              </div>
            </div>
            <div class="text-28px text-#333 mt-1 messagebutton leading-6">
              <span class="mr-1">{{ item.content }} </span>
              <div class="flex item-center">
                <span class="text-#666 text-24px mr-3">
                  {{ dayjs(item.createTime).format('MM-DD') }}
                </span>
                <van-button round size="small" @click="replyClick(item)">
                  回复
                </van-button>
                <van-button round size="small" v-if="item.whetherDelete" @click="deleteComment(item, commentList)">
                  <!--currentVideo.userId == item.userId || -->
                  删除
                </van-button>
                <!-- <van-button
                  round
                  size="small"
                  :class="item.whetherLike ? 'text-#5CA5FF' : ''"
                  @click="likeClick(item)"
                >
                  <van-icon name="good-job-o" />点赞
                </van-button> -->
              </div>
            </div>
            <div class="flex items-top w-full mt-1" v-for="ele in item.childCommentsList" :key="ele.autoId">
              <img loading="lazy" src="@/assets/public/female.png" class="avatar w-30px h-30px rounded-[50%]" />
              <div class="ml-2 text-28px text-#333 w-full">
                <div class="text-28px leading-4 text-#999 mb-1 flex items-center">
                  {{ ele.userName }}
                  <span v-if="currentVideo.userId == ele.userId"
                    class="bg-#dceafb py-1px px-2 text-24px text-#5CA5FF rounded-3 ml-2">
                    作者
                  </span>
                  <div class="flex-1 text-right">
                    <span @click="likeClick(ele)" :class="ele.whetherLike ? 'text-#5CA5FF' : ''">
                      <van-icon name="good-job-o" size="15" />
                      {{ ele.likeVolume }}
                    </span>
                  </div>
                </div>
                <div class="leading-[35px]">
                  回复
                  <span class="mx-1 text-#999">{{ ele.replyUserName }} </span>
                  {{ ele.content }}
                </div>
                <div class="flex items-center messagebutton mt-1">
                  <span class="text-24px text-#999 mr-2">{{
                    dayjs(ele.createTime).format('MM-DD')
                  }}</span>
                  <van-button round size="small" @click="replyClick(ele)">
                    回复
                  </van-button>
                  <van-button round size="small" v-if="ele.whetherDelete"
                    @click="deleteComment(ele, item.childCommentsList)">
                    <!--  currentVideo.userId == ele.userId || -->
                    删除
                  </van-button>
                  <!-- <van-button
                    round
                    size="small"
                    :class="ele.whetherLike ? 'text-#5CA5FF' : ''"
                    @click="likeClick(ele)"
                  >
                    <van-icon name="good-job-o" />点赞
                  </van-button> -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </refreshList>
      <!-- 气泡样式 -->
      <div class="absolute left-50% top-50% -translate-50% z-99">
        <waterIntergral v-model:show="showBubble" :score="scoreNum" bigSize="132px" midSize="52px" smallSize="40px"
          scorefontSize="40px"></waterIntergral>
      </div>
    </div>
    <div class="videoCommentInput absolute w-full bottom-0 px-28px pb-3 box-border">
      <van-field v-model="commentContent" type="textarea" ref="commentRef" :placeholder="placeholder" :rows="1"
        :autosize="{ maxHeight: 60, minHeight: 20 }" :maxlength="150" @blur="commentBlur()">
        <template #button>
          <van-button size="small" type="primary" :disabled="!commentContent.length" @click="onCommentClick()">
            评论
          </van-button>
        </template>
      </van-field>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
import { getVideoComments, publishComment, deleteFunComment } from '@/api/video';
import { likeOperate } from '@/api/public';
import { showToast, showConfirmDialog } from 'vant';
import { ref, watch } from 'vue';
import dayjs from 'dayjs';
import refreshList from "@/components/refreshList/index.vue"
const waterIntergral = defineAsyncComponent(() => import('@/components/Bubble/waterIntergral.vue'))
import utils from '@/utils/utils';
const props = defineProps({
  showBottomPopup: {
    type: Boolean,
  },
  currentVideo: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['update:showBottomPopup']);
const loadMoreRef = ref(null)
const commentContent = ref('');
const placeholder = ref('请输入评论内容...');
const commentRef = ref(null);
const shortCommentsId = ref<undefined | string>(undefined);
const commentList = ref<any>([]);
const loading = ref(false);
const finished = ref(false);
/**
 * 加载更多评论
 */
let pageNum = 1;
async function onLoad() {
  loading.value = true;
  let { code, data, hasNextPage, total } = await getVideoComments({
    shortVideoId: props.currentVideo.shortVideoId,
    pageSize: 10,
    pageNum,
  });
  finished.value = !hasNextPage;
  if (hasNextPage) {
    pageNum += 1;
  }
  loading.value = false;
  if (code == 200) {
    commentList.value = [...commentList.value, ...data];
    //重置刷新状态及 判断是否加载完成
    if (loadMoreRef.value) {
      loadMoreRef.value.onLoadSuc(commentList.value.length, total);
    }
  }
}
// 刷新
const onRefreshList = () => {
  pageNum = 1;
  onLoad();
};
/**
 * 发表评论
 */
async function onCommentClick(commentsId: string | undefined = undefined) {
  utils.isLoginDialog(async (isLogin: boolean) => {
    if (isLogin) {
      let { code, data, message } = await publishComment({
        shortVideoId: props.currentVideo.shortVideoId,
        shortVideoCommentsId: shortCommentsId.value,
        content: commentContent.value,
      });
      if (code === 200) {
        commentContent.value = '';
        commentBlur();
        showToast('评论成功！,请等待审核！');
      } else {
        showToast('评论失败！' + message);
      }
    }
  })

}

function replyClick(item) {
  commentRef.value?.focus();
  shortCommentsId.value = item.shortVideoCommentsId;
  placeholder.value = `回复@${item.userName} `;
}

function commentBlur() {
  if (commentContent.value) return;
  placeholder.value = '请输入评论内容...';
  shortCommentsId.value = undefined;
}

// 气泡提示框参数设置
const showBubble = ref(false);
const scoreNum = ref(0);
let isReq = false
/**
 * 点赞|取消点赞
 */
const likeClick = item => {
  if (isReq) return
  isReq = true;
  likeOperate({ sourceId: item.shortVideoCommentsId }).then(res => {
    isReq = false
    if (res.code == 200) {
      if (!res.data?.statefulFlowState) {
        showToast('已取消点赞');
        item.likeVolume -= 1;
      } else {
        item.likeVolume += 1;
        showToast({ type: 'success', message: '点赞成功', duration: 1000 })
      }
      item.whetherLike = res.data;
      if (res.data?.score) {
        scoreNum.value = res.data.score
        // 气泡展示
        setTimeout(() => { showBubble.value = true }, 1000)
      }
    }
  })
    .catch(() => {
      isReq = false
    })
};

/**
 * 删除我的评论
 */
function deleteComment(comment, list: any) {
  showConfirmDialog({
    title: '提示',
    message: '是否要删除评论内容？',
  })
    .then(async () => {
      let index = list.findIndex(
        item => item.shortVideoCommentsId === comment.shortVideoCommentsId
      );
      let { code, message } = await deleteFunComment({
        shortVideoCommentsId: comment.shortVideoCommentsId,
      });
      if (code == 200) {
        list.splice(index, 1);
        props.currentVideo.commentsVolume -= 1;
        showToast('删除成功');
      } else {
        showToast('删除失败！' + message);
      }
    })
    .catch(() => {
      showToast('已取消删除');
    });
}

watch(
  [() => props.currentVideo, () => props.showBottomPopup],
  ([val, val1]) => {
    if (val.shortVideoId && val1) {
      pageNum = 1;
      commentList.value = [];
      commentContent.value = '';
      commentBlur();
      onLoad();
    }
  },
  { immediate: true, deep: true }
);
</script>

<style lang="scss" scoped>
.messagebutton {
  :deep(.van-button) {
    --van-button-small-font-size: 20px;
    --van-button-small-height: 45px;
    --van-button-default-background: #e9eaeb;
    --van-button-border-width: 0px;
    --van-button-default-line-height: 45px;

    margin-right: 10px;
  }

}

:deep(.van-popup__close-icon) {
  font-size: 40px;
}

.videoCommentList {
  width: 100%;
  height: 75%;
  overflow: auto;
}

.videoCommentInput {
  --van-cell-vertical-padding: 5px;

  .van-cell {
    background: #f6f7f8;
    border-radius: 37px;
    font-size: 30px;
  }

  --van-button-small-height: 50px;
  --van-button-small-font-size: 26px;
  --van-button-border-width: 0px;
  --van-button-small-padding: 0 20px;
  --van-cell-horizontal-padding: 20px;
  --van-button-radius: 25px;

  .van-button {
    top: -5px;
  }
}
</style>
