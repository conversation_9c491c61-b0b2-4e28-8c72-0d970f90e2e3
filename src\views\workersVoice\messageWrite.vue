<template>
  <div class="w-full relative" :class="$style['message-write']">
    <div class="flex bg-[#F6F7F8] rounded-[10px] mx-[75px]">
      <div v-for="(item, index) in applyStateArr" class="w-1/2 py-[17px] flex justify-center items-center relative"
        :class="`${applyState === item.value ? 'text-[#5CA5FF]' : ''} ${index === 0 ? 'line-after' : ''}`"
        @click="handleStateChange(item)">
        {{ item.title }}
      </div>
    </div>
    <MessageList :api-list="getPersonalList" :if-nothing="ifNothing" :if-del="true" @delete-card="handleDelete"
      :params="listParams" ref="messageListRef" />
    <div class="bg-button-long fixed bottom-4 inset-x-0 flex justify-center items-center text-[#fff]"
      @click="handleWrite" v-if="companyId">
      <img loading="lazy" :src="bgButtonIcon" class="w-[27px] h-[27px] mr-2" />
      留言
    </div>

    <van-popup v-model:show="showCenter" round>
      <div class="text-[#333333] text-[36px] text-center">是否删除留言？</div>
      <p class="text-[#666666] text-[26px] text-center">删除后，您可以继续浏览或 添加新的留言。</p>
      <div class="absolute top-[300px] left-1/2">
        <div
          class="w-[400px] submitbtn h-[78px] rounded-[39px] flex justify-center items-center text-[#fff] absolute left-1/2 -translate-x-1/2"
          @click="handleConfirmDel">
          确认
        </div>
        <div
          class="w-[400px] bg-transparent h-[78px] rounded-[39px] mt-81px flex justify-center items-center text-[#333333] absolute left-1/2 -translate-x-1/2"
          @click="handleCancel">
          取消
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script lang="ts" setup>
import bgButtonIcon from '@/assets/workers-voice/bg-button-icon.png';
import { showSuccessToast, showFailToast } from 'vant';
import MessageList from './list.vue';
import { deleteLine, getPersonalList } from '@/api/workerVoice';
import { useUserStore } from '@/store/modules/user';

const router = useRouter();

const companyId = computed(() => !!useUserStore().getUserInfo.companyId);

const applyStateArr = [
  { title: '已回复', value: true },
  { title: '未回复', value: false },
];

const messageListRef = ref<{ onRefreshList: Function }>();

const showCenter = ref<boolean>(false);

const applyState = ref<boolean>(true);

// 删除中间对象
const deleteRecord = ref<Recordable>();

const ifNothing = computed(() => !unref(applyState));

const listParams = reactive<{ replyState: boolean }>({
  replyState: true,
});

// 删除
function handleDelete({ item }: Recordable) {
  showCenter.value = true;
  deleteRecord.value = item;
}

function handleCancel() {
  showCenter.value = false;
}

function handleStateChange(item: Recordable) {
  applyState.value = item.value;
  listParams.replyState = item.value;
}

function handleConfirmDel() {
  deleteLine(`?employeeMessageId=${unref(deleteRecord)?.employeeMessageId}`).then(
    ({ code, message }) => {
      code === 200 ? showSuccessToast('删除成功！') : showFailToast(`删除失败！${message || ''}`);

      showCenter.value = false;

      unref(messageListRef)?.onRefreshList?.();
    }
  );
}

// 留言
function handleWrite() {
  router.push({ path: '/messageWriteOpt' });
}
</script>

<style lang="less" module>
.message-write {
  :global {
    .line-after {
      &:after {
        content: '';
        width: 1px;
        height: 60%;
        position: absolute;
        background-color: #e5e5e5;
        right: 0;
      }
    }

    .van-popup {
      background-color: transparent;
      background-image: url('@/assets/workers-voice/bg-delete.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: center;
      width: 500px;
      height: 520px;
      position: relative;
      padding: 60px !important;
    }

    .submitbtn {
      background: linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%) !important;
    }
  }


}
</style>
