<template>
  <div class="h-[100vh]" :class="$style['scan-qrcode']">
    <div class="mx-[16px] mt-5 bg-[#fff] h-[70vh] relative rounded-[16px]">
      <div class="w-full text-center pt-10 font-bold">{{ signStatus }}</div>
      <div class="flex justify-center items-center mt-10">
        <img loading="lazy" :src="signStatus === '签到失败'? scanQrCodeErrorBg:scanQrCodeBg" class="w-[340px] h-[301px]" />
      </div>

      <div class="w-full text-center mt-20" v-if="showScore?.operateFlag">恭喜您获得
        <span class="text-[40px] text-[#f2a813] font-bold">{{ showScore?.score }}</span>
        积分
      </div>

      <div class="flex justify-center items-center absolute bottom-2 w-full mb-20">
        <Button name="返回" class="!w-[470px] !h-[70px]" @click="handleClick" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import scanQrCodeBg from '@/assets/position/scan-qr-code-success.png';
import scanQrCodeErrorBg from '@/assets/position/scan-qr-code-error.png';
import Button from '@/components/Button/button.vue';
import { addRecord } from '@/api/position';
import { useRoute } from "vue-router";
const route = useRoute();
import { useUserStore } from '@/store/modules/user';
import utils from '@/utils/utils';
const userStore = useUserStore();
const signStatus = ref<any>('签到失败');
const showScore = ref(null)
//扫码签到
function getSign() {
  addRecord({
    positionInfoId: route.query.data,
    userId: userStore.getUserInfo?.userId,
    userName: userStore.getUserInfo?.nickname,
    companyId: userStore.getUserInfo?.companyId,
    companyName: userStore.getUserInfo?.companyName,
    phone: userStore.getUserInfo?.phone,
    recordType: "scanQrCode"
  }).then(res => {
    if (res.code == 200) {
      signStatus.value = '签到成功';
      if (res.data.operateFlag) {
        showScore.value = res.data
      }
    } else {
      signStatus.value = '签到失败';
    }
  })
}
onMounted(() => {
  if (route.query.data) {
    getSign()
  }
})
function handleClick() {
  utils.citySercive(route.fullPath, '二维码跳转', 'close', 'qrpage');
}
</script>

<style lang="less" module>
.scan-qrcode {
  :global {
    * {
      box-sizing: border-box;
    }

    background-image: url('@/assets/position/scan-qr-code-bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
}
</style>
