import { createApp } from 'vue';
import './style.css';
import './styles/index.less';
import App from './App.vue';
import Vant from 'vant';
import 'vant/lib/index.css';
import router from './router'; // 注册路由
import 'virtual:uno.css';
import '@/utils/timeTool.js';
import { setupStore } from '@/store';
import { useUserStore } from '@/store/modules/user';
import 'core-js/stable';
import 'regenerator-runtime/runtime'; // 如果你的代码使用了生成器（Generator），你也需要这个 Polyfill
import registerDirectives from './directives/index.js';//注册指令


import WebTracing from '@web-tracing/vue3'

const app = createApp(App);

app.use(Vant);
setupStore(app);

app.use(router);
app.mount('#app');
app.config.globalProperties.$store = useUserStore();

// 发布时记得判断
if(process.env.NODE_ENV === 'production'){
 app.use(WebTracing, {
    dsn: import.meta.env.VITE_BASE_API+'/h5/pageVisitsRecord/pv-duration',
    appName: 'ncapp',
    appCode:'ncapp',
    debug: false,
    recordScreen:false,
    pv: true,
    performance: {
        code:false,
        firstResource:false,
        server:false
    },
    error: true,
    event: {
        core:true
    },
    cacheMaxLength: 5,//设置上报数据最大缓存数
    cacheWatingTime:3000,//设置上报数据最大等待时间(ms)
    // 查询埋点信息、清除埋点信息、获取埋点基础信息 不需要进行捕获
    ignoreRequest:[],
    beforeSendData: (data) => {
        // 可以在这里对上报数据进行修改，比如添加额外的信息
        data.token = useUserStore().getSaToken;
        return data;
    },
    sendTypeByXmlBody:true, // 设置为true，上报数据将以XMLHttpRequest的body形式发送
  })   
}


const preloadComponents = () => {
    const routes = router.getRoutes()
    routes.forEach(route => {
        if(route.name === 'home' || route.name === 'welcome'){
           const componentFn =  route.components?.default as () => void 
           if(typeof componentFn === 'function'){
                componentFn() // 预加载首页
           }

        } 
    })
}

if('requestIdleCallback' in window){
    requestIdleCallback(preloadComponents)
}else{
    setTimeout(preloadComponents, 1)
}
// 注册指令
registerDirectives(app)


