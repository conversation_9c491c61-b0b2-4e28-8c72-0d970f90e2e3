<template>
    <div class="detail bg-#fff w-full">
        <div class="detail-info  p-30px border-box relative">
            <div class="rounded-[20px] px-[20px] py-[28px] box-border flex items-center mb-[20px]">
                <img loading="lazy" :src="judgeStaticUrl(Data.groupList.logo)" alt=""
                    class="w-120px h-120px rounded-[50%] mr-[40px] object-cover">
                <div>
                    <div class="text-[#333] text-[32px]">{{ Data.groupList.groupName }}</div>
                    <van-rate v-model="Data.groupList.score" color="#ffd21e" void-icon="star" void-color="#BFBFBF"
                        class="pt-20px my-rate" readonly />
                    <div class="flex items-center my-[20px]">
                        <div class="flex items-center text-[#999] text-[26px] mr-[34px]">
                            <img loading="lazy" src="@/assets/interest/icon_num.png" class="w-28px h-28px mr-10px">
                            {{ Data.groupList.memberCount }} 人
                        </div>
                        <!-- <div class="flex items-center text-[#999] text-[24px] ">
                            <img loading="lazy" src="@/assets/interest/icon_act.png" class="w-28px h-28px mr-10px">
                           {{ Data.groupList.activityNum }} 次
                        </div> -->

                    </div>
                    <div class="flex items-center">
                        <div class="tag rounded-[8px] px-[14px] py-[8px] box-border mr-[18px] text-[#5AA4FF] text-[24px]"
                            v-for="e in Data.groupList.labels" :key="e">{{ e.labelName }}</div>
                    </div>
                </div>
            </div>
            <div
                class="relative top-0 bg-#fff w-90% left-1/2 -translate-x-1/2 p-30px rounded-20px mt-12px text-28px leading-1.3em">
                {{ Data.groupList.groupDesc }}
            </div>
        </div>
        <div class="pb-120px">
            <div class="flex w-full items-center tabs">
                <van-tabs v-model:active="Data.tab.active"
                    :class="Data.groupList.identityType == '20' ? 'w-70%' : 'w-100%'" swipeable
                    title-active-color="#5AA4FF" @change="chooseTab">
                    <van-tab v-for="(item, index) in Data.tab.nav" :title="item" class="text-34px" :key="index">
                    </van-tab>
                </van-tabs>
                <div class="w-30% flex justify-end mr-20px"
                    v-if="Data.tab.active == 2 && Data.groupList.identityType == '20'">
                    <div class="w-98px rounded-20px text-26px text-#5AA4FF manage py-4px px-12px text-center"
                        v-if="Data.groupList.identityType == '20'" @click="toManage()">管理</div>
                </div>
            </div>
            <div class="p-30px">
                <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
                    <interestList :data="Data.interestList" v-if="Data.tab.active == 0" />
                    <topicList :data="Data.topicList" :source="'dynamic'" v-if="Data.tab.active == 1" />
                    <div v-if="Data.tab.active == 2" v-for="(item, index) of Data.menberList" :key="index"
                        class="py-22px hr flex items-center">
                        <img loading="lazy" :src="!item.isChoose ? noChoose : yesChoose" alt=""
                            class="w-28px h-28px mr-28px" v-if="Data.showTool && item.identityType != '20'"
                            @click="choose(item)">
                        <div class="flex items-center justify-between w-90%">
                            <div class="flex items-center">
                                <img loading="lazy" src="@/assets/public/head_default.png" alt=""
                                    class="w-76px mr-25px">
                                <div class="text-30px">{{ item.nickName }}</div>
                                <div class="bg-#DEEDFF w-100px h-30px flex  items-center justify-center text-20px text-#5AA4FF rounded-15px ml-30px"
                                    v-if="item.identityType == '20'"><img loading="lazy"
                                        src="@/assets/interest/icon_admin.png" alt="" class="w-19px mr-5px">管理员</div>
                            </div>
                            <div v-if="item.state == 'forbidden'"
                                class="bg-#5AA4FF w-100px h-30px flex  items-center justify-center text-26px text-#FFFFFF rounded-15px py-3px"
                                @click="UpdateState('normal', item.userId)">解除</div>
                        </div>

                    </div>
                </refreshList>
            </div>

        </div>
        <div class="flex items-center justify-between w-[100%] px-[90px] box-border fixed bottom-[84px] "
            v-if="Data.showTool && Data.tab.active == 2">
            <div class="w-[260px] h-[76px] rounded-[39px] justify-center flex items-center text-[30px] text-[#5AA4FF] border-[1px] border-[#5AA4FF] border-solid mr-[30px] bg-#fff"
                @click="adminOprea('delete')">删除成员</div>
            <div class="w-[260px] h-[78px] rounded-[39px] justify-center flex items-center text-[30px] text-[#fff]"
                style="background:linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%)" @click="adminOprea('shut')">禁言</div>
        </div>
        <div class="btn w-60/100  m-auto text-34px text-#fff py-23px text-center fixed bottom-40px left-1/2 -translate-x-1/2"
            @click="join()" v-if="Data.tab.active != 2">{{ Data.groupList.joinState ? '已加入' : '加入小组' }}</div>
        <Popup :show="Data.showPop" :titleName="'加入原因'" :placeHolder="'请输入加入原因'" @submit-content="joinContent"
            @close-popup="closePopup" />
    </div>
</template>
<script lang="ts" setup>
import interestList from "@/components/List/interestList.vue";
import topicList from "@/components/List/topicList.vue";
import Popup from '@/components/Popup/popup.vue';
import { showToast } from 'vant';
import noChoose from "@/assets/interest/icon_no.png";
import yesChoose from "@/assets/interest/icon_yes.png";
import { detailByGroupId, h5UpdateState } from "@/api/interestGroup";
import { useRoute } from "vue-router";
import { useUserStore } from '@/store/modules/user';
const useStore = useUserStore();
const route = useRoute();
import { activityInfoList } from "@/api/activity";
import refreshList from '@/components/refreshList/index.vue';
import { joinGroup, commentsList, h5GroupUserList, h5Remove } from "@/api/interestGroup";
import { judgeStaticUrl } from "@/utils/utils";
const Data = ref({
    groupList: {},
    tab: { active: 0, nav: ['活动', '动态', '成员'] },
    interestList: [],//兴趣爱好
    topicList: [],
    showPop: false,
    menberList: [],
    showTool: false,
    chooseList: [],
    joinObj: {},
    pageNum: 1
})
//切换
function chooseTab(index) {
    Data.value.pageNum = 1;
    Data.value.tab.active = index;
    if (Data.value.tab.active == 0) {
        getActList();
    } else if (Data.value.tab.active == 1) {
        getcommentsList();
    } else {
        getUserList()
    }
}
//加入
function join() {
    Data.value.joinObj = Data.value.groupList
    if (!Data.value.groupList?.joinState) {
        Data.value.showPop = true;
    }
}

//确认提交
function joinContent(val) {
    if (!val) {
        showToast({
            message: "请输入加入原因",
            icon: 'none',
        })
        return
    }
    joinGroup({
        groupName: Data.value.joinObj?.groupName,
        groupId: Data.value.joinObj?.groupId,
        reason: val
    }).then(res => {
        if (res.data) {
            showToast("提交成功，等待管理员审核~")
        }
        Data.value.showPop = false;
    })
}


//关闭
function closePopup() {
    Data.value.showPop = false;
}
//管理
function toManage() {
    Data.value.showTool = !Data.value.showTool;
}
//管理员操作
function adminOprea(type) {
    Data.value.chooseList = []
    Data.value.menberList.map(el => {
        if (el.isChoose) {
            Data.value.chooseList.push(el.userId)
        }
    })
    if (type == 'delete') {
        UpdateRemove()
    } if (type == 'shut') {
        UpdateState('forbidden')
    }
}
//删除
function UpdateRemove() {
    h5Remove({
        userIds: Data.value.chooseList,
        groupId: route.query.groupId,
    }).then(res => {
        if (res.code == 200) {
            showToast('删除成功');
            getUserList();
        }
    })
}
//禁言--解除禁言
function UpdateState(type, userId) {
    h5UpdateState({
        userIds: type == 'normal' ? [userId] : Data.value.chooseList,
        groupId: route.query.groupId,
        state: type,//"normal"//forbidden:禁言，normal：正常
    }).then(res => {
        if (res.code == 200) {
            showToast(type == 'normal' ? '解除禁言' : '禁言成功');
            getUserList();
        }
    })
}
//选择成员列表
function choose(item) {
    if (item.identityType == '20') {
        return
    }
    item.isChoose = !item.isChoose;

}
//详情
function getDetail() {
    detailByGroupId({
        groupId: route.query.groupId
    }).then(res => {
        if (res.code == 200) {
            Data.value.groupList = res.data;
        }
    })
}
//获取活动列表
const loadMoreRef = ref(null)
function getActList() {
    activityInfoList({
        activityCategory: 'interestGroup',
        groupId: route.query.groupId,
        pageSize: 10,
        pageNum: Data.value.pageNum
    }).then(res => {
        if (res.code == 200) {
            if (Data.value.pageNum === 1) Data.value.interestList = [];
            Data.value.interestList = Data.value.interestList.concat(res.data);
            //重置刷新状态及 判断是否加载完成
            if (loadMoreRef.value) {
                loadMoreRef.value.onLoadSuc(Data.value.interestList.length, res.total);
            }
        }
    })
}
//动态列表
function getcommentsList() {
    commentsList({
        dataSources: "group", //评论来源 : group:小组  activity：活动
        commentType: 'dynamic',//评论类型 （groupComment：小组评价，dynamic：小组动态，activityComment：活动评论，activityDynamic：活动评价 ）
        pageSize: 10,
        pageNum: Data.value.pageNum,
        groupId: route.query.groupId,
        sortType: 'desc',
        orderBy: 'create_time'
    }).then(res => {
        if (res.code == 200) {
            if (Data.value.pageNum === 1) Data.value.topicList = [];
            Data.value.topicList = Data.value.topicList.concat(res.data);
            //重置刷新状态及 判断是否加载完成
            if (loadMoreRef.value) {
                loadMoreRef.value.onLoadSuc(Data.value.topicList.length, res.total);
            }
        }
    })
}
//成员列表
function getUserList() {
    h5GroupUserList({
        groupId: route.query.groupId,
        pageSize: 10,
        pageNum: Data.value.pageNum,
        orderBy: 'identity_type,a.auto_id',
        sortType: 'desc,asc'
    }).then(res => {
        if (res.code == 200) {
            res.data.map(el => {
                el.isChoose = false;
            })
            if (Data.value.pageNum === 1) Data.value.menberList = [];
            Data.value.menberList = Data.value.menberList.concat(res.data);
            //重置刷新状态及 判断是否加载完成
            if (loadMoreRef.value) {
                loadMoreRef.value.onLoadSuc(Data.value.menberList.length, res.total);
            }
        }
    })
}
// 刷新
const onRefreshList = () => {
    Data.value.pageNum = 1;
    if (Data.value.tab.active == 0) {
        getActList();
    } else if (Data.value.tab.active == 1) {
        getcommentsList();
    } else {
        getUserList()
    }
};
// 加载更多
const onLoadMore = () => {
    Data.value.pageNum++;
    if (Data.value.tab.active == 0) {
        getActList();
    } else if (Data.value.tab.active == 1) {
        getcommentsList();
    } else {
        getUserList()
    }
};
onMounted(() => {
    getDetail();
    getActList();
})
</script>
<style lang="scss" scoped>
.detail {
    .detail-info {
        background: url('@/assets/interest/bg_detail.png'), #f5f5f5;
        background-repeat: no-repeat;
        background-size: cover;

        .tag {
            background: rgba(90, 164, 255, .1);
        }
    }

    .btn {
        background: url("@/assets/public/butto.png") no-repeat;
        background-size: 100% 100%;
    }

    :deep(.van-tab) {
        font-size: 32px;
        line-height: 1;
    }

    :deep(.van-tabs--line .van-tabs__wrap) {
        height: 78px;
    }

    .hr {
        border-bottom: 1px solid #EBEBEB;
    }

    .manage {
        border: 1px solid #5AA4FF;
    }

    .my-rate {
        :deep(.van-rate__icon) {
            font-size: 24px;
        }
    }

}
</style>