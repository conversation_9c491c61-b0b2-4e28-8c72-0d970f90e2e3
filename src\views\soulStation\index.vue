<template>
  <div class="w-full relative max-h-100vh" :class="$style['soul-station']">
    <div class="w-full h-[360px] banner-bg"></div>
    <div class="rounded-[16px] w-full p-[30px] rounded-[16px] relative top-[-49px] bg-[#fff]">
      <div class="pt-[8px] flex">
        <img loading="lazy" :src="counseling" class="w-[335px] h-[200px]"
          @click="handleRouter('/psychologicalCounseling', 'expert')" />
        <div class="pl-[19px] py-[5px] flex flex-col justify-between items-center">
          <img loading="lazy" :src="assessment" class="w-[335px] h-[88px]" @click="handleNew()" />
          <img loading="lazy" :src="relax" class="w-[335px] h-[88px]" @click="handleRouter('/relaxUnwind', '')" />
        </div>
      </div>
      <div class="flex justify-between items-center py-[40px]">
        <div class="text-[#333333] font-medium text-[32px]">心理小课堂</div>
        <div
          class="border border-1px border-solid border-#5AA4FF w-77px h-40px rounded-20px flex items-center justify-center input-container"
          @click="handleRouter('/searchList', '')">
          <img loading="lazy" src="@/assets/public/icon_s.png" alt="" class="w-20px h-20px mr-5px" />
          <span class="text-20px text-#999">搜</span>
        </div>
      </div>

      <!-- list -->
      <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
        <newsCell v-for="(item, index) in Data.list" :key="index" :content="item" :showborderBottom="false"></newsCell>
      </refreshList>
    </div>
    <!-- <PhoneIcon /> -->
  </div>
</template>

<script lang="ts" setup>
import relax from '@/assets/soulStation/relax.png';
import assessment from '@/assets/soulStation/psychological-assessment.png';
import counseling from '@/assets/soulStation/psychological-counseling.png';
import refreshList from '@/components/refreshList/index.vue';
import PhoneIcon from './phone.vue';
import newsCell from "@/components/Cell/newsCell.vue";
import { getPsychologicalToken, getPsychologicalUrl } from '@/api/soulStation';
const router = useRouter();
import { getCategoryInfo, getNewsList } from '@/api/news/index';
import { checkUserIdentity } from '@/api/video';
import utils from '@/utils/utils';
import { showFailToast } from 'vant';
const loadMoreRef = ref();

const Data = ref({
  pageNum: 1,
  pageSize: 10,
  list: [],
  tabData: [],
  url: ''
});
//心理测评
async function handleNew() {
  const res = await checkUserIdentity({})
  if (!res.data) {
    return showFailToast("仅限南充市认证会员咨询！")
  }
  getPsychologicalToken({}).then(res => {
    if (res.code == 200) {
      if (utils.isApp()) {
        utils.citySercive(Data.value.url + '?token=' + res.data, '心理测评', 'open', 'ce')
      } else {
        window.location.href = Data.value.url + '?token=' + res.data
      }
    }
  })
}
function handleRouter(path: string, type) {
  if (type == 'expert') {
    checkUserIdentity({}).then(async res => {
      if (res.data) {
        router.push({ path });
      } else {
        showFailToast("仅限南充市认证会员咨询！")
      }
    })
    return
  }

  router.push({ path });
}

//获取栏目
async function getColumn() {
  getCategoryInfo({
    categoryCode: 'xin_li_xiao_ke_tang',
    platformType: 30,
  }).then(res => {
    Data.value.tabData = res.data;
    if (Data.value.tabData) {
      getLists();
    }

  });
}
//获取url链接
function getUrl() {
  getPsychologicalUrl({}).then(res => {
    Data.value.url = res.data
  })
}
// // 刷新
const onRefreshList = () => {
  Data.value.pageNum = 1
  Data.value.list = [];
  getLists()
}
// 加载更多
const onLoadMore = () => {
  Data.value.pageNum++
  getLists()
}
// //新闻列表
async function getLists() {
  let res = await getNewsList({
    categoryCode: Data.value.tabData?.categoryCode,
    platformType: 30,
    pageNum: Data.value.pageNum,
    pageSize: 10,
  });
  if (Data.value.pageNum === 1) Data.value.list = [];
  Data.value.list = Data.value.list.concat(res.data);
  //重置刷新状态及 判断是否加载完成
  if (loadMoreRef.value) {
    loadMoreRef.value.onLoadSuc(Data.value.list.length, res.total);
  }
}
onMounted(() => {
  getColumn();
  getUrl();
})
</script>

<style module lang="less">
.soul-station {
  :global {
    .banner-bg {
      background-image: url('@/assets/soulStation/banner-bg.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }

    .van-cell {
      .van-cell__value {
        display: flex;
        align-items: center;
        text-align: left;
      }

      &::after {
        left: 0;
        right: 0;
        border-color: #eeeeee !important;
      }
    }
  }
}
</style>
