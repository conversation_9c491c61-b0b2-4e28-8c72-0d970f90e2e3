<template>
  <div class="lotteryActivity w-full h-100vh relative" style="background-size: 100% 100%;"
    :style="{ backgroundImage: `url(${activityDetail.appDetailsCover ? useStore.getPrefix + activityDetail.appDetailsCover : backgroundImg})` }">
    <!--    <template v-if="!useStore?.activityDetail?.appDetailsCover">-->
    <!--      <div class="absolute top-30px left-30px flex items-center text-35px text-[#fff]">-->
    <!--        <img loading="lazy" src="@/assets/activity/logo.png" alt="" class="w-60px mr-10px">-->
    <!--        {{ useStore?.activityDetail?.companyName || '南充市总工会' }}-->
    <!--      </div>-->
    <!--    </template>-->
    <div class="absolute top-950px w-full flex flex-col items-center">
      <div class="w-440px h-106px btn flex justify-center items-center text-[#fff] text-42px pb-12px mb-40px"
        @click="toLottery">
        立即抽奖
      </div>
      <div class="flex btns w-65/100 justify-around">
        <div class="" @click="toDetail"><img loading="lazy" src="@/assets/activity/actinfo.png" alt=""
            class="w-26px mr-10px">活动详情
        </div>
        <div @click="toLotteryRecord"><img loading="lazy" src="@/assets/activity/actjl.png" alt=""
            class="w-34px mr-8px">中奖记录</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { showDialog } from "vant";
import { getAwardState } from '@/api/activity';
import backgroundImg from '@/assets/activity/background.jpg'
import { ref, onMounted } from 'vue'
import { activityValidator, activityDetailValidator, defaultValidator } from '@/hooks/useValidator'
import { useRouter } from "vue-router";
import { useUserStore } from "@/store/modules/user";
import utils from "@/utils/utils";
const useStore = useUserStore();
const router = useRouter();
const defaultAwardNum = ref(null)
const awardNum = ref(null)

const toLottery = () => {
  if (!activityValidator()) return
  if (
    defaultAwardNum.value !== null &&
    defaultAwardNum.value <= awardNum.value
  ) {
    showDialog({
      title: "温馨提示",
      message: `每日可抽奖${defaultAwardNum.value}次,您今日已无抽奖机会,明日再来吧~`,
      confirmButtonText: "我知道了",
      lockScroll: false,
    });
    return;
  }
  router.push('/activityHome/lottery')
}

const toDetail = () => {
  if (!activityDetailValidator()) return
  router.push('/activityHome/activityDetail')
}
const toLotteryRecord = () => {
  if (!defaultValidator()) return
  router.push('/activityHome/lotteryRecord')
}
const getAwardStateCount = async () => {
  const { code, data } = await getAwardState({
    userId: useStore.userInfo.userId,
    activityId: useStore.activityDetail.activityId,
  });
  if (code === 200) {
    awardNum.value = data || 0;
    const { numberPerDay } = useStore.activityDetail?.luckDrawInfo || { numberPerDay: 0 };
    defaultAwardNum.value = numberPerDay;
  }
}
const activityDetail = computed(() => useStore.activityDetail || {});
watch(activityDetail, () => {
  if (activityDetail.value.activityId) {
    getAwardStateCount();
  }
})
onMounted(() => {
  if (activityDetail.value.activityId) {
    getAwardStateCount();
  }
})
</script>

<style lang="scss" scoped>
.lotteryActivity {
  background-size: 100% 100%;

  .btn {
    background-image: url('@/assets/activity/draw_btn.png');
    background-size: 100% 100%;
  }

  .btns {
    >div {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 188px;
      height: 56px;
      font-size: 28px;
      color: #FFFFFF;
      background: #D1170B;
      border-radius: 14px;
      border: 1px solid #FFFFFF;
    }
  }
}
</style>
