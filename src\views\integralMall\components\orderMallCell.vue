<!-- 积分商城订单cell -->
<template>
    <div class="order-cell bg-[#fff] p-28px mb-20px" :class="{ 'showBottom': showBottom }">
        <div class="order-header flex justify-between mb-52px" v-if="showHeader">
            <div class="title text-#666 text-24px">兑换单号：{{ content?.recordId }}</div>
            <div class="state text-26px">
                <span :style="`color:${formatterStatus.color}`">{{ formatterStatus.text }}</span>
            </div>
        </div>
        <div class="order-content flex">
            <div class="w-148px h-148px rounded-20px rounded-20px bg-center bg-cover"
                :style="{ backgroundImage: `url(${bgCover})` }">
            </div>
            <div class="flex flex-col ml-25px flex-1">
                <div class="text-28px text-#333 ">{{ content?.productName }}</div>
                <div class="text-#999 text-24px flex-1 flex items-center">兑换来源：{{content?.exchangeSource === '1'?'积分商城':content?.exchangeSource === '2'?'一岁一礼':'-'}}</div>
                <div class="text-#999 text-24px flex justify-between">
                    <div>{{ content?.productSubName }}</div>
                    <div>x1</div>
                </div>
            </div>
        </div>
        <div class="order-footer text-26px mt-20px flex justify-between">
            <slot name="exchangeTime"></slot>
            <div class="text-right text-#666 flex-1">
                消耗积分：<span class="text-#FF4344 font-bold">{{ content?.exchangeSource === '2'? content?.birthdayConsumeIntegral :content?.consumeIntegral }}</span>
            </div>
            <!-- 未对接现金支付 -->
            <!-- <div class="text-right mt-13px text-#666">
                实付金额：<span class="text-#FF4344 font-bold">￥{{}}</span>
            </div> -->
        </div>
    </div>
</template>
<script setup lang="ts">
import utils from '@/utils/utils';
const props = defineProps({
    content: {
        type: Object,
        default: () => { }
    },
    showHeader: {
        type: Boolean,
        default: true
    },
    showBottom: {
        type: Boolean,
        default: true
    }
})
const bgCover = computed(() => {
    return props.content?.productCoverImg ? utils.judgeStaticUrl(props.content?.productCoverImg) : ''
})

const formatterStatus = computed(() => {
    let status = {
        text: '',
        color: '#FF4344'
    }
    switch (props.content?.integralPayment) {
        case '1':
            if (props.content?.deliveryStatus === 'deliver') {
                status.text = '未发货'
                status.color = '#FFA025'
            }
            else if (props.content?.deliveryStatus === 'receive') {
                status.text = '待收货'
                status.color = '#FF4344'
            }
            else if (props.content?.deliveryStatus === 'over') {
                status.text = '已完成'
                status.color = '#46B15A'
            }
            break
        case '2':
            if (props.content?.state === 'wait') {
                status.text = '未核销'
                status.color = '#FFA025'
            }
            else if (props.content?.state === 'used') {
                status.text = '已核销'
                status.color = '#46B15A'
            }
            break
    }
    return status
})
</script>
<style lang="scss" scoped>
.showBottom {
    border-bottom: 1px solid #e4e4e4;
}
</style>