import {openHttp} from  '@/utils/http/axios';
// 普惠商品列表
export const inclusiveGoodsList = (params:any) => {
    return openHttp.get({
      url:'/customProductInfo/inclusiveProductFindVoList',
      params
   })
}
// 销量筛选
export const inclusiveGoodsSales = (params:any) => {
    return openHttp.get({
      url:'/customProductInfo/inclusiveProductListSalesNumSort',
      params
   })
}

// 普惠商户详情
export const inclusiveMerchantsDetails = (companyId:any) => {
    return openHttp.get({
      url:'/openCompanyInfo/getVoByCompanyId',
      params:{companyId}
   })
}

// 商品评分阶段统计
export const inclusiveGoodsStatistics = (productId:any) => {
    return openHttp.get({
      url:'/inclusiveProductComment/getInclusiveProductCommentStage',
      params:{productId}
   })
}
// 商品评分列表
export const inclusiveGoodsCommentList = (params:any) => {
    return openHttp.get({
      url:'/inclusiveProductComment/findVoList',
      params
   })
}
// 用户评分
export const inclusiveGoodsComment = (params:any) => {
    return openHttp.post({
      url:'/inclusiveProductComment/saveOrUpdateByDTO',
      params
   })
}

// 购物车
export const shoppingCartList = () => {
    return openHttp.post({
      url:'/openBusiness/getCurrentShopCartInfo',
   })
}
// 购物车操作
export const shoppingCartHandle = (params:any) => {
    return openHttp.post({
      url:'/openBusiness/inclusiveShopCartOperate',
      params
   })
}

// 购物车/立即支付创建订单
export const shoppingCreateOrder = (params:any) => {
    return openHttp.post({
      url:'/customOrder/createInclusiveOrder',
      params
   })
}


// =====订单管理=====
// 我的订单列表-查询（搜索专用）
export const orderMySearchList = (params:any) => {
    return openHttp.get({
      url:'/customOrder/customSearchTransProductSnapshot',
      params
   })
}
// 我的订单列表-查询（状态筛选专用）
export const orderMyStateList = (params:any) => {
   return openHttp.get({
     url:'/customOrder/findOrderVoList',
     params
  })
}
// 订单详情
export const orderDetails = (orderId:any) => {
    return openHttp.get({
      url:'/customOrder/getOrderSnapshotByOrderId',
      params:{orderId}
   })
}

// 取消订单
export const orderCancel = (orderId:any) => {
    return openHttp.post({
      url:'/customOrder/cancelOrderByOrderId?orderId='+orderId,
   })
}
// 确认收货
export const orderConfirm = (orderId:any) => {
    return openHttp.post({
      url:'/customOrder/orderConfirm?orderId='+orderId,
   })
}
// 售后
export const orderAfterSale = (params:any) => {
    return openHttp.post({
      url:'/customSaleService/apply',
      params
   })
}

// 售后列表
export const afterSaleList = (params:any) => {
    return openHttp.get({
      url:'/customSaleService/findCustomSaleServiceList',
      params
   })
}
// 取消售后申请
export const afterSaleCancel = (params:any) => {
    return openHttp.post({
      url:'/customSaleService/cancelSaleServiceApply',
      params
   })
}

// 上传物流信息
export const uploadLogistics = (params:any) => {
    return openHttp.post({
      url:'/customSaleService/updateSaleServiceTransportInfo',
      params
   })
}
// 退款详情
export const refundDetails = (sgi:any) => {
    return openHttp.get({
      url:'/customSaleService/getSaleServiceDetail?sgi='+sgi,
   })
}

// =====end=====