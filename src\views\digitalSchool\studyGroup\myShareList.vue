<template>
    <div class="myShareList">
        <div class="tab-box mb-21px" v-if="tab && tab.length">
            <van-tabs v-model:active="tabActive" sticky color="#5AA4FF" title-active-color="#5AA4FF"
                title-inactive-color="#333333" line-width="30" @click-tab="onClickTab">
                <van-tab :title="item.label" v-for="(item, index) in tab" :key="index"
                    title-class="tab-title"></van-tab>
            </van-tabs>
        </div>
        <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
            <shareList :data="list" @detail="toPage"  :type="tab[tabActive]&&tab[tabActive].value=='refuse' ? 'examine': ''"></shareList>
        </refreshList>
    </div>
</template>

<script lang="ts" setup>
import { useDictionary } from '@/store/modules/dictionary';
import { shareFindVoListH5Person } from "@/api/digitalSchools/group"
import refreshList from "@/components/refreshList/index.vue"
import shareList from '../components/shareList.vue';
const dictionary = useDictionary()
const router = useRouter()
const tab = ref([])
const tabActive = ref(0)
const pageNum = ref(1)
const list = ref([])
const loadMoreRef = ref(null)
onMounted(() => {
    tab.value = dictionary.getDictionaryOpt?.['groupStudyStatus'] && dictionary.getDictionaryOpt?.['groupStudyStatus'];
    tab.value.forEach(item => {
        if (item.value == 'wait') {
            item.label = '审核中'
        } else if (item.value == 'refuse') {
            item.label = '未通过'

        }
        else if (item.value == 'pass') {
            item.label = '已发布'

        }
    })
    getShareList()
})


//详情
function toPage(item) {
    const { userName = '', createTime, describes = '',file='',auditStatus='',auditRemarks='' } = item
    let info = JSON.stringify({
        userName, createTime, describes,
        file,
        auditStatus,
        auditRemarks
    })
    router.push({
        path: '/digitalSchool/studyGroup/shareDetail',
        query:{
            info
        } 
    })
}
// 刷新
const onRefreshList = () => {
    pageNum.value = 1;
    getShareList();
};
// 加载更多
const onLoadMore = () => {
    pageNum.value++;
    getShareList();
};

function onClickTab(item: any) {
    tabActive.value = item.name;
    pageNum.value = 1;
    if (loadMoreRef.value) loadMoreRef.value.resetStatus();
    onRefreshList();
}


//列表
function getShareList() {
    shareFindVoListH5Person({
        pageNum: pageNum.value,
        pageSize: 10,
        auditStatus: tab.value[tabActive.value].value
    }).then(res => {
        if (res.code == 200) {
            if (pageNum.value == 1) {
                list.value = res.data || []
            } else {
                list.value = list.value.concat(res.data);
            }
            //重置刷新状态及 判断是否加载完成
            if (loadMoreRef.value) {
                loadMoreRef.value.onLoadSuc(list.value.length, res.total);
            }
        }
    })
}


</script>

<style lang="scss" scoped>
.myShareList {
    background-color: #f2f2f2;

    .tab-box {
        :deep(.tab-title) {
            font-weight: 400;
            font-size: 32px;
            color: #333333;
        }

        :deep(.van-tab--active) {
            font-weight: 400;
            font-size: 32px;
            color: #5aa4ff;
        }
    }
}
</style>