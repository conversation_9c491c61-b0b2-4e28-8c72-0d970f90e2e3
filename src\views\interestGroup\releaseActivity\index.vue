<template>
    <div class="pb-20px">
        <van-form ref="formRef" @submit="submit">
            <van-cell-group>
                <van-field required v-model="Data.formatData.activityName" label="活动名称"
                    :rules="[{ required: true, message: '请填写活动名称' }]" label-width="25%" placeholder="请填写活动名称"
                    input-align="right" error-message-align="right"></van-field>
                <van-field required label="选择小组" @click="showPickerFn(Data.groupList, 'group')" v-model="Data.groupName"
                    :rules="[{ required: true, message: '请选择' }]" label-width="25%" placeholder="请选择" right-icon="arrow"
                    readonly input-align="right" error-message-align="right"></van-field>
                <van-field required v-model="Data.formatData.signUpInfo.maxCount" label="报名人数上限"
                    :rules="[{ required: true, message: '请输入成员数量' }]" label-width="35%" placeholder="请输入成员数量"
                    input-align="right" error-message-align="right"></van-field>
                <van-field required label="活动类别" @click="showPickerFn(Data.labelList, 'label')" v-model="Data.labelName"
                    :rules="[{ required: true, message: '请选择' }]" label-width="25%" placeholder="请选择" right-icon="arrow"
                    readonly input-align="right" error-message-align="right"></van-field>
                <van-field required v-model="Data.formatData.signUpInfo.enrollmentScore" label="报名所需分值"
                    :rules="[{ required: true, message: '请输入报名所需分值' }]" label-width="35%" placeholder="请输入报名所需分值"
                    input-align="right" error-message-align="right"></van-field>
                <van-field readonly clickable name="picker" v-model="Data.formatData.signUpInfo.signUpStartTime"
                    label="报名开始时间：" label-width="38%" required placeholder="请选择时间"
                    @click="showcPicker('signUpStartTime')" input-align="right"
                    :rules="[{ required: true, message: '请选择时间' }]" error-message-align="right" is-link />
                <van-field readonly clickable name="picker" v-model="Data.formatData.signUpInfo.signUpEndTime"
                    label="报名结束时间：" required label-width="38%" placeholder="请选择时间" @click="showcPicker('signUpEndTime')"
                    input-align="right" :rules="[{ required: true, message: '请选择时间' }]" error-message-align="right"
                    is-link />
                <van-field readonly clickable name="picker" v-model="Data.formatData.activityStartTime" label="活动开始时间："
                    required label-width="38%" placeholder="请选择时间" @click="showcPicker('activityStartTime')"
                    input-align="right" :rules="[{ required: true, message: '请选择时间' }]" error-message-align="right"
                    is-link />
                <van-field readonly clickable name="picker" v-model="Data.formatData.activityEndTime" label="活动结束时间："
                    required label-width="38%" placeholder="请选择时间" @click="showcPicker('activityEndTime')"
                    input-align="right" :rules="[{ required: true, message: '请选择时间' }]" error-message-align="right"
                    is-link />
                <div class="textarea">
                    <van-field required v-model="Data.formatData.activityContent" label="活动详情介绍" type="textarea"
                        :rules="[{ required: true, message: '请输入活动详情介绍' }]" label-width="100%" rows="2"
                        placeholder="请输入活动详情介绍"></van-field>
                </div>
                <div class="textarea">
                    <van-field required v-model="Data.formatData.activityAddress" label="活动地址" type="textarea"
                        :rules="[{ required: true, message: '请输入活动地址' }]" label-width="100%" rows="2"
                        placeholder="请输入活动地址"></van-field>
                </div>
                <div class="p-28px">
                    <div class="text-28px text-#333 mb-20px"><span class="text-#CC3333 ">*</span>活动封面</div>
                    <van-uploader v-model="Data.imgList" reupload max-count="1" accept="image/*"
                        :after-read="afterRead" />
                </div>

            </van-cell-group>
            <van-button type="primary" block
                class="btn w-65/100 mt-40px m-auto text-34px text-#fff py-34px text-center border-none"
                native-type="submit">确定</van-button>
        </van-form>
        <van-popup v-model:show="Data.showPicker" position="bottom">
            <van-picker :columns="Data.columns" @confirm="onConfirm" @cancel="Data.showPicker = false" />
        </van-popup>
        <!-- <van-calendar  @confirm="onConfirmDate"  v-model:show="Data.showTime"/> -->
        <van-popup v-model:show="Data.showTime" position="bottom" :overlay-style="{ zIndex: 1000 }">
            <van-picker-group title="时间选择" :tabs="['选择日期']" @confirm="onConfirmDate">
                <div style="display: flex; width: 100%">
                    <van-date-picker v-model="Data.startTime" style="width: 60%" />
                    <van-time-picker v-model="Data.endTime" style="width: 40%" />
                </div>
            </van-picker-group>
            <!-- <van-date-picker @confirm="onConfirm" @cancel="Data.showTime = false" type="date" v-model="Data.currentDate"/> -->
        </van-popup>
    </div>
</template>
<script lang="ts" setup>
import { createActivity } from '@/api/interestGroup';
import { showFailToast, showSuccessToast } from 'vant';
import { join } from 'lodash-es';
import router from '@/router';
import { useUserStore } from '@/store/modules/user';
const useStore = useUserStore();
import { uploadFile } from '@/api/public';
import dayjs from 'dayjs';
const Data = ref({
    formatData: {
        activityName: '',
        groupId: '',
        activityContent: '',
        activityStartTime: '',
        activityEndTime: '',
        activityCategory: "interestGroup",//固定值
        activityMode: "interestGroup",//固定值
        customerType: "interestGroup",//固定值
        activityType: '',
        appCover: '',
        activityAddress: '',
        signUpInfo: {
            maxCount: null, //报名人数上限
            enrollmentScore: null,//固定值
            signUpStartTime: "",//报名开始时间
            signUpEndTime: "",//报名截止时间
            auditFlag: "y"//固定值
        }
    },
    startTime: [],
    endTime: [],
    labelName: '',
    groupName: '',
    columns: [],
    showPicker: false,
    type: '',
    imgList: [],
    showTime: false,
    groupList: [],
    labelList: [],
    dateType: '',
    currentDate: (dayjs(new Date()).format('YYYY/MM/DD'))
})
//选择
const showcPicker = (type) => {
    Data.value.showTime = true;
    Data.value.dateType = type;
}
//时间选择
const onConfirmDate = (value) => {
    Data.value.showTime = false;
    if (Data.value.dateType == 'signUpStartTime' || Data.value.dateType == 'signUpEndTime') {
        Data.value.formatData.signUpInfo[Data.value.dateType] = Data.value.startTime.map(el => el).join('-') + ' ' + Data.value.endTime.map(el => el).join(':') + ':00';
        return
    }
    Data.value.formatData[Data.value.dateType] = Data.value.startTime.map(el => el).join('-') + ' ' + Data.value.endTime.map(el => el).join(':') + ':00';

}

const showPickerFn = (list, type) => {
    Data.value.columns = list
    Data.value.showPicker = true
    Data.value.type = type
}
//单选确认
const onConfirm = ({ selectedOptions }) => {
    switch (Data.value.type) {
        case 'group':
            Data.value.groupName = selectedOptions[0]?.text;
            Data.value.formatData.groupId = selectedOptions[0]?.value;
            break;
        case 'label':
            Data.value.labelName = selectedOptions[0]?.text;
            Data.value.formatData.activityType = selectedOptions[0]?.value;
            break;
    }
    Data.value.showPicker = false
}
//文件上传
function afterRead(file) {
    let filedata = {
        operateType: "16", //操作模块类型
        file: file.file,
    };
    uploadFile(filedata).then((res) => {
        if (res.code == 200 && res.data) {
            file.status = "success";
            file.url = res.data[0];
            let arr = [];
            Data.value.imgList.forEach((item) => {
                arr.push(item.url);
            });
            Data.value.formatData.appCover = arr.join(",");
        } else {
            file.status = "failed";
            showFailToast(res.message);
        }
    });
}
//提交
function submit() {
    createActivity(Data.value.formatData).then(res => {
        if (res.code == 200) {
            showSuccessToast("提交成功，等待管理员审核");
            router.go(-1)
        }
    })
}
onMounted(() => {
    useStore.getUserGroupData?.interestGroups.map(el => {
        el.text = el.groupName;
        el.value = el.groupId
    })
    useStore?.getLabelData.map(el => {
        el.text = el.labelName;
        el.value = el.autoId
    })
    Data.value.groupList = useStore.getUserGroupData?.interestGroups.filter(item => item.identityType == '20');
    Data.value.labelList = useStore.getLabelData
})
</script>
<style lang="scss" scoped>
:deep(.textarea) {
    .van-field__control {
        background: #F6F7F8;
        border-radius: 8px;
        padding: 20px;
    }

    .van-field__body {
        margin-top: 25px;
        font-size: 28px;
    }
}

:deep(.van-cell) {
    padding-bottom: 30px;
    padding-top: 30px;
}

:deep(.van-field__label) {
    font-size: 30px !important;
    white-space: nowrap;
}

:deep(.van-field__body) {
    font-size: 28px !important;
}

:deep(.van-field__right-icon) {
    font-size: 28px !important;
}

.btn {
    background: url("@/assets/public/butto.png") no-repeat;
    background-size: 100% 100%;
}
</style>