<template>
    <div class="">
        <div class="  mb-[20px] bg-[#fff] rounded-[20px] px-[20px] py-[28px] box-border" @click="toDetails(item)"
            v-for="(item, index) of data" :key="index">
            <div class="flex items-center" :class="showBtn ? 'justify-between' : ''">
                <img loading="lazy" :src="item.logo ? judgeStaticUrl(item.logo) :
                    item.groupCover ? judgeStaticUrl(item.groupCover) :
                        iconGroup" @error="noFind()" alt="" class="w-120px h-120px rounded-[50%] object-cover"
                    :class="showBtn ? '' : 'mr-[40px]'">
                <div class="w-60%">
                    <div class="text-[#333] text-[30px] truncate">{{ item.groupName }}</div>
                    <slot name="dataPart" :item="item">
                        <div class="flex items-center my-[20px]">
                            <!-- <div class="flex items-center text-[#666] text-[24px] mr-[34px]">
                            <van-icon name="star" size="15" color="#DFDFDF" class="mr-10px" />
                            活动：{{ item.activityNum }}
                        </div> -->
                            <div class="flex items-center text-[#666] text-[26px]">
                                <img loading="lazy" src="@/assets/interest/icon_num.png" alt=""
                                    class="w-26px h-26px mr-10px">
                                成员：{{ item.memberCount }}/{{ item.memberMax }}
                            </div>
                        </div>
                    </slot>

                    <div class="flex items-center flex-wrap " v-if="item.labels">
                        <div class="bg-[#f2f8ff] rounded-[8px] px-[14px] py-[8px] box-border mr-[18px] text-[#5AA4FF] text-[24px] mb-5px"
                            v-for="(e, i) of item.labels" :key="i">{{ e.labelName }}</div>
                    </div>
                </div>
                <div>
                    <slot name="showBtn" :item="item"></slot>
                </div>
            </div>

            <slot name="bContent" :item="item"> </slot>
        </div>
    </div>
</template>
<script lang="ts" setup>
import iconGroup from '@/assets/interest/icon_group.png'
import { judgeStaticUrl } from '@/utils/utils';
const props = defineProps({
    data: {
        type: Array,
        default: [],
    },
    type: {
        type: String,
        default: '',//examine-审核信息，不展示标签及数据部分
    },
    showBtn: {
        type: Boolean,
        default: false
    }
})
const noFind = () => {
    let img = new URL(`../../assets/interest/icon_group.png`, import.meta.url).href
    return img
}
const emit = defineEmits(['details'])
function toDetails(item: any) {
    emit('details', item)
}
</script>
<style lang="scss">
.topic {
    .topic-icon {
        border-radius: 50%;
    }
}
</style>