<!-- 隐患上报 -->
<template>
  <div class="hazard box-border flex flex-col">
    <div class="hazard-header text-center mt-5vh">
      <img src="@/assets/hazard/page_title.png" alt="" class="h-10vh" />
    </div>
    <div class="hazard-content flex flex-col flex-1">
      <div class="pic flex flex-col items-center mt-3vh" style="flex:.5">
        <img src="@/assets/hazard/page_pic.png" alt="" class="h-40vh" />
      </div>
      <div class="btn flex flex-col justify-center items-center mt-2vh" style="flex:.5">
        <!-- 立即上传隐患 -->
        <img src="@/assets/hazard/page_btn1.png" alt="" class="h-10vh block" @click="toNav('1')" />
        <!-- 我的上报记录 -->
        <img src="@/assets/hazard/page_btn2.png" alt="" class="h-10vh mt-3vh mb-4vh block" @click="toNav('2')" />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'hazardIndex',
})
const router = useRouter()
const toNav = (type: string) => {
  router.push({
    path: '/hazard/form',
    query: {
      type,
    }
  })
}
</script>
<style scoped lang="scss">
.hazard {
  width: 100%;
  min-height: 100vh;
  background-image: url('@/assets/hazard/page_bg.png');
  background-size: 100% auto;
  background-repeat: no-repeat;
  background-color: #7796f3;
}

// 小屏幕手机
@media screen and (max-width: 350px) {
  .hazard-header {
    img {
      height: 8vh !important;
    }
  }

  .hazard-content {
    .pic {
      img {
        height: 32vh !important;
      }
    }
  }

  .btn {
    img {
      height: 8vh !important;
    }
  }
}
</style>