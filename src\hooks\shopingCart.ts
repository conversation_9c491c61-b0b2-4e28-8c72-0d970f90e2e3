import {shoppingCartHandle,} from '@/api/mall/inclusive'
import { showToast } from 'vant'
export default function () {
    const addCart = (params:any,callback) => {
        shoppingCartHandle(params).then((res) => {
           if(res.code === 200){
                showToast('添加购物车成功')
                callback(params)
           }
          else showToast(res.message || '添加购物车失败')
            
        })
    }
    // 编辑购物车商品数量等操作
    const editCart = (params:any,callback) => {
        console.log('editCar',params)
        shoppingCartHandle(params).then((res) => {
            if(res.code === 200) {
                // showToast('编辑购物车成功')
                callback()
            }
            else showToast(res.message || '编辑购物车失败')
        })
    }
    const delCart = (deleteDataMap:any,callback) => {
        shoppingCartHandle({
            deleteDataMap,
            operateType: "delete"
        }).then((res) => {
            if(res.code === 200) {
                showToast('删除成功')
                callback(res)
            }
            else showToast(res.message || '删除失败')
        })
    }
    return {
        addCart,
        editCart,
        delCart
    }
}