import { h5Http,dataCenterHttp } from '@/utils/http/axios';
import { BasicResponse } from '.';

export interface birthCard {
    autoId?: string;
}
interface commonInfo{
    createTime: string,
    createUser: string,
    updateTime: string,
    updateUser: string,
}
export interface detailInfo extends commonInfo{
    autoId?: number | string,
	userId?: string,
	templateCode?: string,
	templatePicture?: string,
	backgroundPicture?: string|null,
	buttonColor?: string,
	buttonContent?: string,
    year?: null,
}
// 判断是否展示生日卡片
export const judgeSendBirthCard = () => {
    return h5Http.get<Recordable>({
        url: '/birthdayUserRecord/getBirthdayCard'
    });
}
// 获取生日卡片
export const getUserBirthdayCard = (autoId:any) => {
    return h5Http.get<Recordable>({
        url: '/birthdayUserRecord',
        params:{autoId}
    });
}

// 获取卡片列表
export const getBirthdayCardList = (params:any) => {
    return h5Http.get<Recordable>({
        url: '/birthdayUserRecord/findVoList',
        params,
    });
}

// 优惠券列表
export  const BirthdayCardCouponList = () => {
    return h5Http.get<Recordable>({
        url: '/couponInfo/birthdayCouponList',
    });
}
// 领券
export const getCardCoupon = (params:any) => {
    return h5Http.post<Recordable>({
        url: '/activityInfo/h5/coupon/receiveBirthDayCoupon',
        params,
    });
}
// 获取生日领取积分
export const getBirthdayIntegral = () => {
    return h5Http.get<Recordable>({
        url: '/birthdayUserRecord/getBirthDayRule',
    });
}
// 领取生日积分
export const receiveBirthdayIntegral= () => {
    return h5Http.post<Recordable>({
        url: '/birthdayUserRecord/getBirthDayBenefits',
    });
}
// 判断当日是不是生日
export const judgeBirthday = () => {
    return h5Http.get<Recordable>({
        url: '/birthdayUserRecord/getBirthDayFlag',
    })
}
// 判断是否能够领取积分
export const judgeUserCanIntegral = () => {
    return dataCenterHttp.get<Recordable>({
        url: '/customIntegral/weatherGetBirthdayScore',
    })
}