<template>
  <div class="bg-[#f5f5f5] min-h-100vh w-100vw flex flex-col">
    <img loading="lazy" :src="bannner" alt="" class="w-100% h-253px" />
    <div class="bg-#fff flex-1 p-30px box-border mt-[-40px]">
      <div class="tab-box mb-36px">
        <van-tabs v-model:active="Data.tab.active" sticky type="card" color="#F2F2F2" title-inactive-color="#666666"
          title-active-color="#5AA4FF" line-width="30" @click-tab="onClickTab">
          <van-tab :title="item.name" v-for="(item, index) in Data.tab.nav" :key="index"
            title-class="tab-title"></van-tab>
        </van-tabs>
      </div>
      <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
        <activityList :data="Data.list" @toDetails="(item) => toDetail(item)">
          <template #status="{ item }">
            <div class="text-24px text-#fff px-15px py-8px box-border rounded-ss-16px rounded-br-16px"
              :style="{ background: dealStatus(item.progress, 'bgColor') }">
              {{ dealStatus(item.progress, "text") }}
            </div>
          </template>
        </activityList>
      </refreshList>
    </div>
  </div>
</template>
<script lang="ts" setup>
import bannner from "@/assets/vitality/volunteerService/banner.png";
import icon_jljf from "@/assets/vitality/volunteerService/icon_jljf.png";
import icon_smqd from "@/assets/vitality/volunteerService/icon_smqd.png";
import icon_wdhd from "@/assets/vitality/volunteerService/icon_wdhd.png";
import icon_wdxx from "@/assets/vitality/volunteerService/icon_wdxx.png";
import activityList from "@/components/activity/list.vue";
import refreshList from "@/components/refreshList/index.vue";
import { activityInfoList } from "@/api/activity";

import router from "@/router";
import { toDetail } from "@/hooks/useValidator";
const Data = ref({
  tab: {
    active: 0,
    nav: [
      { name: "全部", code: "" },
      {
        name: "进行中",
        code: "2",
        bgColor: "linear-gradient(90deg, #FD633F, #FE952E)",
      },
      {
        name: "未开始",
        code: "1",
        bgColor: "linear-gradient(90deg, #2FB095, #55D1AC)",
      },
      {
        name: "已结束",
        code: "3",
        bgColor: "linear-gradient(90deg, #999999, #CCCCCC)",
      },
    ],
  },
  // 暂未使用
  card: [
    {
      name: "我的活动",
      icon: icon_wdhd,
      path: "/vitality/volunteerService/myActivity",
    },
    {
      name: "扫码签到",
      icon: icon_smqd,
    },
    {
      name: "我的信息",
      icon: icon_wdxx,
      // path: "/vitality/personalInfo",//去提交
      path: "/vitality/personalInfo/info", //个人信息
    },
    {
      name: "奖励积分",
      icon: icon_jljf,
      path: "/vitality/rewards",
    },
  ],
  list: [],
  pageNum: 1,
});

onMounted(() => {
  getActList();
});
/**
 * 点击状态标签页的处理函数
 *
 * @param item 被点击的标签页对象
 */
function onClickTab(item: any) {
  Data.value.tab.active = item.name;
  Data.value.pageNum = 1;
  getActList();
}
function dealStatus(status: string, type: string) {
  if (type === "text")
    return Data.value.tab.nav.find((el: any) => el.code === status)?.name;
  if (type === "bgColor")
    return Data.value.tab.nav.find((el: any) => el.code === status)?.bgColor;
}
function toJump(path: string) {
  if (!path) return;
  router.push({ path });
}
//活动列表
const loadMoreRef = ref(null);
// 刷新
const onRefreshList = () => {
  Data.value.pageNum = 1;
  getActList();
};
// 加载更多
const onLoadMore = () => {
  Data.value.pageNum++;
  getActList();
};
async function getActList() {
  let res = await activityInfoList({
    activityCategory: "volunteerService",
    pageSize: 10,
    pageNum: Data.value.pageNum,
    progress: Data.value.tab.nav[Data.value.tab.active].code,
  });
  if (res.code == 200) {
    if (Data.value.pageNum === 1) Data.value.list = [];
    Data.value.list = Data.value.list.concat(res.data);
    //重置刷新状态及 判断是否加载完成
    if (loadMoreRef.value) {
      loadMoreRef.value.onLoadSuc(Data.value.list.length, res.total);
    }
  }
}
</script>
<style lang="scss" scoped>
.tab-box {
  :deep(.van-tabs__nav--card) {
    border: none !important;
    margin: 0 !important;
  }

  :deep(.van-tab--card) {
    border-right: none !important;
    border-radius: 44px;
    background-color: #f2f2f2;
    color: #666;
    margin-right: 44px;
  }

  :deep(.van-tab--card):last-child {
    margin-right: 0;
  }

  :deep(.van-tab--active) {
    background-color: #f6faff !important;
    border: 1px solid #5aa4ff !important;
    font-weight: 500;
    font-size: 28px;
    color: #5aa4ff;
  }

  :deep(.tab-title) {
    font-weight: 400;
    font-size: 28px;
    color: #666;
  }
}
</style>
