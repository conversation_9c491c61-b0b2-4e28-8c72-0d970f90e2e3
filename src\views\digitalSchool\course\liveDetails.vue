<template>
  <!-- 直播 -->
  <div class="w-100%" v-if="Data.type == 'live' || Data.type == 'video'">
    <!-- <video class="w-100% h-400px" controls
      :src="Data.type == 'live' ? Data.info.livePlayBack : useStore.prefix + Data.info.catalogueVideo"></video> -->

    <!--  isOpenLive  notStart未开始 onGoing进行中 hasEnd 已结束-->
    <video class="w-100% h-400px bg-#333 z-index-0" id="videoId" :autoplay="true" :controls="true"
      controlslist="nodownload  noremoteplayback noplaybackrate" disablePictureInPicture
      :poster="Data.type == 'live' ? judgeStaticUrl(Data.info.liveCover) : ''" :src="Data.type == 'live' ? Data.info.isOpenLive === 'onGoing' ? Data.info.livePlayBack :
        Data.info.isOpenLive === 'hasEnd' ? Data.info.livePlayReturn : '' : judgeStaticUrl(Data.info.catalogueVideo)"
      :muted="false" :loop="true" preload="preload" language="zh-CN" x5-video-player-type="h5"
      x5-video-player-fullscreen="fasle" x5-video-player-style="position:fixed;z-index:0;"
      x5-video-orientation="portraint" playsinlin z-index="0" v-if="Data.info.isOpenLive !== 'notStart'"></video>

    <div v-else class="w-full h-400px relative bg-cover bg-center"
      :style="{ backgroundImage: `url(${Data.type == 'live' ? judgeStaticUrl(Data.info.liveCover) : ''})` }">
    </div>


    <div class="px border-b-20px border-b-solid border-b-#F6F7F8 pb-[23px]">
      <div class="text-36px text-#333 mt-30px mb-37px">
        {{ Data.info.catalogueName }}
      </div>
      <div class="flex w-100% items-center">
        <img loading="lazy" src="@/assets/vitality/volunteerService/icon_bmsj.png" alt="" class="w-26px h-26px mr-6px">
        <div class="text-#666 text-28px flex-1 mr-10px" v-if="Data.type == 'live'">
          {{ dayjs(Data.info.liveStartTime).format('YYYY-MM-DD HH:mm') || "--" }} 至 {{
            dayjs(Data.info.liveEndTime).format('YYYY-MM-DD HH:mm') || "--" }}
        </div>

        <div class="text-#666 text-28px mr-10px" v-else>来源：{{ Data.info.companyName }}</div>

        <div class="flex items-center text-#999 text-24px">
          <van-icon name="eye-o" size="16" color="#999" class="mr-15px" />{{ Data.info.clickNum || 0 }}
        </div>
      </div>
      <!-- <div
        class="flex items-center justify-between px-50px box-border mt-50px mb-23px"
      >
        <div
          v-for="(item, index) in Data.threeOperate"
          :key="item.name"
          class="relative flex flex-col justify-center items-center"
        >
          <div
            v-if="item.name === '点赞'"
            class="absolute right-[-80%] top-0 bg-#5CA5FF rounded-8px text-16px text-#fff px-10px box-border"
          >
            1086
          </div>
          <van-icon :name="item.icon" size="30" color="#999" />
          <div class="text-24px text-#999 mt-10px">{{ item.name }}</div>
        </div>
      </div> -->
    </div>
    <div class="p-40px box-border">
      <div class="text-36px text-#333">{{ Data.type == 'live' ? '直播' : '视频' }}简介</div>
      <div class="text-#333 text-28px mt-38px mb-80px" v-html="Data.info.catalogueIntroduce">
      </div>
    </div>
  </div>
  <div v-if="Data.type == 'photo'" class="w-full">
    <div class="px border-b-20px border-b-solid border-b-#F6F7F8 pb-[23px]">
      <div class="text-36px text-#333 mt-30px mb-37px">
        {{ Data.info.catalogueName }}
      </div>
      <div class="flex w-100% justify-between">
        <div class="text-#666 text-28px">来源：{{ Data.info.companyName }}</div>
        <div class="flex items-center text-#999 text-24px">
          <van-icon name="eye-o" size="16" color="#999" class="mr-15px" />{{ Data.info.clickNum || 0 }}
        </div>
      </div>
    </div>
    <div class="p-28px">
      <div class="text-#333 text-33px flex items-center mb-28px">
        <img loading="lazy" :src="iconTitle" alt="" class="w-30px h-auto mr-10px" />
        简介
      </div>
      <div v-html="Data.info.graphic" class="w-full overflow-auto"></div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs'
import Hls from 'hls.js';
import { judgeStaticUrl } from '@/utils/utils';
import cover from '@/assets/vitality/volunteerService/cover.jpg';
import Button from '@/components/Button/button.vue';
import { useRoute } from 'vue-router';
import { curriculumCatalogueGetVoByDtoH5 } from '@/api/digitalSchools/index';
import { useUserStore } from '@/store/modules/user';
import iconTitle from '@/assets/digitalSchool/icon_title.png';
import { showToast } from 'vant';
const route = useRoute();
const useStore = useUserStore();
const Data = ref({
  threeOperate: [
    { name: '点赞', icon: 'good-job-o' },
    { name: '收藏', icon: 'star-o' },
    { name: '分享', icon: 'share-o' },
  ],
  info: {},
  type: '',
});
const beingPlayed = ref(null);
const playing = ref(true);
const video = document.getElementById('videoId');

//课程详情/直播详情
function getDetail() {
  curriculumCatalogueGetVoByDtoH5({
    catalogueId: route.query?.catalogueId || '',
  }).then(res => {
    if (res.code == 200) {
      Data.value.info = res.data;
    }
  });
}
onMounted(() => {
  Data.value.type = route.query.type || '';

  getDetail();

  setTimeout(() => {
    onVideoPlay();
  }, 500);
});

/**
 * 视频播放
 */
let pageHls: any = null;
function onVideoPlay() {
  if (pageHls) {
    pageHls.stopLoad();
    pageHls.destroy();
  }
  beingPlayed.value = document.getElementById('videoId');
  if (Data.value.type == 'live' && Data.value.isOpenLive !== 'onGoing') {
    if (Hls.isSupported()) {
      pageHls = new Hls();
      pageHls.loadSource(beingPlayed.value.src);
      pageHls.attachMedia(beingPlayed.value);
      pageHls.on(Hls.Events.MANIFEST_PARSED, () => {
        beingPlayed.value.play();
      });
    } else if (beingPlayed.value.canPlayType('application/vnd.apple.mpegURL')) {
      beingPlayed.value.addEventListener('loadedmetadata', () => {
        beingPlayed.value.play();
      });
    }
  } else if (Data.value.type == 'video') {
    beingPlayed.value.play();
  }
}

/**
 * 播放|暂停
 */
const onVideoClick = () => {
  if (beingPlayed.value && playing.value) {
    beingPlayed.value.pause();
    playing.value = false;
    showToast('已暂停播放');
  } else {
    beingPlayed.value.play();
    playing.value = true;
    showToast('继续播放');
  }
};

onBeforeUnmount(() => {
  const videos = document.querySelectorAll('video');
  for (const video of videos) {
    if (video && video.id) {
      video.pause();
    }
  }
});

onUnmounted(() => {
  if (pageHls) {
    pageHls.stopLoad()
    pageHls.destroy()
  }
})
</script>
<style lang="scss" scoped>
.tab-box {
  border-bottom: 1px solid #ececec;

  :deep(.van-tabs__nav--card) {
    border: none !important;
    margin: 0 !important;
  }

  :deep(.van-tabs__line) {
    background: linear-gradient(0deg, #a1cbff 0%, #5aa4ff 100%);
    border-radius: 3px;
  }

  :deep(.tab-title) {
    font-weight: 400;
    font-size: 28px;
    color: #666;
  }

  :deep(.van-tab--active) {
    font-weight: 500;
    font-size: 32px;
    color: #333;
  }
}
</style>
