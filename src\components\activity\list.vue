<template>
  <div v-for="(item, index) in data" :key="index" :style="{
    background: bgColor,
    boxShadow: !septalLine
      ? '0px 3px 10px 0px rgba(128, 153, 162, 0.1)'
      : 'none',
  }" :class="septalLine
    ? 'border-b-[1px] border-b-#EBEBEB border-b-solid '
    : 'rounded-20px '
    " class="mb-30px relative pb-10px box-border item-box" @click="toDetails(item)">
    <img loading="lazy" :src="judgeStaticUrl(item.appCover) || defaultCover" alt=""
      class="h-240px w-100% rounded-16px object-cover" :class="imgClass" />
    <div class="item-content">
      <div class="text-30px text-#333 mt-27px mb-21px">
        {{ item.activityName }}
      </div>
      <slot name="subContent" :item="item">
        <div class="text-24px text-#999 mb-30px">
          <div class="mb-10px">
            报名时间：{{ dayjs(item.signUpInfo?.signUpStartTime).format('YYYY-MM-DD HH:MM') }} 至 {{
              dayjs(item.signUpInfo?.signUpEndTime).format('YYYY-MM-DD HH:MM') }}
          </div>
          <div class="flex items-center">
            <div class="flex-1 flex items-start">
              <div>服务地址：</div>
              <div class="flex-1 truncate max-w-420px">{{ item.activityAddress || '暂无地址' }}</div>
            </div>
            <div class="flex items-center ml-20px">
              <van-icon name="eye-o" size="15" class="z-1" color="#5088F0" />
              <span class="ml-7px text-#5088F0">{{ item.readCount || 0 }}</span>
            </div>
          </div>
        </div>
      </slot>
    </div>
    <div class="absolute -top-1px list-status" :class="statusPosition == 'left' ? '-left-1px' : '-right-1px'">
      <slot name="status" :item="item"></slot>
    </div>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs'
import { judgeStaticUrl } from "@/utils/utils";
import defaultCover from '@/assets/activity/tu.png'
const props = defineProps({
  data: {
    type: Array,
    default: [],
  },
  bgColor: {
    //item的背景色
    type: String,
    default: "transparent",
  },
  septalLine: {
    //是否有分割线
    type: Boolean,
    default: true,
  },
  statusPosition: {
    type: String,
    default: "left",
  },
  imgClass: {
    type: String,
    default: "rounded-16px",
  }
});
const emit = defineEmits(["toDetails"]);
function toDetails(item: any) {
  emit("toDetails", item);
}
</script>
