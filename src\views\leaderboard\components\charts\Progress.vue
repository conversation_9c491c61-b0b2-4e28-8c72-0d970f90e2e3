<template>
  <div class="progress">
    <div class="list" v-for="(v, i) in list" :key="i">
      <div v-show="type == 1" class="index" :class="{ 'index-1': i == 0, 'index-2': i == 1, 'index-3': i == 2 }">{{
        v.rank }}
      </div>
      <div v-show="type == 2" class="index type-2">第{{ v.rank }}名</div>
      <div class="info">
        <div class="top">
          <div class="name text-ellipsis">{{ v.title }}</div>
          <div class="val">{{ v.num }}</div>
        </div>
        <van-progress v-show="type == 1 && i == 0" :percentage="v.percentage" stroke-width="8" :color="color1"
          :show-pivot="false" />
        <van-progress v-show="type == 1 && i == 1" :percentage="v.percentage" stroke-width="8" :color="color2"
          :show-pivot="false" />
        <van-progress v-show="type == 1 && i == 2" :percentage="v.percentage" stroke-width="8" :color="color3"
          :show-pivot="false" />
        <van-progress v-show="type == 1 && i > 2" :percentage="v.percentage" stroke-width="8" :color="defaultColor"
          :show-pivot="false" />
        <van-progress v-show="type != 1" :percentage="v.percentage" stroke-width="8" :color="color1"
          :show-pivot="false" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
// 进度条
const props = defineProps({
  list: {
    type: Object,
    default: () => {
      return [
        {rank:1, title: '顺庆区', percentage: 30, num: 300 },
        {rank:2, title: '高坪区', percentage: 30, num: 300 },
        {rank:3, title: '嘉陵区', percentage: 30, num: 300 },
        {rank:4, title: '阆中区', percentage: 30, num: 300 },
        {rank:5, title: '南部区', percentage: 30, num: 300 },
        {rank:6, title: '西充区', percentage: 30, num: 300 },
        {rank:7, title: '仪陇区', percentage: 30, num: 300 },
        {rank:8, title: '营山区', percentage: 30, num: 300 },
        {rank:9, title: '蓬安区', percentage: 30, num: 300 },
      ]
    },
  },
  type: {
    type: Number,
    default: 2 // 0 不排   1排序  2排名   
  },
})
const color1 = ref("linear-gradient(to right, #FEDC45, #FFB03F)")
const color2= ref("linear-gradient(to right, #E9E9E9, #C3CFD5)")
const color3 = ref("linear-gradient(to right, #FFDED6, #FCAD96)")
const defaultColor = ref("linear-gradient(to right, #A1CBFF, #5AA4FF)")

</script>
<style lang="scss" scoped>
.progress {
  width: 100%;
  margin-top: 30px;

  .list {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // margin-bottom: 22px;
    margin-bottom: 32px;

    .index {
      width: 60px;
      height: 60px;
      background: #D7E9FE;
      border-radius: 50%;
      font-size: 35px;
      font-weight: 500;
      font-style: italic;
      color: #4898FB;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 19px;

      &.type-2 {
        width: 79px;
        height: 48px;
        background: rgba(255, 102, 74, .1);
        border-radius: 7px;
        font-size: 23px;
        font-weight: 500;
        font-style: italic;
        color: rgba(255, 102, 74, 1);
      }
    }

    .index-1 {
      color: #FFB03F;
      background: rgba(255, 155, 13, 0.2);
    }

    .index-2 {
      background: rgba(196, 207, 213, 0.5);
      color: #AFAFAF;
    }

    .index-3 {
      background: #FFDED6;
      color: #FB8B6B;
    }

    .info {
      flex: 1;

      .top {
        display: flex;
        justify-content: space-between;
        margin-bottom: 11px;

        .name {
          font-size: 24px;
          height: 34px;
          color: #666;
          overflow: hidden;
          width: 75%;

        }

        .val {
          font-size: 29px;
          color: #333;
        }
      }

      .van-progress {
        border-radius: 10px;
      }
    }
  }
}
</style>
