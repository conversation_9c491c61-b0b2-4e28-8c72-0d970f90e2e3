<template>
    <div class="w-100%">
        <div class="relative interest p-30px pt-230px border-box w-full">
            <div class="absolute right-0 top-40px text-28px text-#fff setting flex justify-end items-center p-10px"
                @click="toPage('/labelInfo')">设置</div>
            <img loading="lazy" src="@/assets/interest/banner.png" alt="" class="h-320px w-full absolute top-0 left-0">
            <div class="content h-full w-full relative">
                <div class="white-box flex justify-between py-30px px-16px pb-10px">
                    <swiper :autoplay="{
                        delay: 2500,
                        disableOnInteraction: false,
                    }" :loop="false" :modules="Data.modules" :slides-per-view="5" :space-between="0"
                        class="mySwiper w-100%" :pagination="{ clickable: true }" key="interestSwiper">
                        <swiper-slide class="!flex flex-col items-center" v-for="(item, index) of Data.menu"
                            :key="index" @click="toPage(item.path, item)">
                            <img loading="lazy" :src="item.src" alt="" class="w-80px h-80px">
                            <p class="text-28px">{{ item.name }}</p>
                        </swiper-slide>
                    </swiper>

                </div>
                <div class="white-box p-24px">
                    <Title :titleImg="'group.png'" :icon="'3.png'" :title="'group'" @look-more="lookMore"></Title>
                    <div class="flex pt-28px">
                        <swiper :autoplay="{
                            delay: 2500,
                            disableOnInteraction: false,
                        }" :loop="false" :modules="Data.modules" :slides-per-view="3" :space-between="15"
                            key="interestSwiper1" class="mySwiper !ml-0">
                            <swiper-slide
                                class="group-box rounded-10px relative !flex items-center justify-center flex-col   p-25px border-box m-auto"
                                v-for="(item, index) of Data.recommend" :key="index" @click="toDetail(item)">
                                <div
                                    class="text-#fff text-24px absolute top-0 left-0 w-70px h-30px text-center group-num text-center">
                                    {{ item.memberCount }}人</div>
                                <div class="w-56px h-56px mb-12px">
                                    <img loading="lazy" :src="judgeStaticUrl(item.logo)" alt=""
                                        class="w-full h-full group-icon   m-auto">
                                </div>
                                <div
                                    class="text-26px text-center text-ellipsis line-clamp-2 whitespace-nowrap text-wrap h-70px">
                                    {{ item.groupName }}</div>
                                <div class="text-#fff text-24px rounded-18px w-80px mt-12px text-center py-4px"
                                    @click.stop="join(item)"
                                    :class="item.joinState || item.memberCount == item.memberMax ? 'bg-#999' : 'bg-#5AA4FF'">
                                    {{ item.joinState ? '退出' : item.memberCount == item.memberMax ? '已满' : '加入' }}</div>
                            </swiper-slide>
                        </swiper>

                    </div>
                </div>
                <div class="white-box py-24px px-20px">
                    <Title :titleImg="'interest.png'" :icon="'2.png'" :title="'interest'" @lookMore="lookMore"></Title>
                    <div class="pt-20px">
                        <interestList :data="Data.interestList" />
                    </div>
                </div>
                <div class="white-box p-24px">
                    <Title :titleImg="'hot.png'" :icon="'1.png'" :title="'hot'" :showMore="false"></Title>
                    <div class="pt-20px">
                        <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore"
                            ref="loadMoreRef">
                            <topicList :data="Data.topicList" :source="'reply'" />
                        </refreshList>

                    </div>
                </div>
            </div>
            <div
                class="btn w-80/100 mt-40px m-auto text-30px text-#fff py-23px text-center fixed bottom-40px left-1/2 -translate-x-1/2">
                发布兴趣动态#分享记录生活#</div>
        </div>
        <div class="fixed top-0 left-0 w-100% z-999" v-if="Data.showLabel">
            <labelChoose @submit-content="submitContent" :navData="Data.navData" />
        </div>
        <Popup :show="Data.showPop" :titleName="'加入原因'" :placeHolder="'请输入加入原因'" @submit-content="joinContent"
            @close-popup="closePopup" />
        <div class="btn w-80/100 mt-40px m-auto text-30px text-#fff py-23px text-center fixed bottom-40px left-1/2 -translate-x-1/2"
            @click="toPage('/createDynamics')">发布兴趣动态#分享记录生活#</div>
    </div>

</template>
<script lang="ts" setup>
import createGroup from "@/assets/interest/icon_cj.png";
import publish from "@/assets/interest/icon_fb.png";
import activity from "@/assets/interest/icon_mct.png";
import group from "@/assets/interest/icon_group.png";
import audit from "@/assets/interest/icon_sh.png";
import Title from "@/components/Title/index.vue";
import interestList from "@/components/List/interestList.vue";
import topicList from "@/components/List/topicList.vue";
import { Swiper, SwiperSlide } from 'swiper/vue';
import { getCurrentUserInfo, interestGroupLabelRelation, getGroupH5, joinGroup, getCurrentUserLabel, exitGroup, commentsList } from '@/api/interestGroup';
import { useUserStore } from '@/store/modules/user';
//用到哪些功能自行添加
import { Scrollbar, A11y, Pagination } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/scrollbar';
import 'swiper/css/pagination';
import router from "@/router";
import labelChoose from "./myInterestGroup/labelChoose.vue";
import { showConfirmDialog, showFailToast, showToast } from "vant";
import Popup from '@/components/Popup/popup.vue';
import { activityInfoList } from "@/api/activity";
import refreshList from '@/components/refreshList/index.vue';
import { judgeStaticUrl } from "@/utils/utils";
const useStore = useUserStore();
const Data = ref({
    modules: [
        Scrollbar, A11y, Pagination
    ],
    menu: [
        { name: '创建小组', src: createGroup, path: '/creatGroup' },
        { name: '发布活动', src: publish, id: 'release', path: '/releaseActivity' },
        // {name:'发布话题',src:topic,path:'/createTopic'},
        { name: '我的活动', src: activity, path: '/myInterestActivity' },
        { name: '我的小组', src: group, path: '/myInterestGroup' },
        { name: '审核消息', src: audit, path: '/groupExamine' },
    ],//目录
    interestList: [],//兴趣爱好
    recommend: [],//推荐小组
    topicList: [],
    userData: {},
    showLabel: false,
    showPop: false,
    pageNum: 1,
    joinObj: {},
    navData: []

})

//活动列表
function getActList() {
    activityInfoList({
        activityCategory: 'interestGroup',
        pageSize: 3,
        pageNum: 1,
    }).then(res => {
        if (res.code == 200) {
            if (Data.value.pageNum === 1) Data.value.interestList = [];
            Data.value.interestList = Data.value.interestList.concat(res.data);

        }

    })
}
//获取兴趣小组列表
const getGroupList = () => {
    getGroupH5({
        pageNum: 1,
        pageSize: 10
    }).then((res) => {
        Data.value.recommend = res.data;
    })
}
//加入
function join(item) {
    Data.value.joinObj = item;
    if (item.memberCount == item.memberMax) {
        return
    }
    if (!item.joinState) {
        Data.value.showPop = true;
    } else {
        showConfirmDialog({
            title: "提示",
            message: `确认退出${item.groupName}小组?`,
            confirmButtonText: "确认",
            cancelButtonText: "取消",
            className: "close",
        }).then(async () => {
            // 自定义代码
            exitGroup({ groupId: Data.value.joinObj.groupId }).then(res => {
                if (res.code == 200) {
                    showToast("退出成功");
                    item.joinState = !item.joinState
                } else {
                    showFailToast(res.message)
                }
            })
        });

    }

}
//确认提交
function joinContent(val) {
    if (!val) {
        showToast({
            message: "请输入加入原因",
            icon: 'none',
        })
        return
    }
    joinGroup({
        groupName: Data.value.joinObj.groupName,
        groupId: Data.value.joinObj.groupId,
        reason: val
    }).then(res => {
        if (res.data) {
            val = '';
            getGroupList();
            showToast("提交成功，等待管理员审核~")
        } else {
            showFailToast(res.message)
        }

    })
    Data.value.showPop = false;
}
//关闭
function closePopup() {
    Data.value.showPop = false;
}
//获取兴趣小组用户信息
const getUserInfo = () => {
    getCurrentUserInfo({}).then((res) => {
        if (!res.data?.labels?.length) {
            Data.value.showLabel = true;

        }
        getUserLabel();
        Data.value.userData = res.data;
        useStore.setUserGroupData(res.data)
    })
}
//提交标签
const submitContent = (val) => {
    interestGroupLabelRelation({
        sourceType: "user",
        labelIds: val
    }).then(res => {
        if (res) {
            showToast("提交成功");
            Data.value.showLabel = false;
        }
    })
}
//获取兴趣小组用户标签
const getUserLabel = () => {
    getCurrentUserLabel().then((res) => {
        Data.value.navData = res.data;
        Data.value.navData?.map(el => {
            el.isChoose = false
        })
        Data.value.navData?.map((item, index) => {
            if (Data.value.userData?.labels) {
                if (Data.value.userData?.labels.some(ele => ele.labelId === item.autoId)) {
                    Data.value.navData[index].isChoose = true;
                }
            }

        });
        useStore.setLabelData(Data.value.navData)
    })
}
//跳转兴趣小组
function lookMore(title) {
    let path = ""
    if (title == 'interest') {
        path = '/interestList'
    } else if (title == 'group') {
        path = '/groupList'
    }
    router.push(path)
}
//跳转二级页面
function toPage(path, item) {
    if (item?.id == 'release') {
        let data = Data.value.userData.interestGroups?.filter(item => item.identityType == '20');
        console.log(data);

        if (data.length) {
            router.push('/releaseActivity')
        } else {
            showFailToast("您当前暂无发布活动权限~")
        }
        return
    }
    router.push(path)
}
//发布兴趣动态
function toPublishPage(path) {
    console.log(Data.value.userData.interestGroups);

}
//跳转详情
function toDetail(item) {
    router.push({
        path: '/groupDetail',
        query: {
            groupId: item.groupId
        }
    })
}
//动态列表
const loadMoreRef = ref(null);
function getcommentsList() {
    commentsList({
        dataSources: "group", //评论来源 : group:小组  activity：活动
        commentType: 'dynamic',//评论类型 （groupComment：小组评价，dynamic：小组动态，activityComment：活动评论，activityDynamic：活动评价 ）
        pageSize: 10,
        pageNum: Data.value.pageNum,
        sortType: 'desc',
        orderBy: 'create_time'
    }).then(res => {
        if (res.code == 200) {
            if (Data.value.pageNum === 1) Data.value.topicList = [];
            Data.value.topicList = Data.value.topicList.concat(res.data);
            //重置刷新状态及 判断是否加载完成
            if (loadMoreRef.value) {
                loadMoreRef.value.onLoadSuc(Data.value.topicList.length, res.total);
            }
        }
    })
}
// 刷新
const onRefreshList = () => {
    Data.value.pageNum = 1;
    getcommentsList();
};
// 加载更多
const onLoadMore = () => {
    Data.value.pageNum++;
    getcommentsList();
};
onMounted(() => {
    getUserInfo();
    getGroupList();
    getActList();
    getcommentsList()
})
</script>
<style lang="scss" scoped>
.interest {
    min-height: calc(100vh - 260px);
    background: #F6F7F8;
    box-sizing: border-box;

    :deep(.swiper-pagination-bullet) {
        width: 34px;
        height: 4px;
        border-radius: 2px;
        background: #5AA4FF;
        position: relative;
        top: 20px;
        margin: 0;
    }

    .setting {
        background: url('@/assets/interest/set.png') no-repeat;
        width: 130px;
        height: 70px;
        background-size: 100% 100%;
        z-index: 100;
    }

    .content {
        .white-box {
            background: #FFFFFF;
            border-radius: 20px;
            margin-bottom: 20px;

            .group-box {
                // height: 189px;
                background: linear-gradient(0deg, #FFFFFF 42%, #EBF4FF 100%);

                .group-num {
                    background: linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%);
                    border-radius: 10px 0px 10px 0px;
                }

                .group-icon {
                    border-radius: 50%;
                }
            }
        }
    }

    .btn {
        background: url("@/assets/public/button.png") no-repeat;
        background-size: 100% 100%;
    }

}
</style>
