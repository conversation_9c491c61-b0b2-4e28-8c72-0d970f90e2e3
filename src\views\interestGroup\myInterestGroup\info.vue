<template>
    <div class="pb-20px infos-form">
        <van-form ref="formRef" @submit="submit">
            <van-cell-group>
                <van-field required v-model="Data.formatData.nickName" label="用户昵称"
                    :rules="[{ required: true, message: '请填写用户昵称' }]" label-width="25%" placeholder="请填写用户昵称"
                    input-align="right" error-message-align="right"></van-field>
                <van-field required label="性别" :rules="[{ required: true, message: '请选择' }]" label-width="65%"
                    placeholder="请选择">
                    <template #input>
                        <van-radio-group v-model="Data.formatData.gender" direction="horizontal" icon-size="15px"
                            shape="dot">
                            <van-radio :name="'男'">男</van-radio>
                            <van-radio :name="'女'">女</van-radio>
                        </van-radio-group>
                    </template>
                </van-field>
                <van-field required label="出生日期" @click="showPickerFn(Data.IndustryList, 'Industry')"
                    v-model="Data.formatData.birthday" :rules="[{ required: true, message: '请选择' }]" label-width="25%"
                    placeholder="请选择" right-icon="arrow" readonly input-align="right"
                    error-message-align="right"></van-field>
                <van-field v-model="Data.formatData.areaName" required is-link label-width="25%" readonly label="地区"
                    :rules="[{ required: true, message: '请选择' }]" placeholder="请选择所在地区" @click="Data.showArea = true"
                    input-align="right" error-message-align="right" />
                <div class="label">
                    <van-field required label="我的兴趣标签" placeholder="兴趣标签" label-align="left" label-width="35%"
                        right-icon="arrow" @click="showLabelFn" readonly input-align="right"
                        error-message-align="right">

                    </van-field>
                    <div class="flex flex-wrap p-20px">
                        <div v-for="(item, index) of Data.labelChoose" :key="index" class="flex w-23%  mr-12px mb-12px">
                            <div class="w-100% py-16px  rounded-10px flex text-28px justify-center"
                                :class="item.isChoose ? 'bg-#E4F0FF text-#5AA4FF' : 'bg-#F6F7F8'">{{ item.labelName }}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="textarea">
                    <van-field v-model="Data.formatData.hobby" label="爱好" type="textarea" label-width="100%"
                        placeholder="请填写爱好"></van-field>
                </div>
                <div class="textarea">
                    <van-field v-model="Data.formatData.remark" label="备注" type="textarea" label-width="100%"
                        placeholder="请填写备注"></van-field>
                </div>
            </van-cell-group>
            <van-button type="primary" block
                class="btn w-65/100 mt-40px fixed bottom-40px left-1/2 border-none -translate-x-1/2 m-auto text-34px text-#fff py-34px text-center"
                native-type="submit">确定</van-button>
        </van-form>
        <van-popup v-model:show="Data.showPicker" position="bottom">
            <van-date-picker v-model:value="Data.formatData.birthday" title="选择日期" :minDate="Data.minDate"
                @confirm="onConfirm" />
        </van-popup>
        <van-popup v-model:show="Data.showArea" round position="bottom">
            <van-cascader v-model="Data.cascaderValue" title="请选择所在地区" :options="Data.options"
                @close="Data.showArea = false" @finish="onFinish" />
        </van-popup>
        <van-popup v-model:show="Data.showLabel" position="bottom" round>
            <labelChoose @submit-content="submitContent" :navData="navData" :showBtn="false" />
        </van-popup>
    </div>
</template>
<script lang="ts" setup>
import { useUserStore } from '@/store/modules/user';
import { useDictionary } from '@/store/modules/dictionary';
const useStore = useUserStore();
const dictionary = useDictionary();
import { useCascaderAreaData } from '@vant/area-data';
import { interestGroupUserInfo, getCurrentUserInfo } from '@/api/interestGroup';
import { showFailToast, showSuccessToast } from 'vant';
import labelChoose from "./../myInterestGroup/labelChoose.vue";
import { join } from 'lodash-es';
import router from '@/router';
const Data = ref({
    minDate: new Date(1960, 0, 1),
    formatData: {
        nickName: '',
        gender: '',
        hobby: '',
        remark: '',
        birthday: '',
        areaName: '',
        labelIds: []
    },
    birthday: new Date(),
    columns: [],
    showPicker: false,
    type: '',
    IndustryList: [],
    imgList: [],
    areaList: [],
    showArea: false,
    cascaderValue: '',
    options: useCascaderAreaData(),
    showLabel: false,
    labelChoose: []

})
const navData = computed(() => {
    return useStore.getLabelData
})
const showLabelFn = () => {
    Data.value.showLabel = true
}
//确认选择兴趣标签
const submitContent = (val) => {
    Data.value.labelIds = []
    Data.value.showLabel = false;
    Data.value.labelChoose = val;
    Data.value.labelChoose.map(el => {
        Data.value.formatData.labelIds.push(el.autoId)
    })

}
//选择
const showPickerFn = (list, type) => {
    Data.value.columns = list
    Data.value.showPicker = true
    Data.value.type = type
}
const onConfirm = ({ selectedValues, selectedOptions }: Recordable) => {
    Data.value.showPicker = false

    Data.value.formatData.birthday = join(selectedValues, '-');


}

//级联数据选择
const onFinish = ({ selectedOptions }) => {
    Data.value.showArea = false;
    Data.value.formatData.areaName = selectedOptions.map((option) => option.text).join(',');
}

//提交
function submit() {
    if (!Data.value.labelChoose?.length) {
        showFailToast("请选择兴趣标签");
        return
    }
    interestGroupUserInfo(Data.value.formatData).then(res => {
        if (res.code == 200) {
            showSuccessToast("提交成功");
            router.go(-1)
        }
    })
}
//获取兴趣小组用户信息
const getUserInfo = () => {
    getCurrentUserInfo({}).then((res) => {
        Data.value.formatData.gender = res.data.gender;
        Data.value.formatData.nickName = res.data.nickName;
        Data.value.formatData.hobby = res.data.hobby;
        Data.value.formatData.remark = res.data.remark;
        Data.value.formatData.birthday = res.data.birthday;
        Data.value.formatData.areaName = res.data.areaName;
        res.data.labels?.map(el => el.isChoose = true);
        Data.value.labelChoose = res.data.labels
    })
}
onMounted(() => {
    getUserInfo()
})
</script>
<style lang="scss" scoped>
.textarea {
    :deep(.van-field__control) {
        background: #F6F7F8;
        border-radius: 8px;
        padding: 20px;
    }

    :deep(.van-field__body) {
        margin-top: 15px;
    }
}

.label {
    border-bottom: 1px solid #f5f5f5;

    :deep(.van-cell:after) {
        border-bottom: none !important;
    }
}

.btn {
    background: url("@/assets/public/butto.png") no-repeat;
    background-size: 100% 100%;
}

.infos-form {
    :deep(.van-field__control--custom) {
        justify-content: flex-end;
    }

    :deep(.van-field__label) {
        font-size: 28px;
    }

    :deep(.van-cell) {
        line-height: normal;
    }

    :deep(.van-cell__value) {
        font-size: 28px;
    }

    :deep(.van-radio-group--horizontal, .van-checkbox-group--horizontal) {
        height: 50px;
    }
}
</style>