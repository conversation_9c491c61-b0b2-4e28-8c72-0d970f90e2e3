export default [
    {
        path: "/merchants/guide",
        name: "merchantsGuide",
        component: () => import("@/views/merchants/guide.vue"),
        meta: {
            title: "商家入驻",
            isShowTabBar: false,
            isBack: true,
        }
    }, 
    {
        path: "/merchants/form",
        name: "merchantsForm",
        component: () => import("@/views/merchants/form.vue"),
        meta: {
            title: "入驻填写",
            isShowTabBar: false,
            isBack: true,
        }
    }, 
    {
        path: "/merchants/status",
        name: "merchantsStatus",
        component: () => import("@/views/merchants/status.vue"),
        meta: {
            title: "审核状态",
            isShowTabBar: false,
            isBack: true,
        }
    }, 
    {
        path: "/merchants/info",
        name: "merchantsInfo",
        component: () => import("@/views/merchants/info.vue"),
        meta: {
            title: "商家核销",
            isShowTabBar: false,
            isBack: true,
        }
    }, 
    {
        path: "/merchants/detail",
        name: "merchantsDetail",
        component: () => import("@/views/merchants/detail.vue"),
        meta: {
            title: "商家详情",
            isShowTabBar: false,
            isBack: true,
        }
    }, 
]