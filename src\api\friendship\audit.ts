// 单身联谊审核接口
import { h5Http } from '@/utils/http/axios';
import { BasicResponse } from '.';

// 职工审核列表
export const findRecordAuditList = (params) => {
    return h5Http.get({
        url:'/singleInfo/findRecordAuditListH5',
        params
    });
}

// 职工审核列表详情
export const findAuditDetail = (params) => {
    return h5Http.get({
        url:'/singleInfo/findAuditDetail',
        params
    });
}

// 职工审核
export const singleAudit = (params) => {
    return h5Http.post({
        url:'/singleInfo/singleAuditH5',
        params
    });
}

// 朋友圈列表
export const singlePostFindVoList = (params) => {
    return h5Http.get({
        url:'/singlePost/findVoListH5',
        params
    });
}

// 朋友圈审核
export const postAudit = (params) => {
    return h5Http.post({
        url:'/singlePost/postAuditH5',
        params
    });
}

// 评论列表
export const singlePostCommentList = (params) => {
    return h5Http.get({
        url:'/singlePostComment/findVoListH5',
        params
    });
}

// 评论审核
export const commentAudit = (params) => {
    return h5Http.post({
        url:'/singlePostComment/commentAuditH5',
        params
    });
}

// 查询审核列表数量统计
export const queryTableCount = (params) => {
    return h5Http.get({
        url:'/singleInfo/queryTableCount',
        params
    });
}
