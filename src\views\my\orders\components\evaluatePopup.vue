<template>
    <van-popup :show="props.show" position="bottom">
        <div class="popup-content relative">
            <img loading="lazy" src="@/assets/inclusive/shop/evaluate_bg.png" class="w-full relative z-1 block" />
            <div class="close-icon absolute z-2 top-66px right-16px text-50px text-#fff" @click="close">
                <van-icon name="cross" />
            </div>
            <div class="absolute z-2 left-0 right-0 top-180px bottom-0 safe_area_bottom"
                v-if="inclusiveProductCommentList[evaluateIndex]">
                <div class="bg-#fff rounded-28px px-24px py-30px">
                    <div class="goods-info flex items-center">
                        <div class="goods-img w-112px h-112px rounded-20px bg-red">
                            <img loading="lazy" class="w-full h-full rounded-20px object-cover"
                                :src="inclusiveProductCommentList[evaluateIndex]?.productSubImg" />
                        </div>
                        <div class="goods-name ml-26px text-30px">
                            {{ inclusiveProductCommentList[evaluateIndex]?.productName }}
                            <div class="text-#999 text-28px mt-20px">
                                {{ inclusiveProductCommentList[evaluateIndex]?.productSubName }}
                            </div>
                        </div>
                    </div>
                    <div class="text-30px text-center mb-34px text-#A3A3A3 mt-20px">
                        {{ currentName ? currentName : '感觉' }}
                    </div>
                    <div class="evaluate-info px-16px">
                        <div v-for="item, index in evaluateArray" :key="index" @click="selectStar(item)">
                            <div class="stars text-66px">
                                <van-icon name="star"
                                    :color="inclusiveProductCommentList[evaluateIndex].score >= item.value ? '#FF4344' : '#EDEDED'" />
                            </div>
                            <div class="text-#A3A3A3 text-28px mt-26px">{{ item.label }}</div>
                        </div>
                    </div>
                </div>

                <div class="hide-text flex items-center text-30px bg-#fff rounded-28px py-38px px-30px mt-24px">
                    <van-checkbox v-model="inclusiveProductCommentList[evaluateIndex].anonymousFlg" shape="round"
                        checked-color="#FF4344" />
                    <span class="text-#333 ml-20px">匿名提交</span>
                </div>

                <div class="submit h-78px mb-60px mx-30px bg-#FF4344 rounded-40px text-34px 
                text-#fff flex items-center justify-center mt-80px" @click="submit">
                    {{ productList.length === 1 ? '提交' : '评价下一个' }}
                </div>
            </div>
        </div>
    </van-popup>
</template>
<script lang="ts" setup>
import { showConfirmDialog, showToast } from 'vant'
import { judgeStaticUrl } from '@/utils/utils'
const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    productList: {
        type: Object,
        default: () => ([])
    }
})
const evaluateArray = ref([
    {
        value: 1,
        label: '极差'
    },
    {
        value: 2,
        label: '较差'
    },
    {
        value: 3,
        label: '一般'
    },
    {
        value: 4,
        label: '满意'
    },
    {
        value: 5,
        label: '很棒'
    }
])
const evaluateIndex = ref(0) // 评价索引
const inclusiveProductCommentList = ref<any>([])// 评价列表数据
const emit = defineEmits(['update:show', 'submit'])
const close = () => {
    showConfirmDialog({
        title: '提示',
        message: '确定要关闭吗？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
    }).then(() => {
        emit('update:show', false)
    })
}
const submit = () => {
    if (inclusiveProductCommentList.value.length === 0) return showToast('请先选择商品进行评分')
    if (inclusiveProductCommentList.value[evaluateIndex.value].score === 0) return showToast('请选择评分')

    if (inclusiveProductCommentList.value.length === props.productList.length) {
        emit('submit', inclusiveProductCommentList.value)
    }
    else {
        evaluateIndex.value++
        resetPush()
    }

}
watch(() => props.show, (val) => {
    if (!val) {
        evaluateIndex.value = 0
        inclusiveProductCommentList.value = []
    } else {
        if (props.productList && props.productList.length > 0) {
            evaluateIndex.value = 0
            resetPush()
        }
    }
})
const resetPush = () => {
    const { productId, productName, productSubName, productSubId, productSubImg } = props.productList[evaluateIndex.value]
    if (evaluateIndex.value === 0) {
        inclusiveProductCommentList.value = []
    }
    // 如果已经存在，则不重复添加数据
    if (inclusiveProductCommentList.value[evaluateIndex.value]) return
    inclusiveProductCommentList.value.push(
        {
            productId,
            productName,
            productSubId,
            productSubName,
            productSubImg: judgeStaticUrl(productSubImg),
            score: 0,
            anonymousFlg: true,
        }
    )
}

const currentName = ref('')
const selectStar = (item: any) => {
    inclusiveProductCommentList.value[evaluateIndex.value].score = item.value
    currentName.value = item.label
}
</script>
<style scoped lang="scss">
.van-popup {
    background: transparent;
}

.evaluate-info {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 10px;
    place-items: center;
}
</style>