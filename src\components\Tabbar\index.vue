<template>
  <van-tabbar v-model="active" :placeholder="true" :route="true" fixed z-100>
    <van-tabbar-item v-for="(item, index) in tabbarData" :key="index" class="" @click="toPage(item)">
      <div class="pb-10px relative " :class="active == index ? 'text-#5AA4FF text-30px -bottom-28px' : '-bottom-0px'">{{
        item.title }}</div>
      <template #icon="props">
        <div v-if="active == index" class="w-100px h-100px circle rounded-1/2 absolute 
        -top-62px z-999 left-1/2 -translate-x-1/2 flex items-center justify-center">
          <img loading="lazy" class="!w-[86px] !h-[90px]" :src="item.iconActive" />
        </div>
        <img loading="lazy" class="w-[55px] h-[60px]" :src="item.icon" v-else />
      </template>
    </van-tabbar-item>
  </van-tabbar>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import Inclusive from "@/assets/tab/icon_inclu.png";
import InclusiveA from "@/assets/tab/icon_inclu_a.png";
import Activity from "@/assets/tab/icon_act.png";
import ActivityA from "@/assets/tab/icon_act_a.png";
import Home from "@/assets/tab/icon_home.png";
import HomeA from "@/assets/tab/icon_home_a.png";
import County from "@/assets/tab/icon_count.png";
import CountyA from "@/assets/tab/icon_count_a.png";
import My from "@/assets/tab/icon_my.png";
import MyA from "@/assets/tab/icon_my_a.png";
import {
  useRouter,
  useRoute,
  type LocationQueryRaw,
  type RouteParamsRaw
} from "vue-router";
import { findH5XQBannerList } from '@/api/news';
import { useUserStore } from '@/store/modules/user';
import { isLoginDialog } from '@/utils/utils';
const useStore = useUserStore();
const active = ref(2);
const route = useRoute();
const router = useRouter();
const isPoint = ref(true);
const tabbarData = computed(() => {
  return [
    {

      title: "普惠",
      icon: Inclusive,
      iconActive: InclusiveA,
      path: '/inclusive'
    },
    {

      title: "活动",
      icon: Activity,
      iconActive: ActivityA,
      path: '/activity'
    },
    {
      title: "首页",
      icon: Home,
      iconActive: HomeA,
      path: '/home'
    },
    {

      title: useStore.getCityData?.subscribeFlag == 'Y' ? useStore.getCityData?.subscribeCityName : '区县',
      icon: County,
      iconActive: CountyA,
      path: useStore.getCityData?.subscribeFlag == 'Y' ? '/countyDetail' : '/county'
    },
    {

      title: "我的",
      icon: My,
      iconActive: MyA,
      path: '/my'
    }
  ]
})
//页面跳转
function toPage(item) {
  if (item.path == '/countyDetail') {
    router.push(item.path);
    return
  }
  router.replace(item.path)
}
watch(active, (newVal, oldVal) => {
  if (newVal === 4 && !useStore?.getToken) active.value = oldVal
})
//获取区县列表
function getBannerList() {
  findH5XQBannerList({
    userId: useStore.getUserInfo?.userId
  }).then(res => {
    if (res.code == 200) {
      res.data.map((el, index) => {
        if (el.subscribeFlag == 'Y') {
          tabbarData.value[3].title = el.subscribeCityName;
          tabbarData.value[3].path = '/countyDetail';
        }
      })


    }
  })
}
watch(
  () => router.currentRoute.value.name,
  (newValue, oldValue) => {
    // tabbarData.value[3].title = useStore.getCityData?.subscribeFlag == 'Y' ? useStore.getCityData?.subscribeCityName : '区县';
    // tabbarData.value[3].path = useStore.getCityData?.subscribeFlag == 'Y' ? '/countyDetail' : '/county';
    if (router.currentRoute.value.path == '/activity') {
      active.value = 1;
    } else if (router.currentRoute.value.path == "/inclusive") {
      active.value = 0;
    } else if (router.currentRoute.value.path == "/my") {
      active.value = 4;
    } else if (router.currentRoute.value.path == "/county") {

      active.value = 3;
    } else {
      active.value = 2;
    }
  },
  { immediate: true }
);

</script>

<style lang="scss" scoped>
:deep(.van-tabbar) {
  margin: 0;
  width: 100%;
  height: 110px;
  padding: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: border-box;
  background: url('@/assets/tab/tab_bg.png') no-repeat;
  background-size: 100% 100%;
  box-shadow: 0px -1px 40px 0px rgba(45, 52, 67, 0.17);
  z-index: 999;
}

:deep(.van-tabbar-item__text) {
  font-size: 28px;
  color: #999999;
}

:deep(.van-tabbar-item__icon img) {
  height: auto;
}

.circle {
  background: url("@/assets/tab/circle.png") no-repeat;
  background-size: 100% auto;
  // background-color:red;
}
</style>
