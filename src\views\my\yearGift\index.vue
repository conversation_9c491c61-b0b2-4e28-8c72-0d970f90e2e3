<template>
    <div class="exchange bg-[#F9F9F9] h-fit min-h-100vh box-border flex flex-col pt-30px">
        <div class="px-30px pt-15px pb-25px list_view flex-1">
            <refreshList ref="loadMoreRef" :immeCheck="true" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore">
                <div class="list min-h-full">
                    <div class="cell flex py-26px px-30px bg-[#fff] mb-22px rounded-16px" @click="lookCards(item)"
                        v-for="item, index in list" :key="index">
                        <div class="left_item w-130px h-130px bg-[#F5F5F5] rounded-11px bg-cover bg-center"
                            :style="{ backgroundImage: `url(${userStore.getPrefix}${item.listPictures})` }">
                        </div>
                        <div class="right_item text-[#FF4344] text-32px flex-1 flex flex-col justify-between ml-24px">
                            <div class="title text-[30px] text-#333">{{ item.belongYear }}年生日卡片</div>
                            <div class="bottom_cell flex justify-between items-center">
                                <div class="text-[#A1A1A1] text-24px"> {{ item.createTime }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </refreshList>
        </div>
    </div>
</template>
<script lang="ts" setup>
defineOptions({
    name: 'myYearGiftList'
})
import refreshList from '@/components/refreshList/index.vue';
import { getBirthdayCardList } from '@/api/yearGift'
import { useUserStore } from '@/store/modules/user';
const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo)
// 列表
const list = ref<any>([])
const pageNum = ref(1)
const loadMoreRef = ref<any>(null)
const onRefreshList = () => {
    pageNum.value = 1
    loadMoreData()
}
// 加载更多
const onLoadMore = () => {
    pageNum.value++
    loadMoreData()
}
const loadMoreData = async () => {
    const { data, code, total } = await getBirthdayCardList({
        pageNum: pageNum.value,
        pageSize: 10,
        userId: userInfo.value?.userId,
    })
    if (code === 200) {
        if (pageNum.value === 1) list.value = data
        else list.value = [...list.value, ...data]
    }
    //重置刷新状态及 判断是否加载完成
    if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(list.value.length, total)
    }
}

// 查看卡片
const router = useRouter()
const lookCards = (item: any) => {
    router.push({
        path: '/my/yearGiftCard',
        query: {
            autoId: item?.autoId
        }
    })
}

</script>
<style lang="scss" scoped>
.exchange {
    .tabs {

        :deep(.van-tabs--line .van-tabs__wrap) {
            height: 100px;
        }

        :deep(.van-tab) {
            line-height: 1;
            height: 100%;
        }

        :deep(.van-tabs__nav--line) {
            height: 100%;
            padding-bottom: 15px;
        }

        :deep(.van-tabs__line) {
            background: linear-gradient(86deg, #5AA4FF 0%, #C7E0FF 100%);
            width: 54px;
            height: 6px;
            z-index: 1;
        }

        :deep(.van-tab__text) {
            font-size: 32px;
            z-index: 2;
        }

    }

    .list {
        .cell {
            box-shadow: 0px 3px 10px 0px rgba(119, 151, 203, 0.15);
        }
    }

}
</style>