<template>
  <div class="echart-box" style="width:100%;height:100%">
    <div ref="echartRef" class="w-full h-full"></div>
  </div>
</template>

<script lang="ts" setup>
// 饼图
import { useECharts } from '@/utils/useECharts'
import { Ref, ref, watch, onMounted } from 'vue'
import * as echarts from 'echarts'
import { getVwSize} from '../../data'

const props = defineProps({
  dataSource: {
    type: Object,
    default: () => {
      return [
        { name: 'name1', value: 100, percent: 40 },
        { name: 'name2', value: 100, percent: 40 },
        { name: 'name3', value: 100, percent: 40 },
        { name: 'name4', value: 100, percent: 40 },
        { name: 'name5', value: 100, percent: 40 },
      ]
    },
  }, //color
})

const echartRef = ref<HTMLDivElement | null>(null)
const { setOptions } = useECharts(echartRef as Ref<HTMLDivElement>)

const initChart = () => {
  let data = props.dataSource
  let sum = data.reduce((sum, v) => sum + v.value, 0);
  let colors = [
    new echarts.graphic.LinearGradient(1, 1, 1, 0, [{
      offset: 0,
      color: '#20BCF0'
    },
    {
      offset: 1,
      color: '#82D6F9'
    }
    ]),
    new echarts.graphic.LinearGradient(1, 1, 1, 0, [{
      offset: 0,
      color: '#C879F9'
    },
    {
      offset: 1,
      color: 'rgba(200, 121, 249, 0.5)'
    }
    ]),
    new echarts.graphic.LinearGradient(1, 1, 1, 0, [{
      offset: 0,
      color: 'rgba(251, 73, 125, 1)'
    },
    {
      offset: 1,
      color: 'rgba(252, 151, 181, 1)'
    }
    ]),
    new echarts.graphic.LinearGradient(1, 1, 1, 0, [{
      offset: 0,
      color: 'rgba(255, 154, 85, 1)'
    },
    {
      offset: 1,
      color: 'rgba(252, 168, 111, 1)'
    }
    ]),
    new echarts.graphic.LinearGradient(1, 1, 1, 0, [{
      offset: 0,
      color: 'rgba(245, 188, 49, 1)'
    },
    {
      offset: 1,
      color: 'rgba(250, 208, 104, 1)'
    }
    ]),
  ]
// 绘制图表
var option = {
  title: {
    text: `{a|总数}\n{c|${sum}}`,
    x: '15%',
    y: 'center',
    textStyle: {
      rich: {
        a: {
          width: getVwSize(110),
          fontSize: getVwSize(26),
          color: '#999',
          align: 'center'
        },
        c: {
          width: getVwSize(110),
          fontSize: getVwSize(38),
          color: '#333',
          align: 'center',
          padding: [getVwSize(10), 0, 0, 0]
        }
      }
    }
  },
  tooltip: {
    show: true,
    textStyle: {
      fontSize: getVwSize(20),

    }

  },
  grid: {
    top: '0%',
    left: '0%',
    right: '0%',
    bottom: '0%',
    // containLabel: true,
  },
  legend: {
    type: data.length > 10?'scroll':'plain',
    show: true,
    icon: 'circle',
    pageIconSize: getVwSize(18), // 分页按钮大小
    pageButtonGap: getVwSize(18),
    pageTextStyle: {      // 分页页码样式
      color: '#6495ed',
      fonrSize: getVwSize(20),
    },
    pageIconColor: '#6495ed',       // 分页按钮激活颜色
    pageIconInactiveColor: '#ccc',   // 分页按钮未激活颜色
    // orient: 'vertical',
    // width:50,
    top: "top",
    left: '50%',
    itemWidth: getVwSize(20),
    itemHeight: getVwSize(20),
    itemGap: getVwSize(20),
    formatter: function (name) {
      let obj = data.find(v => v.name == name)
      return `{title|${name}：}{value|${obj.value}家  占${(obj.value/sum*100).toFixed(2)}%}`
    },
    textStyle: {
      rich: {
        title: {
          fontSize: getVwSize(20),
          lineHeight: getVwSize(20),
          color: "#666"
        },
        value: {
          fontSize: getVwSize(20),
          lineHeight: getVwSize(20),
          color: "#333"
        }
      }
    },
  },
  series: [
    {
      name: '',
      type: 'pie',
      clockWise: false,
      center: ["25%", "50%"],
      radius: ['40%', '68%'],
      hoverAnimation: false,
      // color: colors,
      label: {
        show: false
      },
      itemStyle: {
        borderWidth: getVwSize(4),
        borderColor: '#fff'
      },
      data: data
    }
  ],
}

setOptions(option)
}

onMounted(() => {
  initChart()
})

// 监听dataSource
watch(
  () => props.dataSource,
  (newValue, oldValue) => {
    // console.log('watch', newValue, oldValue);
    initChart()
  },
  { deep: true, immediate: true }
)
</script>
<style scoped lang="scss">
.echart-box {}
</style>
