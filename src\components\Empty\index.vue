<script setup lang="ts">
import emptyImg from "@/assets/public/icon_nodata.png";
const props = defineProps({
  description: {
    type: String,
    default: "虽然这里暂时没有数据，但是探索的乐趣才刚刚开始",
  },
  imageWidth: {
    type: String,
    default: '45%',
  },
});
</script>

<template>
  <div class="empty flex items-center">
    <!-- <div v-show="loading" class="loading">
      <van-loading color="#1989fa" />
    </div> -->
    <van-empty :image="emptyImg" :image-size="[imageWidth, 'auto']" >
      <slot>
        <div class="text-24px text-#999 text-center leading-1.6em">虽然这里暂时没有数据<br>但是探索的乐趣才刚刚开始</div>
      </slot>
    </van-empty>
  </div>
</template>

<style lang="less" scoped>
.empty {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;

  .loading {
    width: 50vw;
    height: 50vw;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  :deep(.van-empty__description){
    text-align: center;
    font-size:28px;
    color:#999;
  }
}
</style>
