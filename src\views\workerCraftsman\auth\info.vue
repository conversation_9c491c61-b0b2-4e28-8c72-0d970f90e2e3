<template>
    <div class="info bg-[#F5F5F5] h-fit min-h-screen">
        <div class="banner relative">
            <!-- 判断审核状态 -->
            <img loading="lazy" src="@/assets/workerCraftsman/auth/auth_success_banner.jpg" class="w-full" />
        </div>
        <div
            class="form px-30px pb-50px bg-[#fff] -mt-20px mx-30px -translate-y-62px rounded-24px pt-47px overflow-hidden">
            <div class="header flex justify-between items-center">
                <div class="title text-32px text-[#333] pl-20px font-medium active">
                    {{ infoColumn[type].title }}</div>
                <!-- 普通模式-工匠切换 -->
                <div
                    class="types flex w-270px bg-white border-[1px] border-[#FF864C] border-solid text-[#FF864C] rounded-[26px]">
                    <div class="tab w-140px h-50px flex items-center z-1 box-border justify-center text-[28px] "
                        :class="{ 'active-btn text-[#fff] z-2  rounded-[26px]': type === item.value }"
                        @click="changeType(item.value)" v-for="item, index in typeList" :key="index">
                        {{ item.name }}
                    </div>
                </div>
            </div>
            <div class="infos" v-if="infos?.auditStatus === 'pass'">
                <div v-for="item, index in infoColumn[type].columns" :key="index">
                    <div class="cell mt-42px flex"
                        v-if="item.props !== 'evidentiaryMaterial' || infos.dataSources === 'staffIdentification'">
                        <div class="label text-30px text-[#666] mr-20px w-138px">{{ item.label }}: </div>
                        <div class="content flex-1">
                            <template v-if="item.type === 'img'">
                                <div v-if="infos[item.props]">
                                    <div class="flex flex-wrap">
                                        <img loading="lazy" class="mr-10px object-cover w-142px h-142px rounded-15px"
                                            @click="previewImages(infos[item.props], index)"
                                            v-for="img, index in infos[item.props]" :src="img" :key="index" />
                                    </div>
                                </div>
                                <div v-else class="text-30px text-[#333]">-</div>
                            </template>
                            <div class="text-30px text-[#333]" v-else>
                                {{ infos[item.props] ? infos[item.props] : '-' }}
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <div v-else>
                <div class="toAuth m-auto w-full text-center mt-150px">
                    <img loading="lazy" src="@/assets/workerCraftsman/auth/auth_tips.png" class="w-133px  mb-50px">

                    <div class="text-30px text-[#666]" v-if="!infos">请先去进行{{ type === 'worker' ? '劳模'
                        : '工匠'
                    }}身份认证
                    </div>

                    <div class="text-30px text-[#666]" v-else-if="infos.auditStatus === 'wait'">您提交的{{ type ===
                        'worker' ?
                        '劳模'
                        : '工匠' }}信息正在审核中
                    </div>
                    <div class="text-30px text-[#666]" v-else-if="infos.auditStatus === 'return'">您提交的{{ type ===
                        'worker' ?
                        '劳模'
                        : '工匠' }}信息认证失败,请重新提交
                    </div>
                    <div class="text-30px text-[#666]" v-else-if="infos.auditStatus === 'refuse'">您提交的{{ type ===
                        'worker' ?
                        '劳模'
                        : '工匠' }}信息认证失败
                    </div>
                </div>
            </div>
            <div class="resubmit_btn relative m-auto w-75% mt-45px"
                v-if="!infos || infos?.auditStatus === 'return' || infos?.auditStatus === 'wait'">
                <img loading="lazy" src="@/assets/workerCraftsman/auth/page_btn.png" class="w-full block" />
                <div class="absolute left-50% top-50% -translate-50% leading-none
                text-34px text-[#fff] font-medium" @click="toPage">
                    {{ !infos ? '去认证' : infos?.auditStatus === 'return' ? '重新提交' : '去查看' }}
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { showImagePreview } from 'vant';
import { checkAuth } from '@/api/workerCraftsman';
import { judgeStaticUrl } from '@/utils/utils'
import { useDictionary } from '@/store/modules/dictionary';
const dictionary = useDictionary();

onMounted(() => {
    if (route.query.type) type.value = route.query.type === '0' ? 'worker' : 'craftsman'
    getInfos(route.query.type as string)
})
const router = useRouter()
const route = useRoute()
const type = ref('worker')
const infoColumn = reactive({
    // 劳模
    'worker': {
        title: '劳模资料',
        columns: [
            {
                label: '劳模姓名',
                props: 'userName'
            },
            {
                label: '劳模头像',
                props: 'avatar',
                type: 'img'
            },
            {
                label: '联系电话',
                props: 'phone'
            },
            {
                label: '身份证号',
                props: 'identityCardNumber'
            },
            {
                label: '所属工会',
                props: 'companyName'
            },
            {
                label: '性别',
                props: 'genderName'
            },
            {
                label: '民族',
                props: 'nationalityName'
            },
            {
                label: '出生年月',
                props: 'dateOfBirth'
            },
            {
                label: '文化程度',
                props: 'educationName'
            },
            {
                label: '政治面貌',
                props: 'politicsStateName'
            },
            {
                label: '工作单位',
                props: 'workUnitName'
            },
            {
                label: '何时获得劳模称号',
                props: 'whenModelWorker'
            },
            {
                label: '所属类别',
                props: 'typeName'
            },
            {
                label: '个人摘要',
                props: 'personalStyle'
            },
            {
                label: '证明材料',
                props: 'evidentiaryMaterial',
                type: 'img'
            }
        ],
        status: ''
    },
    // 工匠
    'craftsman': {
        title: '工匠资料',
        columns: [
            {
                label: '工匠姓名',
                props: 'userName'
            },
            {
                label: '工匠头像',
                props: 'avatar',
                type: 'img'
            },
            {
                label: '联系电话',
                props: 'phone'
            },
            {
                label: '身份证号',
                props: 'identityCardNumber'
            },
            {
                label: '性别',
                props: 'genderName'
            },
            {
                label: '工作单位',
                props: 'companyName'
            },
            {
                label: '所属类别',
                props: 'typeName'
            },
            {
                label: '个人摘要',
                props: 'personalStyle'
            },
            {
                label: '资格证书',
                props: 'evidentiaryMaterial',
                type: 'img'
            }
        ],
        status: ''
    }
})

const infos = ref<any>(null)

const getInfos = async (modelType: string) => {
    const { code, data } = await checkAuth(modelType)
    if (code === 200 && data) {
        // 当前审核状态
        if (!data || data.auditStatus !== 'pass') return infos.value = {
            auditStatus: data?.auditStatus || ''
            // 未认证 成功
        }
        infos.value = data
        infos.value.avatar = infos.value.avatar ? [judgeStaticUrl(infos.value.avatar, true)] : []
        infos.value.evidentiaryMaterial = infos.value.evidentiaryMaterial ? infos.value.evidentiaryMaterial.split(',').map((item: string) => judgeStaticUrl(item)) : []
        // 格式化下拉展示字典
        infos.value.genderName = dictionary.getDictionaryOpt?.['gender'].find((item: any) => item.value === data.gender)?.label || ''
        infos.value.nationalityName = dictionary.getDictionaryOpt?.['nation'].find((item: any) => item.value === data.nationality)?.label || ''
        infos.value.educationName = dictionary.getDictionaryOpt?.['modelEducation'].find((item: any) => item.value === data.education)?.label || ''
        infos.value.politicsStateName = dictionary.getDictionaryOpt?.['politicsState'].find((item: any) => item.value === data.politicsState)?.label || ''
    }
    else {
        infos.value = null
    }
}
const previewImages = (images: string[], startPosition: number) => {
    showImagePreview({
        images,
        startPosition,
        closeable: true,
    });
}
// 类型列表
const typeList = ref([
    {
        name: '劳 模',
        value: 'worker'
    },
    {
        name: '工 匠',
        value: 'craftsman'
    },
])
const changeType = (value: string) => {
    type.value = value
    let modelType = value === 'worker' ? '0' : '1'
    getInfos(modelType)
}
const toPage = () => {
    if (infoColumn[type.value].status === 'return') {
        router.push({
            path: '/workerCraftsman/auth/form',
            query: {
                type: type.value === 'worker' ? '0' : '1',
                tag: 'reSubmit'
            }
        })
    }
    else if (!infoColumn[type.value].status) {
        router.push({
            path: '/workerCraftsman/auth/form',
            query: {
                type: type.value === 'worker' ? '0' : '1',
            }
        })
    }
    else if (infoColumn[type.value].status === 'wait') {
        router.replace({
            path: '/workerCraftsman/auth/status',
            query: {
                type: type.value === 'worker' ? '0' : '1',
            }
        })
    }
}
</script>

<style scoped lang="scss">
.form {
    .title {
        position: relative;
    }

    .title::after {
        width: 8px;
        height: 31px;
        background-color: #2970FF;
        border-radius: 4px;
        display: block;
        content: ' ';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
    }

    .active::after {
        background-color: #F48F39;
    }
}

.active-btn {
    background: linear-gradient(to right, #FF4B2F, #FF864C);
}
</style>