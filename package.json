{"name": "nanc-trade-union-app", "version": "0.0.0", "private": true, "type": "module", "scripts": {"bootstrap": "pnpm install", "build": "vue-tsc && vite build", "build:pro": "vite build --mode production", "dev": "vite --mode development", "preview": "vite preview"}, "dependencies": {"@lucky-canvas/vue": "^0.1.11", "@unocss/preset-legacy-compat": "^0.64.1", "@vant/area-data": "^2.0.0", "@vitejs/plugin-legacy": "5.4.3", "@vueuse/core": "^12.0.0", "@web-tracing/vue3": "^2.0.4", "axios": "^1.7.7", "core-js": "^3.40.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "hls.js": "^1.5.17", "lodash-es": "^4.17.21", "mux.js": "^7.1.0", "pinia": "2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "prettier": "^3.3.3", "prettier-plugin-packagejson": "^2.5.1", "qs": "^6.13.0", "regenerator-runtime": "^0.14.1", "swiper": "^11.1.15", "vant": "^4.9.8", "videojs-contrib-hls": "^5.15.0", "videojs-flvjs": "^0.3.1", "vite-plugin-vue-setup-extend": "^0.4.0", "vue": "^3.3.8", "vue-picture-cropper": "^0.7.0", "vue-router": "^4.4.5", "vue3-baidu-map-gl": "^2.6.2", "vue3-seamless-scroll": "^2.0.1"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/lodash-es": "^4.17.12", "@types/node": "^22.7.5", "@types/qs": "^6.9.15", "@vitejs/plugin-vue": "^4.5.0", "less": "^4.2.0", "postcss-loader": "^8.1.1", "postcss-px-to-viewport": "^1.1.1", "sass-embedded": "^1.81.0", "typescript": "^5.2.2", "unocss": "^0.64.1", "unplugin-auto-import": "^0.18.3", "vite": "^5.0.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-legacy-swc": "^1.2.3", "vite-plugin-pwa": "^0.17.4", "vue-tsc": "^1.8.22"}}