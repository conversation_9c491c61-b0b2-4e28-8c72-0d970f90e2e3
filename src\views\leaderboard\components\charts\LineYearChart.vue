<template>
  <div class="echart-box" style="width:100%;height:100%">
    <div ref="echartRef" class="w-full h-full"></div>
  </div>
</template>

<script lang="ts" setup>
// 饼图
import { useECharts } from '@/utils/useECharts'
import { Ref, ref, watch, onMounted, inject } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  dataSource: {
    type: Object,
    default: {
      x: [
        '7月',
        '8月',
        '9月',
        '10月',
        '11月',
        '12月',
      ],
      y: ['1411', '1026', '1200', '1400', '1008', '1411',],
    },
  }, //color
})

const echartRef = ref<HTMLDivElement | null>(null)
const { setOptions } = useECharts(echartRef as Ref<HTMLDivElement>)

const initChart = () => {
  var e = document.body.clientWidth

  let data = { x: [], y: [], y2: [] }
  data.x = props.dataSource?.x
  data.y = props.dataSource?.y

  // 绘制图表
  var option = {
    tooltip: {
      show: true,
      trigger: 'axis',
    },
    grid: {
      top: '22%',
      left: '0%',
      right: '3%',
      bottom: '0%',
      containLabel: true,
    },
    legend: {
      show: true,
      icon: 'square',
      orient: 'horizontal',
      top: '0%',
      right: 'center',
      width:'100%',
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 15,
      textStyle: {
        // color: '#FFFFFF'
        color: '#666666',
        fontSize: 12,
      },
    },
    xAxis: [
      {
        type: 'category',
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitArea: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        boundaryGap: true,
        axisLabel: {
          show: true,
          interval: 0,
          // rotate: 30,
          fontSize: 12,
          lineHeight: 15,
          margin: 10,
          color: '#999',
          // hideOverlap: true,
        },
        data: data.x,
      },
    ],
    yAxis: [
      {
        name: '人数(万)',
        nameTextStyle: {
          color: '#999',
        },
        type: 'value',
        min: 0,
        minInterval: 1,
        splitLine: {
          show: true,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#E6E6E6',
          },
        },
        axisLabel: {
          // margin: 20 / 1920 * e,
          textStyle: {
            color: '#999',
          },
        },
        axisTick: {
          show: false,
        },
      },
      {
        name: '发布量',
        nameTextStyle: {
          color: '#999',
        },
        type: 'value',
        min: 0,
        minInterval: 1,
        splitLine: {
          show: true,
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#E6E6E6',
          },
        },
        axisLabel: {
          // margin: 20 / 1920 * e,
          textStyle: {
            color: '#999',
          },
        },
        axisTick: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: '发布量',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        showSymbol: false,
        yAxisIndex:1,
        z: 1,
        label: {
          show: false,
        },
        lineStyle: {
          width: 2,
          shadowColor: 'rgba(48,224,189,0.3)',
          shadowBlur:2,
          shadowOffsetY: 3
        },
        itemStyle: {
          normal: {
            color: '#30E0BD',
          },
        },
        tooltip: {
          show: false,
        },
        data: data.y,
      },
      {
        name: '访问人数',
        type: 'line',
        yAxisIndex:0,
        smooth: true,
        symbol: 'circle',
        showSymbol: false,
        z: 3,
        label: {
          show: false,
          position: 'top',
        },

        lineStyle: {
          width: 2,
          shadowColor: 'rgba(244,152,250,0.6)',
          shadowBlur: 2,
          shadowOffsetY: 2
        },
        itemStyle: {
          normal: {
            color: '#F498FA',
          },
        },
        data: ['12', '10', '10', '14', '16', '130', '140', '108', '141',],
      },
      {
        name: '注册用户',
        type: 'line',
        yAxisIndex:0,
        smooth: true,
        symbol: 'circle',
        showSymbol: false,
        z: 3,
        label: {
          show: false,
          position: 'top',
        },

        lineStyle: {
          width: 2,
          shadowColor: '#FADB89',
          shadowBlur: 2,
          shadowOffsetY: 2
        },
        itemStyle: {
          normal: {
            color: '#FFCB40',
          },
        },
        data: ['2', '102', '120', '114', '161', '30', '40', '108', '41',],
      },
      {
        name: '工会会员',
        type: 'line',
        yAxisIndex:0,
        smooth: true,
        symbol: 'circle',
        showSymbol: false,
        z: 3,
        label: {
          show: false,
          position: 'top',
        },

        lineStyle: {
          width: 2,
          shadowColor: 'rgba(255, 111, 76, 0.6)',
          shadowBlur: 2,
          shadowOffsetY: 2
        },
        itemStyle: {
          normal: {
            color: '#FF6F4C',
          },
        },
        data: ['42', '110', '120', '134', '106', '30', '40', '80', '89',],
      },
    ],
  }
  setOptions(option)
}

onMounted(() => {
  initChart()
})

// 监听dataSource
watch(
  () => props.dataSource,
  (newValue, oldValue) => {
    // console.log('watch', newValue, oldValue);
    initChart()
  },
  { deep: true, immediate: true }
)
</script>
<style scoped lang="scss">
.echart-box {}
</style>
