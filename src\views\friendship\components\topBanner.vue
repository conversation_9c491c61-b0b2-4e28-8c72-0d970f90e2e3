<template>
    <div class="topBanner w-full">
        <div class="banner relative w-full">
            <!-- <img loading="lazy" src="@/assets/friendShip/home_banner.png" class="w-full h-full"> -->
            <van-swipe class="w-full h-full" :autoplay="3000" lazy-render>
                <van-swipe-item v-for="(image, index) in categoryList" :key="index">

                    <img loading="lazy" style="object-fit: cover;"
                        @click="toDetail({ activityMode: 'friendship', activityId: image.pageLink, externalLink: 'n' }, { isSingle: true, singleStatus: props.singleStatus })"
                        :src="judgeStaticUrl(image?.indexCover)" class="w-full h-full">
                </van-swipe-item>
                <van-swipe-item v-if="categoryList.length == 0">
                    <img loading="lazy" src="@/assets/friendShip/home_banner.png" class="w-full h-full">
                </van-swipe-item>

                <template #indicator="">
                </template>
            </van-swipe>
            <div class="right-icon flex  absolute" v-if="props.showIcon">
                <span class="icon-box  mr-15px relative">
                    <img loading="lazy" @click="toPage('/friendship/Notice')"
                        style="filter: drop-shadow(#D76AAC 2px 4px 6px);" src="@/assets/friendShip/message_icon.png" />
                    <span class="w-14px h-14px bg-red absolute rounded-50% right-[-1px] top-[-1px]"
                        v-if="props.unreadMessageCount > 0"></span>
                </span>
                <van-popover v-model:show="showPopover" placement="bottom-end">
                    <div class="pop_list">
                        <div class="pop_item" @click="toPage('/friendship/myFollowList', { nav: 0 })">
                            <span>我的关注</span><van-icon name="arrow" />
                        </div>
                        <div class="pop_item" @click="toPage('/friendship/myFollowList', { nav: 1 })">
                            <span>我的粉丝</span><van-icon name="arrow" />
                        </div>
                        <div class="pop_item" @click="toPage('/friendship/MyApply')">
                            <span>申请记录</span><van-icon name="arrow" />
                        </div>
                        <div class="pop_item" @click="toPage('/friendship/blackList')">
                            <span>我的黑名单</span><van-icon name="arrow" />
                        </div>
                    </div>

                    <template #reference>
                        <img loading="lazy" style="filter: drop-shadow(#D76AAC 2px 4px 6px);"
                            src="@/assets/friendShip/more_icon.png" class="w-33px ml-34px" />
                    </template>
                </van-popover>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { judgeStaticUrl } from '@/utils/utils';
import { toDetail } from "@/hooks/useValidator";
const router = useRouter()
const showPopover = ref(false);
const props = defineProps({
    showIcon: {
        type: Boolean,
        default: true
    },
    unreadMessageCount: {
        type: Number,
        default: 0
    },
    singleStatus: {
        type: String,
        default: 'n'
    },
    categoryList: {
        type: Array,
        default: []
    }

})
//详情
function toPage(path, query) {
    router.push({
        path,
        query
    })
}

</script>

<style lang="scss" scoped>
:deep(.van-popover__content) {
    .pop_list {
        width: 187px;
        box-shadow: 0px 1px 25px 2px rgba(88, 88, 88, 0.21);
        border-radius: 16px;
    }

    .pop_item {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        padding: 17px 12px 12px 15px;
        border-bottom: 1px solid #EBEBEB;
        font-weight: 400;
        font-size: 26px;
        color: #333333;
    }
}

.topBanner {
    .banner {
        height: 302px;
        width: 100%;

        .right-icon {
            right: 30px;
            top: 30px;

            img {
                width: 32px;
                margin-left: 17px;
            }
        }
    }

}
</style>