<template>
  <div
    class="absolute top-0 bg-[#5BA5FF] text-[#FFFFFF] w-full flex"
    :class="$style['scroll-index']"
  >
    <div class="z-1 bg-[#5BA5FF] h-full">
      <img loading="lazy" :src="voice" />
    </div>

    <div class="scroll-text w-full whitespace-nowrap">
      {{ indexStr }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { isNumber, join, map } from 'lodash-es';
import voice from '@/assets/position/voice.png';
import { h5PositionTopStatistics } from '@/api/position';

const indexs = ref<Recordable[]>([
  { title: '今日浏览量', value: '-', unit: '次', key: 'today' },
  { title: '本月浏览量', value: '-', unit: '次', key: 'thisMonth' },
  { title: '累计浏览量', value: '-', unit: '次', key: 'accumulate' },
]);

const indexStr = computed(() =>
  join(
    map(unref(indexs), v => `${v.title}：${v.value}（次）`),
    '  '
  )
);

function formatVal(val: number) {
  if (!isNumber(val)) return 0;
  const fmt = new Intl.NumberFormat('zh-CN');
  return fmt.format(val);
}

onMounted(async () => {
  const dataRecord = await h5PositionTopStatistics();

  indexs.value = map(unref(indexs), v => ({ ...v, value: formatVal(dataRecord[v.key]) }));
});
</script>

<style lang="less" module>
.scroll-index {
  :global {
    overflow: hidden; /* 隐藏超出部分 */
    display: flex; /* 横向排列内容 */
    align-items: center; /* 垂直对齐 */

    .scroll-text {
      animation: scroll-horizontal 10s linear infinite;
      position: relative; /* 滚动内容独立定位 */
      flex: 1; /* 占据剩余空间 */
      z-index: 0; /* 确保滚动内容在图片下方 */
    }
  }
}
</style>

<style>
@keyframes scroll-horizontal {
  0% {
    transform: translateX(100%); /* 从右侧开始 */
  }
  100% {
    transform: translateX(-100%); /* 移动到左侧 */
  }
}
</style>
