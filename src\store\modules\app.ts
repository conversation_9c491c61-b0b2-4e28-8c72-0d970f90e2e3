
import { defineStore } from 'pinia';

interface appStore {
    includes: any[];
    refreshApiList: any[];
}

  
export const useAppStore = defineStore('app-nanconApp', {
    state: ():appStore => {
      return {
        includes: [],
        refreshApiList: []
      }
    },
    actions: {
      // 添加需要缓存的组件
      setincludes(payload:any) {
        this.includes = [...new Set([...this.includes, payload.name])]
      },
      // 添加需要刷新的api
      addRefreshApi(payload:any) {
        const refreshApiList = this.refreshApiList || []
        const current:any = refreshApiList.find((item:any) => item.pageName === payload.pageName)
        if (current) {
          const addFunNames = payload.funsName.filter((item:any) => !current.funsName.includes(item))
          current.funsName.push(...addFunNames)
        } else {
          this.refreshApiList.push({
            pageName: payload.pageName,
            funsName: payload.funsName
          })
        }
      },
      // 删除需要刷新的api
      removeRefreshApi(pageName:any) {
        const refreshApiList = this.refreshApiList || []
        const index = refreshApiList.findIndex((item:any) => item.pageName === pageName)
        index !== -1 && this.refreshApiList.splice(index, 1)
      },
      //删除缓存的组件
      removeIncludes(name:any) {
        const index = this.includes.findIndex((item:any) => item === name)
        index !== -1 && this.includes.splice(index, 1)
      },
      // 
      removeIncludesAll() {
        this.includes = []
      }
    }
  })