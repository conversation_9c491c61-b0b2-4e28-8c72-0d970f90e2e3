<template>
    <van-popup v-model:show="props.show" position="center" round class="popup box-border flex flex-col items-center"
        @click-overlay="close">
        <div class="popup-content relative">
            <img loading="lazy" src="@/assets/friendShip/apply_contact_bg.png" class="w-100%" />
            <div class="text-content absolute top-45% left-8% right-8%">
                <div class="text-#444 text-32px text-center">
                    向Ta申请联系方式，<br> 对方同意后可获得联系方式
                </div>
                <div class="controll-btn flex justify-between items-center mt-48px">
                    <div @click="close"
                        class="text-34px text-#333 mr-24px flex-1 h-80px rounded-40px leading-none flex items-center justify-center bg-[#F3F3F3]">
                        再想想</div>
                    <div @click="confirm"
                        class="text-34px text-#fff flex-1 h-80px rounded-40px confirm-btn leading-none flex items-center justify-center">
                        确认发送</div>
                </div>
            </div>
        </div>
    </van-popup>
</template>
<script lang="ts" setup>
import { applyContact } from '@/api/friendShip'
import { showToast } from 'vant';

const props = defineProps({
    show: {
        type: Boolean,
        default: false
    },
    tips: {
        type: String,
        default: '确认执行吗？',
    },
    showCancel: {
        type: Boolean,
        default: true,
    },
    showConfirm: {
        type: Boolean,
        default: true,
    },
    cancelText: {
        type: String,
        default: '取消',
    },
    confirmText: {
        type: String,
        default: '确认',
    },
    aimUserId: {
        type: String,
        default: '',
        required: true
    }
})
const emit = defineEmits(['update:show'])
const close = () => {
    emit('update:show', false)
}
const confirm = async () => {
    const { code, message } = await applyContact({
        aimUserId: props.aimUserId
    })
    if (code === 200) {
        close()
        showToast('发送成功')
    }

    else {
        showToast(message)
        setTimeout(() => {
            close()
        }, 1000)
    }
}
</script>
<style lang="scss" scoped>
.popup {
    width: calc(100% - 200px);
}

.confirm-btn {
    background: linear-gradient(90deg, #FAA8AB, #FF7097);
}
</style>