<template>
  <div class="evaluate" v-for="(item, index) of data" :key="index">
    <div class="flex mb-20px">
      <img loading="lazy" src="@/assets/public/head_default.png" alt="" class="w-76px h-76px rounded-[50%]" />
      <div class="topic-right pl-33px flex-1">
        <div class="flex items-center">
          <div class="flex-1">{{ item.userName }}</div>
          <slot name="score" :index="index" :item="item"></slot>
        </div>
        <div class="text-#999999 text-20px mt-16px">{{ item.createTime }}</div>
        <slot name="shortContent" :index="index" :item="item"></slot>
      </div>

    </div>
    <slot name="longContent" :index="index" :item="item"></slot>
  </div>
</template>
<script lang="ts" setup>
const props = defineProps({
  data: {
    type: Array,
    default: [],
  },
});
</script>
<style lang="scss" scoped></style>
