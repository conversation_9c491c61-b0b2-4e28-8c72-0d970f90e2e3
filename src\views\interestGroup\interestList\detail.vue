<template>
    <div class="detail relative w-100%">
        <img loading="lazy"
            :src="activityDetail?.appCover != '' ? useStore.getPrefix + activityDetail?.appCover : bannerImg" alt=""
            class="h-400px absolute top-0 w-full">
        <div class="relative top-350px bg-#fff content w-full ">
            <div class="p-32px">
                <div class="text-36px mb-20px">{{ activityDetail?.activityName }}</div>
                <div class="text-28px text-#666 mb-30px">
                    <div class="mb-15px flex items-start leading-40px">
                        <img loading="lazy" src="@/assets/interest/icon_sign.png" class="w-30px mr-10px">
                        报名时间：{{
                            dayjs(activityDetail?.signUpInfo?.signUpStartTime).format('YYYY-MM-DD') }}至{{
                            dayjs(activityDetail?.signUpInfo?.signUpEndTime).format('YYYY-MM-DD') }}
                    </div>
                    <div class="mb-15px flex items-start leading-40px"><img loading="lazy"
                            src="@/assets/interest/icon_act.png" class="w-30px mr-10px">活动时间：{{
                                dayjs(activityDetail?.activityStartTime).format('YYYY-MM-DD')
                            }}至{{ dayjs(activityDetail?.activityEndTime).format('YYYY-MM-DD') }}</div>
                    <div class="mb-15px flex items-start leading-40px"><img loading="lazy"
                            src="@/assets/interest/icon_add.png" class="w-30px mr-10px">{{
                                activityDetail.activityAddress || '暂无' }}</div>
                    <div class="mb-15px flex items-start leading-40px" v-if="activityDetail?.signUpInfo"><img
                            loading="lazy" src="@/assets/interest/icon_signd.png" class="w-30px mr-10px">已报名：<span
                            class="text-#5AA4FF">{{ activityDetail?.signUpInfo?.signUpCount }}</span> / {{
                                activityDetail?.signUpInfo?.maxCount }}</div>
                    <div class="mb-15px flex items-start leading-40px" v-if="activityDetail?.signUpInfo"><img
                            loading="lazy" src="@/assets/interest/icon_inte.png" class="w-30px mr-10px">报名所需：<span
                            class="text-#5AA4FF">{{ activityDetail?.signUpInfo?.enrollmentScore }}积分</span> </div>
                </div>
                <groupInfo :data="activityDetail" />
            </div>
            <div class="detail-contain pb-120px">
                <!-- w-90% -->
                <van-tabs v-model:active="Data.tab.active" class="w-100%" swipeable title-active-color="#5AA4FF"
                    @change="chooseTab">
                    <van-tab v-for="(item, index) in Data.tab.nav" :title="item" class="text-34px" :key="index">
                        <!-- 内容 {{ index }} -->
                    </van-tab>
                </van-tabs>
                <div class="px-50px py-25px text-#333 ">
                    <div v-if="Data.tab.active == 0" class="text-28px leading-normal content">
                        <div v-html="activityDetail?.activityContent || '暂无详情'"></div>
                    </div>
                    <div v-else>
                        <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore"
                            ref="loadMoreRef">
                            <topicList :data="Data.topicList" />
                        </refreshList>

                    </div>
                </div>
            </div>
        </div>
        <div class="fixed fix-box bg-#fff h-110px flex w-100% bottom-0">
            <div class="flex text-22px text-#333 justify-around"
                :class="activityDetail?.signUpInfo ? 'w-35%' : 'w-100%'">
                <div class="flex flex-col justify-center" @click="share">
                    <img loading="lazy" src="@/assets/interest/icon_share.png" alt="" class="w-40px mb-12px">
                    <div>分享</div>
                </div>
                <div class="flex flex-col justify-center" v-if="activityDetail?.signUpInfo" @click="toPage()">
                    <img loading="lazy" src="@/assets/interest/icon_comment.png" alt="" class="w-40px mb-12px">
                    <div>评论</div>
                </div>
            </div>
            <div class=" w-65% flex items-center justify-center" @click="toSign()" v-if="activityDetail?.signUpInfo">
                <div class="join-sign text-34px text-#fff text-center">
                    {{ activityDetail.status == 'end' ? '活动已结束' : signUpRecord ? '我的报名' : '报名参与' }}</div>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import bannerImg from '@/assets/interest/banner2.jpg'
import groupInfo from '@/components/List/groupInfo.vue';
import topicList from "@/components/List/topicList.vue";
import router from '@/router';
import { applicationRecord, signUp } from '@/api/activity';
import { useRoute } from "vue-router";
const route = useRoute();
import { useUserStore } from '@/store/modules/user';
const useStore = useUserStore();
import { commentsList, detailByGroupId } from '@/api/interestGroup';
import refreshList from '@/components/refreshList/index.vue';
import dayjs from 'dayjs'
import { showDialog, showFailToast, showSuccessToast } from 'vant';
import utils from '@/utils/utils';
import { computed } from "vue";
import { activityValidator, checkIntegral } from "@/hooks/useValidator";
const Data = ref({
    tab: { active: 0, nav: ['详情', '动态', '评论'] },
    detail: {},
    joinInfo: {},
    topicList: [],
    pageNum: 1,
    type: ''
})
const signUpRecord = ref()



//分享
function share() {
    utils.SharedWorker(activityDetail.value?.activityName, window.location.href)
}
//报名
async function toSign() {
    if (!activityValidator()) {
        return
    }

    if (activityDetail.value.customerType === 'interestGroup' && !activityDetail.value.joinState) {
        showFailToast("您还未加入该兴趣小组，请先加入")
        return
    }
    if (signUpRecord.value) {
        router.push(`/activityHome/signUpActivity/record?activityId=${activityDetail.value.activityId}`)
        return
    }

    const personMax = activityDetail.value.signUpInfo.maxCount
    const signUpCount = activityDetail.value.signUpInfo.signUpCount
    if (personMax <= signUpCount) {
        showDialog({
            title: '温馨提示',
            message: '报名人数已满~',
            confirmButtonText: '我知道了',
            lockScroll: false,
        });
        return
    }
    if (! await checkIntegral()) {
        return;
    }
    //报名活动，但不需要填写报名表
    if (activityDetail.value?.signUpInfo?.writeFlag === 'n') {
        signUp({
            activityId: activityDetail.value.activityId,
            platform: "app",
        }).then(res => {
            if (res.code == 200) {
                getSignUpRecord()
                showSuccessToast('提交成功')

            } else {
                showFailToast(res.message)
            }
        })
        return
    }
    router.push('/activityHome/signUpActivity/form')
}

// 获取报名记录
const getSignUpRecord = async () => {
    try {
        const { code, data } = await applicationRecord({
            activityId: activityDetail.value?.activityId
        })
        if (code === 200) {
            signUpRecord.value = data
        }
    } catch (error) {
        console.error('获取报名记录失败:', error)
    }
}

//评论
function toPage() {
    if (!activityDetail.value?.joinState) {
        showFailToast("您还未加入该兴趣小组，请先加入")
        return
    }
    if (!signUpRecord.value) {
        showFailToast('您还未参与该活动，暂无法评论哦~')
        return
    }
    if (signUpRecord.value.state == 'wait') {
        showFailToast("您的报名申请正在审核中，请耐心等待~")
        return
    }
    //   if(signUpRecord.value.state=='refuse') {
    //     showFailToast("您的报名申请被拒绝，暂无法评论！")
    //     return
    //   }

    router.push({
        path: '/interestComment',
        query: {
            activityId: activityDetail.value?.activityId,
            groupId: activityDetail.value?.groupId
        }
    })
}
// 计算属性
const activityDetail = computed(() => {
    return useStore.activityDetail || {};
});
const loadMoreRef = ref(null)
//动态列表
function getcommentsList() {
    commentsList({
        dataSources: "group", //评论来源 : group:小组  activity：活动
        commentType: Data.value.type,//评论类型 （groupComment：小组评价，dynamic：小组动态，activityComment：活动评论，activityDynamic：活动评价 ）
        groupId: activityDetail.value?.groupId,
        pageSize: 10,
        pageNum: 1,
        sortType: 'desc',
        orderBy: 'create_time'
    }).then(res => {
        if (res.code == 200) {
            if (Data.value.pageNum === 1) Data.value.topicList = [];
            Data.value.topicList = Data.value.topicList.concat(res.data);
            //重置刷新状态及 判断是否加载完成
            if (loadMoreRef.value) {
                loadMoreRef.value.onLoadSuc(Data.value.topicList.length, res.total);
            }
        }
    })
}
//成员列表
// function getUserList() {
//     h5GroupUserList({
//         groupId: route.query.groupId,
//         pageSize:10,
//         pageNum:1
//     }).then(res=>{
//         if(res.code==200){
//             res.data.map(el=>{
//                 el.isChoose=false;
//             })
//             if (Data.value.pageNum === 1) Data.value.menberList = [];
//             Data.value.menberList = Data.value.menberList.concat(res.data);
//             //重置刷新状态及 判断是否加载完成
//             if (loadMoreRef.value) {
//                 loadMoreRef.value.onLoadSuc(Data.value.menberList.length, res.total);
//             }
//         }
//     })
// }
function chooseTab(index) {
    Data.value.pageNum = 1;
    Data.value.tab.active = index;
    if (Data.value.tab.active == 1) {
        Data.value.type = 'dynamic'
    } else if (Data.value.tab.active == 2) {
        Data.value.type = 'activityComment'
    }
    getcommentsList();
}
// 刷新
const onRefreshList = () => {
    Data.value.pageNum = 1;
    if (Data.value.tab.active == 1) {
        getcommentsList();
    } else if (Data.value.tab.active == 2) {
        // getUserList()
    }
};
// 加载更多
const onLoadMore = () => {
    Data.value.pageNum++;
    if (Data.value.tab.active == 1) {
        getcommentsList();
    } else if (Data.value.tab.active == 2) {
        // getUserList()
    }
};
//详情
function getGroupDetail() {
    detailByGroupId({
        groupId: activityDetail.value.groupId
    }).then(res => {
        if (res.code == 200) {
            console.log(res.data);

        }
    })
}
onMounted(async () => {
    if (!activityDetail.value && route.query.activityId) {
        useUserStore().setActivityDetail(route.query.activityId as string)
    }

    setTimeout(() => {
        getSignUpRecord();
    }, 1000)
    //   getGroupDetail()
})
</script>
<style lang="scss" scoped>
.detail {
    .content {
        border-radius: 30px 30px 0px 0px;
        box-sizing: border-box;
    }

    .fix-box {
        box-shadow: 0px 6px 17px 0px rgba(194, 194, 194, 0.6);
    }

    .join-sign {
        width: 450px;
        height: 78px;
        background: linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%);
        border-radius: 39px 39px 39px 39px;
        line-height: 78px;
    }

    .detail-contain {
        border-top: 18px solid #f5f5f5;
    }

    :deep(.van-tabs) {
        .van-tab {
            font-size: 30px;
            line-height: 1;
        }

        .van-tab--active {
            font-size: 32px !important;
        }

        .van-tabs__line {
            width: 40px;
        }

        .van-tabs__wrap {
            height: 78px;
        }
    }

    .content {

        image,
        img {
            max-width: 100%;
            max-height: 100%;
        }
    }
}
</style>
