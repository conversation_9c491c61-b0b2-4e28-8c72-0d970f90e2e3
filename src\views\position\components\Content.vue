<template>
  <div class="w-full z-999 absolute bottom-0"
    :class="`${showMap ? 'slide-in-bottom' : 'slide-out-bottom'} ${$style.content}`">
    <slot name="right-click" />
    <div class="h-[51vh] w-full bg-list rounded-2xl px-1 box-border">
      <Search class="mb-1" @button-click="handleSearch" @show-area="show => (showArea = show)"
        :areaCodeName="areaCodeName" />

      <div class="w-full h-[calc(100%-40px)] bg-[#fff] rounded-2xl box-border select-tab">
        <van-tabs v-model:active="active" @click="handleTabChange" class="pt-2">
          <van-tab v-for="item in dataTabs" :title="item.labelName" :name="item.value">
            <template #title>
              <div class="flex flex-col items-center justify-center text-28px">
                <img loading="lazy" :src="fixImageIcon(item)" class="w-[33px] h-[33px] mb-10px" />
                {{ item.labelName }}
              </div>
            </template>
          </van-tab>
        </van-tabs>

        <div class="pt-[20px] max-h-[calc(100%-44px)] px-2 overflow-auto box-border">
          <refreshList @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
            <!-- <van-cell v-for="item in list" :key="item">
              <van-row @click="handleClick(item)">
                <van-col :span="8" class="grid justify-center items-center">
                  <div class="w-[200px] h-[244px]">
                    <img loading="lazy" :src="item.positionCoverImage
                      ? exchangeImg(item.positionCoverImage)
                      : defaultPositionLittle
                      " class="w-full h-full object-contain rounded-10px" />
                  </div>
                </van-col>
                <van-col :span="16" class="pl-2 flex-col flex">
                  <div class="text-[#333333] text-[30px] font-bold text-left w-full truncate">
                    {{ item.positionName || '' }}
                  </div>
                  <div class="flex items-center text-[24px]">
                    <van-icon name="clock-o" />
                    <div class="line-right" :class="`${item.openState ? 'text-[#5AA4FF]' : 'text-[#f25f20]'}`">{{
                      item.openState
                        ? '开放中' : '暂未开放' }}</div>
                    <div>{{ fixOpenWeekDay(item) }}</div>
                  </div>
                  <div class="text-left text-[24px]">
                    <span v-if="item.morningOpenTime && item.morningCloseTime">
                      {{ exchangeTime(item.morningOpenTime) }} -
                      {{ exchangeTime(item.morningCloseTime) }}
                    </span>
                    &nbsp;
                    <span v-if="item.afterOpenTime && item.afterCloseTime">
                      {{ exchangeTime(item.afterOpenTime) }} -
                      {{ exchangeTime(item.afterCloseTime) }}
                    </span>
                  </div>
                  <div class="flex items-center text-left text-[24px] w-full truncate">
                    <van-icon name="location-o" />
                    <div class="line-right">{{ fixDistance(item.actualDistance) }}</div>
                    <div class="w-full flex justify-start">{{ item.address }}</div>
                  </div>
                  <div class="w-full text-[22px] block text-left multiline-text">
                    <div v-for="tag in item.venueServiceTypes" class="box-border pr-[4px] inline-block">
                      <van-tag color="#FCF0E2" class="text-[#A94800] px-[2px]">
                        {{ tag.serviceTypeName }}
                      </van-tag>
                    </div>
                  </div>
                  <div class="flex justify-start items-center">
                    <div
                      class="active:shadow-[0_0_10px_0_#5ba5ff] text-[#4297FF] mr-2 cursor-pointer border border-[#4297FF] border-solid px-[54px] rounded-xl"
                      @click.stop="handlePhoneClick(item)">电话</div>
                    <div
                      class="active:shadow-[0_0_10px_0_#5ba5ff] text-[#fff] bg-[#4297FF] cursor-pointer border border-[#4297FF] border-solid px-[54px] rounded-xl"
                      @click.stop="handleNavigationClick(item)">导航</div>
                  </div>
                </van-col>
              </van-row>
            </van-cell> -->
            <positionList :list="list" />
          </refreshList>
        </div>
      </div>
    </div>
  </div>

  <!-- 层级问题先放在这 -->
  <van-popup v-model:show="showArea" closeable close-icon="close" position="bottom" :style="{ height: '55vh' }"
    :close-on-click-overlay="false">
    <div class="p-[22px] w-full h-full box-border">
      <h3 class="text-center">阵地区域</h3>
      <div v-for="(item, index) in areaNC" class="inline-block px-[10px] box-border my-[15px]"
        :class="`${index === areaNC.length - 1 ? 'w-2/3' : 'w-1/3'} `">
        <div class="flex justify-center items-center py-[15px] rounded-3xl"
          :class="`${item.value === areaCode ? 'bg-[#5AA4FF] text-[#FFFFFF]' : 'bg-[#F2F3F5] text-[#636569]'}`"
          @click="areaCode = item.value; areaCodeName = item.label">
          {{ item.label }}
        </div>
      </div>
      <div class="bg-button-long fixed bottom-4 inset-x-0 flex justify-center items-center text-[#fff]"
        @click="handleConfirm">
        确认
      </div>
    </div>
  </van-popup>
</template>

<script lang="ts" setup>
import Search from './Search.vue';
import { useDictionary } from '@/store/modules/dictionary';
import { h5FindVenuePositionVoList } from '@/api/position';
import { concat } from 'lodash-es';
import refreshList from '@/components/refreshList/index.vue';
import { useUserStore } from '@/store/modules/user';
import positionList from './positionList.vue';

const userStore = useUserStore();

const emit = defineEmits(['data-change']);

const dictionary = useDictionary();

const props = withDefaults(
  defineProps<{
    showMap: boolean;
    lnglat: Nullable<Recordable>;
    dataTabs: Recordable[];
  }>(),
  {
    showMap: true,
    lnglat: null,
    dataTabs: () => [],
  }
);

const { showMap } = toRefs(props);

const showArea = ref<boolean>(false);

const areaCode = ref<string>();
const areaCodeName = ref<string>();

//获取列表
const loadMoreRef = ref<any>();

const searchVal = ref();

const pageNum = ref<number>(1);

const active = ref<string>('');

// list
const list = ref<Recordable[]>([]);

const areaNC = computed(
  () => [{ label: '全部', value: undefined }, ...dictionary.getDictionaryOpt?.['regionCode']] || []
);
/*****FIX START******/

const fixImageIcon = (item: Recordable) => {
  // if (!unref(active)) return item.clickIcon

  if (unref(active) === item.value) {
    return !item.value ? item.clickIcon : userStore.getPrefix + item.clickIcon;
  }

  return !item.value ? item.showIcon : userStore.getPrefix + item.showIcon;
};

/*****FIX END******/
function handleConfirm() {
  // api
  showArea.value = false;
  onRefreshList();
}

function handleSearch({ value }: Recordable) {
  searchVal.value = value;
  onRefreshList();
}

// list
const onLoad = () => {
  const point = unref(props.lnglat)?.point;

  h5FindVenuePositionVoList({
    pageSize: 10,
    pageNum: unref(pageNum),
    userCoordinate: `${point?.lng},${point?.lat}`,
    areaCode: unref(areaCode),
    positionName: unref(searchVal),
    typeManageId: unref(active),
  }).then(({ data, total = 0 }: Recordable) => {
    if (unref(pageNum) === 1) list.value = [];

    list.value = concat(unref(list), data);

    unref(loadMoreRef)?.onLoadSuc(unref(list)?.length, total);

    emit('data-change', unref(list));
  });
};

const onRefreshList = () => {
  pageNum.value = 1;
  onLoad();
};

// 加载更多
const onLoadMore = () => {
  pageNum.value++;
  onLoad();
};

//
function handleTabChange() {
  unref(loadMoreRef)?.resetStatus();

  onRefreshList();
}

watch(
  () => props.lnglat,
  () => {
    if (props.lnglat) {
      onRefreshList();
    }
  },
  { deep: true, immediate: true }
);
</script>

<style lang="less" module>
.content {
  :global {
    .bg-list {
      background-image: url('@/assets/position/bg-list.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    .van-tabs {
      line-height: 1;

      .van-tabs__wrap {
        height: 100px;

        .van-tabs__nav {
          background: transparent !important;
        }
      }
    }

    .van-cell {
      &::after {
        border-color: #cfcfcf !important;
      }
    }

    .van-image {
      img {
        border-radius: 16px;
        width: 100%;
        height: 100%;
      }
    }

    .line-right {
      &::after {
        content: ' ';
        display: inline;
        margin-left: 10px;
        margin-right: 10px;
        border: 0.7px solid #cccccc;
      }
    }

    .multiline-text {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      /* 设置n行，也包括1 */
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
    }
  }
}
</style>
