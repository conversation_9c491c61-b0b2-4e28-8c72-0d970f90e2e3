<template>
    <div class="after-sale-cell" @click="toRawDetail">
        <div class="napeTop">
            <div class="flex justify-between items-center">
                <span>{{ content.companyName }}</span>
                <span class="napeType napeRed">{{ content.statusText }}</span>
            </div>
            <div class="text-26px mt-20px">
                订单号:{{ content.orderId }}
            </div>
        </div>

        <div class="napecenter" v-for="(item, i) of content.detailVOList" :key="i">
            <div class="goodBxo flex">
                <div class="img">
                    <img :src="judgeStaticUrl(item.productSubSnapshotJson.productSubImg)">
                </div>
                <div class="oneGondInfo flex">
                    <div class="text-#333 text-30px textOverflow">
                        {{ item.productSubSnapshotJson.productName }}</div>
                    <div class="textOverflow text-#999 text-28px">
                        {{ item.productSubSnapshotJson.productSubName }}</div>
                    <div class="gonin flex">
                        <div>
                            ￥<span>{{ item.productSubSnapshotJson.nowPrice }}<span
                                    v-if="item.productSubSnapshotJson.payIntegral">+{{
                                        item.productSubSnapshotJson.payIntegral }}积分</span>
                            </span>
                        </div>
                        <span>x{{ item.productSubSnapshotJson.currentProductCount }}</span>
                    </div>
                    <div class="flex justify-between mt-10px">
                        <div class="gonin textOver_two">
                            退款金额：<span>{{ item.backAmount }}</span><span v-if="item.backIntegral">+{{ item.backIntegral
                            }}积分</span>
                        </div>
                    </div>
                    <div class="flex justify-between">
                        <div class="gonin textOver_two" v-if="item.backResultState">
                            退款状态：<span style="color:rgb(90, 164, 255);">{{ item.backResultStateText }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="pt-24px text-28px flex items-center justify-between">
            <span>
                共<span style="color:rgb(90, 164, 255);">{{ allNum }}</span>件
            </span>
            <span>
                退款总金额：<span>￥{{ content.backAmountTotal }}</span>
                <span v-if="content.backIntegralTotal">+{{ content.backIntegralTotal }}积分</span>
            </span>
        </div>

        <!-- 按钮 -->
        <div class="controll-btns flex items-center justify-end mt-20px">
            <div v-for="item, index in orderBtnArr" :key="index" @click.stop="handleBtn(item)"
                class="text-28px btns default-btn rounded-24px py-12px px-20px leading-none mr-20px">
                {{ item.text }}
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { judgeStaticUrl } from '@/utils/utils';
const props = defineProps({
    content: {
        type: Object,
        default() {
            return {}
        }
    }
})
const allNum = computed(() => {
    return props.content?.detailVOList.reduce((pre, cur) => {
        return pre + cur.productSubSnapshotJson.currentProductCount
    }, 0)
})
// 订单按钮数组:申请退款\取消售后申请\上传单号
const orderBtnArr = computed(() => {
    return matchSet()
})
const emit = defineEmits(['toDetail', 'handleControll'])
const matchSet = () => {
    switch (props.content?.serviceState) {
        case 'wait':
            return [{ text: '取消售后申请', type: 'cancelRefund' }]
        case 'transport':
            return [{ text: '取消售后申请', type: 'cancelRefund' },
            { text: '上传单号', type: 'upload' }]
        case 'refuse':
            return [{ text: '申请退款', type: 'resetRefund' }]
    }
}
const handleBtn = (item) => {
    emit('handleControll', item)
}
const toRawDetail = () => {
    emit('toDetail')
}
</script>
<style scoped lang="scss">
.after-sale-cell {
    width: 90%;
    padding: 20px;
    background-color: #fff;
    border-radius: 16px;
    margin: 20px auto 0;
    font-size: 24px;
    box-sizing: border-box;

    .napeTop {
        border-bottom: 1px solid #EBEBEB;
        padding-bottom: 20px;
        align-content: center;
        color: #666;
        font-size: 28px;


        .napeType {
            color: #333;
            font-size: 28px;
        }

        .napeyell {
            color: rgb(90, 164, 255);
        }

        .napeRed {
            color: rgb(90, 164, 255);
        }
    }

    .napecenter {
        padding: 20px 0;
        border-bottom: 1px solid #EBEBEB;

        .goodBxo {
            padding-bottom: 10px;
            position: relative;

            .img>img {
                width: 140px;
                height: 140px;
                border-radius: 20px;
                object-fit: cover;
                margin-right: 20px;
            }

            .texr {
                position: absolute;
                right: 0;
                align-items: center;
                color: #999999;
                font-size: 28px;
                top: 50%;
                transform: translateY(calc(-10px - 50%));

                img {
                    width: 15px;
                    margin-left: 6px;
                }
            }

            .oneGondInfo {
                /*position: absolute;*/
                /*right: 0;*/
                width: calc(100% - 190px);
                flex-direction: column;
                justify-content: space-around;
                height: 100%;


                .gonin {
                    justify-content: space-between;
                    color: #999;
                    align-items: end;

                    div {
                        color: #FF6342;

                        span {
                            font-size: 30px;
                        }
                    }
                }
            }
        }

        .payInfo {
            font-size: 30px;
            text-align: right;

            span {
                font-size: 24px;
            }
        }
    }

    .controll-btns {
        .default-btn {
            border: 1px solid #999;
            color: #999;
        }

        .active-btn {
            background: linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%);
            color: #fff;
            border: none;
        }

        .btns:last-child {
            margin-right: 0
        }
    }

    .border-top {
        border-top: 1px solid #EFEFEF;
    }
}
</style>