<template>
  <div :class="$style.friendShipActivity" class="w-full">
    <topBanner v-if="route?.query?.type != 'activity' && !isSingle" :showIcon="isSingle" :singleStatus=singleStatus
      :categoryList=categoryList></topBanner>
    <div class="py-35px px-30px  flex items-center search_input">
      <van-field v-model="activityName" :center="true" :clearable="true" placeholder="关键字搜索"
        class="!py-[6px] !pr-[10px] w-5/6">
        <template #button>
          <van-button size="small" class="w-[85px] !text-26px" @click="onRefreshList">
            搜索
          </van-button>
        </template>
        <template #left-icon>
          <img loading="lazy" :src="iconSearch" class="w-[27px] h-[27px] mt-[10px]" />
        </template>
      </van-field>
      <div class="flex items-center justify-center w-1/6 text-[#999999] text-[28px]" @click="handleOpen">
        <img loading="lazy" :src="searchIcon" class="w-[40px] h-[40px] mr-8px" />
        筛选
      </div>
    </div>
    <div class="tab-box mb-27px">
      <van-tabs v-model:active="tab.active" sticky line-width="20" @click-tab="onClickTab">
        <van-tab :title="item.name" v-for="(item, index) in tab.nav" :key="index" title-class="tab-title"></van-tab>
      </van-tabs>
    </div>
    <div class="px-[28px]">
      <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
        <activityList :data="list" @toDetails="(item) => toDetail(item, { isSingle, singleStatus })"
          statusPosition="right" imgClass="rounded-28px">
          <template #subContent="{ item }">
            <div class="text-28px text-#999 mb-30px">
              <div class="mb-15px flex w-full items-center">
                <img loading="lazy" :src="iconPerson" alt="" srcset="" class="h-30px w-auto mr-5px">
                报名人数：<van-progress :percentage="item.signUpInfo?.signUpCount / item.signUpInfo?.maxCount * 100"
                  pivot-text="" stroke-width="6" pivot-color="#7232dd" track-color="#deedff" color="#5aa4ff"
                  class="w-60%" />
                <div class="text-26px text-#8d9099 ml-19px">
                  <span class="text-#5aa4ff"> {{ item.signUpInfo?.signUpCount }}</span>/{{ item.signUpInfo?.maxCount }}
                </div>
              </div>
              <div class="mb-10px flex items-center " v-if="item.signUpInfo">
                <img loading="lazy" :src="iconTime" alt="" srcset="" class="h-30px w-auto mr-5px">
                报名日期：{{ dayjs(item.signUpInfo?.signUpStartTime).format('YYYY-MM-DD') }} 至 {{
                  dayjs(item.signUpInfo?.signUpEndTime).format('YYYY-MM-DD') }}
              </div>
            </div>
          </template>
          <template #status="{ item }">
            <div class="text-24px text-#fff w-119px h-44px leading-44px text-center"
              :style="{ background: `url(${statusObj[item.progressMsg]})`, backgroundSize: '100%' }">
              {{ item.progressMsg }}
            </div>
          </template>
        </activityList>
      </refreshList>

    </div>
    <bottomPopup :showPop="showPop" :data="filterData" @close-popup="() => { showPop = false }"
      @select-click="selectFilterData" @rest-click="rest" @confirm-click="confirm"></bottomPopup>
    <tipsPopup :showPop="showTipPop" @close-popup="closeTipPopup" @to-write="toWrite"></tipsPopup>

    <van-floating-bubble v-model:offset="offset" axis="xy" magnetic="x" :icon="btn" class="moveSingleBtnClass"
      @click="toWrite" v-if="route?.query?.type != 'activity' && !isSingle" :gap="15" />

  </div>
</template>
<script lang="ts" setup>
import { toDetail } from "@/hooks/useValidator";

defineOptions({
  name: 'FriendshipActivity'
})
import iconSearch from '@/assets/public/icon_search_blue.png';
import searchIcon from '@/assets/position/search-icon.png';
import activityList from "@/components/activity/list.vue";
import iconPerson from "@/assets/public/icon_person.png"
import iconTime from "@/assets/public/icon_time.png"
import bottomPopup from './components/bottomPopup.vue';
import tipsPopup from './components/tipsPopup.vue';
import iconOning from "@/assets/friendship/icon_oning.png"
import iconNoStart from "@/assets/friendship/icon_nostart.png"
import iconEnd from "@/assets/friendship/icon_end.png"
import { activityInfoList } from "@/api/activity";
import refreshList from '@/components/refreshList/index.vue';
import { useDictionary } from "@/store/modules/dictionary";

import { useUserStore } from "@/store/modules/user";
import { showToast } from "vant";
import dayjs from 'dayjs';
import btn from "@/assets/friendship/btnSingle.png"
import { queryMyDetail, findCategoryList } from "@/api/friendship/index"
import topBanner from '../components/topBanner.vue';

const dictionary = useDictionary()
const router = useRouter();
const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo)
const route = useRoute();
const offset = ref({ x: -window.innerWidth, y: 400 });
const list = ref([
])

const tab = ref({
  active: 0,
  nav: [
    {
      name: "全部",
      code: "",
    },
    {
      name: "未开始",
      code: "1",

    },
    {
      name: "进行中",
      code: "2",

    },
    {
      name: "已结束",
      code: "3",

    },
  ],
},)

const filterData = ref([
  {
    name: '区域',
    active: 0,
    code: "unionInformation",
    paramsKey: 'companyId',
    list: [
    ]
  },
  {
    name: '类型',
    active: 0,
    paramsKey: 'activityType',
    code: "friendshipActivityType",
    list: [
    ]
  },

])

const pageNum = ref(1)

const loadMoreRef = ref(null)
const activityName = ref("")
const params = ref({})
const clickItem = ref({})

const statusObj = ref({
  '未开始': iconNoStart,
  '进行中': iconOning,
  '已结束': iconEnd,
})

const showPop = ref(false)
const showTipPop = ref(false)

const isSingle = ref(false)
const singleStatus = ref('n')
// 推荐活动
const categoryList = ref([])
const getCategoryList = async () => {
  const { data, code } = await findCategoryList({
    pageSize: 5,
    pageNum: 1,
    publishStatus: 'y'
  })
  if (code === 200) {
    categoryList.value = data
  }
}
onMounted(() => {

  getIsSingle()
  // 处理筛选列表
  filterData.value.forEach(item => {
    const dictList = dictionary.getDictionaryOpt?.[item.code]?.map(t => {
      const { label, value } = t
      return { value, name: label }
    })
    item.list = [{ name: "全部", value: '' }, ...dictList]
  })
  getActList();
  getCategoryList()
})

function selectFilterData({ parentIndex, childIndex }) {
  filterData.value[parentIndex].active = childIndex
}

function confirm() {
  showPop.value = false
  filterData.value.forEach(item => {
    params.value[item.paramsKey] = item.list[item.active].value
  })
  onRefreshList()
}

function rest() {
  filterData.value.forEach(item => {
    item.active = 0
  })
}

function closeTipPopup() {
  router.push({
    path: '/friendship/activity/detail?activityId',
    query: { activityId: clickItem.value.activityId, isSingle: isSingle.value }
  })
  showTipPop.value = false


}
function toWrite() {

  if (userInfo.value.authenticationFlag == '未实名') {
    console.log(userInfo.value);
    showToast('只有实名用户才能认证')
    return
  }
  // todo
  // showToast("暂未开放")
  // showTipPop.value = false

  router.push({
    path: '/friendship/personFillIn',
  })
}

// 获取是否单身认证
function getIsSingle() {
  queryMyDetail().then(res => {
    if (res.code == 200) {
      if (res.data) {
        isSingle.value = true
        singleStatus.value = res.data.singleStatus
      } else {
        isSingle.value = false
      }
    } else {
      isSingle.value = false

    }
  })
}


function getActList() {
  activityInfoList({
    ...params.value,
    progress: tab.value.nav[tab.value.active].code,
    activityName: activityName.value,
    activityCategory: 'friendship',
    pageSize: 10,
    pageNum: pageNum.value,
  }).then(res => {
    if (res.code == 200) {
      if (pageNum.value === 1) {
        list.value = [];
      }
      res.data = res.data?.map(t => {
        const { activityStartTime, activityEndTime } = t
        t.activityStartTime = activityStartTime?.split(' ')[0] ?? ''
        t.activityEndTime = activityEndTime?.split(' ')[0] ?? ''
        return t
      })
      list.value = list.value.concat(res.data);
      //重置刷新状态及 判断是否加载完成
      if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(list.value.length, res.total);
      }
    }

  })
}

// 刷新
const onRefreshList = () => {
  pageNum.value = 1;
  getActList();
};
// 加载更多
const onLoadMore = () => {
  pageNum.value++;
  getActList();
};


function handleOpen() {
  showPop.value = true
}

function onClickTab(item: any) {
  tab.value.active = item.name;
  getActList()
}

</script>
<style lang="less" module>
.friendShipActivity {
  :global {
    background: #fff;

    .top_banner {
      background-image: url("@/assets/friendship/home_banner.png");
      background-size: 100%;
      height: 302px;

      .icon_msg {
        position: relative;

        .icon_red {
          content: '';
          width: 13px;
          height: 13px;
          background: #F43F32;
          border-radius: 50%;
          position: absolute;
          right: 0;
          top: 0;
          display: inline-block;
        }
      }
    }

    .van-tabs__wrap {
      height: 88px;
    }

    .van-field__control {
      font-size: 26px;
    }

    .van-field {
      background-color: #f6f7f8;
      border-radius: 50px;

      .van-button {
        border: unset !important;
        color: #5aa4ff;
        background-color: rgba(90, 164, 255, 0.1);
        border-radius: 40px;
        font-size: 24px;
        height: auto;
        padding: 14px 0;
      }
    }

    .van-cell:after {
      border: none !important;
    }

    .van-tab {

      font-size: 28px;
      color: #8d9099;
      line-height: 34px;
    }

    .van-tab--active {
      font-size: 30px;
      color: #404455;
    }

    .van-tabs__line {
      background: linear-gradient(86deg, #C7E0FF 0%, #5AA4FF 100%);
      bottom: 40px;
    }
  }
}
</style>

<style lang="less">
.moveSingleBtnClass {
  background-color: transparent !important;
  overflow: visible;

  .van-icon__image {
    width: 142px;
    height: 140px;
  }
}
</style>
