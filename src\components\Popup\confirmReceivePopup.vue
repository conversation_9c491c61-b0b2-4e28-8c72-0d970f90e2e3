<template>
    <van-popup v-model:show="props.show" position="bottom" @click-overlay="close">
        <div class="px-135px py-25px bg-[#fff] popup-content">
            <div class="product-img w-138px h-138px rounded-28px bg-cover bg-center mx-auto relative"
                :style="{ backgroundImage: `url(${imgCover})` }">
                <div class="absolute bottom-0 w-full text-center py-10px text-#fff goods-num text-28px" v-if="goodsNum">
                    共{{
                        goodsNum }}件
                </div>
            </div>
            <div class="text-tips text-center text-[#333] mt-33px">
                为保证你的售后权益，请收到商品确认无误后在确认收获
            </div>
            <div class="confirm-btn relative w-70% mx-auto mt-50px safe_area_bottom" @click="confirm">
                <img loading="lazy" src="@/assets/public/butto.png" class="w-full" />
                <span class="absolute whitespace-nowrap leading-none
                left-50% top-50% -translate-50% text-34px text-[#fff]">确认</span>
            </div>
        </div>
    </van-popup>
</template>
<script setup lang="ts">
import { judgeStaticUrl } from '@/utils/utils';

const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    bgCover: {
        type: String,
        default: ''
    },
    goodsNum: {
        type: Number,
        default: 0
    }
})
const imgCover = computed(() => {
    return judgeStaticUrl(props.bgCover)
})
const emit = defineEmits(['update:show', 'confirm'])
const close = () => {
    emit('update:show', false)
}
const confirm = () => {
    emit('confirm')
}
</script>
<style lang="scss" scoped>
.check-circle {
    border: 2px solid #5AA4FF;
}

.border-bottom {
    border-bottom: 1px solid #EFEFEF;
}

.van-popup {
    background: transparent;
}

.popup-content {
    border-radius: 20px 20px 0px 0px;
}

.goods-num {
    background-color: rgba(0, 0, 0, .4);
    border-radius: 0% 0% 28px 28px;
}
</style>