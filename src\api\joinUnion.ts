import { cloudManageHttp } from '@/utils/http/axios';
import { BasicResponse } from '.';

enum API {
  findList = '/findList',
  view = '/getById',
  saveOrUpdate = '/applyUnion',
  saveApi = '/',
  updateApi = '/',
  scanQRCodeCheck ='/scanQRCodeCheck',
  cancelApply='/cancelApply'
}

function getApi(url?: string) {
  if (!url) {
    return '/joinUnionAuditRecord';
  }
  return '/joinUnionAuditRecord' + url;
}

// 列表
export const list = (params: Recordable) => {
  return cloudManageHttp.get<BasicResponse>(
    { url: getApi(API.findList), params },
    {
      isTransformResponse: false,
    }
  );
};
// 校验是否可以唤起扫一扫【扫码入会】
export const scanQRCodeCheck = (params: Recordable) => {
  return cloudManageHttp.get<BasicResponse>(
    { url: getApi(API.scanQRCodeCheck), params },
    {
      isTransformResponse: false,
    }
  );
};
// 【扫码入会】获取工会
export const unionQrCodeRecord = (params: Recordable) => {
  return cloudManageHttp.get<BasicResponse>(
    { url: '/unionQrCodeRecord/queryUnionQrCodeRecordByUnionQrCode', params },
    {
      isTransformResponse: false,
    }
  );
};

// 新增入会申请
export const saveOrUpdate = (params: Recordable) => {
  return cloudManageHttp.post<BasicResponse>(
    {
      url: getApi(API.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
// 取消申请
export const cancelApply = (params: Recordable) => {
  return cloudManageHttp.post<BasicResponse>(
    {
      url: getApi(API.cancelApply),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
// 获取所有工会
export const getUnionPagedAllForApp = (params: Recordable) => {
  return cloudManageHttp.get<BasicResponse>(
    {
      url: '/areaOfIndustryOfUnion/getUnionPagedAllForApp',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
//个人中心申请列表
export const getAuditRecord = (params: Recordable) => {
  return cloudManageHttp.get<BasicResponse>(
    {
      url: '/joinUnionAuditRecord/findH5AppList',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
//个人中心申请审核
export const getAudit = (params: Recordable) => {
  return cloudManageHttp.post<BasicResponse>(
    {
      url: '/joinUnionAuditRecord/audit',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
//个人中心审核数
export const countWaitNum = (params: Recordable) => {
  return cloudManageHttp.get<BasicResponse>(
    {
      url: '/joinUnionAuditRecord/countWaitNum',
      params,
    },
  );
};

// 修改
export const updateApi = (params: Recordable) => {
  return cloudManageHttp.post<BasicResponse>(
    {
      url: getApi(API.updateApi),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// view
export const view = (params: Recordable) => {
  return cloudManageHttp.get<Recordable>(
    {
      url: getApi(API.view),
      params,
    },
    { isTransformResponse: true }
  );
};

// 删除
export const deleteLine = (autoId: number[] | number) => {
  return cloudManageHttp.delete<BasicResponse>(
    {
      url: getApi() + '?autoId=' + autoId,
    },
    {
      isTransformResponse: false,
    }
  );
};
