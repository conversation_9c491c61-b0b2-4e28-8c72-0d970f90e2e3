<template>
  <refreshList @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" :immeCheck="true" ref="loadMoreRef">
    <van-cell v-for="item in list" :key="item" @click="handlePlaceDetail(item)">
      <van-row>
        <van-col :span="8">
          <div class="w-[200px] h-[244px] rounded-[16px]">
            <img loading="lazy" :src="item?.appCover ? userStore.getPrefix + item?.appCover : defaultPlaceLittle"
              class="w-full h-full rounded-[16px]" />
          </div>
        </van-col>
        <van-col :span="16" class="pl-2 flex-col flex">
          <div class="text-[#333333] text-[30px] font-bold text-left w-full truncate leading-1.2em">
            {{ item?.venueName || '' }}
          </div>
          <div class="py-1 w-full">
            <div class="flex items-center text-[24px] w-full leading-1.8em">
              <span class="text-[#808080]">场所状态：</span>

              <div :class="`${item?.openState ? 'text-[#5AA4FF]' : 'text-[#f25f20]'}`">
                {{ item?.openState ? '开放中' : '暂未开放' }}
              </div>
            </div>
            <div class="flex items-center text-[24px] truncate leading-1.8em">
              <span class="text-[#808080]">容纳人数：</span>
              <span class="text-[#333333]">{{ item?.capacity }} 人</span>
            </div>
            <div class="flex items-center text-[24px] truncate leading-1.8em">
              <span class="text-[#808080]">场所地址：</span>
              <span class="text-[#333333] text-left inline-block w-full truncate">
                {{ item?.address }}
              </span>
            </div>
          </div>

          <div class="flex justify-start items-center" v-if="item?.reserveState === 'y'">
            <div class="default-shadow text-[#fff] bg-[#4297FF] cursor-pointer leading-1.8em
              border border-[#4297FF] border-solid px-[54px]  rounded-xl text-28px" @click.stop="handleReserve(item)">
              预约
            </div>
          </div>
        </van-col>
      </van-row>
    </van-cell>
  </refreshList>
</template>

<script lang="ts" setup>
import refreshList from '@/components/refreshList/index.vue';
import { concat } from 'lodash-es';
import { findList } from '@/api/position';
import { useUserStore } from '@/store/modules/user';
import { checkReservation } from '../utils';
import defaultPlaceLittle from '@/assets/position/default-place-little.jpg';
import {isLoginDialog} from '@/utils/utils'
const userStore = useUserStore();

const router = useRouter();

const loadMoreRef = ref();

const pageNum = ref<number>(1);

const list = ref<Recordable[]>([]);

const detailRecord = inject<Recordable>('detailRecord', {});

// 加载更多
const onLoadMore = () => {
  pageNum.value++;
  onLoad();
};

const onRefreshList = () => {
  pageNum.value = 1;
  onLoad();
};

const onLoad = () => {
  if (!unref(detailRecord)?.positionInfoId) return;

  findList?.({
    positionInfoId: unref(detailRecord)?.positionInfoId,
    pageSize: 10,
    pageNum: unref(pageNum),
    systemQueryType: 'h5',
  }).then(({ data, total }: Recordable) => {
    if (unref(pageNum) === 1) list.value = [];
    list.value = concat(unref(data));
    unref(loadMoreRef)?.onLoadSuc(unref(list)?.length, total);
  });
};

// 预约
function handleReserve(item: Recordable) {
  isLoginDialog((isLogin: boolean) => {
    if(!isLogin){
      return;
    }
    checkReservation(
      {
        venueInfoId: item?.venueInfoId,
        userId: userStore.getUserInfo.userId,
      },
      item?.autoId,
      item?.recordId,
      router
    );    
  })

}

// 详情
function handlePlaceDetail(item: Recordable) {
  router.push({
    path: '/placeDetail',
    query: {
      actualDistance: unref(detailRecord)?.actualDistance,
      address: unref(detailRecord)?.address,
      autoId: item?.autoId,
    },
  });
}
</script>
