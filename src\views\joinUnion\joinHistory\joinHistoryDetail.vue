<template>
  <div class="h-100vh w-full px-[30px] box-border" :class="$style['join-history-detail']">
    <div class="flex  items-center mt-40px relative h-250px">
      <img loading="lazy" :src="record.icon" class="w-[298px] h-[197px] absolute bottom-0 right-0" />
      <div class="flex items-center ">
        <img loading="lazy" :src="record.littleIcon" alt="" class="w-50px h-50px">
        <img loading="lazy" :src="record.textIcon" alt="" class="w-160px h-40px ml-20px">
      </div>
    </div>
    <div class="bg-white rounded-[20px] relative" :class="ifFail ? 'pb-[100px]' : 'pb-[30px]'">
      <van-form :readonly="true" input-align="right">
        <van-cell-group inset>
          <van-field v-model="record.nickname" name="nickname" label="申请人" />
          <van-field v-model="record.phone" name="phone" label="联系电话" />
          <van-field v-model="record.identityNumber" name="identityNumber" label="身份证号" />
          <van-field v-model="record.auditCompanyName" name="auditCompanyName" label="所入工会" />
          <van-field v-model="record.belongCompanyName" name="belongCompanyName" label="单位名称" />

          <div class="p-20px">
            <van-cell class="!px-30px !py-20px bg-[#f0f0f0] !rounded-8px"
              v-show="record.auditState === 'cancel' || record.auditState === 'return'">
              <div class="text-[#CC3333] text-[28px] text-left">* {{ record.auditState === 'return' ? '驳回' : '取消' }}原因：</div>
              <div class="text-left">{{ record.auditRemark || '--' }}</div>
            </van-cell>
          </div>
        </van-cell-group>


      </van-form>
    </div>
    <div class="flex justify-center items-center w-full mt-80px"
      v-show="record.auditState == 'wait' || record.auditState == 'return'">
      <van-button block type="primary" native-type="submit" class="bg-transparent border-transparent w-[70%]  text-34px"
        :style="{
          backgroundImage: `url(${joinBgButton})`,
          backgroundRepeat: 'no-repeat',
          backgroundSize: '100% 100%',
        }" @click="handleClick">
        {{ record.auditState == 'wait' ? '取消申请' : '重新提交' }}
      </van-button>
    </div>
    <Popup :show="Data.showPop" :titleName="'取消原因'" :placeHolder="'请输入取消原因'" @submit-content="joinContent"
      @close-popup="closePopup" />
  </div>
</template>

<script lang="ts" setup>
import { view } from '@/api/joinUnion';
import Popup from '@/components/Popup/popup.vue';
import { showFailToast, showToast } from 'vant';
import { cancelApply } from '@/api/joinUnion';
import joinFailIcon from '@/assets/join-union/join-code/join-img-fail.png';
import joinPassIcon from '@/assets/join-union/join-code/join-img-pass.png';
import joinWaitIcon from '@/assets/join-union/join-code/join-img-audit.png';
import joinBgButton from '@/assets/join-union/join-code/join-bg-button.png';
import joinFail from '@/assets/join-union/join-code/join-icon-fail.png';
import joinPass from '@/assets/join-union/join-code/join-icon-pass.png';
import joinWait from '@/assets/join-union/join-code/join-icon-audit.png';
import joinReturn from '@/assets/join-union/join-code/join-icon-return.png';
import joinCancel from '@/assets/join-union/join-code/join-icon-cancel.png';
import joinFailText from '@/assets/join-union/join-code/return.png';
import joinPassText from '@/assets/join-union/join-code/pass.png';
import joinWaitText from '@/assets/join-union/join-code/wait.png';
import joinReturnText from '@/assets/join-union/join-code/return.png';
import joinCancelText from '@/assets/join-union/join-code/cancel.png';
const IconRelation: Recordable = {
  wait: joinWaitIcon,
  pass: joinPassIcon,
  return: joinFailIcon,
  refuse: joinFailIcon,
  cancel: joinWaitIcon,
};
const IconShow: Recordable = {
  wait: joinWait,
  pass: joinPass,
  return: joinReturn,
  refuse: joinFail,
  cancel: joinCancel,
};
const textIcon: Recordable = {
  wait: joinWaitText,
  pass: joinPassText,
  return: joinReturnText,
  refuse: joinFailText,
  cancel: joinCancelText,
};
const Data = ref({
  showPop: false,
  objData: null
})
const router = useRouter();

const route = useRoute();

const state = ref<string>();

const ifFail = computed(() => unref(state) === 'fail' || unref(state) === 'cancel');

const record = ref<Recordable>({});
//确认提交
async function joinContent(val) {
  if (!val) {
    showToast({
      message: "请输入取消原因",
      icon: 'none',
    })
    return
  }
  cancelApply({
    autoId: record.value.autoId,
    auditState: "cancel",
    auditRemark: val
  }).then(async res => {
    if (res.code == 200) {
      showToast('取消成功');
      Data.value.showPop = false;
      const autoId = route.query?.autoId;
      const viewData = await view({ autoId });
      state.value = viewData?.auditState;

      record.value = { ...(viewData || {}), icon: IconRelation[unref(state) as string], littleIcon: IconShow[unref(state) as string], textIcon: textIcon[unref(state) as string] };

    } else {
      showFailToast(res.message)
    }
  })
}


//关闭
function closePopup() {
  Data.value.showPop = false;
}

function handleClick() {
  if (record.value.auditState == 'wait') {
    Data.value.showPop = true;
    return
  }
  router.push({
    path: '/join',
    query: {
      state: 'audit',
      autoId: record.value.autoId
    },
  });
}

onMounted(async () => {
  const autoId = route.query?.autoId;
  const viewData = await view({ autoId });
  state.value = viewData?.auditState;

  record.value = { ...(viewData || {}), icon: IconRelation[unref(state) as string], littleIcon: IconShow[unref(state) as string], textIcon: textIcon[unref(state) as string] };
});
</script>

<style lang="less" module>
.join-history-detail {
  :global {
    background-image: url('@/assets/join-union/join-audit/join-bg-audit.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    .van-cell-group--inset {
      margin: 0 !important;
    }
  }
}
</style>
