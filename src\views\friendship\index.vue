<template>
    <!-- 已认证界面 -->
    <div class="friendship-home w-full">
        <topBanner :unreadMessageCount="unreadMessageCount" :singleStatus="singleStatus" :categoryList=categoryList>
        </topBanner>
        <div class="has-auth">
            <div class="navs ">
                <div class="nav flex items-center justify-between">
                    <!-- 联谊活动 -->
                    <img loading="lazy" src="@/assets/friendShip/activity_nav_bg.png" class="w-224px h-auto"
                        @click="toPage('/friendship/activity', { type: 'activity' })" />
                    <!-- 个人资料 -->
                    <img loading="lazy" src="@/assets/friendShip/profile_nav_bg.png" class="w-224px h-auto"
                        @click="toPage('/friendship/myProfile')" />
                    <!-- 广场 -->
                    <img loading="lazy" src="@/assets/friendShip/square_nav_bg.png"
                        @click="toPage('/friendship/square')" class="w-224px h-auto" />
                </div>

            </div>
            <div class="recommend">
                <img loading="lazy" src="@/assets/friendShip/recommend_txt.png" alt="">
                <van-notice-bar :scrollable="false" v-if="recommendList.length">
                    <van-swipe class="notice-swipe w-full h-85px" :autoplay="3000" :touchable="false"
                        :show-indicators="false" vertical>
                        <van-swipe-item v-for="item, index in recommendList" :key="index" @click="toDetailPage(item)"
                            class="w-full h-full flex items-center ">
                            <span class="recommend-text text-ellipsis line-clamp-1 w-full py-5px">
                                <span class="name">{{ item.nickname }}</span>
                                正在寻找心中的那个Ta，去了解下吧！</span>
                        </van-swipe-item>
                    </van-swipe>
                </van-notice-bar>
            </div>
            <div class="line"></div>
            <div class="fate-box">
                <div class="fate-title">
                    <div class="title-left">
                        <img loading="lazy" class="wall" src="@/assets/friendShip/wall_icon.png" alt="">
                        缘分墙
                        <img loading="lazy" class="heart" src="@/assets/friendShip/love_icon.png" alt="">
                        <img loading="lazy" class="heart" src="@/assets/friendShip/love_icon.png" alt="">
                        <img loading="lazy" class="heart" src="@/assets/friendShip/love_icon.png" alt="">
                    </div>
                    <div class="title-right">
                        <div class="search-box" :style="searchFocuse ? 'width:60%' : ''">
                            <img loading="lazy" src="@/assets/friendShip/search_icon.png" alt="">
                            <input type="text" placeholder="搜索" @focus="searchFocuse = true" @blur="searchFn"
                                v-model="searchText">
                        </div>
                        <div class="filter-box" @click="showFilter = true">
                            <img loading="lazy" src="@/assets/friendShip/filter_icon.png" alt="">筛选
                        </div>

                    </div>
                </div>
                <refreshList key="actList" @onRefreshList="onRefreshList" :immeCheck="false" @onLoadMore="onLoadMore"
                    ref="loadMoreRef">
                    <div class="fate-list">
                        <div class="fate-item" @click="toDetailPage(item)" v-for="(item, index) in ListObj.List"
                            :key="index">
                            <img loading="lazy" class="fate-pic object-cover" :src="judgeStaticUrl(item?.avatar, true)"
                                alt="">
                            <div class="fate-info">
                                <div class="name-sex">
                                    <span class="name">{{ item?.nickname }}</span>
                                    <span class="sex" :class="item?.gender">
                                        <img loading="lazy" :src="item?.gender == 'male' ? male : female" alt="">{{
                                            item?.age
                                        }}</span>
                                </div>
                                <div class="tag-box">
                                    <div
                                        class="tag whitespace-nowrap overflow-hidden text-ellipsis max-w-105px! border-box">
                                        {{
                                            getDictionaryLabel('modelEducation', item?.education)
                                        }}</div>
                                    <div
                                        class="tag whitespace-nowrap overflow-hidden text-ellipsis max-w-85px! border-box">
                                        {{ item?.height }}cm</div>
                                    <div
                                        class="tag whitespace-nowrap overflow-hidden text-ellipsis max-w-95px! border-box">
                                        {{ getDictionaryLabel('incomeMonth', item?.incomeMonth) }}</div>
                                </div>
                                <div
                                    class="info-text text-ellipsis line-clamp-2 whitespace-nowrap text-wrap h-60px overflow-hidden">
                                    {{ item.biography ? item.biography : '暂无自我介绍' }}
                                </div>
                            </div>
                        </div>
                    </div>
                </refreshList>
            </div>
        </div>
        <van-popup v-model:show="showFilter" class="filter-pop" position="bottom" style="height: 60%;width: 100%;">
            <div class="filter">
                <div class="form">
                    <div class="sift-box">
                        <span class="sift-tit">性别</span>
                        <div class="sift-choose">
                            <span :class="pager.gender == null ? 'active' : ''"
                                v-on:click="pager.gender = null;">不限</span>
                            <span :class="pager.gender == 'male' ? 'active' : ''"
                                v-on:click="pager.gender = 'male';">男</span>
                            <span :class="pager.gender == 'female' ? 'active' : ''"
                                v-on:click="pager.gender = 'female';">女</span>
                        </div>
                    </div>

                    <div class="sift-box">
                        <span class="sift-tit">年龄</span>
                        <div class="filter-input-group"><input type="number" class="filter-input"
                                v-model="pager.upperAge">
                            <span>~</span> <input type="number" class="filter-input" v-model="pager.lowerAge">
                        </div>
                    </div>

                    <div class="sift-box">
                        <span class="sift-tit">学历</span>
                        <div class="sift-choose">
                            <span v-for="(i, j) in educationList" :key="j"
                                :class="pager.education == i.value ? 'active' : ''"
                                v-on:click="pager.education = i.value;">{{ i.label }}</span>
                        </div>
                    </div>
                    <div id="confirm-btn-box" class="sift-box" style="text-align: center;">
                        <button type="button" class="confirm-btn gray-btn" @click="reset">重置</button>
                        <button type="button" class="confirm-btn " @click="search">确定</button>
                    </div>
                </div>
            </div>
        </van-popup>
    </div>
</template>
<script lang="ts" setup>
defineOptions({
    name: 'Friendship'
})
import refreshList from '@/components/refreshList/index.vue'
import male from '@/assets/friendShip/male_icon_w.png'
import female from '@/assets/friendShip/female_icon_w.png'
import { getSingleInfoList, getRecommendPerson, unreadMessageCountApi, findCategoryList, queryMyDetail } from '@/api/friendship/index';
import { useUserStore } from '@/store/modules/user';
import { useDictionary } from '@/store/modules/dictionary';
import { judgeStaticUrl } from '@/utils/utils';
import router from '@/router';
import topBanner from './components/topBanner.vue';

const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo)
const dictionary = useDictionary();
const searchState = ref(false)

const showFilter = ref(false)
const educationList = ref([])
onMounted(async () => {
    educationList.value = [{ label: "不限", value: '' }, ...dictionary.getDictionaryOpt?.['modelEducation']]
    pager.value.gender = userInfo.value?.gender == '男' ? 'female' : 'male'
    getRecommend()
    getCategoryList()
    getUnreadMesg()
    getIsSingle()
    onRefreshList()
})
let unreadMessageCount = ref(0)
const getUnreadMesg = async () => {
    const { code, data } = await unreadMessageCountApi()
    if (code == 200) {
        unreadMessageCount.value = data
    }
}
// 推荐活动
const categoryList = ref([])
const getCategoryList = async () => {
    const { data, code } = await findCategoryList({
        pageSize: 5,
        pageNum: 1,
        publishStatus: 'y'
    })
    if (code === 200) {
        categoryList.value = data
    }
}
// 智能推荐
const recommendList = ref([])
const getRecommend = async () => {
    const { data, code } = await getRecommendPerson({
        pageSize: 50,
        pageNum: 1,
        userId: userInfo.value?.userId
    })
    if (code === 200) {
        recommendList.value = data
    }
}
const singleStatus = ref('')
// 获取是否单身认证
function getIsSingle() {
    queryMyDetail().then(res => {
        if (res.code == 200) {
            if (res.data) {
                singleStatus.value = res.data.singleStatus
            }
        }
    })
}
// 筛选项
const pager = ref({
    gender: null,
    lowerAge: '',
    upperAge: '',
    education: '',
    industry: null,
    key: '',
})
const ListObj = ref({
    pageIndex: 1,
    List: [],
})
const loadMoreRef = ref(null)
const getList = async () => {
    const { data, code, total } = await getSingleInfoList({
        pageSize: 10,
        pageNum: ListObj.value.pageIndex,
        upperAge: pager.value.upperAge,
        lowerAge: pager.value.lowerAge,
        gender: pager.value.gender,
        nickname: searchText.value,
        education: pager.value.education,
    })
    if (code === 200) {
        if (ListObj.value.pageIndex === 1) ListObj.value.List = data
        else ListObj.value.List = [...ListObj.value.List, ...data]
        if (loadMoreRef.value) {
            loadMoreRef.value.onLoadSuc(ListObj.value.List.length, total);
        }
    }
}
const searchText = ref('')
const searchFocuse = ref(false)
const searchFn = () => {
    if (searchText.value) pager.value.gender = null; //搜索时 性别不限
    else pager.value.gender = userInfo.value?.gender == '男' ? 'female' : 'male'

    searchFocuse.value = false
    onRefreshList()
}
// 刷新
const onRefreshList = () => {
    ListObj.value.List = []
    ListObj.value.pageIndex = 1
    ListObj.value.loading = true
    ListObj.value.isLoad = true
    ListObj.value.finished = false
    getList()
}

const onLoadMore = () => {
    ListObj.value.pageIndex++;
    getList()
}
// 筛选重置
const reset = () => {
    pager.value.gender = null;
    pager.value.lowerAge = '';
    pager.value.upperAge = '';
    pager.value.education = '';
    pager.value.industry = null;
    pager.value.key = '';
}
// 筛选
const search = () => {
    searchState.value = true
    showFilter.value = false
    onRefreshList()
}
//详情
function toDetailPage(item) {
    router.push({
        path: '/friendship/detail',
        query: {
            singleUserId: item.singleUserId
        }
    })
}
//详情
function toPage(path, query) {
    router.push({
        path,
        query
    })
}
const getDictionaryLabel = (dictionaryType, value) => {
    let label
    try {
        let c = dictionary.getDictionaryOpt?.[dictionaryType].find(t => { return t.value == value })
        label = c.label
    } catch {
        label = value
    }
    return label
}
</script>
<style lang="scss" scoped>
.friendship-home {
    background: #F5F5F5;
    min-height: 100vh;

    .has-auth {
        .navs {
            background: #FFFFFF;

            .nav {
                justify-content: space-around;
                box-sizing: border-box;
                padding: 15px 10px;
            }
        }

        .recommend {
            background: #FFFFFF;
            border-bottom: 1px solid #F5F5F5;
            display: flex;
            align-items: center;
            height: 85px;
            border-top: 1px solid #F5F5F5;
            box-sizing: border-box;
            padding: 0 10px 0 25px;

            >img {
                width: 100px;
                margin-right: 22px;
            }

            .recommend-text {
                font-weight: 500;
                font-size: 28px;
                color: #404455;
                display: block;

                .name {
                    color: #5AA4FF;
                    margin-right: 10px;
                }
            }

            :deep(.van-notice-bar) {
                background-color: transparent;
                padding: 10px;
                flex: 1;
                height: 85px;
            }
        }

        .line {
            width: 100%;
            height: 20px;
            background: #fff;
        }

        .fate-box {
            background: linear-gradient(0deg, #F5F5F5 0%, #FFDEE9 100%) no-repeat;
            background-size: 100% 100%;
            box-sizing: border-box;
            padding: 0 30px;

            .fate-title {
                height: 84px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 5px;

                .title-left {
                    display: flex;
                    align-items: center;
                    font-weight: 500;
                    font-size: 32px;
                    color: #404455;

                    .wall {
                        width: 33px;
                        margin-right: 16px;
                    }

                    .heart {
                        width: 22px;
                        margin-left: 7px;

                        &:nth-child(2) {
                            margin-left: 16px;
                        }
                    }
                }

                .title-right {
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    flex: 1;

                    .search-box {
                        width: 154px;
                        height: 58px;
                        background: #FFFFFF;
                        border-radius: 29px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        box-sizing: border-box;
                        padding: 0 10px 0 20px;

                        >img {
                            width: 27px;
                            margin-right: 5px;
                        }

                        >input {
                            background: transparent;
                            flex: 1;
                            width: 0;
                            border: none;
                            font-weight: 400;
                            font-size: 25px;
                        }
                    }

                    .filter-box {
                        display: flex;
                        align-items: center;
                        font-weight: 400;
                        font-size: 28px;
                        color: #8D9099;
                        margin-left: 26px;

                        >img {
                            width: 25px;
                            margin-right: 6px;
                        }
                    }
                }
            }

            .fate-list {
                display: flex;
                flex-flow: row wrap;
                width: 100%;

                .fate-item {
                    width: calc(50% - 5px);
                    // height: 520px;
                    background: #FFFFFF;
                    border-radius: 10px;
                    margin-right: 10px;
                    margin-bottom: 14px;
                    display: flex;
                    flex-flow: column;

                    .fate-info {
                        flex: 1;
                        box-sizing: border-box;
                        padding: 12px 20px;

                        .name-sex {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-bottom: 13px;

                            .name {
                                font-weight: 500;
                                font-size: 30px;
                                color: #333333;
                            }

                            .sex {
                                font-weight: 400;
                                font-size: 24px;
                                color: #FFFFFF;
                                width: 85px;
                                height: 36px;

                                border-radius: 18px;
                                display: flex;
                                align-items: center;
                                justify-content: center;

                                >img {
                                    width: 19px;
                                    margin-right: 7px;
                                }

                                &.female {
                                    background: linear-gradient(90deg, #FAA8AB, #FF7097);
                                }

                                &.male {
                                    background: linear-gradient(90deg, #9FC9FF, #4196FF);
                                }
                            }

                        }

                        .tag-box {
                            display: flex;

                            .tag {
                                font-weight: 400;
                                font-size: 20px;
                                line-height: 27px;
                                color: #5AA4FF;
                                background: #EAF1F9;
                                border-radius: 13px;
                                padding: 3px 14px;
                                margin-right: 8px;
                                // white-space: nowrap;
                                // height: 27px;
                                max-width: 100px;
                            }
                        }

                        .info-text {
                            margin-top: 10px;
                            font-weight: 400;
                            font-size: 24px;
                            color: #999999;
                            line-height: 30px;
                        }


                    }

                    .fate-pic {
                        width: 100%;
                        height: 340px;
                        border-radius: 10px 10px 0px 0px;
                    }

                    &:nth-child(2n) {
                        margin-right: 0;
                    }
                }
            }
        }
    }

    .filter {
        width: 100%;
        height: 100%;
        background: #fff;
        box-sizing: border-box;
        padding: 30px;
        padding-bottom: 100px;
        overflow-y: auto;

        .form {
            width: 100%;
            //   min-height: 100%;
            color: #444;

            #confirm-btn-box {
                position: fixed;
                width: 100%;
                bottom: 0;
                left: 0;
                background-color: #fff;
                padding: 15px 30px 30px;

                display: flex;
                align-items: center;
                justify-content: space-around;
                box-sizing: border-box;
                box-shadow: 0px 0px 12px 0px rgba(204, 204, 204, 0.31);

                .gray-btn {
                    background: #EAF1F9 !important;
                    color: #5AA4FF !important;
                    border-radius: 36px;
                    border: 1px solid #5AA4FF !important;
                }

                .confirm-btn {
                    width: 41%;
                    display: inline-block;

                    height: 72px;

                    line-height: 38px;
                    padding: 0 18px;
                    background: linear-gradient(-23deg, #68AEFB, #93CFFD);
                    border-radius: 36px;
                    color: #fff;
                    white-space: nowrap;
                    text-align: center;
                    font-size: 28px;
                    border: none;
                    cursor: pointer;
                }
            }

            .form-title {
                font-size: 16px;
            }

            .sift-box {
                margin-top: 60px;

                .filter-input-group {
                    display: flex;
                    justify-items: center;
                    margin-top: 20px;
                    margin-bottom: 20px;


                    span {
                        width: 40px;
                        text-align: center;
                        line-height: 80px;
                    }

                    input {
                        height: 80px;
                        font-size: 32px;
                        text-align: center;
                        width: 150px;
                        padding-left: 0;
                        color: #5AA4FF;
                        line-height: 1.3;
                        line-height: 38px\9;
                        border-width: 1px;
                        border-style: solid;
                        background: #F1F6FB;
                        border-radius: 10px;
                        border: 1px solid #5AA4FF;
                    }
                }

                .sift-tit {
                    width: 100%;
                    font-weight: 500;
                    font-size: 34px;
                    color: #333333;

                }

                .sift-choose {
                    display: flex;
                    flex-wrap: wrap;
                    margin-top: 20px;

                    >span {
                        display: inline-block;
                        height: 75px;
                        line-height: 75px;
                        font-weight: 400;
                        font-size: 28px;
                        color: #333333;
                        text-align: center;
                        border-radius: 12px;
                        background: #F5F5F5;
                        padding: 0 7px;
                        min-width: 190px;
                        margin: 6px 5% 8px 0;
                    }

                    .active {
                        color: #5AA4FF;
                        background-color: #EAF1F9;
                    }
                }
            }
        }
    }

    .filter-pop {
        border-radius: 36px 36px 0px 0px;
        // padding-top: 50px;
    }
}
</style>