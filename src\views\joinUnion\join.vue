<template>
  <div class="h-100vh w-full px-[30px] box-border" :class="$style['join-union']">
    <div class="flex w-full h-299px items-center relative" :style="{
      backgroundImage: `url(${bgImage})`,
      backgroundRepeat: 'no-repeat',
      backgroundSize: '100% 100%',
    }">
      <div class="left-[10vw] absolute">
        <div class="flex items-center">
          <img loading="lazy" :src="iconImage" class="w-50px h-50px mr-1" v-if="!!iconImage" />
          <img loading="lazy" :src="textImage" class="w-157px h-37px" v-if="!!textImage" />
        </div>
        <div class="text-[#5AA4FF] mt-1" v-show="ifAudit">专属福利，贴心保障</div>
      </div>
      <div v-show="rightImage" class="flex justify-end w-full">
        <img loading="lazy" :src="rightImage" class="w-[298px] h-[197px]" />
      </div>
    </div>
    <div class="bg-white rounded-[20px] relative top-[-48px]" :class="readonly ? 'pb-[30px]' : 'pb-[100px]'">
      <van-form @submit="onSubmit" :readonly="readonly" input-align="right">
        <van-cell-group inset class=" group-cell">
          <van-field v-model="formData.auditCompanyName" is-link :required="true" readonly label="所入工会"
            placeholder="请选择所入工会" :rules="[{ required: true, message: '请选择所入工会' }]" input-align="right"
            error-message-align="right" @click="showArea = true" />
          <van-field v-model="formData.nickname" :required="true" :rules="[{ required: true, message: '请输入姓名' }]"
            input-align="right" error-message-align="right" name="nickname" label="姓名" placeholder="请输入姓名" />
          <van-field v-model="formData.phone" name="phone" :required="true" :rules="rules.telRules" input-align="right"
            error-message-align="right" label="手机号" placeholder="请输入手机号" />
          <van-field v-model="formData.identityNumber" :required="true" :rules="rules.idCardRules" input-align="right"
            error-message-align="right" name="identityNumber" label="身份证号" placeholder="请输入身份证号" />
          <van-field v-model="formData.belongCompanyName" :required="true"
            :rules="[{ required: true, message: '请输入您的单位名称' }]" input-align="right" error-message-align="right"
            label="单位名称" placeholder="请输入您的单位名称"></van-field>


          <van-popup v-model:show="showArea" round position="bottom" :style="{ height: '45%' }">
            <div class="bg-#f5f5f5 p-20px">
              <Search @on-search="search" :showBtn="false" class="search-box !mb-0 " />
            </div>
            <!-- <refreshList @onRefreshList="onRefreshList" @onLoadMore="onLoadMore"ref="loadMoreRef" >
                <div v-for="(item,index) of list" class="p-20px text-28px">
                    {{ item.text }}
                </div> -->
            <van-picker :columns="list" :columns-field-names="customFieldName" @cancel="showArea = false"
              @confirm="onConfirm" />
            <!-- </refreshList> -->


          </van-popup>
          <van-cell class="!px-30px !py-20px bg-[#f0f0f0] !rounded-8px" v-show="ifFail">
            <div class="text-[#CC3333] text-[28px] text-left">* 失败原因：</div>
            <div class="text-left">身份证号码填写错误，请重新填写信息。</div>
          </van-cell>
        </van-cell-group>

        <div class="flex justify-center items-center w-full mt-40px" v-show="ifAudit || ifFail">
          <van-button block type="primary" native-type="submit" class="bg-transparent border-transparent w-[70%]"
            :style="{
              backgroundImage: `url(${joinBgButton})`,
              backgroundRepeat: 'no-repeat',
              backgroundSize: '100% 100%',
            }">
            {{ ifFail ? '重新提交' : '提交' }}
          </van-button>
        </div>
      </van-form>
    </div>

    <van-overlay :show="showOverlay">
      <div class="w-full h-full justify-center items-center flex">
        <div class="relative">
          <img loading="lazy" :src="joinClose" class="absolute right-38px top-0 w-50px h-50px cursor-pointer"
            @click="showOverlay = false" />
          <div class="absolute w-full flex justify-center items-center top-130px">登记成功，请耐心等待审核！</div>
          <img loading="lazy" :src="overlayBg" class="h-500px" />
        </div>
      </div>
    </van-overlay>
  </div>
</template>

<script lang="ts" setup>
import { useDictionary } from '@/store/modules/dictionary';
import Search from '@/components/Search/index.vue';
import joinIcon1 from '@/assets/join-union/join-code/join.png';
import joinIcon2 from '@/assets/join-union/join-code/join-icon-2.png';
import joinBgButton from '@/assets/join-union/join-code/join-bg-button.png';
import overlayBg from '@/assets/join-union/join-code/overlay-bg.png';
import joinClose from '@/assets/join-union/join-code/join-close.png';
import joinIconAudit from '@/assets/join-union/join-code/join-icon-audit.png';
import joinIconFail from '@/assets/join-union/join-code/join-icon-fail.png';
import joinImgAudit from '@/assets/join-union/join-code/join-img-audit.png';
import joinImgFail from '@/assets/join-union/join-code/join-img-fail.png';
import joinTextFail from '@/assets/join-union/join-code/join-text-fail.png';
import joinTextAudit from '@/assets/join-union/join-code/join-text-audit.png';
// import { join } from 'lodash-es';
import { saveOrUpdate, getUnionPagedAllForApp, view } from '@/api/joinUnion';
// import utils from '@/utils/utils';
import { closeToast, showFailToast, showToast } from 'vant';
// import refreshList from '@/components/refreshList/index.vue';
import { telRules, idCardRules } from '@/utils/rulesValidator';
import { concat, map } from 'lodash-es';
import router from '@/router';
const Images: Recordable = {
  fail: [joinTextFail, joinIconFail, null, joinImgFail],
  wait: [joinTextAudit, joinIconAudit, null, joinImgAudit],
  audit: [joinIcon1, null, joinIcon2, null],
};

const dictionary = useDictionary();

const route = useRoute();
import { useUserStore } from "@/store/modules/user";
const useStore = useUserStore();
const formData = reactive({
  nickname: '',
  phone: useStore.getUserInfo?.phone,
  identityNumber: '',
  applyChannel: 'app_yjrh',
  belongCompanyName: '',
  auditCompanyName: ''
});
const rules = {
  telRules,
  idCardRules,
}
const state = ref<string>('audit');

const iconImage = computed(() => Images[unref(state)]?.[1]);

const textImage = computed(() => Images[unref(state)]?.[0]);

const bgImage = computed(() => Images[unref(state)]?.[2]);

const rightImage = computed(() => Images[unref(state)]?.[3]);

const ifAudit = computed(() => unref(state) === 'audit');

const ifFail = computed(() => unref(state) === 'fail');

const showOverlay = ref(false);

const readonly = ref(false);

const showArea = ref(false);
const list = ref<Recordable[]>([]);

const pageNum = ref<number>(1);

//获取列表
const loadMoreRef = ref<any>();
const searchVal = ref('')
const onLoad = () => {
  if (searchVal.value == '') {
    showFailToast("请输入工会名称查询");
    return
  }
  /**
   * isCanHasMember是否包含会员
   * 传0代表没有会员，意味着“总工会”类型
   * 传1代表有会员，以为排除“总工会”类型
*/
  showToast({
    message: '数据查询中...',
    forbidClick: true,
    duration: 0
  })
  getUnionPagedAllForApp({
    pageSize: 10,
    pageNum: unref(pageNum),
    puid: '6650f8e054af46e7a415be50597a99d5',
    isCanHasMember: 0,
    un: searchVal.value
  }).then(({ data, total = 0 }) => {
    closeToast()
    data = map(data || [],
      (v: Recordable) => ({
        text: v.c0100,
        value: v.id,
        companyClassicIds: v.c0203,

      })
    );
    if (unref(pageNum) === 1) list.value = [];

    list.value = concat(unref(list), data);
    unref(loadMoreRef)?.onLoadSuc(unref(list)?.length, total);
  });
};
// 加载更多
const onLoadMore = () => {
  pageNum.value++;
  onLoad();
};

const onRefreshList = () => {
  pageNum.value = 1;
  onLoad();
};
//搜索
function search(val) {
  searchVal.value = val;
  onRefreshList();

}
// 地区
const areaList = ref<Recordable[]>([]);

const customFieldName = { text: 'text', value: 'value', children: 'children' };
//确认
const onConfirm = ({ selectedValues, selectedOptions }: Recordable) => {
  showArea.value = false;
  formData.auditCompanyName = selectedOptions[0].text;
  formData.auditCompanyId = selectedOptions[0]?.value;
  formData.companyClassicIds = selectedOptions[0]?.companyClassicIds;
};
//提交
function onSubmit() {
  saveOrUpdate(formData).then(res => {
    if (res.code == 200) {
      showOverlay.value = true;
      state.value = 'wait';
      // 变为详情
      readonly.value = true;
      setTimeout(() => {
        router.go(-1)
      }, 1500)
    } else {
      showFailToast(res.message)
    }
  })

  // 审核成功
  /**
  showOverlay.value = true;
  state.value = 'wait'; 
  */


  if (unref(ifFail)) {
    state.value = 'pass';
  }


}

onMounted(async () => {

  const routeState = route.query.state as string;
  routeState && (state.value = routeState);
  if (route.query.autoId) {
    const autoId = route.query?.autoId;
    const viewData = await view({ autoId });
    formData.auditCompanyName = viewData.auditCompanyName;
    formData.auditCompanyId = viewData.auditCompanyId;
    formData.companyClassicIds = viewData.companyClassicIds;
    formData.autoId = viewData.autoId;
    formData.nickname = viewData.nickname;
    formData.phone = viewData.phone;
    formData.identityNumber = viewData.identityNumber;
    formData.belongCompanyName = viewData.belongCompanyName;
  }
});

</script>

<style lang="less" module scoped>
.join-union {
  :global {
    background-image: url('@/assets/join-union/join-code/join-bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    .group-cell {
      .van-cell {
        padding: 31px 0 31px 0;

        .van-field__control,
        .van-field__label {
          color: #333333 !important;
        }

        &::after {
          border-color: #ebebeb !important;
        }
      }
    }

    .van-button {
      &::before {
        background-color: #a1caff !important;
        border-color: #a1caff !important;
      }
    }
  }
}
</style>