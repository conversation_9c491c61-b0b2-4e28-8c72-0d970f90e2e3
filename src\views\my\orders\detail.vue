<template>
    <!-- 普惠订单详情 -->
    <div class="order-detail bg-[#F9F9F9] w-full min-h-100vh flex flex-col">
        <van-skeleton title :row="15" :loading="loading">
            <div class="flex-1 pb-46px">
                <div class="status-header relative z-1">
                    <img loading="lazy" :src="statusMatch.statusImg" alt="" class="w-full" />
                    <div class="absolute top-44px left-41px text-#fff">
                        <div class="status-title text-36px">
                            {{ statusMatch.statusText }}
                        </div>
                        <div class="status-des text-28px mt-21px" v-if="statusMatch.statusDes">{{ statusMatch.statusDes
                            }}</div>
                    </div>
                </div>
                <div class="-mt-100px relative z-10 ">
                    <div class="address flex items-center bg-[#fff] rounded-16px mx-30px py-33px px-30px">
                        <img loading="lazy" src="@/assets/integralMall/order_info_address.png" class="w-37px" />
                        <div class="flex-1 ml-30px" v-if="details?.receiveSnapshot?.receiverName">
                            <div class="name text-#333 text-32px font-medium flex items-center flex-wrap">
                                <span class="mr-20px">{{ details?.receiveSnapshot?.receiverName }}</span>
                                <span class="flex-1">{{ details?.receiveSnapshot?.receiverPhone }}</span>
                                <div class="w-56px  ml-5px"></div>
                            </div>
                            <div class="text-#474747 text-28px mt-20px">地址：{{ details.receiveSnapshot?.detailArea }}{{
                                details?.receiveSnapshot?.detailAddress }}</div>
                        </div>
                        <div class="ml-30px text-28px text-#333" v-else>未获取到收件人信息</div>
                    </div>
                    <!-- 待发货/已发货/已完成 展示 -->
                    <template v-if="route.query.ty !== 'refund'">
                        <div class="address flex items-center bg-[#fff] rounded-16px mx-30px py-33px px-30px"
                            v-if="['deliver', 'receive', 'over'].includes(details?.shopOrderRecord?.orderState)">
                            <img loading="lazy" src="@/assets/integralMall/send_ad_icon.png" class="w-37px" />
                            <div class="flex-1 ml-30px" v-if="details?.shopOrderRecord?.transportName">
                                <div class="name text-#333 text-32px font-medium flex items-center flex-wrap">
                                    <span class="mr-20px">{{ details?.shopOrderRecord?.transportName }}</span>
                                    <span class="flex-1">{{ details?.shopOrderRecord?.transportNumber }}</span>
                                    <div class="copy text-#666 bg-[#EDEDED] p-6px text-22px leading-none rounded-4px w-56px ml-10px"
                                        @click="toCopy(1)">
                                        复制</div>
                                </div>
                            </div>
                            <div class="ml-30px text-28px text-#333" v-else>未获取到发货信息</div>
                        </div>
                    </template>

                </div>
                <!-- 商品信息 -->
                <div class="mx-30px mt-20px bg-#fff rounded-12px goods">
                    <div class="company px-24px py-20px">
                        <div class="flex items-center text-30px">
                            <img loading="lazy" src="@/assets/inclusive/shop/icon-store.png"
                                class="w-34px h-32px block" />
                            <span class="ml-10px">{{ details?.transProductSnapshot?.companyName }}</span>
                        </div>
                    </div>
                    <template v-if="route.query.ty === 'refund'">
                        <div class="goods-cell px-24px py-34px flex" v-for="info, index in refundInfo?.detailVOList"
                            :key="index">
                            <div class="left-cover">
                                <img :src="utils.judgeStaticUrl(info?.productSubSnapshotJson?.productSubImg)"
                                    v-previewImg="utils.judgeStaticUrl(info?.productSubSnapshotJson?.productSubImg)"
                                    class="w-140px h-140px rounded-20px object-cover" />
                            </div>
                            <div class="right-text flex-1 flex flex-col justify-between ml-20px">
                                <div>
                                    <div
                                        class="title text-28px text-#333 text-ellipsis line-clamp-2 whitespace-nowrap text-wrap">
                                        {{ info.productSubSnapshotJson?.productName }}
                                    </div>
                                    <div
                                        class="productSub flex justify-between items-center text-#999 text-26px w-full mt-14px">
                                        <div class="">{{ info?.productSubSnapshotJson?.productSubName }}</div>
                                        <div>x{{ info?.productSubSnapshotJson?.currentProductCount }}</div>
                                    </div>
                                </div>

                                <div class="text-#FF4344 text-28px mt-10px flex justify-between">
                                    <div class="font-bold">
                                        <span class="text-24px">￥</span>{{
                                            info?.productSubSnapshotJson?.nowPrice }}
                                    </div>
                                    <div class="text-#5AA4FF text-24px">
                                        {{ info?.backResultStateText }}
                                    </div>
                                </div>

                            </div>
                        </div>
                    </template>
                    <template v-else>
                        <orderDetailCell v-for="(item, index) in details?.transProductSnapshot?.productInfoList"
                            :key="index" :content="item" :orderState="details?.shopOrderRecord?.orderState"
                            :showBtn="true" @refund="(priceIndex: any) => singleRefund(item, priceIndex)"
                            class="goods-cell">
                        </orderDetailCell>
                    </template>

                </div>


                <!-- 退款信息 -->
                <div class="refund-info bg-#fff mx-30px mt-20px p-30px" v-if="route.query.ty === 'refund'">
                    <div class="text-#333 text-32px font-bold mb-36px">退款信息</div>
                    <div class="cell text-#333 text-30px flex items-center">
                        <div class="label">申请时间</div>
                        <div class="value ml-44px flex-1">{{ refundInfo?.createTime }}</div>
                        <div class="copy text-#666 bg-[#EDEDED] p-6px text-22px leading-none rounded-4px ml-10px"
                            @click="toCopy(3)">
                            复制</div>
                    </div>
                    <div class="cell text-#333 text-30px flex items-center mt-26px">
                        <div class="label">退款原因</div>
                        <div class="value ml-44px">{{ refundInfo?.serviceReason }}</div>
                    </div>
                    <div class="cell text-#333 text-30px flex items-center mt-26px">
                        <div class="label">退款备注</div>
                        <div class="value ml-44px">{{ refundInfo?.serviceRemark || '--' }}</div>
                    </div>
                    <div class="cell text-#333 text-30px flex items-center mt-26px">
                        <div class="label">退款金额</div>
                        <div class="value text-#FF4344 ml-44px">￥{{ refundInfo?.backAmountTotal }}</div>
                    </div>
                    <div class="cell text-#333 text-30px flex items-center mt-26px" v-if="refundInfo.backIntegralTotal">
                        <div>退款积分</div>
                        <div class="value ml-44px">{{ refundInfo.backIntegralTotal }}积分</div>
                    </div>
                    <div class="cell text-#333 text-30px flex items-center mt-26px">
                        <div>审核时间</div>
                        <div class="value ml-44px">{{ refundInfo.handleTime || '-' }}</div>
                    </div>

                    <template v-if="refundInfo?.transportNumber">
                        <div class="cell text-#333 text-30px flex items-center mt-26px">
                            <div>物流公司</div>
                            <div class="value ml-44px">{{ refundInfo?.transportName }}</div>
                        </div>
                        <div class="cell text-#333 text-30px flex items-center mt-26px">
                            <div>物流单号</div>
                            <div class="value ml-44px flex-1 break-all">{{ refundInfo.transportNumber }}</div>
                            <div class="copy text-#666 bg-[#EDEDED] p-6px text-22px leading-none rounded-4px ml-10px"
                                @click="toCopy(4)">
                                复制</div>
                        </div>
                    </template>
                </div>
                <!-- 关于价格 -->
                <div class="price-info bg-#fff mx-30px mt-20px p-30px" v-else>
                    <div class="cell text-#333 text-30px flex items-center justify-between">
                        <div class="label">商品总价</div>
                        <div class="value">￥{{ totalPrice }}</div>
                    </div>
                    <div class="cell text-#333 mt-30px text-30px flex items-center justify-between" v-if="details?.shopOrderRecord?.discountAmount &&
                        details.shopOrderRecord.discountAmount !== '0.00'">
                        <div class="label">优惠券</div>
                        <div class="value text-#FF4344">
                            <span>￥{{ details.shopOrderRecord.discountAmount }}</span>
                            <van-icon name="arrow" class="text-#818181 text-30px"></van-icon>
                        </div>
                    </div>
                    <div class="cell text-#333 mt-30px text-30px flex items-center justify-between"
                        v-if="details?.shopOrderRecord?.payIntegral">
                        <div class="label">消耗积分</div>
                        <div class="value">{{ details.shopOrderRecord.payIntegral }}分</div>
                    </div>
                    <div class="cell text-#333 mt-30px text-30px flex items-center justify-between">
                        <div class="label">合计</div>
                        <div class="value">￥{{ details?.shopOrderRecord?.payAmount }}</div>
                    </div>
                </div>
                <!-- 交易信息 -->
                <div class="transaction bg-#fff mx-30px mt-20px p-30px">
                    <div class="text-#333 text-32px font-bold mb-36px">交易信息</div>
                    <div class="cell text-#333 text-30px flex items-center">
                        <div class="label text-#666666">订单编号</div>
                        <div class="flex-1 ml-44px value break-all">{{ details?.orderId }}</div>
                        <div class="copy text-#666 bg-[#EDEDED] p-6px text-22px leading-none rounded-4px"
                            @click="toCopy(2)">
                            复制</div>
                    </div>
                    <div class="cell mt-36px text-#333 text-30px flex items-center">
                        <div class="label text-#666666">创建时间</div>
                        <div class="value flex-1 ml-44px">{{ details?.shopOrderRecord?.createTime }}</div>
                    </div>
                    <div class="cell mt-36px text-#333 text-30px flex items-center"
                        v-if="details?.shopOrderRecord?.paymentTime">
                        <div class="label text-#666666">付款时间</div>
                        <div class="value flex-1 ml-44px">{{ details?.shopOrderRecord?.paymentTime }}</div>
                    </div>
                    <div class="cell mt-36px text-#333 text-30px flex items-center"
                        v-if="details?.shopOrderRecord?.deliverTime">
                        <div class="label text-#666666">发货时间</div>
                        <div class="value flex-1 ml-44px">{{ details?.shopOrderRecord?.deliverTime }}</div>
                    </div>
                    <div class="cell mt-36px text-#333 text-30px flex items-center"
                        v-if="details?.shopOrderRecord?.receiveTime">
                        <div class="label text-#666666">收货时间</div>
                        <div class="value flex-1 ml-44px">{{ details?.shopOrderRecord?.receiveTime }}</div>
                    </div>
                </div>
            </div>

            <div class="controll-btns h-110px bg-#fff flex items-center justify-end px-30px" v-if="orderBtnArr?.length"
                :class="{ 'justify-center': orderBtnArr?.length === 1 }">
                <div v-for="item, index in orderBtnArr" :key="index" class="text-32px btns default-btn 
                rounded-30px py-16px px-32px flex items-center justify-center 
                leading-none mr-20px" :class="{
                    'active-btn': item.type === 'pay' || item.type === 'receive' || item.type === 'evaluate',
                    'w-60%': orderBtnArr?.length === 1
                }" @click.stop="handleControll(item)">
                    {{ item.text }}
                </div>
            </div>
        </van-skeleton>

        <!--确认收货弹窗 -->
        <confirmReceivePopup v-model:show="popupShow" @confirm="confirmReceiveFn" :bgCover="bgCover"
            :goodsNum="goodsNum">
        </confirmReceivePopup>
        <!-- 评价 -->
        <evaluatePopup v-model:show="evaluateShow" :productList="details?.transProductSnapshot?.goodsFlatten"
            @submit="getEvaluateInfo">
        </evaluatePopup>
        <!-- 失败提示 -->
        <!-- <confirmPopup v-model:show="failShow" @confirm="failShow = false" :tips="errorTips" confirmText="我知道了"
            :showCancel="false"></confirmPopup> -->
    </div>
</template>
<script lang="ts" setup>
import waitSendStatus from '@/assets/integralMall/wait_send_bg.png';
import waitReStatus from '@/assets/integralMall/wait_re_bg.png';
import finishStatus from '@/assets/integralMall/finished_bg.png';
import refundStatus from '@/assets/inclusive/shop/refund-fill.png';
import { orderDetails, refundDetails } from "@/api/mall/inclusive"
import { useDictionary } from '@/store/modules/dictionary';
import orderDetailCell from './components/orderDetailCell.vue';
import utils from '@/utils/utils';
import useOrderFn from '@/hooks/orderHooks.ts'
import useRefreshFun from '@/hooks/app.ts';
import router from '@/router';
import { showToast } from 'vant';

const confirmReceivePopup = defineAsyncComponent(() => import('@/components/Popup/confirmReceivePopup.vue'))
const evaluatePopup = defineAsyncComponent(() => import('./components/evaluatePopup.vue'))
const confirmPopup = defineAsyncComponent(() => import('@/components/Popup/confirmPopop.vue'))
const dictionary = useDictionary()
const statusArr = dictionary.getDictionaryOpt?.['transOrderState'] || []
const route = useRoute()
const details = ref<any>({})
const statusMatch = ref({
    statusText: '',
    statusImg: '',
    statusDes: ''
})
const loading = ref(true)
const totalPrice = computed(() => {
    return (parseFloat(details.value?.shopOrderRecord.payAmount) +
        parseFloat(details.value?.shopOrderRecord.discountAmount)).toFixed(2)
})
const getOrderDetail = async () => {
    const res = await orderDetails(route.query.orId)
    loading.value = false
    if (res.code === 200) {
        details.value = res.data
        // 扁平化商品数据
        let goodsFlatten: any = [] // 商品列表扁平化处理
        details.value.transProductSnapshot.productInfoList.forEach((goods: any) => {
            goodsFlatten.push(...goods.priceListInfo)
        })
        details.value.transProductSnapshot.goodsFlatten = goodsFlatten
    }
}
// 退款详情
const refundInfo = ref<any>({})
const failShow = ref(false)
const errorTips = ref('')

const getRefundDetail = () => {
    refundDetails(route.query.sId).then(res => {
        if (res.code === 200) {
            refundInfo.value = res.data
            refundInfo.value.detailVOList.forEach((item: any) => {
                item.backResultStateText = dictionary.getDictionaryOpt?.['backAmountState'].find((dic: any) => dic.value === item.backResultState)?.label || ''
            })
            // if (refundInfo.value.serviceState === 'refuse') {
            //     errorTips.value = '退款失败原因：' + refundInfo.value.handleRemark

            //     failShow.value = true
            // }
        }
    })
}

watchEffect(() => {
    // 订单详情状态|退款状态变化时重新计算顶部信息

    if (details.value?.shopOrderRecord?.orderState || refundInfo.value?.serviceState) {
        mathStatusFn()
    }
})


const orderBtnArr = ref<any>([])
const mathStatusFn = () => {
    // 退款订单信息
    if (route.query.ty === 'refund') {
        statusMatch.value.statusText = dictionary.getDictionaryOpt?.['saleServiceState'].find((item: any) => item.value === refundInfo.value?.serviceState)?.label || ''
        statusMatch.value.statusImg = refundStatus
        switch (refundInfo.value?.serviceState) {
            case 'wait':
                return [{ text: '取消售后申请', type: 'cancelRefund' }]
            case 'transport':
                return [{ text: '取消售后申请', type: 'cancelRefund' },
                { text: '上传单号', type: 'upload' }]

            case 'refuse':
                if (refundInfo.value?.handleRemark) statusMatch.value.statusDes = '退款失败原因：' + refundInfo.value?.handleRemark
                return orderBtnArr.value = [
                    { text: '申请退款', type: 'refund' }
                ]
        }
        return
    }

    // 正常订单信息
    switch (details.value?.shopOrderRecord?.orderState) {
        case 'deliver':
            statusMatch.value.statusText = '待发货'
            statusMatch.value.statusDes = '支付成功，等待商家发货'
            statusMatch.value.statusImg = waitSendStatus
            orderBtnArr.value = [
                { text: '申请退款', type: 'refund' }
            ]

            break;
        case 'receive':
            statusMatch.value.statusText = '待收货'
            statusMatch.value.statusDes = '商品已发出，请耐心等待'
            statusMatch.value.statusImg = waitReStatus
            orderBtnArr.value = [
                { text: '申请退款', type: 'refund' },
                { text: '确认收货', type: 'receive' }
            ]

            break;
        case 'over':
            statusMatch.value.statusText = '已完成'
            statusMatch.value.statusDes = '商品已完成收货'
            statusMatch.value.statusImg = finishStatus
            if (details.value?.shopOrderRecord?.evaluated === 'n') orderBtnArr.value = [
                { text: '去评价', type: 'evaluate' }
            ]
            break;

        case 'payment':
            statusMatch.value.statusImg = waitSendStatus
            statusMatch.value.statusText = '待支付'
            statusMatch.value.statusDes = '订单已生成，请尽快支付'
            orderBtnArr.value = [
                { text: '取消订单', type: 'cancel' },
                { text: '立即支付', type: 'pay' }
            ]
            break;
        default:
            statusMatch.value.statusImg = waitSendStatus
            statusMatch.value.statusText =
                statusArr.find((item: any) => item.value === details.value?.shopOrderRecord?.orderState)?.label || ''
            statusMatch.value.statusDes = ''
            break;
    }
}



const toCopy = (from: number) => {
    if (from === 1) utils.copyToClibboard(details.value?.shopOrderRecord.transportNumber)//物流单号
    else if (from === 2) utils.copyToClibboard(details.value?.orderId)//订单编号
    else if (from === 3) utils.copyToClibboard(refundInfo.value?.createTime)//退款时间
    else if (from === 4) utils.copyToClibboard(refundInfo.value?.transportNumber)//退款运单号
}
// 按钮操作
const { orderCancelFn, orderConfirmFn, submitCommentFn, cancelAfterSaleFn } = useOrderFn()
const popupShow = ref(false)
const bgCover = ref('')
const goodsNum = ref(0)


const handleControll = (typeInfo: any) => {
    switch (typeInfo.type) {
        case 'cancel':
            // 取消订单
            orderCancelFn(route.query.orId, () => {
                refresh()
                setTimeout(() => {
                    router.go(-1)
                }, 1000)
            })
            break;
        case 'pay':
            showToast('暂不支持支付')
            // 去支付
            break;
        case 'receive':
            // 确认收货
            goodsNum.value = 0
            bgCover.value = details.value?.transProductSnapshot?.productInfoList[0]?.priceListInfo[0]?.productSubImg
            details.value?.transProductSnapshot?.productInfoList.forEach((item: any) => {
                goodsNum.value += item.priceListInfo?.length || 0
            })
            popupShow.value = true

            break;
        case 'evaluate':
            // 去评价
            evaluateShow.value = true
            break;
        case 'refund':
            router.replace({
                path: '/order/refund',
                query: {
                    orId: details.value.orderId,
                    type: 'backOrder'
                }
            })
            break
        case 'cancelRefund':
            // 取消退款申请
            cancelAfterSaleFn({ serviceGroupId: refundInfo.value.serviceGroupId }, () => {
                refresh()
            })
            break;
        case 'upload':
            // 上传单号
            router.push({
                path: '/order/logistics',
                query: {
                    orId: details.value.orderId,
                    sId: refundInfo.value.serviceGroupId
                }
            })
            break;
    }
}
// 确认收货
const confirmReceiveFn = () => {
    popupShow.value = false
    orderConfirmFn(details.value.orderId, () => {
        refresh()
    })
}
// 评价
const evaluateShow = ref(false)
const getEvaluateInfo = (inclusiveProductCommentList: any) => {
    submitCommentFn({
        orderId: details.value.orderId,
        inclusiveProductCommentList: inclusiveProductCommentList.map((item: any) => {
            if (item.anonymousFlg) item.anonymousFlg = 'y'
            else item.anonymousFlg = 'n'
            return item
        })
    }, () => {
        evaluateShow.value = false
        refresh()
        setTimeout(() => {
            router.go(-1)
        }, 1000)
    })
}

// ====退款====

// 单个退款 
// productInfo:商品信息,priceIndex:规格索引值
const singleRefund = (productInfo: any, priceIndex: any) => {
    router.replace({
        path: '/order/refund',
        query: {
            type: 'backDetail',
            orId: details.value.orderId,
            psubId: productInfo.priceListInfo[priceIndex].orderProductSubId,//规格id
        }
    })
}

// ====end====

// 触发列表刷新执行事件
const { addRefreshList } = useRefreshFun()
const refresh = () => {
    addRefreshList({ pageName: 'myOrders', funsName: ['changePage'] })
}

onBeforeMount(() => {
    getOrderDetail()
    if (route.query.ty === 'refund') {
        getRefundDetail()
    }
})
</script>
<style scoped lang="scss">
.goods {
    .goods-cell {
        border-bottom: 1px solid #F2F2F2;

        &:last-child {
            border-bottom: none;
        }
    }
}

.company {
    border-bottom: 1px solid #F2F2F2;
}

.controll-btns {
    border-top: 1px solid #EFEFEF;

    .default-btn {
        border: 1px solid #999;
        color: #999;
    }

    .active-btn {
        background: linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%);
        color: #fff;
        border: none;
    }

    .btns:last-child {
        margin-right: 0
    }
}
</style>