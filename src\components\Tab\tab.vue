<template>
  <div class="tab overflow-hidden ">
    <div class="flex justify-between items-center">
      <van-tabs v-model:active="data.active" class="w-90%" swipeable title-active-color="#5AA4FF"
        :swipe-threshold="swipeThreshold" @change="chooseTab">
        <van-tab v-for="(item, index) in data.nav" :title="item.labelName" class="text-34px" :key="index">
          <!-- 内容 {{ index }} -->
        </van-tab>
      </van-tabs>
      <img loading="lazy" src="@/assets/public/icon_sx.png" alt="" class="w-29px h-29px" @click="showOverLay" />
    </div>
    <van-overlay :show="show" @click="show = false" lock-scroll>
      <div class="wrapper w-full relative top-220px !h-[calc(100%-220px)]" :class="wrapperClass"
        @click.stop="showOverLay">
        <div class="bg-#fff p-30px pr-10px max-h-28vh overflow-scroll" v-if="showArea">
          <div class="text-32px text-#333 mb-22px">区域</div>
          <div class="flex flex-wrap">
            <div v-for="(item, index) in areaList" :key="index"
              :class="active == index ? 'active text-#5AA4FF' : 'bg-#F6F7F8'"
              class="kind rounded-10px text-center py-17px mb-20px text-28px mr-20px" @click="chooseArea(index, item)">
              {{ item.dictName }}
            </div>
          </div>
        </div>
        <div class="bg-#fff p-30px pr-10px max-h-30vh overflow-scroll">
          <div class="text-32px text-#333 mb-22px">选择分类</div>
          <div class="flex flex-wrap">
            <div v-for="(item, index) in data.nav" :key="index"
              :class="data.active == index ? 'active text-#5AA4FF' : 'bg-#F6F7F8'"
              class="kind rounded-10px text-center py-17px mb-20px text-28px mr-20px" @click="choose(index, item)">
              {{ item.labelName }}
            </div>
          </div>
        </div>

      </div>
    </van-overlay>
  </div>
</template>
<script lang="ts" setup>
import { useDictionary } from '@/store/modules/dictionary';
const dictionary = useDictionary()
const props = defineProps({
  data: {
    type: Object,
    default: {},
  },
  wrapperClass: {
    type: String,
    default: '',
  },
  swipeThreshold: {
    type: Number,
    default: 5,
  },
  showArea: {
    type: Boolean,
    default: false

  }
});

const show = ref(false);
const emit = defineEmits(['showOverLay', 'chooseIndex', 'chooseArea']);
//显示遮罩
function showOverLay() {
  show.value = !show.value;
}
function chooseTab(index) {
  props.data.nav[index].activIndex = index;
  emit('chooseIndex', props.data.nav[index]);
}
function choose(index, item) {
  // props.data.active=index;
  item.activIndex = index;
  console.log(index);
  emit('chooseIndex', item);
}
function chooseArea(index, item) {
  item.index = index;
  active.value = index;
  emit('chooseArea', item);
}
const areaList = ref([]);
const active = ref(0)
onMounted(() => {
  if (props.showArea) {
    areaList.value = [...dictionary.getDictionaryOBJMap?.['regionCode']]
  }
})

</script>
<style lang="scss">
.tab {
  .van-tab__text {
    font-size: 32px;
  }

  .van-overlay {
    background: transparent;
  }

  .wrapper {
    width: 100%;
    background: rgb(0, 0, 0, 0.3);
  }

  .active {
    background: rgba(90, 164, 255, 0.1);
  }

  .kind {
    min-width: 157px;
  }
}
</style>
