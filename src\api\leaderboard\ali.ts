import {dataCenterHttp,aliHttp}from '@/utils/http/axios'
// 权限校验
export const authLeaderView = () => {
   return dataCenterHttp.get({
        url:'/userCadreInfo/cadreInfo'
    })
}
// 用户活跃指数-首页
export const userActiveIndex = (params:any) => {
    return aliHttp.get({
        url:'/getH5FirstPage',
        params
    })
}
// ALI榜单
export const aliRanking = (params:any) => {
    return aliHttp.get({
        url:'/getTableList',
        params
    })
}
