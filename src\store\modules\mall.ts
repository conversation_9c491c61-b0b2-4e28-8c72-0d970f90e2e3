import { defineStore } from 'pinia';
import { store } from '@/store';
export const useMallStore = defineStore({
    id: 'app-mallInfo',
    state: () => ({
        selectIntegralGoodsInfo:{},//当前支付积分商品信息
        selectedInclusiveGoodsList:[],//当前选中积分商品信息
        addressInfo:{},//当前选中地址
        ordersPageNum: 0,//普惠订单页码

    }),
    getters: {
        getselectIntegralGoodsInfo: (state) => state.selectIntegralGoodsInfo,
        getInclusiveGoodsList: (state) => state.selectedInclusiveGoodsList,
        getAddressInfo: (state) => state.addressInfo,
        getOrdersPageNum: (state) => state.ordersPageNum,
    },
    actions: {
        setOrdersPageNum(num:number) {
            this.ordersPageNum = num;
        },
        // 积分商城商品信息暂存
        setIntegralGoodsInfo(info:any) {
            this.selectIntegralGoodsInfo = info;
        },
        // 普惠商城商品信息暂存
        setInclusiveGoodsList(info:any) {
            this.selectedInclusiveGoodsList = info;
        },
        setAddressInfo(info:any) {
            this.addressInfo = info;
        },
    },
})
export function useMallStoreWithOut() {
    return useMallStore(store);
}