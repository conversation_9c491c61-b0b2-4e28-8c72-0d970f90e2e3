<template>
    <div class="echart-box" style="width:100%;height:100%">
        <div ref="echartRef" class="w-full h-full"></div>
    </div>
</template>

<script lang="ts" setup>
defineOptions({
    name: 'unionTrendChart'
})
// 饼图
import { useECharts } from '@/utils/useECharts'
import { Ref, ref, watch, onMounted } from 'vue'
import * as echarts from 'echarts'
import { LabelBg, getVwSize } from '../../data'

const props = defineProps({
    dataSource: {
        type: Object,
        default: {
            columnList: [],
            joinCountList: [],
            readCountList: [],
        },
    }
})

const echartRef = ref<HTMLDivElement | null>(null)
const { setOptions } = useECharts(echartRef as Ref<HTMLDivElement>)

const initChart = () => {
    let data = { x: [], y1: [], y2: [] }
    let maxNumJoin = 0, maxNumRead = 0
    data.x = props.dataSource?.columnList
    if (props.dataSource?.joinCountList) {
        maxNumJoin = Math.max(...props.dataSource?.joinCountList)
        if (maxNumJoin >= 10000) {
            data.y1 = props.dataSource?.joinCountList.map((item: any) => parseFloat((item / 100000).toFixed(2)))
        } else {
            data.y1 = props.dataSource?.joinCountList
        }

    }
    if (props.dataSource?.readCountList) {
        maxNumRead = Math.max(...props.dataSource?.readCountList)
        if (maxNumRead >= 10000) {
            data.y2 = props.dataSource?.readCountList.map((item: any) => parseFloat((item / 100000).toFixed(2)))
        } else {
            data.y2 = props.dataSource?.readCountList
        }

    }
    var bar = {
        barColor: ['rgba(79, 172, 254, 1)', 'rgba(0, 215, 254, 1)'],
        pictorialBarColor: ['rgba(23, 144, 255, 1)', 'rgba(252, 161, 38, 1)', '#FF3522'],
    }
    let dataZoom = [
        {
            // 设置滚动条的隐藏与显示
            show: true,
            // 设置滚动条类型
            type: 'slider',
            // 设置背景颜色
            backgroundColor: 'rgba(237, 244, 252, 1)',
            // 设置选中范围的填充颜色
            fillerColor: 'rgba(186, 219, 255, 1)',
            // 设置边框颜色
            borderColor: 'rgba(237, 244, 252, 1)',
            // 是否显示detail，即拖拽时候显示详细数值信息
            showDetail: false,
            // 数据窗口范围的起始数值
            startValue: 0,
            // 数据窗口范围的结束数值（一页显示多少条数据）
            endValue: 5,
            // empty：当前数据窗口外的数据，被设置为空。
            // 即不会影响其他轴的数据范围
            filterMode: 'empty',
            // 设置滚动条宽度，相对于盒子宽度
            width: '90%',
            // 设置滚动条高度
            height: 8,
            // 设置滚动条显示位置
            left: 'center',
            // 是否锁定选择区域（或叫做数据窗口）的大小
            zoomLoxk: true,
            // 控制手柄的尺寸
            handleSize: 0,
            // dataZoom-slider组件离容器下侧的距离
            bottom: 15,
        },
        {
            // 没有下面这块的话，只能拖动滚动条，
            // 鼠标滚轮在区域内不能控制外部滚动条
            type: 'inside',
            // 滚轮是否触发缩放
            zoomOnMouseWheel: false,
            // 鼠标滚轮触发滚动
            moveOnMouseMove: true,
            moveOnMouseWheel: true,
        },
    ]

    // 绘制图表
    var option = {
        // tooltip: {
        //   show: true,
        //   trigger: 'axis',
        //   formatter: (params) => {
        //     return ''
        //   },
        //   axisPointer: {
        //     type: 'shadow',
        //     // shadowStyle:{
        //     //   color:new echarts.graphic.LinearGradient(0, 0, 1, 1, [{
        //     //       offset: 0,
        //     //       color: 'rgba(141, 226, 218, 0.1)'
        //     //     },
        //     //     {
        //     //       offset: 1,
        //     //       color: 'rgba(141, 226, 218, 0.8)'
        //     //     }
        //     //     ])
        //     // }
        //   }
        // },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                label: {
                    show: true,
                    backgroundColor: 'rgba(141, 226, 218, .8)',
                    color: '#fff',
                    borderColor: 'rgba(0,0,0,0)',
                    shadowColor: 'rgba(0,0,0,0)',
                    shadowOffsetY: 0,
                    fontSize: getVwSize(22)
                },
                type: 'line',
                lineStyle: {
                    color: 'rgba(141, 226, 218, .2)',
                    width: getVwSize(28),
                    type: 'solid'
                }
            },
            backgroundColor: '#fff',
            textStyle: {
                fontSize: getVwSize(22)
            },
            rounded: getVwSize(40),
            padding: [getVwSize(10), getVwSize(10)],
            extraCssText: 'box-shadow: 1px 0 2px 0 rgba(163,163,163,0.2)',
            formatter: function (params: any) {
                let result = params[0].name + '<br/>'
                params.forEach((item: any) => {
                    let value = item.value
                    if (item.seriesIndex === 0) {
                        value = props.dataSource.joinCountList[item.dataIndex]
                    }
                    else if (item.seriesIndex === 1) {
                        value = props.dataSource.readCountList[item.dataIndex]
                    }
                    result += item.marker + item.seriesName + ':' + value + `<br/>`
                })
                return result
            }
        },
        grid: {
            top: '20%',
            left: '0%',
            right: '3%',
            bottom: '0%',
            containLabel: true,
        },
        legend: {
            show: true,
            icon: 'circle',
            orient: 'horizontal',
            top: '0%',
            right: '0%',
            itemWidth: getVwSize(20),
            itemHeight: getVwSize(20),
            itemGap: getVwSize(60),

            textStyle: {
                color: '#666666',
                fontSize: getVwSize(24),
            },
        },
        xAxis: [
            {
                type: 'category',
                splitLine: {
                    show: false,
                },
                axisTick: {
                    show: false,
                },
                splitArea: {
                    show: false,
                },
                axisLine: {
                    show: false,
                },
                boundaryGap: true,
                axisLabel: {
                    show: true,
                    interval: 0,
                    rotate: 40,
                    fontSize: getVwSize(22),
                    lineHeight: getVwSize(30),
                    margin: getVwSize(20),
                    color: '#999',
                },
                data: data.x,
            },
        ],
        yAxis: [
            {
                name: `参与量(${maxNumJoin >= 10000 ? '(万)' : ''}人)`,
                nameTextStyle: {
                    color: '#999',
                    fontSize: getVwSize(20),
                    padding: [0, 0, 0, getVwSize(50)]
                },
                type: 'value',
                min: 0,
                minInterval: 1,
                splitLine: {
                    show: true,
                },
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: '#E6E6E6',
                    },
                },
                axisLabel: {
                    textStyle: {
                        color: '#999',
                        fontSize: getVwSize(20),
                    },
                },
                axisTick: {
                    show: false,
                },
            },
            {
                name: `访问量(${maxNumRead >= 10000 ? '(万)' : ''}人)`,
                nameTextStyle: {
                    color: '#999',
                    fontSize: getVwSize(20),
                    padding: [0, getVwSize(50), 0, 0]
                },
                type: 'value',
                min: 0,
                minInterval: 1,
                splitLine: {
                    show: true,
                },
                axisLine: {
                    show: false,
                    lineStyle: {
                        color: '#E6E6E6',
                    },
                },
                axisLabel: {
                    textStyle: {
                        color: '#999',
                        fontSize: getVwSize(20),
                    },
                },
                axisTick: {
                    show: false,
                },
            },
        ],
        series: [
            {
                name: '参与量',
                type: 'line',
                smooth: true,
                symbol: 'circle',
                showSymbol: false,
                yAxisIndex: 0,
                z: 1,
                lineStyle: {
                    width: getVwSize(6),
                    shadowColor: 'rgba(66, 163, 255, 0.1)',
                    shadowBlur: getVwSize(6),
                    shadowOffsetY: getVwSize(6)
                },
                itemStyle: {
                    normal: {
                        color: '#42A3FF',
                    },
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [{
                        offset: 0,
                        color: '#fff'
                    },
                    {
                        offset: 1,
                        color: '#D8ECFF'
                    }
                    ])
                },
                data: data.y1,
            },
            {
                name: '访问量',
                type: 'line',
                yAxisIndex: 1,
                smooth: true,
                symbol: 'circle',
                showSymbol: false,
                z: 3,
                // label: {
                //   show: true,
                //   position: 'top',
                //   formatter: (params) => {
                //     return `{c|${params.value}w}`;
                //   },
                //   rich: {
                //     c: {
                //       color: '#fff',
                //       fontSize: 12,
                //       padding: [4, 10, 10],
                //       verticalAlign: "top",
                //       backgroundColor: {
                //         image: LabelBg
                //       },
                //     }
                //   }
                // },

                lineStyle: {
                    width: getVwSize(6),
                    shadowColor: 'rgba(98, 42, 220, 0.1)',
                    shadowBlur: getVwSize(6),
                    shadowOffsetY: getVwSize(6)

                },
                itemStyle: {
                    normal: {
                        color: '#8DE2DA',
                    },
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [{
                        offset: 0,
                        color: '#fff'
                    },
                    {
                        offset: 1,
                        color: 'rgba(141, 226, 218, 0.1)'
                    }
                    ])
                },
                data: data.y2,
            },
        ],
    }

    if (data.x?.length > 10) {
        option.dataZoom = dataZoom
    }

    setOptions(option)
}

onMounted(() => {
    initChart()
})

// 监听dataSource
watch(
    () => props.dataSource,
    (newValue, oldValue) => {
        initChart()
    },
    { deep: true, immediate: true }
)
</script>
