<template>
  <refreshList @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
    <van-cell v-for="(item, index) in Data.listData" :key="index" class="!flex items-center px-0 w-full"
      @click="handleAsk(item)">
      <div>
        <img loading="lazy" :src="item.avatar" class="w-[66px] h-[66px] rounded-[20px]" />
      </div>
      <div class="flex flex-col justify-between pl-[37px] w-full">
        <div class="flex items-center justify-between w-full">
          <span class="text-[#333333]"> {{ item.userName || '' }}</span>
          <span class="text-[#999999]">
            {{ fixDateTime(item.psychologicaDialogue?.createTime) }}</span>
        </div>

        <div class="w-full flex justify-between items-center">
          <div class="text-[24px] text-[#999999] truncate w-full max-w-[390px]">
            {{ item?.psychologicaDialogue?.content || '' }}
          </div>
          <div v-if="item.notReadNum > 0"
            class="rounded-full flex justify-center items-center w-[40px] h-[40px] bg-[#EB4135] text-[22px] text-[#fff]">
            {{ item.notReadNum > 99 ? '99' : item.notReadNum }}</div>
        </div>
      </div>
    </van-cell>
  </refreshList>
</template>

<script lang="ts" setup>
import refreshList from '@/components/refreshList/index.vue';
import dayjs from 'dayjs';
import { judgeStaticUrl } from '@/utils/utils';
import { getExpertLists } from '@/api/soulStation';
const router = useRouter();

function handleAsk(item) {
  router.push({
    path: '/chatDoctor',
    query: { psychologicalExpertId: item.psychologicalExpertId, showIcon: Boolean(false) },
  });
}

function fixDateTime(time: string | number | dayjs.Dayjs | Date | null | undefined) {
  const day = dayjs(dayjs().format('YYYY-MM-DD')).diff(dayjs(time).format('YYYY-MM-DD'), 'day');
  switch (day) {
    case 0:
      return '今天' + dayjs(time).format('HH:mm');
    case 1:
      return '昨天' + dayjs(time).format('HH:mm');
    default:
      return dayjs(time).format('YYYY-MM-DD HH:mm');
  }
}

const Data = ref({
  pageNum: 1,
  listData: [],
});
// 刷新
const onRefreshList = () => {
  Data.value.pageNum = 1;
  getList();
};
// 加载更多
const onLoadMore = () => {
  Data.value.pageNum++;
  getList();
};
//搜索列表
const loadMoreRef = ref(null);
//获取专家列表
function getList() {
  getExpertLists({
    pageNum: Data.value.pageNum,
    pageSize: 10,
  }).then(res => {
    if (res.code == 200) {
      res.data.forEach((item: Recordable) => {
        item.avatar = judgeStaticUrl(item.avatar, true);
      });
      if (Data.value.pageNum === 1) Data.value.listData = [];
      Data.value.listData = Data.value.listData.concat(res.data);
      //重置刷新状态及 判断是否加载完成
      if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(Data.value.listData.length, res.total);
      }
    }
  });
}
onMounted(() => {
  getList();
});
</script>

<style lang="less" module></style>
