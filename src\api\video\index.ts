import { h5Http } from '@/utils/http/axios';



/**
 * 获取视频专区列表
 * @param params 请求参数
 * @returns Promise
 */
export const getShortPublicVideos = (params: Recordable) => {
    return h5Http.get({
        url: '/videoModel/findVoListH5',
        params
    });
}


/**
 * 获取我的视频作品列表
 * @param params 请求参数
 * @returns Promise
 */
export function getMyShortVideos(params: Recordable) {
    return h5Http.get({
        url: '/shortVideo/getVideoList',
        params
    });
}


/**
 * 收藏的视频作品列表 (我的收藏)
 * @param params 请求参数
 * @returns Promise
 */
export function getMyFavoritesVideos(params: Recordable) {
    return h5Http.get({
        url: '/shortVideo/selectCollectList',
        params
    });
}


/**
 * 获取被点赞收场的视频列表
 * @param params 请求参数
 * @returns Promise
 */
export function getMyLikeAndCollect(params: Recordable) {
    return h5Http.get({
        url: '/shortVideo/likeAndCollectList',
        params
    });
}


/**
 * 获取评论列表
 * @param params 请求参数
 *  @returns Promise
 */
export function getReplyCommentList(params: Recordable) {
    return h5Http.get({
        url: '/shortVideoComments/selectReplyCommentsList',
        params
    });
}



/**
 * 获取我的视频统计
 * @param params 请求参数
 * @returns Promise
 */
export function getMyVideoStatistics(params: Recordable) {
    return h5Http.get({
        url: '/shortVideo/getUserShortVideoCount',
        params
    });
}



/**
 * 获取视频详情
 * @param params 请求参数
 * @returns Promise
 */
export function getVideoDetails(params: Recordable) {
    return h5Http.get({
        url: '/shortVideo/selectById',
        params
    });
};

export function getLoveDetails(params: Recordable) {
    return h5Http.get({
        url: '/liveInfo/getVoByDtoH5',
        params
    });
};




/**
 * 视频作品上传
 * @param params 请求参数
 * @returns Promise
 */
export function uploadVideo(params: Recordable) {
    return h5Http.post({
        url: '/shortVideo/saveByDTO',
        data: params,
    });
};

/**
 * 作品上传描述
 * @param params 请求参数
 * @returns Promise
 */
export function uploadVideoDesc(params: Recordable) {
    return h5Http.get({
        url: '/shortVideoDescription/getDescription',
        data: params,
    });
};

/**
 * 作品上传前校验
 * @param params 请求参数
 * @returns Promise
 */
export function checkVideo(params: Recordable) {
    return h5Http.get({
        url: '/shortVideo/checkRule',
        data: params,
    });
};


/**
 * 获取视频评论列表
 * @param params 请求参数
 * @returns Promise
 */
export function getVideoComments(params: Recordable) {
    return h5Http.get({
        url: '/shortVideoComments/selectCommentsList',
        params,
    });
};

/**
 * 发布评论
 * @param params 请求参数
 * @returns Promise
 */
export function publishComment(params: Recordable) {
    return h5Http.post({
        url: '/shortVideoComments/saveByDTO',
        data: params,
    });
};


/**
 * 删除评论
 * @param params 请求参数
 * @returns Promise
 */
export function deleteFunComment(params: Recordable) {
    return h5Http.post({
        url: `/shortVideoComments/deleteById/${params.shortVideoCommentsId}`,
        data: params,
    });
};


/**
 * 删除视频
 * @param params 请求参数
 * @return Promise
 */
export function deleteFunVideo(params: Recordable) {
    return h5Http.post({
        url: `/shortVideo/deleteById/${params.shortVideoId}`,
        data: params,
    })
}


/**
 * 我的视频列表
 * @param params 请求参数
 * @return Promise
 */
export function getPersonalList(params: Recordable) {
    return h5Http.get({
        url: `/shortVideo/getPersonalList`,
        params,
    })
}
//前置校验
export function checkUserIdentity(params: Recordable) {
    return h5Http.get({
        url: `/shortVideo/checkUserIdentity`,
        params,
    })
}

// 卡号信息保存
export function saveCardInfo(params: Recordable) {
    return h5Http.post({
        url: `/shortVideoAccount/saveOrUpdateByDTO`,
        data: params,
    })
}
// 卡号信息查询 
export function getCardInfo() {
    return h5Http.get({
        url: `/shortVideoAccount/queryAccountInfo`,
    })
}
