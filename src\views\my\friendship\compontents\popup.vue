<template>
    <div>
        <van-popup :show="showPop" class="popup1" round position="bottom">
            <van-form @submit="submit" ref="formRef" class="flex flex-col items-center">
                <div
                    class="text-center text-32px text-#2d2d2d font-500 py40px border-b-solid border-b-1px border-b-#F6F7F8 mb-20px w-full">
                    审核不通过的原因
                </div>
                <div class="text-input w-full">
                    <van-field v-model="formData.auditRemarks" label="" rows="5" type="textarea" label-width="0"
                        placeholder="请对审核不通过的原因进行简要说明"></van-field>
                </div>
                <Button name="提交" type="small" class="w-400px h-79px my100px" native-type="submit"
                    @click=" formRef?.submit()"></Button>
                <img loading="lazy" :src="iconClose" class="absolute right-40px top-50px w-25px h-auto" alt="" srcset=""
                    @click="close">
            </van-form>

        </van-popup>
    </div>

</template>
<script lang="ts" setup>
import Button from "@/components/Button/button.vue";
import router from "@/router";
import iconClose from "@/assets/friendship/audit/icon_off.png"
const show = ref(false)

const props = defineProps({
    showPop: {
        type: Boolean,
        default: false
    },
    type: {
        type: String,
        default: 'error'
    },
})
watch(() => props.showPop, (val) => {
    if (!props.showPop) formData.value = { auditRemarks: "" }
})
const formRef = ref(null)
const formData = ref({
    auditRemarks: ""
})
const emit = defineEmits(['closePopup', 'submitPopup']);

function submit() {
    emit('submitPopup', formData.value)
}

function close() {
    show.value = false;
    emit('closePopup', false)
}

</script>
<style lang="scss" scoped>
.van-overlay {
    background: rgba(0, 0, 0, .4);
}

.popup1 {
    .text-input {
        :deep(.van-cell) {
            padding: 0;
        }

        :deep(.van-field__control) {
            background: #f6f7f8;
            border-radius: 10px;
            padding: 20px 35px;
            box-sizing: border-box;
            width: calc(100% - 60px);
            margin: 0 auto;
            font-size: 28px;
        }

        :deep(.van-field__error-message) {
            padding: 10px 35px;
        }
    }

}
</style>