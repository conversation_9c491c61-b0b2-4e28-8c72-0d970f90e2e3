import { h5Http,dataCenterHttp,fileHttp } from '@/utils/http/axios';
import { BasicResponse } from '.';
import { log } from 'console';
//专家列表
export const psychologicalExpert = (params) => {
    return h5Http.get({
        url: '/psychologicalExpert/findVoList',
        params,
    });
};
//获取专家详情
export const getExpertVoByDto = (params) => {
    return h5Http.get({
        url: '/psychologicalExpert/getVoByDto',
        params,
    });
};

//咨询列表
export const getExpertLists = (params) => {
    return h5Http.get({
        url: '/psychologicalExpert/getExpertList',
        params,
    });
};
//咨询或回复发送消息
export const sendMessage = (params) => {
    return h5Http.post({
        url: '/psychologicaDialogue/sendMessage',
        params,
    });
};
//对话列表
export const selectDialogueList = (params) => {
    return h5Http.get({
        url: '/psychologicaDialogue/selectDialogueList',
        params,
    });
};
//获取专家热线
export const psychologicalHotline = (params) => {
    return h5Http.get({
        url: '/psychologicalHotline/findVoList',
        params,
    });
};
//查询当前登录用户是否心理专家
export const whetherExpert = (params) => {
    return h5Http.get({
        url: '/psychologicalExpert/whetherExpert',
        params,
    });
};
//专家获取咨询列表
export const getPsychologicalUserList = (params) => {
    return h5Http.get({
        url: '/psychologicalExpert/getPsychologicalUserList',
        params,
    });
};
//获取资讯未读数
export const getNotReadCount = (params) => {
    return h5Http.get({
        url: '/psychologicaDialogue/getNotReadCount',
        params,
    });
};
//获取心理测评token
export const getPsychologicalToken = (params) => {
    return h5Http.post({
        url: '/psychologicalUser/getPsychologicalToken',
        params,
    });
};
//获取心理测评url
export const getPsychologicalUrl = (params) => {
    return h5Http.get({
        url: '/psychologicalUser/getPsychologicalUrl',
        params,
    });
};
