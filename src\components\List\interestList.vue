<template>
    <div class="flex mb-20px" v-for="(item, index) of data" :key="index" @click="toPage(item)">
        <div class="relative">
            <img loading="lazy" :src="item?.appCover != '' ? useStore.getPrefix + item?.appCover : bannerImg" alt=""
                class="w-200px rounded-8px h-113px">
            <div v-if="props.type != 'examine'"
                class="text-#fff text-24px absolute -top-1px -left-1px w-100px h-40px flex items-center justify-center"
                :class="item.progressMsg == '进行中' ? 'open' : item.progressMsg == '未开始' ? 'wait' : 'end'">{{
                    item.progressMsg }}</div>
        </div>
        <div class="flex-1 pl-29px right-box border-box">
            <div class="truncate text-30px">{{ item.activityName }}</div>
            <div class="text-#666666 text-24px mt-13px flex items-center text-24px">
                <img loading="lazy" :src="judgeStaticUrl(item.logo)" class="w-28px h-30px mr-10px rounded-1/2" />
                {{ item.groupName }}
            </div>
            <div class="text-#666666 text-22px mt-13px flex items-center text-26px" v-if="item?.signUpInfo"> <img
                    loading="lazy" src="@/assets/interest/icon_join.png" class="w-30px h-23px mr-10px">{{
                        item?.signUpInfo?.signUpCount
                    }}/{{ item?.signUpInfo?.maxCount }}</div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import router from '@/router';
import bannerImg from '@/assets/interest/banner2.jpg'
import { useUserStore } from '@/store/modules/user';
import { judgeStaticUrl } from '@/utils/utils';
const useStore = useUserStore();
const props = defineProps({
    data: {
        type: Array,
        default: [],
    },
    type: {
        type: String,
        default: '',//examine-审核信息，不展示标签及数据部分
    }
})

//详情
function toPage(item) {
    useUserStore().setActivityDetail()
    sessionStorage.setItem('activityId', item.activityId)
    router.push({
        path: '/activityHome/interestDetail',
        query: {
            activityId: item.activityId
        }
    })
}
</script>
<style lang="scss" scoped>
.right-box {
    width: calc(100% - 200px);
}

.wait {
    background: url("@/assets/interest/wait.png") no-repeat;
    background-size: 100% 100%;
}

.open {
    background: url("@/assets/interest/open.png") no-repeat;
    background-size: 100% 100%;
}

.end {
    background: url("@/assets/interest/end.png") no-repeat;
    background-size: 100% 100%;
}
</style>
