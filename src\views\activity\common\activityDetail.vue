<template>
  <div class="activityDetail w-full min-h-full pb-35px border-box">
    <div class="w-full p-28px border-b-[#f5f7f9] border-b-19px border-b-style-solid">
      <template v-if="activityDetail.appCover">
        <div class="w-full h-280px mb-20px rounded-14px">
          <img loading="lazy" :src="utils.judgeStaticUrl(activityDetail.appCover)" alt=""
            class="w-full h-full rounded-14px">
        </div>
      </template>
      <template v-else>
        <div class="w-full h-280px mb-15px rounded-14px flex justify-center items-center text-[#fff] text-50px"
          :style="{ backgroundImage: `url(${defaultCover})`, backgroundSize: '100% 100%' }">
        </div>
      </template>
      <div class="text-[#333] text-[34px] font-500 leading-60px">{{ activityDetail.activityName }}</div>
      <div class="flex justify-between w-full items-center">
        <div class="text-28px text-[#999] flex items-center">
          <img loading="lazy" src="@/assets/activity/icon-readCount.png" alt="" class="w-38px mr-10px">
          {{ activityDetail?.readCount }}
        </div>
        <div
          class="w-125px h-40px rounded-20px border-1px border-[#BF1A05] b-solid text-28px text-[#BF1A05] flex justify-center items-center">
          {{ activityDetail?.progressMsg || '-' }}
        </div>
      </div>
      <div class="mt-30px">
        <div class="flex items-center my-20px">
          <div class="text-#999 text-32px">活动时间:</div>
          <div class="text-32px text-[#333] leading-44px ml-20px">
            {{ dayjs(activityDetail?.activityStartTime).format('YYYY.MM.DD') }} 至 {{
              dayjs(activityDetail?.activityEndTime).format('YYYY.MM.DD') }}
          </div>
        </div>
        <template v-if="activityDetail.activityMode === 'signUp'">
          <div class="flex items-center my-20px">
            <div class="text-#999 text-32px">报名时间:</div>
            <div class="text-31px text-[#333] leading-44px ml-20px">
              {{ dayjs(activityDetail?.signUpInfo?.signUpStartTime).format('YYYY.MM.DD') }} 至 {{
                dayjs(activityDetail?.signUpInfo?.signUpEndTime).format('YYYY.MM.DD') }}
            </div>
          </div>
          <div class="flex items-center my-20px">
            <div class="text-#999 text-32px">报名情况:</div>
            <div class="text-31px text-[#333] leading-44px ml-20px">
              <span class="text-#5aa4ff">{{ activityDetail?.signUpInfo?.signUpCount || 0 }}</span>
              <span class="mx-5px">/</span>
              <span class="">{{ activityDetail?.signUpInfo?.maxCount || 0 }}</span>
            </div>
          </div>
        </template>
        <div class="flex items-center my-20px" v-if="activityDetail?.voteInfo?.enableSign == 'y'">
          <div class="text-#999 text-32px">投稿时间:</div>
          <div class="text-31px text-[#333] leading-44px ml-20px">
            {{ dayjs(activityDetail?.voteInfo?.signStartDate).format('YYYY.MM.DD') }} 至 {{
              dayjs(activityDetail?.voteInfo?.signEndDate).format('YYYY.MM.DD') }}
          </div>
        </div>
        <!-- 增加参与对象展示 denglanlan -->
        <div class="flex items-center my-20px">
          <div class="text-#999 text-32px">参与对象:</div>
          <div class="text-31px text-[#333] leading-44px ml-20px">
            {{ matchCustomerType(activityDetail?.areaCode, activityDetail?.customerType) }}
          </div>
        </div>
        <!-- end -->
        <div class="flex items-center my-20px">
          <div class="text-#999 text-32px">每日开放时间:</div>
          <div class="text-31px text-[#333] leading-44px ml-20px">
            {{ formatTimeWithoutSeconds(activityDetail?.openingStartTime) }}-{{
              formatTimeWithoutSeconds(activityDetail?.openingEndTime) }}
          </div>
        </div>
      </div>
    </div>
    <div class="self-style">
      <div class="px-28px pt-20px">
        <div class="w-full px-12px"
          v-if="activityDetail.activityMode === 'coupon' || activityDetail.activityMode === 'summerCoolness'">
          <!-- 票券活动 -->
          <div class="w-full h-180px flex items-center mb-20px" v-if="activityDetail.activityMode === 'coupon'" :style="{
            backgroundImage: `url(${bgCoupon})`,
            filter: (activityDetail?.progress !== '3' && !activityTimeDisabled && item.todayCount < item.dailyIssueCount && item.assignCount < item.issueCount) ? `` : `grayscale(100%)`,
            opacity: (activityDetail?.progress !== '3' && !activityTimeDisabled && item.todayCount < item.dailyIssueCount && item.assignCount < item.issueCount) ? `` : `0.65`
          }" style="background-size: 100% 100%;" v-for="item in couponList" :key="item.couponId"
            @click="onReceiveCoupon(item)">
            <!-- 无门槛 -->
            <template v-if="item.couponType === 'noLimit'">
              <div>
                <div class="text-[#fa261b] font-600 pb-10px flex-shrink-0 w-230px pl-30px pr-25px flex justify-center">
                  <span class="leading-60px text-40px">¥</span>
                  <span class="text-50px text-center break-all leading-55px">
                    {{ item.discountAmount || '-' }}
                  </span>
                </div>
                <div v-if="item.integralOperateType" class="text-center text-26px"
                  :class="item.integralOperateType == 'decrement' ? 'text-red' : 'text-green'">{{
                    item.integralOperateType == 'decrement' ? '-' : '+' }}{{ item.integralScore }}积分</div>
              </div>
              <div class="flex flex-col items-center text-[#fff] font-550 text-36px w-300px pb-8px">
                <div class="w-250px leading-45px text-center truncate">{{ item.couponName }}</div>
                <div class="text-24px mt-15px font-450 text-center"
                  v-if="item?.dailyIssueCount && item?.assignCount < item?.issueCount && activityDetail?.progress !== '3'">
                  发放/剩余: {{ item?.dailyIssueCount }}/{{ item?.todayCount ? (item?.dailyIssueCount -
                    item.todayCount) : item?.dailyIssueCount }}
                </div>
                <div class="text-24px mt-15px font-450 text-center" v-else>
                  发放/剩余: {{ item?.issueCount }}/{{ item?.assignCount ? (item?.issueCount - item.assignCount) :
                    item?.issueCount }}
                </div>
              </div>
            </template>
            <!-- 满减券 -->
            <template v-if="item.couponType === 'fullDecrement'">
              <div>
                <div class="text-[#fa261b] font-600 pb-10px flex-shrink-0 w-230px pl-30px pr-25px flex justify-center">
                  <span class="leading-60px text-40px">¥</span>
                  <span class="text-50px text-center break-all leading-50px">
                    {{ item.discountAmount || '-' }}
                  </span>
                </div>
                <div v-if="item.integralOperateType" class="text-center text-26px"
                  :class="item.integralOperateType == 'decrement' ? 'text-red' : 'text-green'">{{
                    item.integralOperateType == 'decrement' ? '-' : '+' }}{{ item.integralScore }}积分</div>

              </div>
              <div class="flex flex-col items-center text-[#fff] font-550 text-36px w-300px pb-8px">
                <div class="truncate w-100% text-center">{{ item.couponName }}</div>
                <div class="text-28px">满{{ item.amountLimit || '-' }}可使用</div>
                <div class="text-24px mt-15px font-450 text-center"
                  v-if="item?.dailyIssueCount && item?.assignCount < item?.issueCount && activityDetail?.progress !== '3'">
                  发放/剩余: {{ item?.dailyIssueCount }}/{{ item?.todayCount ? (item?.dailyIssueCount -
                    item.todayCount) : item?.dailyIssueCount }}
                </div>
                <div class="text-24px mt-15px font-450 text-center" v-else>
                  发放/剩余: {{ item?.issueCount }}/{{ item?.assignCount ? (item?.issueCount - item.assignCount) :
                    item?.issueCount }}
                </div>
              </div>
            </template>
            <!-- 折扣券 -->
            <template v-if="item.couponType === 'discount'">
              <div>
                <div class="text-[#fa261b] font-600 pb-10px flex-shrink-0 w-230px pl-30px pr-25px flex justify-center">
                  <span class="text-50px leading-55px text-center break-all">
                    {{ item.discountPercent || '-' }}折
                  </span>
                </div>
                <div v-if="item.integralOperateType" class="text-center text-26px"
                  :class="item.integralOperateType == 'decrement' ? 'text-red' : 'text-green'">{{
                    item.integralOperateType == 'decrement' ? '-' : '+' }}{{ item.integralScore }}积分</div>

              </div>
              <div class="flex flex-col items-center text-[#fff] font-550 text-36px w-300px pb-8px">
                <div class="truncate w-100% text-center">{{ item.couponName }}</div>
                <div class="text-28px">满{{ item.amountLimit || '-' }}可使用</div>
                <div class="text-24px mt-15px font-450 text-center"
                  v-if="item?.dailyIssueCount && item?.assignCount < item?.issueCount && activityDetail?.progress !== '3'">
                  发放/剩余: {{ item?.dailyIssueCount }}/{{ item?.todayCount ? (item?.dailyIssueCount -
                    item.todayCount) : item?.dailyIssueCount }}
                </div>
                <div class="text-24px mt-15px font-450 text-center" v-else>
                  发放/剩余: {{ item?.issueCount }}/{{ item?.assignCount ? (item?.issueCount - item.assignCount) :
                    item?.issueCount }}
                </div>
              </div>
            </template>
          </div>
          <!-- 送清凉 cool_bg-->
          <div class="w-full h-170px flex items-center mb-20px" v-if="activityDetail.activityMode === 'summerCoolness'"
            :style="{
              backgroundImage: `url(${coolBg})`,
              filter: (activityDetail?.progress !== '3' && !activityTimeDisabled && item.todayCount < item.dailyIssueCount && item.assignCount < item.issueCount) ? `` : `grayscale(100%)`,
              opacity: (activityDetail?.progress !== '3' && !activityTimeDisabled && item.todayCount < item.dailyIssueCount && item.assignCount < item.issueCount) ? `` : `0.65`
            }" style="background-size: 100% 100%;" v-for="item in couponList" :key="item.couponId"
            @click="onReceiveCoupon(item)">
            <div class="flex items-center">
              <div class="flex flex-col p-24px box-border w-70%">
                <div class="text-50px text-#FE4D40 text-center truncate">{{ item.couponName }}</div>
                <div class="text-#666666 text-20px text-center pt-5px">使用日期：{{ item.useStartTime }}至{{ item.useEndTime
                }}
                </div>
                <div class="text-24px mt-15px font-450 text-center"
                  v-if="item?.dailyIssueCount && item?.assignCount < item?.issueCount && activityDetail?.progress !== '3'">
                  发放/剩余: {{ item?.dailyIssueCount }}/{{ item?.todayCount ? (item?.dailyIssueCount -
                    item.todayCount) : item?.dailyIssueCount }}
                </div>
                <div class="text-24px mt-15px font-450 text-center" v-else>
                  发放/剩余: {{ item?.issueCount }}/{{ item?.assignCount ? (item?.issueCount - item.assignCount) :
                    item?.issueCount }}
                </div>
              </div>
              <div class="w-30% text-#FFFFFF text-46px p-40px box-border text-center font-bold">点击领券</div>
            </div>
          </div>
        </div>

        <template v-if="activityDetail.activityContent">
          <Title title="活动介绍" />
          <div class="text-31px text-[#333] leading-44px mb-40px rich_text intro"
            v-html="activityDetail?.activityContent">
          </div>
        </template>

        <template v-if="activityDetail?.activityRules">
          <Title title="活动规则" />
          <div class="text-31px text-[#333] leading-44px mb-40px rich_text" v-html="activityDetail?.activityRules">
          </div>
        </template>
        <template v-if="activityDetail?.participationMode">
          <Title title="参与方式" />
          <div class="text-31px text-[#333] leading-44px mb-40px rich_text" v-html="activityDetail?.participationMode">
          </div>
        </template>
        <template v-if="activityDetail?.activityRemark">
          <Title title="活动备注" />
          <div class="text-31px text-[#333] leading-44px mb-40px rich_text" v-html="activityDetail?.activityRemark">
          </div>
        </template>
        <!-- 增加活动商家信息 denglanlan -->
        <template
          v-if="activityDetail?.couponExtend?.merchantInfos && activityDetail?.couponExtend?.merchantInfos.length > 0">
          <Title title="活动商家信息" />
          <div v-for="item, index in activityDetail.couponExtend.merchantInfos"
            class="store flex mt-22px bg-#fff p-14px rounded-10px" @click="toAddress(item)">
            <img loading="lazy" :src="utils.judgeStaticUrl(item.companyIcon)"
              class="w-110px h-110px rounded-14px object-cover" />
            <div class="left-info ml-20px flex-1">
              <div class="text-32px">
                {{ item.companyName }}
                <van-icon name="arrow" class="text-[32px] text-#B3B3B3 ml-10px" />
              </div>
              <div class="text-#999999 text-28px mt-16px leading-40px">
                <img loading="lazy" src="@/assets/public/icon_address-gray.png" class="w-22px h-26px" />
                <span class="ml-12px">{{ item.address }}</span>
              </div>
            </div>
          </div>
        </template>
        <!-- end -->
      </div>
      <img loading="lazy" v-if="activityDetail.activityMode == 'vote'" :src="workImg" alt=""
        class="fixed right-0 top-50% w-140px" @click="toMyWork()">
      <img loading="lazy"
        :src="activityDetail.activityMode == 'coupon' || activityDetail.activityMode == 'summerCoolness' ? couponImg : lotteryImg"
        alt="" class="fixed right-0 top-50% w-140px" v-if="activityDetail.couponExtend || activityDetail.luckDrawInfo"
        @click="toLotteryRecord">
      <div class="w-full h-135px"></div>
    </div>

    <div class="w-full flex justify-between items-center px-30px fixed left-0 bottom-0 bg-[#fff] h-105px footer z-99">
      <div class="flex w-45/100 justify-around">
        <div class="flex flex-col items-center text-24px text-[#4C4C4C]" @click="toActivityComment"
          v-if="activityDetail.commentState === 'y'">
          <img loading="lazy" src="@/assets/activity/icon-comment.png" alt="" class="w-46px h-42px">
          {{ activityDetail.commentsCount || '0' }}
        </div>
        <div class="flex flex-col items-center text-24px text-[#4C4C4C]" @click="onLike">
          <img loading="lazy" src="@/assets/activity/icon-like.png" alt="" class="w-46px h-42px"
            v-if="!activityDetail?.likeState">
          <img loading="lazy" src="@/assets/activity/icon-liked.png" alt="" class="w-46px h-42px" v-else>
          {{ activityDetail.likeCount || '0' }}
        </div>
        <div class="flex flex-col items-center text-24px text-[#4C4C4C]" @click="onShare">
          <img loading="lazy" src="@/assets/activity/public/share-btn.png" alt="" class="w-46px h-42px">
          {{ activityDetail.shareCount || '0' }}
        </div>
      </div>
      <template v-if="activityDetail.activityMode !== 'coupon' && activityDetail.activityMode !== 'summerCoolness'">
        <Button v-if="btnText[activityDetail?.activityMode] && btnText[activityDetail?.activityMode].ifShow"
          :disable="disabledBtn" :name="btnText[activityDetail?.activityMode].name" class="w-400px h-68px"
          @click="onClick" />
      </template>
    </div>
    <!-- 气泡样式 -->
    <div class="fixed bottom-140px z-110" :style="`left:${bubbleX}%`">
      <waterIntergral v-model:show="showBubble" :score="scoreNum"></waterIntergral>
    </div>

    <!-- <tips-popup v-model:show="isShowTips" :tipsTitle="tipsTitle" :tipsContent="tipsContent" @confirm="onConfirmTips"></tips-popup> -->
  </div>
</template>

<script setup>
// denglanlan -- 提示弹窗
// const tipsPopup = defineAsyncComponent(() => import('./tipsPopup.vue'));
// end
// 气泡框
const waterIntergral = defineAsyncComponent(() => import('@/components/Bubble/waterIntergral.vue'))

import lotteryImg from '@/assets/activity/btn-lotteryRecord.png';
import couponImg from '@/assets/activity/coupon.png';
import workImg from '@/assets/activity/my_work.png';
import coolBg from '@/assets/activity/cool_bg.png';
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { showToast, showDialog, showConfirmDialog, showSuccessToast } from 'vant';
import defaultCover from '@/assets/activity/default_cover.jpg';
import { useUserStore } from "@/store/modules/user";
import {
  activityValidator,
  checkIntegral,
  defaultValidator,
  voteValidator,
  voteDateValidator,
  checkCouponIntegral
} from '@/hooks/useValidator';
import {
  receiveCoupon,
  applicationRecord,
  getAwardState,
  signUp,
  questionCount,
  myApplyCount,
} from '@/api/activity';
import Title from '@/components/Title/vitalityTitle.vue';
import Button from '@/components/Button/button.vue';
import bgCoupon from '@/assets/activity/coupon-receive.png'
import dayjs from 'dayjs';
import { likeOperate, shareOperate } from '@/api/public';
import utils from '@/utils/utils';
import { qualificationValidator } from '@/utils/actRulesValidator.js'
const useStore = useUserStore();
const router = useRouter();
// 响应式数据
const btnText = computed(() => {
  return {
    luckDraw: { name: '去抽奖', url: '/activityHome/lottery', ifShow: !!activityDetail.value?.luckDrawInfo },
    // 新增
    birthday: { name: '去抽奖', url: '/activityHome/lottery', ifShow: !!activityDetail.value?.luckDrawInfo },
    vieAnswer: { name: '去答题', url: '/activityHome/vieAnswerActivity', ifShow: false },
    vote: {
      name: activityDetail.value?.voteInfo?.enableSign == 'y' && voteValidator() && !checkDate() ? '我要投稿' : '我要投票',
      url: activityDetail.value?.voteInfo?.enableSign == 'y' && voteValidator() && !checkDate() ? '/activityHome/contribute' : '/activityHome/vote', ifShow: !!activityDetail.value?.voteInfo
    },
    questionnaire: { name: '去填写问卷', url: '/activityHome/questionnaireActivity/question', ifShow: !!activityDetail.value?.questionnaireInfo },
    signUp: {
      name: signUpRecord.value ? '我的报名' : '去报名',
      url: signUpRecord.value ? `/activityHome/signUpActivity/record?activityId=${activityDetail.value.activityId}` : '/activityHome/signUpActivity/form'
      , ifShow: !!activityDetail.value?.signUpInfo
    },
    competition: {
      name: signUpRecord.value ? '我的报名' : '去报名',
      url: signUpRecord.value ? `/activityHome/signUpActivity/record?activityId=${activityDetail.value.activityId}` : '/activityHome/signUpActivity/form'
      , ifShow: !!activityDetail.value?.signUpInfo
    },
    funCompetition: {
      name: signUpRecord.value ? '我的报名' : '去报名',
      url: signUpRecord.value ? `/activityHome/signUpActivity/record?activityId=${activityDetail.value.activityId}` : '/activityHome/signUpActivity/form'
      , ifShow: !!activityDetail.value?.signUpInfo
    },
  }
});
// 判断活动时间是否领取 denglanlan
const activityTimeDisabled = computed(() => {
  const now = new Date().getTime()
  const activityTimeBol = now < new Date(activityDetail.value?.activityStartTime).getTime() || now > new Date(activityDetail.value?.activityEndTime).getTime()
  if (activityTimeBol) return activityTimeBol // 判断活动时间是否领取
  // 判断开放时间存在 2025-2-22 取消开放时间判断
  // if (activityDetail.value?.openingStartTime) {
  //   const openTimeBol = dayjs().format('HH:mm:ss') < activityDetail.value?.openingStartTime || dayjs().format('HH:mm:ss') > activityDetail.value?.openingEndTime
  //   return openTimeBol
  // }

  // 用户信息不存在不显示
  const userInfo = useUserStore().getUserInfo;
  if (!userInfo?.userId) return true

  // 地区及身份校验
  const { state } = qualificationValidator(activityDetail, userInfo);
  return !state || activityTimeBol
})
// 判断活动是否结束
const activityIsEnd = computed(() => {
  return now > new Date(activityDetail.value?.activityEndTime).getTime()
})
const disabledBtn = computed(() => {
  const { activityMode, progress } = activityDetail.value
  switch (activityMode) {
    case 'luckDraw':
    case 'birthday':
    case 'questionnaire':
    case 'vote':
      if (progress === '3') {
        btnText.value[activityMode].name = '已结束'
        return true
      }
      return false
    case 'signUp':
    case 'competition':
    case 'funCompetition':
      if (progress === '3' && !signUpRecord.value) {
        btnText.value[activityMode].name = '已结束'
        return true
      }
      return false
  }
})
// 活动提示框 denglanlan 暂时不展示
// const isShowTips = ref(false);
// const tipsTitle = ref('温馨提示');
// const tipsContent = ref('提示内容');
// const onConfirmTips = () => {
//   isShowTips.value = false;
// };
// end


const couponList = ref([])
// 计算属性
const activityDetail = computed(() => {
  return useStore.activityDetail || {};
});
const initCoupon = () => {
  couponList.value = activityDetail.value?.couponExtend?.couponInfos || [];
  couponList.value?.forEach(item => {
    item.discountAmount = parseFloat(item.discountAmount)
    item.amountLimit = parseFloat(item.amountLimit)
    item.discountPercent = parseFloat(item.discountPercent)
  })
}
function formatTimeWithoutSeconds(timeStr) {
  // 正则表达式匹配 "时:分:秒" 并将秒替换为空字符串
  return timeStr?.replace(/(\d{2}):(\d{2}):(\d{2})$/, '$1:$2');
}

// 参与对象匹配 denglanlan
const matchCustomerType = (areaCode, customerType) => {
  switch (customerType) {
    case '1': return areaCode + '注册用户'
    case '2': return areaCode + '认证会员'
    case '3': return areaCode + '工会干部'
    case '4': return areaCode + '工会会员'
    case 'interestGroup': return areaCode + '兴趣小组成员'
    case 'volunteer': return areaCode + '志愿者'
    case 'friendship': return areaCode + '单身联谊会员'
    default: return areaCode
  }
}
// 地址导航 denglanlan
const toAddress = (item) => {
  if (!item?.addressCoordinate) return showToast('暂未商家地址信息')
  showConfirmDialog({
    message: '确定开启导航吗？',
  }).then(() => {
    utils.mapNav({
      win: route.fullPath,
      lon: item?.addressCoordinate?.split(',')[0] || '',
      lat: item?.addressCoordinate?.split(',')[1] || '',
      name: item?.address,
    })
  })
}
// end


const weekDayName = ['周一', '周二', '周三', '周四', '周五', '周六', '周七']
const onReceiveCoupon = async (item) => {
  if (!activityValidator()) return
  const userInfo = useStore.userInfo
  // 校验票券领取日期
  const { weekLimit, couponId, specifyDate, assignStartTime, assignEndTime, useScore } = item
  const nowDate = new Date().format('yyyy-MM-dd')
  if (assignStartTime && (nowDate < assignStartTime || nowDate > assignEndTime)) {
    showDialog({
      title: '温馨提示',
      message: `尊敬的${userInfo.nickname}会员，优惠券领取时间为${assignStartTime}-${assignEndTime}，感谢您的支持~`,
      confirmButtonText: '我知道了',
      lockScroll: false,
    });
    return
  }
  //  指定周几领取
  const day = new Date().getDay()
  const nowWeekDay = day === 0 ? '7' : day + ''
  if (weekLimit && !weekLimit.includes(nowWeekDay)) {
    let message = ''
    weekLimit.split(',').forEach(item => {
      if (message) {
        message = message + '、' + weekDayName[item - 1]
      } else {
        message = message + weekDayName[item - 1]
      }
    })
    showDialog({
      title: '温馨提示',
      message: `尊敬的${userInfo.nickname}会员，优惠券领取时间为每${message}，感谢您的支持~`,
      confirmButtonText: '我知道了',
      lockScroll: false,
    });
    return
  }

  // 校验每月领取日期 specifyDate
  const date = new Date().getDate()
  let specifyDates = null
  if (specifyDate) {
    specifyDates = specifyDate?.split(',')?.map(item => Number(item))?.sort((a, b) => a - b)
  }
  if (specifyDates && !specifyDates.includes(date)) {
    showDialog({
      title: '温馨提示',
      message: `尊敬的${userInfo.nickname}会员，优惠券领取时间为每月${specifyDates?.join('、')}日，感谢您的支持~`,
      confirmButtonText: '我知道了',
      lockScroll: false,
    });
    return
  }
  if (item.todayUserCount >= item.userDailyLimit && (item.userDailyLimit !== null || item.userDailyLimit !== undefined)) {
    showDialog({
      title: '温馨提示',
      message: `尊敬的${userInfo.nickname}用户，该优惠券每天限领${item.userDailyLimit}张，感谢您的支持~`,
      confirmButtonText: '我知道了',
      lockScroll: false,
    })
    return
  }
  // 单张优惠券领取上限
  if (item.userCount >= item.userLimit && (item.userLimit !== null || item.userLimit !== undefined)) {
    showDialog({
      title: '温馨提示',
      message: `尊敬的${userInfo.nickname}用户，该优惠券限领${item.userDailyLimit}张，感谢您的支持~`,
      confirmButtonText: '我知道了',
      lockScroll: false,
    })
    return
  }

  // 今日是否已领完
  if (item.todayCount >= item.dailyIssueCount && (item.dailyIssueCount !== null || item.dailyIssueCount !== undefined)) {
    showDialog({
      title: '温馨提示',
      message: '该优惠券今日已领完，感谢您的支持。',
      confirmButtonText: '我知道了',
      lockScroll: false,
    })
    return
  }
  //是否消耗积分
  if (! await checkCouponIntegral(item)) {
    return;
  }

  const { code, message } = await receiveCoupon({
    activityId: activityDetail.value.activityId,
    couponId: item.couponId,
  })
  // code 10010 已领光
  if (code == 200) {
    item.todayUserCount++
    item.todayCount++
    item.userCount++
    showToast({
      title: '温馨提示',
      message: `领取成功`,
      type: 'success',
      forbidClick: true,
    })
  } else if (code == 10010) {
    const userInfo = useStore.userInfo
    showDialog({
      title: '温馨提示',
      message: `尊敬的${userInfo.nickname}用户，该优惠券今日已领完，感谢您的支持。`,
      confirmButtonText: '我知道了',
      lockScroll: false,
    })
    return
  } else {
    showDialog({
      title: '温馨提示',
      message: message,
      confirmButtonText: '我知道了',
      lockScroll: false,
    })
  }
}
//我的作品
const toMyWork = () => {
  router.push({
    path: '/activityHome/myWorks',
    query: {

    }
  })
}
const toLotteryRecord = () => {
  if (!defaultValidator()) return
  const isCoupon = ['summerCoolness', 'coupon'].includes(activityDetail.value.activityMode)
  router.push({
    path: '/activityHome/lotteryRecord',
    query: {
      active: isCoupon ? '7' : undefined,
      prizeTypes: isCoupon ? '7' : undefined,
    }
  })
}
const toLottery = async () => {
  if (!activityValidator()) return
  if (
    defaultAwardNum.value &&
    defaultAwardNum.value <= awardNum.value
  ) {
    showDialog({
      title: "温馨提示",
      message: `每日可抽奖${defaultAwardNum.value}次,您今日已无抽奖机会,明日再来吧~`,
      confirmButtonText: "我知道了",
      lockScroll: false,
    });
    return;
  }
  if (! await checkIntegral()) {
    return;
  }
  const url = btnText.value[activityDetail.value?.activityMode]?.url

  utils.openActLink({
    title: activityDetail.value.activityName,
    url: url,
    shareName: activityDetail.value.activityName,//'分享标题'
    shareurl: window.location.origin + '/activityHome/lottery?activityId=' + activityDetail.value?.activityId,//'分享地址'
    dataid: activityDetail.value?.activityId,
    type: 1,//类型 1-工会活动，2-普惠活动
    uid: activityDetail.value.companyId,
    win: activityDetail.value?.activityId + Math.floor(Math.random() * 100),
    isExternal: 'n'
  })
  // router.push('/activityHome/lottery')
}
const onClick = async () => {
  if (disabledBtn.value) {
    return;
  }
  switch (activityDetail.value.activityMode) {
    case 'funCompetition':
    case 'competition':
    case 'signUp':
      if (!signUpRecord.value) {
        if (!activityValidator()) {
          return
        }
        // 报名开始时间校验
        if (activityDetail.value?.signUpInfo?.signUpStartTime > Date.now()) {
          showDialog({
            title: '温馨提示',
            message: `报名时间:${activityDetail.value.signUpInfo.signUpStartTime}未到，请耐心等待~`,
            confirmButtonText: '我知道了',
            lockScroll: false,
          });
          return;
        }
        // 报名结束时间校验
        if (activityDetail.value?.signUpInfo?.signUpEndTime < Date.now()) {
          showDialog({
            title: '温馨提示',
            message: `报名时间:${activityDetail.value.signUpInfo.signUpEndTime}已结束，请耐心等待~`,
            confirmButtonText: '我知道了',
            lockScroll: false,
          });
          return;
        }


        const personMax = activityDetail.value.signUpInfo.maxCount
        const signUpCount = activityDetail.value.signUpInfo.signUpCount
        if (personMax <= signUpCount) {
          showDialog({
            title: '温馨提示',
            message: '报名人数已满~',
            confirmButtonText: '我知道了',
            lockScroll: false,
          });
          return;
        }
        if (! await checkIntegral()) {
          return;
        }
        if (activityDetail.value.signUpInfo.writeFlag !== 'y') {
          const platform = sessionStorage.getItem('platform')
          const { code } = await signUp({
            activityId: activityDetail.value.activityId,
            platform,
          })
          if (code === 200) {
            await getSignUpRecord()
            await showConfirmDialog({
              title: '温馨提示',
              message: '报名信息提交成功,感谢您的参与~',
              showConfirmButton: true,
              showCancelButton: activityDetail.value.luckDraw === 'y',
              confirmButtonText: activityDetail.value.luckDraw === 'y' ? '去抽奖' : '我知道了',
              cancelButtonText: activityDetail.value.luckDraw === 'y' ? '返回' : ''
            }).then(() => {
              if (activityDetail.value.luckDraw === 'y') {
                router.replace('/activityHome/lottery')
              }
            })
          }
          return
        }
      }
      break
    case 'birthday':
    case 'luckDraw':
      await toLottery()
      return
    case 'questionnaire':
      if (!activityValidator()) {
        return
      }
      //问卷总份数
      const { data: total } = await questionCount({
        activityId: activityDetail.value.activityId,
      })

      if (total >= activityDetail.value.questionnaireInfo.total) {
        showDialog({
          title: '温馨提示',
          message: `本次问卷调查已收集完成,感谢您的关注~`,
          confirmButtonText: '我知道了',
        })
        return
      }
      //当前用户是否提交
      const { data: userCount } = await questionCount({
        userId: useStore.userInfo.userId,
        activityId: activityDetail.value.activityId,
      })
      if (userCount > 0) {
        showDialog({
          title: '温馨提示',
          message: `您已完成本次问卷调查,感谢您的参与~`,
          confirmButtonText: '我知道了',
        })
        return
      }

      break;
    case 'vote':
      if (!activityValidator()) {
        return
      }
      if (activityDetail.value.voteInfo.enableSign == 'y') {
        if (!voteValidator()) {
          return
        }
        if (btnText.value[activityDetail.value.activityMode].name == '我要投稿') {
          const { data: userCount } = await myApplyCount({
            activityId: activityDetail.value.activityId,
          })
          if (activityDetail.value.voteInfo.userLimit === userCount) {
            showDialog({
              title: '温馨提示',
              message: `投稿已达最大投稿数限制~`,
              confirmButtonText: '我知道了',
            })
            return
          }
        } else {
          if (!voteDateValidator()) {
            return
          }

        }

      }

      break
  }
  const activityMode = activityDetail.value.activityMode;
  utils.openActLink({
    title: activityDetail.value.activityName,
    url: btnText.value[activityMode].url,
    shareName: activityDetail.value.activityName,//'分享标题'
    shareurl: window.location.origin + btnText.value[activityMode].url + '?activityId=' + activityDetail.value?.activityId,//'分享地址'
    dataid: activityDetail.value?.activityId,
    type: 1,//类型 1-工会活动，2-普惠活动
    uid: activityDetail.value.companyId,
    win: activityDetail.value?.activityId + Math.floor(Math.random() * 100),
    isExternal: 'n'
  })
  // router.push(btnText.value[activityMode].url);
};

const toActivityComment = () => {
  if (!defaultValidator()) {
    return
  }
  router.push('/activityHome/activityComment');
};
// 气泡提示框参数设置
const bubbleX = ref(10);//10 30
const showBubble = ref(false);
const scoreNum = ref(0);
let isReq = false

const onLike = async () => {
  if (!defaultValidator()) {
    return
  }
  if (isReq) return;
  isReq = true;
  const res = await likeOperate({
    sourceId: activityDetail.value.activityId,
  });
  isReq = false
  if (res.code == 200) {
    if (res.data?.statefulFlowState) {
      activityDetail.value.likeState = true;
      activityDetail.value.likeCount++;
      showToast({
        message: '点赞成功',
        duration: 1500,
        icon: 'success',
        className: "!z-10000"
      });
    } else {
      activityDetail.value.likeState = false;
      activityDetail.value.likeCount--;
      showToast({
        message: '取消成功',
        duration: 1500,
        icon: 'success',
        className: "!z-10000"
      });
    }

    if (res.data?.score) {
      scoreNum.value = res.data.score
      bubbleX.value = 10
      showBubble.value = true;
    }
  }
};
//分享
const getShare = (dataid, code) => {
  if (isReq) return;
  isReq = true;
  shareOperate({
    sourceId: unref(activityDetail).activityId,//解决参数不对问题
    userId: useStore.getUserInfo?.userId,
  }).then(res => {
    isReq = false
    if (res.code == 200) {
      // 修复分享不成功问题
      showToast({
        message: '分享成功',
        duration: 1500,
        icon: 'success',
        className: "!z-10000"
      });
      activityDetail.value.shareCount++;

      if (res.data?.score) {
        scoreNum.value = res.data.score
        bubbleX.value = 30
        showBubble.value = true;
      }
    }
  })
    .catch(err => {
      isReq = false
    })
}
const onShare = () => {
  if (utils.isApp()) {
    utils.newsAction({ title: unref(activityDetail).activityName, dataId: unref(activityDetail).activityId, source: 'act' }, "share")
  } else {
    showToast("请在川工之家app分享")
  }
};
const signUpRecord = ref(null)
// 获取报名记录
const getSignUpRecord = async () => {
  if (!useUserStore().userInfo) {
    return
  }
  try {
    const { code, data } = await applicationRecord({
      activityId: unref(activityDetail).activityId,
    })
    if (code === 200) {
      signUpRecord.value = data
    }
  } catch (error) {
    console.error('获取报名记录失败:', error)
  }
}

// 抽奖活动
const defaultAwardNum = ref(null)
const awardNum = ref(null)
// 抽奖次数
const getAwardStateCount = async () => {
  if (!useStore.userInfo) {
    return
  }
  const { code, data } = await getAwardState({
    userId: useStore.userInfo.userId,
    activityId: useStore.activityDetail.activityId,
  });
  if (code === 200) {
    awardNum.value = data || 0;
    const { numberPerDay } = useStore.activityDetail?.luckDrawInfo || { numberPerDay: 0 };
    defaultAwardNum.value = numberPerDay;
  }
}
watch(activityDetail, () => {
  init()
})
onMounted(() => {
  window.h5NewsShareReturn = getShare;
  init();
})
const route = useRoute()
const init = async () => {
  if (activityDetail.value.activityId) {
    switch (activityDetail.value.activityMode) {
      case 'coupon':
      case 'summerCoolness':
        initCoupon()
        break;
      case 'signUp':
      case 'funCompetition':
      case 'competition':
        getSignUpRecord()
        break;
      case 'birthday':
      case 'luckDraw':
        getAwardStateCount()
        break;
      case 'vote':
        // checkDate();

        break;
      default:
        break;
    }
    //积分消耗确认
    const { integralFlag, integralOperateType } = activityDetail.value
    //消耗积分
    //debugger
    if (integralFlag === 'y' && integralOperateType === 'decrement') {

    }
  }
}
//校验结束日期
const checkDate = () => {
  const now = new Date()
  const currentDate = now.format('yyyy-MM-dd')
  if (voteValidator() && currentDate > utils.formatTimeWithoutSeconds(activityDetail.value.voteInfo?.signEndDate)) {
    return true
    // btnText.value[activityDetail.value.activityMode].name='我要投票';
    // btnText.value[activityDetail.value.activityMode].url='/activityHome/vote';
  }
  return false
}
const showLimit = ref(false)
async function getUserCount() {
  const { data: userCount } = await myApplyCount({
    activityId: activityDetail.value.activityId,
  })
  if (activityDetail.value.voteInfo.userLimit === userCount) {
    showDialog({
      title: '温馨提示',
      message: `投稿已达最大投稿数限制~`,
      confirmButtonText: '我知道了',
    })
    return
    showLimit.value = true;
    // btnText.value[activityDetail.value.activityMode].name='我要投票';
  }
  console.log(userCount);
}

</script>

<style lang="scss" scoped>
.activityDetail {
  font-family: Source Han Sans CN;

  .footer {
    box-shadow: 0px 0px 8px 0px rgba(204, 204, 204, 0.31);
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    .lottery-btn {
      background: linear-gradient(90deg, #FE9863, #BF1A05);
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 32px;
      color: #FFFFFF;
    }
  }

  // denglanlan
  .self-style {
    background-image: url('@/assets/activity/bg-top.png'), url('@/assets/activity/bg-bottom.png');
    background-repeat: no-repeat, no-repeat;
    background-position: top, bottom;
    background-size: 100% 284px, 100% 424px;
    min-height: calc(100vh - 866px);
    box-sizing: border-box;

    .intro {

      img,
      image {
        width: 100%;
        height: 100%;
        max-width: 100%;
      }
    }

  }

  .store {
    box-shadow: 0px -2px 10px 0px rgba(183, 192, 204, 0.2);
  }
}
</style>
