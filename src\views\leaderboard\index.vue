<template>
    <div class="main p-28px">
        <div class="top-box flex ">
            <div class="label p-15px" v-for="item, i in cardFlagTotal" :key="i" @click="openPop(item)">
                <div class="text-[#999] text-[26px] mb-10px flex items-center">
                    <img loading="lazy" class="icons" :src="item.src" alt="" srcset="">
                    <span>{{ item.title }}</span>
                </div>
                <div class="flex justify-between text-30px">
                    <span>{{ totalObj?.[item.key + 'Text'] }}</span>
                    <van-icon name="arrow" color="#999" />
                </div>
            </div>
        </div>
        <div class="text-[#3493FF] text-[26px] mt-30px mb-30px flex items-center">
            <img loading="lazy" class="w-30px h-30px mr-10px" src="@/assets/leaderBoard/index/icon_month.png" alt=""
                srcset="">
            近一月数据统计
        </div>
        <div class="box ">
            <div class="banner mb-15px" @click="toPage('ph')">
                <div class="banner-top flex items-center mb-10px">
                    <img class="banner-name" src="@/assets/leaderBoard/index/ph_name.png" alt="" srcset="">
                    <img class="banner-icon" src="@/assets/leaderBoard/index/icon_gh.png" alt="" srcset="">
                </div>
                <div class="flex info ">
                    <div class="send-box ml-25px mr-60px">
                        <div class="text-[#3493FF] text-[34px]">{{ monthObj?.inclusivePublishCount || 0 }}</div>
                        <div class="text-[#87BEFD] text-[26px]">发布量(场)</div>
                    </div>
                    <div class="send-box">
                        <div class="text-[#3493FF] text-[34px]">{{ monthObj?.inclusiveJoinCount || 0 }}</div>
                        <div class="text-[#87BEFD] text-[26px]">参与量(人)</div>
                    </div>
                </div>
            </div>
            <div class="banner mb-15px " @click="toPage('gh')">
                <div class="banner-top flex items-center">
                    <img class="banner-name" src="@/assets/leaderBoard/index/gh_name.png" alt="" srcset="">
                    <img class="banner-icon" src="@/assets/leaderBoard/index/icon_gh.png" alt="" srcset="">
                </div>
                <div class="flex info">
                    <div class="send-box ml-25px mr-60px">
                        <div class="text-[#2F6BF9] text-[34px]">{{ monthObj?.unionPublishCount || 0 }}</div>
                        <div class="text-[#759FFC] text-[26px]">发布量(场)</div>
                    </div>
                    <div class="send-box">
                        <div class="text-[#2F6BF9] text-[34px]">{{ monthObj?.unionJoinCount || 0 }}</div>
                        <div class="text-[#759FFC] text-[26px]">参与量(人)</div>
                    </div>
                </div>
            </div>
            <div class="banner mb-15px " @click="toPage('jf')">
                <div class="banner-top flex items-center">
                    <img class="banner-name" src="@/assets/leaderBoard/index/jf_name.png" alt="" srcset="">
                    <img class="banner-icon" src="@/assets/leaderBoard/index/icon_jf.png" alt="" srcset="">
                </div>
                <div class="flex info">
                    <div class="send-box ml-25px mr-60px">
                        <div class="text-[#735DEE] text-[34px]">{{ monthObj?.integralIncrement || 0 }}</div>
                        <div class="text-[#A797ED] text-[26px]">新增量(分)</div>
                    </div>
                    <div class="send-box">
                        <div class="text-[#735DEE] text-[34px]">{{ monthObj?.integralDecrease || 0 }}</div>
                        <div class="text-[#A797ED] text-[26px]">消耗量(分)</div>
                    </div>
                </div>
            </div>
            <div class="banner mb-15px " @click="toPage('sx')">
                <div class="banner-top flex items-center">
                    <img class="banner-name" src="@/assets/leaderBoard/index/sx_name.png" alt="" srcset="">
                    <img class="banner-icon" src="@/assets/leaderBoard/index/icon_sx.png" alt="" srcset="">
                </div>
                <div class="flex info">
                    <div class="send-box ml-25px mr-60px">
                        <div class="text-[#EC724C] text-[34px]">{{ monthObj?.newsReleaseVolume || 0 }}</div>
                        <div class="text-[#F6A57C] text-[26px]">发布量(条)</div>
                    </div>
                    <div class="send-box">
                        <div class="text-[#EC724C] text-[34px]" v-formatNum="10000">{{ monthObj?.newsReadingQuantity ||
                            0 }}
                        </div>
                        <div class="text-[#F6A57C] text-[26px]">阅读量({{ monthObj?.newsReadingQuantity >= 10000 ? '万' : ''
                            }}人)
                        </div>
                    </div>
                </div>
            </div>
            <div class="banner mb-15px " @click="toPage('ali')">
                <div class="banner-top flex items-center">
                    <img class="banner-name" src="@/assets/leaderBoard/index/ali_name.png" alt="" srcset="">
                    <img class="banner-icon" src="@/assets/leaderBoard/index/icon_ali.png" alt="" srcset="">
                </div>
                <div class="flex info">
                    <div class="send-box ml-35px mr-50px flex items-center">
                        <img class="w-60px" src="@/assets/leaderBoard/index/iconali.png" alt="" srcset="">
                    </div>
                    <div class="send-box">
                        <div class="text-[#FBB958] text-[34px]">{{ monthObj?.AliValue || '--' }}</div>
                        <div class="text-[#F7C33C] text-[26px]">{{ monthObj?.AliName || '--' }}</div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 查看数据详情 -->
        <van-popup v-model:show="popData.show" class="pop-box">
            <div class="text-26px text-[#fff] p-15px">各区县{{ popData.type }}数量统计</div>
            <div class="list flex w-full">
                <div class="item flex items-center" v-for="item, i in topDetailList" :key="i">
                    <div class="text-[30px] text-[#333] w-20%">{{ item.areaName }}</div>
                    <div class="progress w-55%">
                        <div class="bar" :style="{ width: item[popData.key + 'progress'] + '%' }"></div>
                    </div>
                    <div class="value text-[#4898FB] text-[30px] w-25% text-right">{{ item[popData.key + 'Text'] }}
                    </div>
                </div>
            </div>
        </van-popup>
    </div>
</template>
<script setup lang="ts">
import { IconUser, IconUnion, IconMenber, IconOfficer, IconTurn, IconRZ } from './data'
import {
    getGuildStatistics, getMonthIntegral, getMonthActivity,
    getMonthNews, getGuildDetail
} from '@/api/leaderboard/index'
import { userActiveIndex } from '@/api/leaderboard/ali'
import { useUserStore } from '@/store/modules/user'
import { showConfirmDialog } from 'vant'
const userStore = useUserStore()
const router = useRouter()
const cardFlagTotal = ref([
    { title: '注册用户', name: "注册用户", src: IconUser, key: 'registerCount' },
    { title: '工会组织', name: '工会组织', src: IconUnion, key: 'unionCount' },
    { title: '工会会员', name: '工会会员', src: IconMenber, key: 'userCount' },
    { title: '工会干部', name: '工会干部', src: IconOfficer, key: 'cadreCount' },
    { title: '用户转化率', name: '转换率', src: IconTurn, key: 'userConvert' },
    { title: '会员认证率', name: '认证率', src: IconRZ, key: 'userCertification' },
])
const popData = ref({
    show: false,
    type: '',
    key: ''
})


function openPop(item: any) {
    unref(popData).type = item.title
    unref(popData).key = item.key
    unref(popData).show = true
}
function toPage(type: string) {
    if (type == 'ph') return router.push({ path: '/leaderInclusive' })
    if (type == 'gh') return router.push({ path: '/leaderActivity' })
    if (type == 'jf') return router.push({ path: '/leaderPoints' })
    if (type == 'sx') return router.push({ path: '/leaderThink' })
    if (type == 'ali') return router.push({
        path: '/leaderAli'
    })
}

//顶部数据统计-查询的是总数据
const totalObj = ref<any>({})
const topTotalStatics = () => {
    getGuildStatistics({
        // 总工会
        belongUnionCode: '6650f8e054af46e7a415be50597a99d5'
    }).then(({ code, data }) => {
        if (code === 200 && data) {
            totalObj.value = data
            // 做千分位处理
            for (const key in totalObj.value) {
                // 单位处理  转化率
                if (key === 'userConvert') totalObj.value[key + 'Text'] = parseFloat(totalObj.value[key] * 100) + '%'
                else if (key === 'userCertification') totalObj.value[key + 'Text'] = parseFloat(totalObj.value[key] * 100) + '%'
                else if (totalObj.value[key] && !isNaN(Number(totalObj.value[key]))) {
                    totalObj.value[key + 'Text'] = (totalObj.value[key]).toLocaleString()
                }
            }
            // 详情
            topDetailStatics()
        }
    })
}
const topDetailList = ref<any>({})//弹窗详情
const topDetailStatics = () => {
    getGuildDetail().then(({ code, data }) => {
        if (code === 200 && data) {
            const keyArr = ['userConvert', 'userCertification', 'registerCount', 'unionCount', 'userCount', 'cadreCount']
            topDetailList.value = data.map((item: any) => {
                keyArr.forEach(k => {
                    if (!item[k]) return

                    item[k + 'progress'] = (parseFloat(item[k] / totalObj.value[k]) * 100).toFixed(2)
                    if (item[k + 'progress'] > 100) item[k + 'progress'] = 100

                    if (k !== 'userConvert' && k !== 'userCertification') item[k + 'Text'] = (item[k]).toLocaleString()// 单位处理
                    else item[k + 'Text'] = parseInt(item[k] * 100) + '%'

                })
                return item
            })
        }
    })
}
// end
const currentUserCode = computed(() => userStore.leaderCode)

// 近一个月数据统计
const monthObj = ref<any>({})
const monthStatics = () => {
    getMonthIntegral({
        companyId: currentUserCode.value
    }).then(res => {
        if (res.code === 200 && res.data) {
            const { increment, decrease } = res.data
            monthObj.value.integralIncrement = increment.toLocaleString()
            monthObj.value.integralDecrease = decrease.toLocaleString()
        }

    })
    getMonthNews({
        companyId: currentUserCode.value
    }).then(res => {
        if (res.code === 200 && res.data) {
            const { readingQuantity, releaseVolume } = res.data
            monthObj.value.newsReadingQuantity = readingQuantity?.toLocaleString()
            monthObj.value.newsReleaseVolume = releaseVolume?.toLocaleString()
        }
    })
    getMonthActivity({
        dateRange: 'month',
        companyId: currentUserCode.value,
        activityCategory: 'union'
    }).then(res => {
        if (res.code === 200 && res.data) {
            const { publishCount, afootCount, joinCount, readCount } = res.data
            monthObj.value.unionPublishCount = publishCount?.toLocaleString()
            monthObj.value.unionAfootCount = afootCount?.toLocaleString()
            monthObj.value.unionJoinCount = joinCount?.toLocaleString()
            monthObj.value.unionReadCount = readCount?.toLocaleString()
        }
    })
    getMonthActivity({
        dateRange: 'month',
        companyId: currentUserCode.value,
        activityCategory: 'inclusive'
    }).then(res => {
        if (res.code === 200 && res.data) {
            const { publishCount, afootCount, joinCount, readCount } = res.data
            monthObj.value.inclusivePublishCount = publishCount?.toLocaleString()
            monthObj.value.inclusiveAfootCount = afootCount?.toLocaleString()
            monthObj.value.inclusiveJoinCount = joinCount?.toLocaleString()
            monthObj.value.inclusiveReadCount = readCount?.toLocaleString()
        }
    })

    // 用户活跃指数
    const currentMonth = new Date().getMonth() + 1 < 10 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1
    userActiveIndex({
        areaCode: currentUserCode.value,
        dateRange: new Date().getFullYear() + '-' + currentMonth
    }).then((res: any) => {
        if (res.code === 200 && res.data) {
            monthObj.value.AliName = res.data.areaName
            monthObj.value.AliValue = res.data.ali?.toLocaleString()
        }
    })
}


onMounted(() => {
    topTotalStatics()
    if (currentUserCode.value) {
        monthStatics()
    } else {
        showConfirmDialog({
            title: "提示",
            message: "所属工会信息丢失，请返回上一级重新操作"
        }).then(() => {
            router.go(-1)
        }).catch(() => { })
    }

})
</script>
<style lang="scss" scoped>
.main {
    width: 100%;
    // height: 100vh;
    min-height: 100vh;
    background: url('@/assets/leaderBoard/index/banner_bg.png') no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;

    .top-box {
        height: 250px;
        flex-wrap: wrap;
        justify-content: space-between;
        margin-top: 100px;

        .label {
            width: 32%;
            border-radius: 20px;
            margin-top: 15px;
            background-color: #fff;
            box-sizing: border-box;

            .icons {
                width: 25px;
                height: 25px;
                margin-right: 10px;
            }
        }
    }

    .title {
        font-family: YouSheBiaoTiHei;
        font-weight: 400;
        font-size: 34px;
        color: #4898FB;
    }

    .box {
        width: 100%;
        height: calc(100% - 350px - 90px);
    }

    .banner {
        // height: calc(20% - 15px);
        border-radius: 20px;
        position: relative;
        padding: 20px;
        height: 184px;

        &:nth-of-type(1) {
            background: url('@/assets/leaderBoard/index/ph_bg.png') no-repeat;
            background-size: 100% 100%;
        }

        &:nth-of-type(2) {
            background: url('@/assets/leaderBoard/index/gh_bg.png') no-repeat;
            background-size: 100% 100%;
        }

        &:nth-of-type(3) {
            background: url('@/assets/leaderBoard/index/jf_bg.png') no-repeat;
            background-size: 100% 100%;
        }

        &:nth-of-type(4) {
            background: url('@/assets/leaderBoard/index/sx_bg.png') no-repeat;
            background-size: 100% 100%;
        }

        &:nth-of-type(5) {
            background: url('@/assets/leaderBoard/index/ali_bg.png') no-repeat;
            background-size: 100% 100%;
        }

        .banner-name {
            width: 40%;
            height: 30px;
            margin-left: 10px;
        }

        .banner-icon {
            width: 35px;
            margin-left: 40px;
            animation: toright 1s linear infinite alternate;

        }

        .info {
            height: calc(100% - 40px);
            display: flex;
            align-items: center;

            .send-box {
                text-align: center;
            }
        }
    }

    .pop-box {
        width: 90%;
        height: 914px;
        background: url('@/assets/leaderBoard/index/pop_bg.png') no-repeat;
        background-size: 100% 100%;
        box-sizing: border-box;
        .list {
            height: calc(100% - 75px);
            padding: 30px;
            box-sizing: border-box;
            flex-direction: column;
            justify-content: space-around;

            .item {
                height: calc(100%/8);
                border-radius: 10px;
                padding: 10px 20px;
                justify-content: space-between;
                box-sizing: border-box;

                &:nth-of-type(even) {
                    background: #F3F8FF;
                }

            }

            .progress {
                height: 16px;
                margin: 0 20px;
                border-radius: 6px;
                background-color: #e0e0e0;

                .bar {

                    height: 100%;
                    background: linear-gradient(-90deg, #A1CBFF, #5AA4FF);
                    border-radius: 6px;
                    transition: width 1s ease-in-out;
                }
            }

            .value {
                font-weight: 600;
            }
        }
    }
    @keyframes toright {
        0% {
            transform: translateX(0);
        }

        100% {
            transform: translateX(10px);
        }
    }
}
</style>