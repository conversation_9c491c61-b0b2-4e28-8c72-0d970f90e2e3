<template>
    <div class="status-header relative z-1">
        <!-- 状态背景图 -->
        <img loading="lazy" :src="statusMatch.bg" alt="" class="w-full" />
        <!-- end -->
        <div class="absolute top-44px left-41px text-#fff">
            <div class="status-title text-36px">
                {{ statusMatch.title }}
            </div>
            <div class="status-des text-28px mt-21px">{{ statusMatch.des }}</div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import waitSendStatus from '@/assets/integralMall/wait_send_bg.png';
import waitReStatus from '@/assets/integralMall/wait_re_bg.png';
import finishStatus from '@/assets/integralMall/finished_bg.png';
const props = defineProps({
    status: {
        type: String,
        default: 'deliver',
    }
})
const statusMatch = computed(() => {
    switch (props.status) {
        case 'deliver':
            return {
                title: '待发货',
                des: '兑换成功，暂未发货',
                bg: waitSendStatus
            };
        case 'receive':
            return {
                title: '待收货',
                des: '货件已发出，请耐心等待',
                bg: waitReStatus
            };
        case 'over':
            return {
                title: '已完成',
                des: '订单已完成，感谢您的支持',
                bg: finishStatus
            }
        default:
            return {

            };
    }
});
</script>
<style scoped lang="scss"></style>