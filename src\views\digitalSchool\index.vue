<template>
  <div class="w-100vw">
    <img loading="lazy" :src="bannner" alt="" class="w-100% h-300px" />
    <div class="px-30px box-border">
      <div class="flex items-center w-100% my-30px">
        <div :class="{ 'mr-20px': ind === 0 }" v-for="(it, ind) in Data.card" :key="ind"
          :style="{ backgroundImage: `url(${it.bg})` }"
          class="bg-no-repeat bg-cover bg-center w-1/2 h-140px flex flex-col justify-center pl-20px box-border"
          @click="onCard(it.path)">
          <div class="text-32px text-#fff mb-10px font-400">{{ it.name }}</div>
          <div class="text-18px text-#CEF2FF">{{ it.tip }}</div>
        </div>
      </div>
      <!-- 直播课程 -->
      <div v-if="Data.zbkcSwiper && Data.zbkcSwiper.length">
        <div class="flex items-center justify-between mb-30px">
          <img loading="lazy" src="@/assets/digitalSchool/zbkc_title.png" alt="" class="h-30px" />
          <div class="text-#418DEA text-24px" @click="onCard('/digitalSchool/course/liveList')">更多>></div>
        </div>
        <div class="flex w-100%">
          <swiper :autoplay="{
            delay: 2500,
            disableOnInteraction: false,
          }" :loop="true" :modules="Data.modules" class="w-100%" ref="onlineSwiper">
            <swiper-slide class="rounded-30px w-100% relative" v-for="(item, index) of Data.zbkcSwiper" :key="index"
              @click="onCard('/digitalSchool/course/liveDetails', { catalogueId: item.catalogueId, type: item.catalogueType })">
              <div
                class="absolute right-0 top-0 text-[#fff] text-24px bg-no-repeat bg-cover bg-center w-150px h-48px flex items-center justify-center"
                :style="{ backgroundImage: `url(${Data.liveStatusObj[item.isOpenLive]})` }">
                {{ item.liveStatus }}
              </div>
              <img loading="lazy" :src="judgeStaticUrl(item.liveCover)" alt="" class="w-100% h-240px rounded-30px" />
            </swiper-slide>
          </swiper>
        </div>
      </div>
      <div class="tab-box mb-36px">
        <van-tabs v-model:active="Data.tab.active" sticky line-width="20" @click-tab="onClickTab">
          <van-tab :title="item.label" v-for="(item, index) in Data.tab.nav" :key="index"
            title-class="tab-title"></van-tab>
        </van-tabs>
      </div>
      <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
        <div class="flex flex-wrap">
          <div v-for="(e, i) in Data.tabList" :key="i" class="rounded-20px item-box mb-21px pb-20px box-border"
            :class="i % 2 != 0 ? 'mr-0px' : 'mr-19px'"
            @click="onCard('/digitalSchool/course/courseDetails', { autoId: e.autoId })">
            <img loading="lazy" :src="judgeStaticUrl(e.curriculumCover)" alt="" class="w-100% h-170px rounded-t-20px" />
            <div class="truncate my-15px pl-20px pr-15px box-border">
              {{ e.curriculumName }}
            </div>
            <div class="pl-20px box-border text-26px">
              <van-icon name="eye-o" class="z-1" color="#999999" />
              <span class="ml-7px text-#999999 ">{{ e.clickNum }}</span>
            </div>
          </div>
        </div>

      </refreshList>
    </div>
  </div>
</template>
<script lang="ts" setup>
import bannner from "@/assets/digitalSchool/banner.png";
import card_xxxz from "@/assets/digitalSchool/card_xxxz.png";
import card_wdkc from "@/assets/digitalSchool/card_wdkc.png";
import icon_zzzb from "@/assets/digitalSchool/icon_zzzb.png";
import icon_zbjs from "@/assets/digitalSchool/icon_zbjs.png";
import icon_zbwks from "@/assets/digitalSchool/icon_zbwks.png";
import { Swiper, SwiperSlide } from "swiper/vue";
import "swiper/css";
import "swiper/css/scrollbar";
import "swiper/css/pagination";
import { Scrollbar, A11y, Pagination } from "swiper/modules";
import { findVoListLive, curriculumInfoFindVoListH5 } from "@/api/digitalSchools/";
import router from "@/router";
// import { useUserStore } from '@/store/modules/user';
import { useDictionary } from '@/store/modules/dictionary';
import refreshList from '@/components/refreshList/index.vue';
import { judgeStaticUrl } from "@/utils/utils";
// const useStore = useUserStore()
const dictionary = useDictionary()
const Data = ref({
  modules: [Scrollbar, A11y, Pagination],
  card: [
    {
      name: "学习小组",
      tip: "加入学习小分队",
      bg: card_xxxz,
      path: "/digitalSchool/studyGroup",
    },
    { name: "我的课堂", tip: "学习、观看、课程", bg: card_wdkc, path: "/digitalSchool/course/courseList", },
  ],
  zbkcSwiper: [],
  tab: {
    active: 0,
    nav: [
      {
        name: "骨干培训",
      },
      {
        name: "工会精神",
      },
      {
        name: "系统操作",
      },
      {
        name: "技能提升",
      },
    ],
  },
  tabList: [],
  pageNum: 1,
  liveStatusObj: {
    'notStart': icon_zbwks,
    'onGoing': icon_zzzb,
    'hasEnd': icon_zbjs,
  }
});

const loadMoreRef: any = ref('')

//直播列表
function getLiveList() {
  findVoListLive({
    pageNum: 1,
    pageSize: 1
  }).then(res => {
    if (res.code == 200) {
      if (res.data && res.data.length) {
        res.data[0].liveStatus = "直播" + dictionary.getDictionaryMap?.[`liveStatus_${res.data[0].isOpenLive}`].dictName;
        res.data[0].liveStatusBgimg = Data.value.liveStatusObj[res.data[0].isOpenLive]
        Data.value.zbkcSwiper[0] = res.data[0]
      }
    }
  })
}

//课堂列表
function getClassList() {
  curriculumInfoFindVoListH5({
    currTypeId: Data.value.tab.nav[Data.value.tab.active].value,
    pageNum: Data.value.pageNum,
    pageSize: 10
  }).then(res => {
    if (res.code == 200) {
      if (Data.value.pageNum == 1) {
        Data.value.tabList = res.data || []
      } else {
        Data.value.tabList = Data.value.tabList.concat(res.data);
      }
      //重置刷新状态及 判断是否加载完成
      if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(Data.value.tabList.length, res.total);
      }
    }
  })
}

function onClickTab(val) {
  Data.value.tab.active = val.name;
  Data.value.pageNum = 1;
  if (loadMoreRef.value) loadMoreRef.value.resetStatus();
  onRefreshList();
}


function onCard(path: string, query: object) {
  if (!path) return;
  router.push({ path, query });
}
// 刷新
const onRefreshList = () => {
  Data.value.pageNum = 1;
  getClassList();
};
// 加载更多
const onLoadMore = () => {
  Data.value.pageNum++;
  getClassList();
};

onMounted(() => {
  Data.value.tab.nav = dictionary.getDictionaryOpt?.['courseSection'];
  getLiveList()
  getClassList()
})
</script>
<style lang="scss" scoped>
.tab-box {
  :deep(.van-tabs__nav--card) {
    border: none !important;
    margin: 0 !important;
  }

  :deep(.van-tab) {
    line-height: 1;
  }

  :deep(.van-tabs__line) {
    background: linear-gradient(86deg, #c7e0ff 0%, #5aa4ff 100%);
    border-radius: 3px;
  }

  :deep(.tab-title) {
    font-weight: 400;
    font-size: 28px;
    color: #666;
  }

  :deep(.van-tab--active) {
    font-weight: 500;
    font-size: 32px;
    color: #333;
  }
}

.item-box {
  box-shadow: 0px 0px 10px 0px #f6f7f8;
  width: calc((100% - 19px) / 2);
}
</style>