import { KeepAlive } from 'vue';

export default [
  {
    path: '/leaderBoard',
    name: 'leaderBoard',
    component: () => import('@/views/leaderBoard/index.vue'),
    meta: {
      title: '动态南充',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/leaderInclusive',
    name: 'leaderInclusive',
    component: () => import('@/views/leaderBoard/inclusive.vue'),
    meta: {
      title: '普惠服务数据统计',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/leaderActivity',
    name: 'leaderActivity',
    component: () => import('@/views/leaderBoard/activity.vue'),
    meta: {
      title: '工会活动数据统计',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/leaderPoints',
    name: 'leaderPoints',
    component: () => import('@/views/leaderBoard/points.vue'),
    meta: {
      title: '频道积分数据统计',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/leaderThink',
    name: 'leaderThink',
    component: () => import('@/views/leaderBoard/think.vue'),
    meta: {
      title: '思想引领数据统计',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/leaderAli',
    name: 'leaderAli',
    component: () => import('@/views/leaderBoard/ali.vue'),
    meta: {
      title: '用户活跃指数',
      isShowTabBar: false,
      isBack: true,
    },
  },
  {
    path: '/leaderAliDetail',
    name: 'leaderAliDetail',
    component: () => import('@/views/leaderBoard/aliDetail.vue'),
    meta: {
      title: '动态南充',
      isShowTabBar: false,
      isBack: true,
    },
  },
];
