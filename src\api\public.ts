import { h5Http,dataCenterHttp,fileHttp,wxUrlHttp} from '@/utils/http/axios';
import { BasicResponse } from '.';
import { log } from 'console';
//点赞
export const likeOperate = (params) => {
    return dataCenterHttp.post({
        url: '/customFlowRecord/likeOperate',
        params,
    });
};
//收藏
export const collectOperate = (params) => {
    return dataCenterHttp.post({
        url: '/customFlowRecord/collectOperate',
        params,
    });
};
// 批量收藏
export const batchCollectOperate = (params) => {
    return dataCenterHttp.post({
        url: '/customFlowRecord/collectOperateBatch',
        params,
    });
};

//分享
export const shareOperate = (params) => {
    return dataCenterHttp.post({
        url: '/customFlowRecord/shareOperate',
        params,
    });
};
//阅读量
export const readOperate = (params) => {
    return dataCenterHttp.post({
        url: '/customFlowRecord/readOperate',
        params,
    });
};
//推送工分
export const ChainShare = (params) => {
    return dataCenterHttp.post({
        url: '/provinceActivation/sharedata/api/ChainShare/event',
        params,
    });
};
//增加积分
export const unionIntegralH5 = (params) => {
    return dataCenterHttp.post({
        url: '/unionIntegralH5/inc',
        params,
    });
};
//文件上传
export const uploadFile = (params) => {
    const formData = new window.FormData();
    formData.append("bucketName", "union-nanchong")
    for (const key in params) {
        formData.append([key], params[key]);
    }
    return fileHttp.post({
        url: '/minio/uploadFile',
        data: formData,
        headers:{
            "Content-Type":'multipart/form-data;charset=UTF-8'
        }
    });
};
// 用户头像上传
export const saveUserAvatar = (params:any) => {
    return dataCenterHttp.post({
        url: '/dataCenterBusiness/modifyAvatar',
        params
    });
};
// 用户头像查询
export const queryUserAvatar = (userId:any) => {
    return dataCenterHttp.get({
        url: '/dataCenterBusiness/getAvatar',
        params:{
            userId
        }
    });
};


//获取省总工分
export const getIntegralH5 = (params) => {
    return dataCenterHttp.get({
        url: '/unionIntegralH5/info',
        params,
    });
};
//获取总工会数据统计
export const currentUnionUserCount = (params) => {
    return dataCenterHttp.get({
        url: '/unionCustomData/currentUnionUserCount',
        params,
    });
};
//获取用户标签
export const queryUserLabel = (params) => {
    return dataCenterHttp.get({
        url: '/userLabelPublicMethod/queryUserLabel',
        params,
    });
};
//获取干部办公权限
export const getWorkPermission = (params) => {
    return dataCenterHttp.get({
        url: '/dataCenterBusiness/getWorkPermission',
        params,
    });
};
//当前区域会员信息
export const userCount = (params) => {
    return dataCenterHttp.get({
        url: '/unionBasicData/userCount',
        params,
    });
};
//新增访问量
export const asyncAddWebVisitSummary = (params) => {
    return dataCenterHttp.get({
        url: '/dataCenterBusiness/asyncAddWebVisitSummary',
        params,
    });
};
//新增频道访问量
export const addBrowseDistrict = (params) => {
    return dataCenterHttp.get({
        url: '/customIntegral/addBrowseDistrict',
        params,
    });
};
//首页推送
export const queryCurrentUserLaunchConfig = (params) => {
    return h5Http.get({
        url: '/homeLaunchConfig/queryCurrentUserLaunchConfig',
        params,
    });
};

// 新闻外链
export const queryNewsLink = (url:string) => {
    return wxUrlHttp.get({
        url:'/'+url
    });
};
