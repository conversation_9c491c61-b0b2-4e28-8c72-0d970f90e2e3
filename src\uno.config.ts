import {
  defineConfig,
  presetTypography,
  presetUno,
  presetAttributify,
  transformerDirectives,
  presetWind,
  transformerAttributifyJsx,
} from 'unocss';
import presetLegacyCompat from '@unocss/preset-legacy-compat';

export default defineConfig({
  presets: [
    presetUno(),
    presetTypography(),
    presetAttributify(),
    presetWind(),
    presetLegacyCompat({
      // options
      commaStyleColorFunction: true,
    }),
  ],
  transformers: [transformerDirectives(), transformerAttributifyJsx()],
  content: {
    pipeline: {
      include: [
        `/src/**/*.{vue,js,ts,jsx.tsx}`, // Ensure this path covers your source files
      ],
    },
  },
});
