<template>
  <div :class="$style['expert-bg']" class="!flex  px-0 w-full pl-[40px] py-[10px] relative">
    <div v-if="showIcon">
      <img loading="lazy" :src="item.avatar" class="w-[140px] h-[140px] rounded-[20px] mt-15px" />
    </div>
    <div class="flex flex-col justify-between w-full py-30px" :class="showIcon ? ' pl-[37px]' : ''">
      <div class="flex justify-between items-center">
        <div>
          <span class="text-[#333333] text-[32px] mr-[22px]"> {{ item.userName || '' }}</span>
          <span>
            <img loading="lazy" :src="belong" class="w-[32px] h-[32px] mr-[15px]" />
            <span class="text-24px">{{ item.affiliatedHospital || '' }}</span>
          </span>
        </div>
      </div>

      <div class="text-[24px] text-[#333333] pt-13px">
        专家资质：<span class="text-[#666666] " v-html="item.qualification || '--'"></span>
      </div>
      <div class="text-[24px] text-[#333333] pt-13px leading-relaxed">
        个人介绍:<span class="text-[#666666]" v-html="item.introduction || '--'"></span>
      </div>
      <div class="text-[24px] text-[#333333] pt-13px leading-relaxed">
        擅长领域：<span class="text-[#666666]" v-html="item.expertiseArea || '--'"></span>
      </div>
      <div class="text-[24px] text-[#333333] pt-13px">
        咨询时段：<span class="text-[#666666]">{{ utils.formatTimeWithoutSeconds(item.startTime) }}-{{
          utils.formatTimeWithoutSeconds(item.endTime) }}</span>
      </div>
    </div>
    <div v-if="!showIcon" class="absolute top-0 right-69px img-box px-16px py-20px bg-#E7F2FF">
      <img loading="lazy" :src="item.avatar" class="w-[80px] h-[80px] rounded-1/2" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import male from '@/assets/public/head_default.png';
import belong from '@/assets/soulStation/belong.png';
import { getExpertVoByDto } from '@/api/soulStation';
import utils from '@/utils/utils';
const route = useRoute();
const props = {
  showIcon: {
    type: Boolean,
    default: true
  }
}
const showIcon = ref(true)
const item = ref<Recordable>({});
//获取详情
function getDetail() {
  getExpertVoByDto({ psychologicalExpertId: route.query.psychologicalExpertId }).then(res => {
    if (res.code == 200) {
      res.data.avatar = utils.judgeStaticUrl(res.data?.avatar) || male;
      item.value = res.data;
    }
  })
}
onMounted(() => {
  if (route.query.showIcon) {
    showIcon.value = route.query.showIcon ? false : true
  }
  if (route.query.psychologicalExpertId) {
    getDetail()
  }
})
</script>

<style lang="less" module>
.expert-bg {
  :global {
    background-image: url('@/assets/soulStation/expert-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 100%;
    min-height: 264px;

    .img-box {
      border-radius: 0px 0px 55px 56px;
      border: 1px solid rgba(159, 202, 255, 0.2);
    }
  }
}
</style>
