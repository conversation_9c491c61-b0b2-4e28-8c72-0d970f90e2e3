<!-- 上传单号 -->
<template>
    <div class="order-upload  bg-[#F9F9F9] w-full min-h-100vh">
        <div class="status-header relative z-1 h-298px flex border-box justify-between p-44px">
            <div class="text-#fff">
                <div class="status-title text-36px">
                    退货中
                </div>
                <div class="status-des text-28px mt-21px">申请已同意请上传物流单号</div>
            </div>
            <img loading="lazy" src="@/assets/inclusive/shop/refund-fill.png" class="w-117px h-105px" />
        </div>
        <div class="-mt-100px relative z-10 ">
            <div class="address flex items-center bg-[#fff] rounded-16px mx-30px py-33px px-30px">
                <img loading="lazy" src="@/assets/integralMall/order_info_address.png" class="w-37px" />
                <div class="flex-1 ml-30px" v-if="details?.receiveSnapshot?.receiverName">
                    <div class="name text-#333 text-32px font-medium flex items-center flex-wrap">
                        <span class="mr-20px">{{ details?.receiveSnapshot?.receiverName }}</span>
                        <span class="flex-1">{{ details?.receiveSnapshot?.receiverPhone }}</span>
                        <div class="w-56px  ml-5px"></div>
                    </div>
                    <div class="text-#474747 text-28px mt-20px">地址：{{ details.receiveSnapshot?.detailArea }}{{
                        details?.receiveSnapshot?.detailAddress }}</div>
                </div>
                <div class="ml-30px text-28px text-#333" v-else>未获取到收件人信息</div>
            </div>
        </div>

        <div class="orderDetal rounded-16px bg-#fff mx-30px my-30px p-24px">
            <div class="text-#333 text-30px font-bold">退款备注</div>
            <div class="text-#666 text-28px mt-34px">退换货要求具备商品收到时完整的外包装，购买物品被人为损坏的不预退款</div>
        </div>

        <div class="my-form mx-30px">
            <van-form ref="formRef" @submit="submit">
                <van-field label="物流名称" name="transportName" v-model="params.transportName" placeholder="请输入物流名称"
                    input-align="right" :rules="[{ required: true, message: '请输入物流名称' }]" />
                <van-field label="物流单号" v-model="params.transportNumber" name="transportNumber" placeholder="请输入您的物流单号"
                    input-align="right" :rules="[{ required: true, message: '请输入您的物流单号' }]" />
            </van-form>

        </div>
        <div class="boutenbox w-400px mx-auto relative mt-100px" @click="handleSubmit">
            <img src="@/assets/public/butto.png" class="w-full block">
            <span class="absolute left-50% top-50% text-#fff
            -translate-50% text-30px leading-none">提交</span>
        </div>
    </div>
</template>
<script lang="ts" setup>
import useOrderFn from '@/hooks/orderHooks.ts'
import { orderDetails } from "@/api/mall/inclusive"
import { showToast } from 'vant'
import useRefreshFun from '@/hooks/app.ts';
const { uploadLogisticsFn } = useOrderFn()//上传单号
const details = ref<any>({})
const route = useRoute()

const params = ref({
    transportName: '',
    transportNumber: '',
    serviceGroupId: route.query.sId as string,
})
// 表单提交
const formRef = ref()
const router = useRouter()
const submit = (val) => {
    if (val) {
        if (!params.value?.serviceGroupId) {
            return showToast('缺少必要参数serviceGroupId')
        }
        uploadLogisticsFn(params.value, () => {
            refresh()
            setTimeout(() => {
                router.go(-1)
            }, 1000)
        })
    }
}
const handleSubmit = async () => {
    formRef.value?.submit()
}

const getOrderDetails = async () => {
    const res = await orderDetails(route.query.orId as string)
    details.value = res.data
}

// 触发列表刷新执行事件
const { addRefreshList } = useRefreshFun()
const refresh = () => {
    addRefreshList({ pageName: 'myOrders', funsName: ['changePage'] })
}
onMounted(() => {
    getOrderDetails()
})
</script>
<style scoped lang="scss">
.status-header {
    background: linear-gradient(90deg, #1580FF, #77BFFF);
}

.my-form {
    :deep(.van-field__error-message) {
        text-align: right;
    }

    :deep(.van-cell) {
        line-height: 1.5;
    }

    :deep(.van-field__label) {
        color: #333333;
        font-size: 28px;
    }

    :deep(.van-field__control) {
        color: #333333;
        font-size: 28px;
    }

    :deep(.van-field__control::placeholder) {
        color: #666;
        font-size: 28px;
    }
}
</style>