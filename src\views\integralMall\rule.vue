<template>
    <div class="rule h-full w-full h-fit box-border pb-100px min-h-100vh">
        <div class="bg-[#fff] mt-26vh mx-30px rounded-26px content flex flex-col">
            <div class="header w-294px mx-auto">
                <img loading="lazy" src="@/assets/integralMall/integral_rule_title.png" class="w-full">
            </div>
            <div class="rich_text px-40px  text-justify text-[#545454] mb-25px flex-1 overflow-scroll" v-html="infos">
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { integralRule } from "@/api/mall/integral";

onMounted(() => {
    getRuleintegralRule();
})
const infos = ref({});
const getRuleintegralRule = () => {
    integralRule().then((res) => {
        if (res.data) infos.value = res.data;
    });
};

</script>
<style lang="scss" scoped>
.rule {
    background: url('@/assets/integralMall/integral_bg.png'), linear-gradient(0deg, #E0EBFE 49%, #0066EC 100%);
    background-size: 100% auto, 100% 100%;
    background-repeat: no-repeat, repeat-y;
    background-color: #e0ebfe;
    height: 100vh;

    .content {
        height: calc(100% - 26vh);
    }
}
</style>