<template>
  <div class="title font-medium text-32px text-#333 pl-16px box-border  flex items-center">
    {{ props.title }}
  </div>
</template>
<script lang="ts" setup>
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
});
</script>
<style lang="scss" scoped>
.title {
  position: relative;
  &::before {
    content: "";
    position: absolute;
    left: 0;
    width: 7px;
    height: 30px;
    background: #5aa4ff;
    border-radius: 4px;
  }
}
</style>