<template>
  <div class="group-info-page bg-[#f6f7f8] min-h-[100vh] relative">
    <img loading="lazy" src="@/assets/interest/examine/bg_2.png" alt=""
      class="w-[100%] h-[834px] absolute top-[0] left-[0]">
    <div class="relative px-[29px] pt-[44px] box-border">
      <div class="bg-[#fff] rounded-[20px]">
        <interestGroupList :data="Data.groupList">
          <!-- <template #dataPart="{ item }">
                    <div class="flex items-center my-[20px]">
                        <div class="flex items-center text-[#999] text-[24px] mr-[34px]">
                            <img loading="lazy" src="@/assets/interest/icon_num.png" class="w-28px h-28px mr-10px">
                            {{ item.totalMember }} 人
                        </div>
                        <div class="flex items-center text-[#999] text-[24px] ">
                            <img loading="lazy" src="@/assets/interest/icon_act.png" class="w-28px h-28px mr-10px">
                           {{ item.activityNum }} 次
                        </div>

                    </div>
                </template> -->
        </interestGroupList>
      </div>
      <div class="bg-[#fff] rounded-[20px] px-[21px] pt-[39px] pb-[1px] box-border mt-20px">
        <!-- :class="index===Data.infoList.length-1?'':'mb-[49px]'" -->
        <div v-for="(item, index) in Data.infoList" :key="index">
          <div class="flex items-center justify-between text-[28px] mb-[49px]" v-if="item.type === 'text'">
            <div class="text-[#666]">{{ item.name }}</div>
            <div class="text-[#333]">{{ item.value }}</div>
          </div>
          <div class="flex flex-col text-[28px]" v-else-if="item.type === 'textarea'">
            <div class="text-[#666]">{{ item.name }}</div>
            <div class="text-[#333] bg-[#F6F7F8] p-[28px] box-border mt-[35px] mb-[25px] rounded-[10px]">{{ item.value
              }}
            </div>
          </div>
        </div>
      </div>
      <div class="bg-[#fff] rounded-[20px] px-[21px] pt-[26px] pb-[1px] box-border mt-[20px]">
        <template v-for="(item, index) in Data.applyInfoList" :key="index">
          <div v-if="item.value">
            <div class="flex items-center  text-[28px] mb-[25px]" v-if="item.type === 'text'">
              <div class="text-[#666]">{{ item.name }}</div>
              <div class="text-[#333] pl-12px">
                {{ item.value == 'wait' ? '待审核' : item.value == 'pass' ? '审核通过' : item.value == 'refuse' ? '审核不通过' :
                  item.value ? item.value : '--' }}
              </div>
            </div>
            <div class="flex flex-col text-[28px]" v-else-if="item.type === 'textarea'">
              <div class="text-[#666]">{{ item.name }}</div>
              <div class="text-[#333] bg-[#F6F7F8] p-[28px] box-border mt-[35px] rounded-[10px]">{{ item.value }}</div>
            </div>
          </div>
        </template>

      </div>
    </div>
    <div class="flex items-center justify-between w-[100%] px-[90px] box-border fixed bottom-[84px]"
      v-if="route.query.isExamine === '1' && Data?.infoList[0]?.state == 'wait'">
      <van-button block @click.stop="onRefuse(Data.infoList[0], 'refuse')"
        class="w-[260px] h-[76px] rounded-[39px] justify-center flex items-center text-[30px] text-[#5AA4FF] border-[1px] border-[#5AA4FF] border-solid mr-[30px]">
        拒绝
      </van-button>
      <van-button block @click.stop="onPass(Data.infoList[0], 'pass')"
        class="w-[260px] h-[78px] rounded-[39px] justify-center flex items-center text-[30px] text-[#fff]"
        style="background: linear-gradient(0deg, #a1cbff 0%, #5aa4ff 100%)">
        通过
      </van-button>
    </div>
    <Popup :show="Data.showPop" :titleName="Data.titleName" :placeHolder="'请输入' + Data.titleName"
      @submit-content="submitContent" @close-popup="closePopup" />
  </div>
</template>
<script lang="ts" setup>
import interestGroupList from "@/components/List/interestGroupList.vue";
import { useRoute } from "vue-router";
const route = useRoute();
import { getAuditDetails, detailByGroupId, groupAudit } from "@/api/interestGroup";
import { useUserStore } from '@/store/modules/user';
const useStore = useUserStore();
import Popup from "@/components/Popup/popup.vue";
import { showToast } from "vant";
const Data = ref({
  groupList: [],
  infoList: [
    { name: '昵称', value: '', type: 'text' },
    { name: '出生日期', value: '', type: 'text' },
    { name: '兴趣爱好', value: '', type: 'text' },
    { name: '所在地区', value: '', type: 'text' },
    { name: '标签', value: '', type: 'text' },
    // {name:'联系邮箱',value:'<EMAIL>',type:'text'},

    // {name:'技能',value:'暂无',type:'text'},
    { name: '加入理由', value: '', type: 'textarea' },
  ],
  applyInfoList: [
    // 未通过和已通过  加对应审核参数即可
    { name: '申请时间', value: '', type: 'text' },
    { name: '审核状态', value: '', type: 'text' },
    { name: '审核意见', value: '', type: 'text' },
  ],
  detailInfo: null,
  showPop: false,
  titleName: '',
  objInfo: {},

});
//详情
function getDetail() {
  getAuditDetails({ autoId: route.query.autoId }).then(res => {
    if (res.code == 200) {
      Data.value.infoList[0].value = res.data.nickName;
      Data.value.infoList[1].value = res.data.birthday;
      Data.value.infoList[2].value = res.data.hobby;
      Data.value.infoList[3].value = res.data.areaName;
      Data.value.infoList[5].value = res.data.reason;
      Data.value.infoList[4].value = res.data.labels.map((item: any) => item.labelName).join(',');
      Data.value.applyInfoList[0].value = res.data.createTime;
      Data.value.applyInfoList[1].value = res.data.state;
      Data.value.applyInfoList[2].value = res.data.auditOpinion;
    }

  })
}
//详情
function getGroupDetail() {
  detailByGroupId({
    groupId: route.query.groupId
  }).then(res => {
    if (res.code == 200) {
      Data.value.groupList.push(res.data);
    }
  })
}
/**
 * 点击拒绝  处理函数
 *
 * @param item 请求项，可以是任意类型的值
 */
function onRefuse(item: any, type) {
  Data.value.showPop = true;
  item.type = type;
  Data.value.titleName = '失败原因'
  Data.value.objInfo = item;

}
//通过
function onPass(item, type) {
  item.type = type;
  Data.value.objInfo = item;
  Data.value.titleName = '通过原因'
  Data.value.showPop = true;
}
//确认提交  拒绝
function submitContent(val: string) {
  // if (!val && Data.value.objInfo.type == 'refuse') {
  //   showToast({
  //     message: "请输入拒绝原因",
  //     icon: "none",
  //   });
  //   return;
  // }
  groupAudit({
    autoId: route.query.autoId,
    state: Data.value.objInfo.type,
    auditOpinion: val
  }).then(res => {
    if (res.code == 200) {
      showToast("审核成功");
      getDetail()
      Data.value.showPop = false;
    }
  })

}
//关闭拒绝弹窗
function closePopup() {
  Data.value.showPop = false;
}

onMounted(() => {
  getDetail();
  getGroupDetail();
})
</script>
<style lang="scss" scoped>
.group-info-page {
  :deep(.van-field__label) {
    font-size: 32px !important;
  }
}
</style>
