import {showConfirmDialog, showDialog, showToast} from 'vant';

import { activityTimeValidator, qualificationValidator,voteUploadWordsTimeValidator,voteTimeValidator } from '@/utils/actRulesValidator.js';
import { useUserStore } from '@/store/modules/user';
import {getUsableIntegral} from '@/api/mall/integral';
import {readOperate} from '@/api/public';
import router from '@/router/index';
import utils from '@/utils/utils';
import {encryptUserInfo} from '@/api/news/index';

// 投票征集日期校验
export const voteValidator = (detail =null) => {
  const activityDetail =detail?detail: useUserStore().activityDetail;
  if (!validator()) return false;
  const userInfo = useUserStore().getUserInfo;
  
  // 地区校验
  const { state, message } = voteUploadWordsTimeValidator(activityDetail, userInfo);
  
  if (!state) {
    showDialog({
      title: '温馨提示',
      message,
      confirmButtonText: '我知道了',
      lockScroll: false,
    });
    return false;
  }
  return true;
};
// 投票日期校验
export const voteDateValidator = (detail =null) => {
  const activityDetail =detail?detail: useUserStore().activityDetail;
  
  if (!validator()) return false;
  const userInfo = useUserStore().getUserInfo;

  // 地区校验
  const { state, message } = voteTimeValidator(activityDetail, userInfo);
  if (!state) {
    showDialog({
      title: '温馨提示',
      message,
      confirmButtonText: '我知道了',
      lockScroll: false,
    });
    return false;
  }
  return true;
};
// 详情 登录 活动时间 地区校验
export const activityValidator = (detail =null) => {
  const activityDetail =detail?detail: useUserStore().activityDetail;
  if (!validator()) return false;
  const userInfo = useUserStore().getUserInfo;

  // 地区校验
  const { state, message } = qualificationValidator(activityDetail, userInfo);
  if (!state) {
    showDialog({
      title: '温馨提示',
      message,
      confirmButtonText: '我知道了',
      lockScroll: false,
    });
    return false;
  }
  return true;
};

// 详情 登录 活动时间校验
export const validator = () => {
  const activityDetail = useUserStore().activityDetail;
  if (!activityDetail) {
    showDialog({
      title: '温馨提示',
      message: '当前活动太火爆,请稍后重试或尝试重新进入活动页面。',
      confirmButtonText: '我知道了',
      lockScroll: false,
    });
    return false;
  }

  if (!useUserStore().getUserInfo?.userId) {
    showDialog({
      title: '温馨提示',
      message: '暂未查询到您的用户信息,请到川工之家APP注册或尝试重新登录。',
      confirmButtonText: '我知道了',
      lockScroll: false,
    });
    return false;
  }
  // 活动时间
  let { state: result, message: msg } = activityTimeValidator(activityDetail);
  if (!result) {
    let userInfo = useUserStore().getUserInfo
    // usePopup({ state: true, userInfo, message: msg });
    showDialog({
      title: '温馨提示',
      message: msg,
      confirmButtonText: '我知道了',
      lockScroll: false,
    });
    return false;
  }
  return true;
};
// 详情 登录校验
export const defaultValidator = () => {
  const activityDetail = useUserStore().activityDetail;
  if (!activityDetail) {
    showDialog({
      title: '温馨提示',
      message: '当前活动太火爆,请稍后重试或尝试重新进入活动页面。',
      confirmButtonText: '我知道了',
      lockScroll: false,
    });
    return false;
  }
  if (!useUserStore().getUserInfo?.userId) {
    showDialog({
      title: '温馨提示',
      message: '暂未查询到您的用户信息,请到川工之家APP注册或尝试重新登录。',
      confirmButtonText: '我知道了',
      lockScroll: false,
    });
    return false;
  }
  return true;
};
// 活动详情校验
export const activityDetailValidator = () => {
  const activityDetail = useUserStore().activityDetail;
  if (!activityDetail) {
    showDialog({
      title: '温馨提示',
      message: '当前活动太火爆,请稍后重试或尝试重新进入活动页面。',
      confirmButtonText: '我知道了',
      lockScroll: false,
    });
    return false;
  }
  return true;
};

export const checkIntegral = async ()=>{
  //积分消耗确认
  const {integralFlag,integralThreshold,integralOperateType,integralScore} = useUserStore().activityDetail ?? {}
  //消耗积分
  //debugger
  const {data:userScore} = await getUsableIntegral()
  
  if(integralFlag === 'y' && integralOperateType === 'decrement'){
   
    if(integralThreshold && integralThreshold > userScore){
      //"积分不足，当前活动需要积分达到" + integralThreshold + "才能参与，您的积分：" + userIntegral
      showDialog({
        title: '积分不足',
        message:`当前活动需要积分达到${integralThreshold}才能参与，您的积分：${userScore}`,
        confirmButtonText: '我知道了',
        lockScroll: false,
      });
      return false;
    }
    if(integralScore > userScore){
      showDialog({
        title: '积分不足',
        message:`当前活动需要消耗${integralScore}，您的积分：${userScore}`,
        confirmButtonText: '我知道了',
        lockScroll: false,
      });
      return false;
    }
    await showConfirmDialog({
      title: "提示",
      message: `参与活动${ integralOperateType === 'decrement'?'消耗':'奖励'}${integralScore}积分,您的积分：${userScore}`,
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      className: "close",
    })
    return true;
  }
  if(integralFlag === 'y' && integralOperateType === 'increment'){
    await showConfirmDialog({
      title: "提示",
      message: `参与活动${ integralOperateType === 'decrement'?'消耗':'奖励'}${integralScore}积分,您的积分：${userScore}`,
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      className: "close",
    })
    return true;
  }
  return true;
}

export const checkCouponIntegral = async (couponInfo)=>{
  //积分消耗确认
  const {integralFlag,integralThreshold,integralOperateType,integralScore,couponName} = couponInfo
  //消耗积分
  //debugger
  const {data:userScore} = await getUsableIntegral()
  
  if(integralFlag === 'y' && integralOperateType === 'decrement'){
    
    if(integralThreshold && integralThreshold > userScore){
      //"积分不足，当前活动需要积分达到" + integralThreshold + "才能参与，您的积分：" + userIntegral
      showDialog({
        title: '积分不足',
        message:`领取【${couponName}】需要积分达到${integralThreshold}才能参与，您的积分：${userScore}`,
        confirmButtonText: '我知道了',
        lockScroll: false,
      });
      return false;
    }
    if(integralScore > userScore){
      showDialog({
        title: '积分不足',
        message:`领取【${couponName}】需要消耗${integralScore}，您的积分：${userScore}`,
        confirmButtonText: '我知道了',
        lockScroll: false,
      });
      return false;
    }
    await showConfirmDialog({
      title: "提示",
      message: `领取【${couponName}】${ integralOperateType === 'decrement'?'消耗':'奖励'}${integralScore}积分,您的积分：${userScore}`,
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      className: "close",
    })
    return true;
  }
  if(integralFlag === 'y' && integralOperateType === 'increment'){
    await showConfirmDialog({
      title: "提示",
      message: `领取【${couponName}】${ integralOperateType === 'decrement'?'消耗':'奖励'}${integralScore}积分,您的积分：${userScore}`,
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      className: "close",
    })
    return true;
  }
  return true;
}

export const toDetail = (record,params = {}) => {
  const { activityId, activityMode, externalLink, externalLinkUrl } = record;
  console.log(activityId,'activityId')
  record.readCount++
  if (externalLink === 'y') {
    readOperate({ sourceId: activityId });
    if(record?.skipNoticeFlag=='y'){
      showConfirmDialog({
        title: '提示',
        message: `是否跳转外链`,
        confirmButtonText: '确认',
        lockScroll: false,
        showConfirmButton:true,
      }).then(()=>{
        getUserInfo(record)
      }).catch(()=>{})
    }else{
      getUserInfo(record)
    }
    return
  }
  
  useUserStore().setActivityDetail()
  sessionStorage.setItem('activityId', activityId)
  switch (activityMode) {
    case 'vieAnswer':
      utils.openActLink({
        title: record.activityName,
        url:'/activityHome/vieAnswerActivity?activityId='+activityId,
        shareName: record.activityName,//'分享标题'
        shareurl: window.location.origin+'/activityHome/vieAnswerActivity?activityId='+activityId,//'分享地址'
        dataid: record?.activityId,
        type:1,//类型 1-工会活动，2-普惠活动
        uid: record.companyId,
        win:record?.activityId+Math.floor(Math.random() * 100),
        isExternal:'n'
      })
      break
    case 'friendship':
      router.push({
        path: '/activityHome/friendship/activity/detail',
        query: { activityId, ...params }
      })
      break
    case 'volunteerService':
      router.push({
        path: "/activityHome/vitality/volunteerService/activityDetail",
        query:{
          activityId
        }
      });
      break
    default:
      utils.openActLink({
        title: record.activityName,
        url:'/activityHome/activityDetail?activityId='+activityId,
        shareName: record.activityName,//'分享标题'
        shareurl: window.location.origin+'/activityHome/activityDetail?activityId='+activityId,//'分享地址'
        dataid: record?.activityId,
        type:record?.activityCategory=='inclusive'?2:1,//类型 1-工会活动，2-普惠活动
        uid: record.companyId,
        win:record?.activityId+Math.floor(Math.random() * 100),
        isExternal:'n'
      })
      // router.push({
      //   path: '/activityHome/activityDetail',
      //   query: { activityId }
      // })
  }
}
//用户信息加密
function getUserInfo(item) {
  //是否过期
  if(item.whetherExpire){
    showToast(("授权已过期！"));
  }else {
    getEncryption(item);//获取登录返回的用户信息
  }
}
function getEncryption(item) {
  encryptUserInfo({
    recordId:item?.activityId,
    userInfo: useUserStore()?.getUserInfo,
  }).then(res=>{
    if (res.code == 200 && res.data) {
      let url = item.externalLinkUrl; //返回的url
      url += item.externalLinkUrl.indexOf("?") == -1 ? "?" : "&"; //判断是否有问号
      utils.openActLink({
        title: item.activityName,
        url:url + "token=" + res.data,
        shareName: item.activityName,//'分享标题'
        shareurl: url,//'分享地址'
        dataid: item?.activityId,
        type:item.activityCategory === 'inclusive' ? 2:1,//类型 1-工会活动，2-普惠活动
        uid: item.companyId,
        win:item?.activityId,
        isExternal:'y'
      })
      
    } else {
      utils.openActLink({
        title: item.activityName,
        url:item.externalLinkUrl,
        shareName: item.activityName,//'分享标题'
        shareurl: item.externalLinkUrl,//'分享地址'
        dataid: item?.activityId,
        type:item.activityCategory === 'inclusive' ? 2:1,//类型 1-工会活动，2-普惠活动
        uid: item.companyId,
        win:item?.activityId,
        isExternal:'y'
      })
      
    }
  })
}

export default {
  activityValidator() {
    const activityDetail = useUserStore().activityDetail;
    if (!validator()) return false;
    const userInfo = useUserStore().getUserInfo;

    // 地区校验
    const { state, message } = qualificationValidator(activityDetail, userInfo);
    if (!state) {
      showDialog({
        title: '温馨提示',
        message,
        confirmButtonText: '我知道了',
        lockScroll: false,
      });
      return false;
    }
    return true;
  },
  validator() {
    const activityDetail = useUserStore().activityDetail;
    if (!activityDetail) {
      showDialog({
        title: '温馨提示',
        message: '当前活动太火爆,请稍后重试或尝试重新进入活动页面。',
        confirmButtonText: '我知道了',
        lockScroll: false,
      });
      return false;
    }
    if (!useUserStore().getUserInfo?.userId) {
      showDialog({
        title: '温馨提示',
        message: '暂未查询到您的用户信息,请到川工之家APP注册或尝试重新登录。',
        confirmButtonText: '我知道了',
        lockScroll: false,
      });
      return false;
    }
    // 活动时间
    let { state: result, message: msg } = activityTimeValidator(activityDetail);
    if (!result) {
      let userInfo = useUserStore().getUserInfo;
      // usePopup({ state: true, userInfo, message: msg });
      showDialog({
        title: '温馨提示',
        message: msg,
        confirmButtonText: '我知道了',
        lockScroll: false,
      });
      return false;
    }
    return true;
  },
  defaultValidatordefaultValidator() {
    const activityDetail = useUserStore().activityDetail;
    if (!activityDetail) {
      showDialog({
        title: '温馨提示',
        message: '当前活动太火爆,请稍后重试或尝试重新进入活动页面。',
        confirmButtonText: '我知道了',
        lockScroll: false,
      });
      return false;
    }
    if (!useUserStore().getUserInfo?.userId) {
      showDialog({
        title: '温馨提示',
        message: '暂未查询到您的用户信息,请到川工之家APP注册或尝试重新登录。',
        confirmButtonText: '我知道了',
        lockScroll: false,
      });
      return false;
    }
    return true;
  },
  activityDetailValidator() {
    const activityDetail = useUserStore().activityDetail;
    if (!activityDetail) {
      showDialog({
        title: '温馨提示',
        message: '当前活动太火爆,请稍后重试或尝试重新进入活动页面。',
        confirmButtonText: '我知道了',
        lockScroll: false,
      });
      return false;
    }
    return true;
  },
};
