<template>
    <div class="auth_form w-full px-30px pb-84px pt-46px box-border">

        <div class="form_info bg-[#fff] h-full rounded-16px pb-72px">
            <div class="form_info_title relative">
                <img loading="lazy" src="@/assets/workerCraftsman/auth/title_left_active.png" class="w-full"
                    :class="{ '-scale-x-100': currentType === '1' }" alt="" />
                <div class="tab flex justify-between absolute 
                w-full top-40% -translate-y-[50%] text-[#fff] text-[32px]">
                    <div v-for="item, index in typeForm" :key="index" @click="changeType(item.value)"
                        :class="{ 'text-[#D12E13] text-[36px] font-medium active_line': currentType === item.value }"
                        class="flex-1 text-center relative">
                        {{ item.name }}
                    </div>
                </div>
            </div>
            <!-- 表单 -->
            <wokerForm ref="workFormRef" v-if="currentType === '0'" :defaultForm="defaultform"
                @getForm="(val) => getForm(val, 'workForm')">
            </wokerForm>
            <craftsmanForm ref="craftFormRef" v-else-if="currentType === '1'" :defaultForm="defaultform"
                @getForm="(val) => getForm(val, 'craftForm')">
            </craftsmanForm>
            <div class="submit_btn text-34px text-[#fff]  h-78px mx-65px rounded-39px flex items-center justify-center"
                @click="handleSubmit">
                立即认证
            </div>
        </div>
        <Popup ref="popupRef" v-model:show="showPopup" :pageTo="navTo" status="wait" title="提交成功,等待审核"
            message="审核成功将发送短信至您的手机上，请留意短信！" btnText="去查看"></Popup>
    </div>
</template>

<script lang="ts" setup>
import wokerForm from './components/wokerForm.vue';
import craftsmanForm from './components/craftsmanForm.vue';
const Popup = defineAsyncComponent(() => import('@/components/Popup/submitWaitPopup.vue'));

import { savewokerAuth, saveCraftAuth, checkAuth } from '@/api/workerCraftsman';
import { showToast } from 'vant';
const router = useRouter()
const route = useRoute()
onMounted(() => {
    initalPage()
})
const typeForm = [
    {
        name: '劳模信息',
        value: '0'
    },
    {
        name: '工匠信息',
        value: '1'
    }
]

const currentType = ref(typeForm[0].value)
const changeType = (type: string) => {
    currentType.value = type
    judgeAuth()
}
// 表单
const workFormRef = ref(null) //劳模
const craftFormRef = ref(null) //工匠
const showPopup = ref(false) //提交提示
const handleSubmit = () => {
    if (currentType.value === '0') workFormRef.value?.submit();
    else craftFormRef.value?.submit();
}

// 处理提交接口
const getForm = (val, type: string) => {
    switch (type) {
        case 'workForm':
            savewokerAuth(val).then((res) => {
                if (res.code === 200) showPopup.value = true;
                else showToast(res.message);
                // if (res.code === 200) setTimeout(() => {
                //     router.back()
                // }, 1000);
            });
            break;
        case 'craftForm':
            saveCraftAuth(val).then((res) => {
                if (res.code === 200) showPopup.value = true;
                else showToast(res.message);
                // if (res.code === 200) setTimeout(() => {
                //     router.back()
                // }, 1000);
            });
            break;
    }

}
// 下一步跳转
const navTo = (status = '') => {
    if (status === 'pass') {
        return router.replace({
            path: '/workerCraftsman/auth/info',
            query: {
                type: currentType.value
            }
        })
    } else {
        return router.replace({
            path: '/workerCraftsman/auth/status',
            query: {
                type: currentType.value
            }
        })
    }

}
const initalPage = () => {
    if (route.query?.type) currentType.value = route.query.type as string;
    judgeAuth()
}
const defaultform = ref<any>()
const judgeAuth = async () => {
    const { data } = await checkAuth(currentType.value)
    if (data) {
        // 没有切换过
        if (route.query.tag === 'reSubmit' && currentType.value === route.query?.type) {
            defaultform.value = data
            // 重新提交则不跳转到认证信息页面
            return
        }
        if (data.auditStatus) {
            navTo(data.auditStatus)
        }
    }
    else defaultform.value = null

}
</script>

<style scoped lang="scss">
.auth_form {
    background-image: url('@/assets/workerCraftsman/auth/auth_bg_1.jpg'), url('@/assets/workerCraftsman/auth/auth_bg_2.jpg');
    background-size: 100% 50%, 100% 50%;
    background-position: top center, bottom center;
    width: 100%;
    height: fit-content;
    background-repeat: no-repeat;

    .active_line::after {
        width: 140px;
        height: 8px;
        background: linear-gradient(90deg, #F33C40 0%, #FB8512 100%);
        border-radius: 4px;
        display: block;
        content: ' ';
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: -10px;
    }

    .submit_btn {
        background: linear-gradient(to right, #FF9A4F, #FC6631)
    }
}
</style>