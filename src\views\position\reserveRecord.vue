<template>
  <div class="bg-[#F5F5F5] h-100vh" :class="$style['reserve-record']">
    <div class="tab-box mb-36px h-70px bg-white sticky top-0 z-99">
      <van-tabs v-if="data.tab.active" v-model:active="data.tab.active" sticky line-width="20" @click-tab="onClickTab">
        <van-tab :title="item.label" v-for="item in data.tab.nav" :key="item.value" :name="item.value"
          title-class="tab-title"></van-tab>
      </van-tabs>
    </div>
    <div class="px-20px">
      <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
        <div v-for="item in data.list" :key="item"
          class="bg-[#FFF] rounded-16px list-items mb-22px p-20px text-26px leading-50px text-[#666] relative"
          @click="handleDetail(item)">
          <div class="text-30px text-[#333] leading-60px font-500">{{ item.venueName || '' }}</div>
          <div>所属阵地：{{ item.positionName || '' }}</div>
          <div>
            预约日期：{{ utils.formatTimeWithoutSeconds(item.reservationDate) }} {{
              utils.formatTimeWithoutSeconds(item.reservationStartTime) }}-{{
              item.reservationEndTime
            }}
          </div>
          <div>联系人：{{ item.userName }}</div>
          <div>手机号码：{{ item.phone }}</div>
          <van-divider class="!my-10px text-[#C5C5C5]" dashed />
          <div class="flex w-full justify-between items-center">
            <div>
              <div v-show="item.auditTime">审核时间：{{ item.auditTime }}</div>
              <div v-if="item.auditOpinion && item.state === 'refuse'" class="text-red-500">失败原因：{{ item.auditOpinion }}
              </div>
            </div>
            <div class="w-150px h-54px text-center leading-54px text-[#fff] detail-btn  rounded-27px">
              查看详情
            </div>
          </div>
          <img loading="lazy" class="absolute top-1/4 right-30px w-151px h-151px" :src="StateImage[item.state]" />
        </div>
      </refreshList>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useDictionary } from '@/store/modules/dictionary';
import refreshList from '@/components/refreshList/index.vue';
import { findRecordList } from '@/api/position';
import { useUserStore } from '@/store/modules/user';
import pass from '@/assets/position/reserve/pass.png';
import review from '@/assets/position/reserve/review.png';
import refuse from '@/assets/position/reserve/refuse.png';
import cancel from '@/assets/position/reserve/cancel.png';
import expire from '@/assets/position/reserve/expire.png';
import used from '@/assets/position/reserve/used.png';
import useExpire from '@/assets/position/reserve/useExpire.png';
import utils from '@/utils/utils';

const dictionary = useDictionary();

const userStore = useUserStore();

const router = useRouter();

const StateImage: Recordable = {
  review,
  pass,
  refuse,
  cancel,
  expire,
  used,
  useExpire,
};

const loadMoreRef = ref();

const data = ref<any>({
  tab: {
    active: '',
    nav: [],
    pageNum: 1,
    list: [],
  },
});

const onClickTab = (item: Recordable) => {
  data.value.tab.active = item.name;
};

function getList() {
  findRecordList({
    pageSize: 10,
    pageNum: unref(data).pageNum,
    state: unref(data).tab.active,
    userId: userStore.getUserInfo.userId,
    systemQueryType: 'h5',
  }).then(res => {
    if (res.code == 200) {
      if (data.value.pageNum === 1) {
        data.value.list = [];
      }
      data.value.list = data.value.list.concat(res.data);
      //重置刷新状态及 判断是否加载完成
      if (loadMoreRef.value) {
        loadMoreRef.value?.onLoadSuc(data.value.list.length, res.total);
      }
    }
  });
}
// 刷新
const onRefreshList = () => {
  data.value.pageNum = 1;
  getList();
};
// 加载更多
const onLoadMore = () => {
  data.value.pageNum++;
  getList();
};

function handleDetail({ recordId }: Recordable) {
  router.push({ path: '/reserveDetail', query: { recordId } });
}

onMounted(() => {
  data.value.tab.nav = dictionary.getDictionaryOpt?.['venueRecordState'] as Recordable[];
  data.value.tab.active = data.value.tab.nav[0].value;
});

watch(
  () => unref(data).tab.active,
  () => {
    onRefreshList();
  },
  { deep: true }
);
</script>

<style lang="less" module>
.reserve-record {
  :global {
    .van-divider {
      &::before {
        border-color: #c5c5c5;
      }
    }

    .van-tab {
      line-height: 1;
      height: 100%;
      font-size: 30px;
    }

    .detail-btn {
      background: linear-gradient(to right, #5BA5FF, #5CB6FF);
    }

    .tab-title {
      font-weight: 400;
      font-size: 30px;
      color: #333333;
    }

    .van-tab--active {
      font-weight: 400;
      font-size: 32px;
      color: #5aa4ff;
    }


    .van-tabs__line {
      background: linear-gradient(86deg, #5aa4ff 0%, #c7e0ff 100%);
      border-radius: 3px;
      height: 6px;
      width: 54px;
    }

    .van-tabs--line .van-tabs__wrap {
      height: 80px;
    }

    .van-tabs__nav--line {
      height: 100%;
      padding-bottom: 15px;
    }
  }
}
</style>
