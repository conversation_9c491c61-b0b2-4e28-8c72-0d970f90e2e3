<template>
  <div class="bg-[#f6f7f8] min-h-[100vh] overflow-y-scroll pb-[122px] bg-#FFF my_digital_detail">
    <div v-for="(item, index) in Data.topicList" :key="index"
      class="mb-[20px] bg-[#fff] px-29px box-border pt-[39px] pb-[19px]">
      <shareList :data="[item]" :isShowDetail="false" :type="item.auditStatus == 'refuse' ? 'examine' : ''">
        <template #centerContent="{ item }">
          <div class="text-#333 text-24px">
            发布小组：<span class="text-#666">{{ item.groupName }}</span>
          </div>
        </template>
        <template #bottomContent="{ item }" v-if="item.auditStatus == 'refuse'">
          <div class="bg-#f6f7f8 rounded-10px px-30px py-20px box-border text-28px  mt-[22px]" v-if="item.auditRemarks">
            <span class="text-#999">拒绝理由：</span> <span class="text-#ff4344">{{ item.auditRemarks }}</span></div>
        </template>
      </shareList>
    </div>
    <van-popup :show="Data.showPop" class="popup1" round>
      <van-form @submit="submit" class="flex flex-col items-center">
        <div class="text-center text-32px text-#333 mb-40px">拒绝原因</div>
        <div class="text-input w-full">
          <van-field required v-model="Data.formData.auditRemarks" label="" rows="3" type="textarea" label-width="0"
            :rules="[{ required: true, message: '请输入拒绝原因' }]"></van-field>
        </div>
        <van-button type="primary"
          class="btn w-65/100 mt-40px mx-auto text-34px text-#fff py-23px text-center border-none rounded-40px"
          native-type="submit" style="background: linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%);">提交</van-button>
        <img loading="lazy" src="@/assets/public/close.png" alt=""
          class="fixed -bottom-15 w-60px h-60px left-1/2 -translate-x-1/2" @click="Data.showPop = false">
      </van-form>

    </van-popup>
    <div class="flex bottom_btn mt52px fixed left-0 bottom-0 w-full pt20px pb40px justify-center"
      v-if="Data?.topicList[0]?.auditStatus == 'wait'">
      <div class="mr33px" @click.stop="clickRefuse">
        拒绝
      </div>
      <div class="!w-330px" @click.stop="clickAgree">
        通过
      </div>
    </div>

  </div>
</template>
<script lang="ts" setup>
import shareList from '@/views/digitalSchool/components/shareList.vue';
import { shareAuditH5 } from "@/api/digitalSchools/index"
import { showSuccessToast, showFailToast } from "vant";
import { useRoute } from "vue-router";
const route = useRoute();
const router = useRouter();
const Data = ref({
  topicList: [],
  titleName: '',
  objInfo: {},
  pageNum: 1,
  formData: {
    auditStatus: "",//参考字典groupStudyStatus，pass我加入的，wait我申请的，refuse未通过的
    auditRemarks: "",  //拒绝必填
    autoId: ""
  },
  showPop: false
});

onMounted(() => {
  Data.value.topicList = [JSON.parse(route.query.info)]
});

// 拒绝
function clickRefuse(item) {
  Data.value.showPop = true
  Data.value.formData.autoId = Data.value.topicList[0].autoId
  Data.value.formData.auditStatus = "refuse"
}
function clickAgree(item) {
  Data.value.formData.autoId = Data.value.topicList[0].autoId
  Data.value.formData.auditStatus = "pass"
  submit()

}
function submit() {
  shareAuditH5(Data.value.formData).then(res => {
    if (res.code == 200) {
      showSuccessToast("提交成功");
      refresh()
      router.go(-1)
    } else {
      showFailToast(res.message)
    }
    Data.value.showPop = false

    for (const key in Data.value.formData) {
      Data.value.formData[key] = ""
    }
  })
}
import useRefreshFun from '@/hooks/app.ts';
// 其他页面触发刷新执行事件
const { addRefreshList } = useRefreshFun()
const refresh = () => {
  addRefreshList({ pageName: 'myDigitalSchool', funsName: ['changePage'] })
}

</script>
<style lang="scss" scoped>
.my_digital_detail {
  .van-overlay {
    background: rgba(0, 0, 0, 0.5);
  }

  .popup1 {
    width: 85vw;
    min-height: 500px;
    padding: 50px 40px;
    box-sizing: border-box;
    top: 40%;
    overflow-y: visible;
  }

  .text-input {
    :deep(.van-cell) {
      padding: 0;
    }

    :deep(.van-field__control) {
      background: #f6f7f8;
      border-radius: 10px;
      padding: 20px 35px;
      box-sizing: border-box;
    }
  }

  .bottom_btn {
    background: #FFFFFF;
    box-shadow: 0px -6px 9px 0px rgba(186, 194, 201, 0.16);

    >div {
      width: 231px;
      height: 78px;
      border-radius: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      font-size: 32px;

      &:nth-child(1) {
        background-color: #fff;
        border: 1px solid #5CA5FF;
        color: #5CA5FF;
      }

      &:nth-child(2) {
        background: linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%);
        color: #FFFFFF;
      }
    }
  }
}
</style>