<template>
    <div class="hand-audit h-100vh">
        <div class="title-bg h-260px relative py-58px px-36px box-border flex">
            <img loading="lazy" :src="cadreInfo?.gender == '1' ? male : cadreInfo?.gender == '2' ? female : defaultImg"
                alt="" class="w-90px h-90px" />
            <div class="text-#fff pl-17px text-26px">
                <div class="flex items-center">
                    <div class="text-32px mr-19px">{{ cadreInfo?.cadreName }}</div><span
                        v-if="cadreInfo?.postName">|</span>
                    <div class="ml-19px">{{ cadreInfo?.postName }}</div>
                </div>

                <div class="mt-12px">单位：{{ cadreInfo?.workCompanyName || '--' }}</div>
            </div>
        </div>
        <div class="p-30px relative my-views box-border">
            <div class="bg-#FFFFFF rounded-20px -mt-60px relative py-10px mb-20px flex items-center pr-25px">
                <div class="flex-1">
                    <van-tabs v-model:active="active" class="w-100%" swipeable title-active-color="#5AA4FF"
                        @click-tab="handleChange">
                        <van-tab v-for="(item, index) in stateArr" :title="item.label" :name="item.value"
                            class="text-34px" :key="index"> </van-tab>
                    </van-tabs>
                </div>
                <!-- 更多筛选项 -->
                <div class="flex flex-col items-center h-fit" @click="filtershow = !filtershow">
                    <img loading="lazy" src="@/assets/public/icon_sx.png" class="w-30px h-30px" />
                </div>
            </div>
            <!-- 筛选弹窗 -->
            <div class="dialog-activity" v-show="filtershow">
                <van-popup :show="filtershow" round position="top" @click-overlay="filtershow = false">
                    <div class="bg-#fff p-20px">
                        <div v-if="cadreInfo?.unionId === '6650f8e054af46e7a415be50597a99d5'">
                            <div class="text-30px mb-20px">下级工会</div>
                            <div class="flex flex-wrap">
                                <div class="min-w-100px dropdown_menu_item box-border
                            px-20px py-6px mb-15px rounded-25px bg-#f7f7f7 text-#4c4c4c mr-15px text-28px"
                                    :class="{ '!bg-#A1CBFF !text-#fFF': item.value == queryCompanyId }"
                                    @click.stop="queryCompanyId = item.value" v-for="item, index in queryCompanyList"
                                    :key="index">
                                    {{ item.label }}
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center mt-20px text-28px">
                            <span class="text-28px mr-10px">包含下级</span>
                            <van-checkbox v-model="nextLevelFlag" shape="square"></van-checkbox>
                        </div>
                    </div>
                </van-popup>
            </div>
            <!-- end -->
            <refreshList @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
                <div v-for="(item, index) in list" :key="index"
                    class="rounded-20px bg-[#fff] border-1px border-solid border-#f5f5f5 shadow-drop-2-center"
                    @click="handleDetail(item)">
                    <div
                        class="flex justify-between text-[30px] p-[var(--van-cell-group-inset-padding)] py-16px items-center">
                        <span class="text-[#2f2f2f] font-bold">{{ item.nickname }}申请入会</span>
                        <span
                            :class="item.auditState == 'return' ? 'text-#E72740' : item.auditState == 'pass' ? 'text-#5DD1AE' : 'text-#5AA4FF'">{{
                                fixState(item)?.dictName || ''
                            }}</span>
                    </div>
                    <div :style="{ border: '1px solid #f4f4f4' }"></div>
                    <van-form :readonly="true" input-align="right">
                        <van-cell-group inset>
                            <van-field v-model="item.nickname" name="nickname" label="申请人" />
                            <van-field v-model="item.phone" name="phone" label="联系电话" />
                            <van-field v-model="item.identityNumber" name="identityNumber" label="身份证号" />
                            <van-field v-model="item.createTime" name="createTime" label="申请时间" />

                            <van-field v-model="item.auditCompanyName" name="auditCompanyName" label="所入工会" />
                            <van-field v-model="item.belongCompanyName" name="belongCompanyName" label="单位名称" />
                        </van-cell-group>
                    </van-form>
                    <!-- 取消申请 -->
                    <!-- <div v-if="item?.auditState=='wait'" :style="{ 'border-top': '1px solid #f4f4f4' }" class="p-21px flex justify-end w-full box-border">
                        <div  class="w-[162px] h-[50px] rounded-[25px] justify-center flex items-center text-[30px] text-[#fff]" style="background: linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%);" @click.stop="cancel(item)">取消申请</div> 
                    </div> -->
                </div>
            </refreshList>
        </div>

    </div>
</template>
<script lang="ts" setup>
import { getAuditRecord as apiList, scanQRCodeCheck, cancelApply } from '@/api/joinUnion';
import { useDictionary } from '@/store/modules/dictionary';
import male from '@/assets/public/male.png';
import female from '@/assets/public/female.png';
import defaultImg from '@/assets/public/head_default.png';
import { useUserStore } from '@/store/modules/user';
import refreshList from '@/components/refreshList/index.vue';
import { concat } from 'lodash-es';
import router from '@/router';
const useStore = useUserStore();
const dictionary = useDictionary();
const cadreInfo = computed(() => useStore.getCadreInfo)//干部信息
const active = ref<string>('');

const stateArr = computed(() => [
    //   { label: '全部', value: '' },
    ...dictionary.getDictionaryOpt?.['comAuditStateShow'].filter(item => item.value !== 'refuse'),
]);

const list = ref<Recordable>([]);

const pageNum = ref<number>(1);

//获取列表
const loadMoreRef = ref<any>();
const Data = ref({
    active: 0,
})

// 加载更多
const onLoadMore = () => {
    pageNum.value++;
    onLoad();
};

const onRefreshList = () => {
    pageNum.value = 1;
    onLoad();
};

function handleChange() {
    unref(loadMoreRef)?.resetStatus();
    onRefreshList();
}
const fixState = (item: Recordable) =>
    dictionary.getDictionaryMap?.[`comAuditStateShow_${item.auditState}`];

const onLoad = () => {
    apiList({
        auditState: unref(active) ? unref(active) : undefined,
        pageSize: 10,
        pageNum: unref(pageNum),
        nextLevelFlag: nextLevelFlag.value,
        queryCompanyId: queryCompanyId.value
    }).then(({ data, total = 0 }) => {
        if (unref(pageNum) === 1) list.value = [];

        list.value = concat(unref(list), data);

        unref(loadMoreRef)?.onLoadSuc(unref(list)?.length, total);
    });
};
function handleDetail(item) {
    router.push({
        path: '/handAuditDetail',
        query: {
            autoId: item.autoId
        }
    })
}
// 筛选
const filtershow = ref(false)
const nextLevelFlag = ref(true) //是否包含下级 默认包含
const queryCompanyId = ref(null) // 查询工会id
const queryCompanyList = computed(() => {
    let arr = dictionary.getDictionaryOpt?.[`unionsInfo`]
    if (arr?.length) arr = arr.filter((el: any) => el.value !== '6650f8e054af46e7a415be50597a99d5')
    arr.unshift({ label: '全部', value: '' })
    return arr
})
watch(filtershow, (val) => {
    if (filtershow.value) return
    onRefreshList()
})
// end

onMounted(() => {
    onLoad()
})
</script>
<style lang="scss" scoped>
.hand-audit {
    background: url("@/assets/my/office/list-bg.png"),#F6F7F8;
    background-size: 100% auto;
    background-repeat: no-repeat;
    background-position: bottom center;

    .title-bg {
        background: url("@/assets/my/office/handAudit/hand_audit.jpg") no-repeat;
        background-size: 100% 100%;
    }

    :deep(.van-tabs__nav) {
        background: transparent;
    }

    :deep(.van-tab__text) {
        font-size: 28px;
    }

    :deep(.van-cell-group--inset) {
        margin: 0 !important;
    }

    .dialog-activity {

        /* 活动主页 tab切换 下拉的弹窗  */
        .van-popup {
            position: absolute;
            top: 50px;
            left: 0;
            right: 0;
            background-color: #fff;
            z-index: 1000;
        }

        .van-overlay {
            background: rgba($color: #000000, $alpha: 0.3);
            top: 50px;
            bottom: 0;
            left: 0;
            right: 0;
            position: absolute;
        }

        .dropdown_menu_box {
            border-radius: 0px 0px 12px 12px;

            .dropdown_menu_item {}
        }

        :deep(.van-checkbox) {
            transform: scale(0.8);
        }

    }

    .my-views {
        min-height: calc(100vh - 260px);
    }
}
</style>