<template>
    <div class="pay-success w-full min-h-100vh pt-120px box-border">
        <div class="pay-success__img w-full text-center">
            <img loading="lazy" src="@/assets/public/pay_success.png" class="w-60%" alt="" />
        </div>
        <div class="pay-success-text text-center">
            <div class="text-30px text-#333">支付成功</div>
            <div class="text-28px text-#838383 mt-10px">可在积分商城“兑换记录”中查看</div>
        </div>
        <div class="pay-success-btn flex justify-between text-#FF4344 text-30px px-80px mt-70px">
            <div class="h-82px rounded-40px text-center leading-82px px-54px" @click="toDetail">
                {{
                    route.query?.integralPayment === '1' || used ? '查看订单详情' : '查看核销码'
                }}
            </div>
            <div class="h-82px rounded-40px text-center leading-82px px-54px" @click="toMall">回商城逛逛</div>
        </div>
        <codePopup v-model:show="qrCodeShow" :url="qrCodeUrl"></codePopup>
    </div>
</template>
<script setup lang="ts">
import codePopup from '@/views/integralMall/components/codePopup.vue';
// 目前积分商城之后接入普惠商城
const router = useRouter()
const route = useRoute()
import { getIntegralGoodsQrCode } from '@/api/mall/integral'
import { closeToast, showLoadingToast, showToast } from 'vant';
const qrCodeShow = ref(false)
const qrCodeUrl = ref('')
let btnClick = false
const toDetail = async () => {
    if (route.query?.integralPayment === '1') router.replace({
        path: '/integralMall/order/detail',
        query: {
            recordId: route.query?.recordId,
        },
    })
    else {
        if (used.value) {
            router.replace({ path: '/integralMall/order/writeOff', query: { recordId: route.query?.recordId } })
            return
        }

        if (btnClick) return;
        btnClick = true
        showLoadingToast({
            message: '获取核销码...',
            forbidClick: true
        })
        // const { code, data, message } = await getIntegralGoodsQrCode(route.query?.recordId as string)
        // if (code === 200) {
        //     qrCodeUrl.value = data
        //     qrCodeShow.value = true
        //     qrCodeShowClose()
        // }
        // else {
        //     showToast({
        //         message,
        //         icon: 'none'
        //     })
        // }
        await qrCodeShowClose()
        btnClick = false
        closeToast()
    }
}
const timer = ref<any>(null)
const used = ref(false)
// 获取核销码状态
// 隐藏关闭
watch(() => qrCodeShow.value, (val) => {
    if (!val) {
        // clearInterval(timer.value)
        isPolling = false;
        clearTimeout(timer.value)
    }
})
let isPolling = false;
let maxWaitTime = 5 * 60 * 1000; //最大等待时间（毫秒）
let waitTime = 0; //当前等待时间（毫秒）
const pollInterval = 10000; //轮询间隔时间（毫秒）
const qrCodeShowClose = async () => {
    // if (timer.value) clearInterval(timer.value)
    // timer.value = setInterval(
    //     async () => {
    //         if (isPolling) return;
    //         isPolling = true;
    //         try {
    //             const { code, data } = await getIntegralGoodsQrCode(route.query?.recordId as string)
    //             if (!data) {
    //                 qrCodeShow.value = false
    //                 qrCodeUrl.value = ''
    //                 clearInterval(timer.value)
    //                 used.value = true // 已使用
    //             }
    //         }
    //         catch (error) {
    //             clearInterval(timer.value)
    //         }
    //         finally {
    //             isPolling = false;
    //         }
    //     }, 5000)

    if (isPolling) return;
    isPolling = true;
    let isWait = true//是否开始轮询
    try {
        const { code, data, message } = await getIntegralGoodsQrCode(route.query?.recordId as string)
        if (code === 200) {
            qrCodeUrl.value = data
            qrCodeShow.value = true
        }
        else if (!data) {
            showToast({
                message,
                icon: 'none'
            })
            qrCodeShow.value = false
            qrCodeUrl.value = ''
            isWait = false//停止轮询
            used.value = true // 已使用
        }
    }
    catch (error) {
        isWait = false//停止轮询
    }
    finally {
        clearTimeout(timer.value)
        if (isWait && waitTime < maxWaitTime) {
            timer.value = setTimeout(() => {
                waitTime += pollInterval; //每次轮询增加等待时间
                qrCodeShowClose()
            }, pollInterval)
        } else {
            waitTime = 0;
        }
        isPolling = false;
    }
}
// 回商城
const toMall = () => {
    if (route.query?.payType === 'integral') {
        router.go(-Number(route.query?.index))
        if (route.query?.exchangeSource === '1') router.replace({ path: '/integralMall' })
        else if (route.query?.exchangeSource === '2') router.replace({ path: '/integralMall/birthdayGoods' })
    }
}
</script>
<style lang="scss" scoped>
.pay-success-btn div {
    border: 2px solid #FF4344;
}
</style>