<template>
    <div class="creatTopicActivity">
        <van-tabs v-model:active="Data.tab.active" swipeable title-active-color="#5AA4FF" @change="chooseTab">
            <van-tab v-for="(item, index) in Data.tab.nav" :title="item.name" class="text-34px" :key="index">
            </van-tab>
        </van-tabs>
        <div class="p-30px">
            <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
                <interestActList :data="Data.list" />
            </refreshList>

        </div>

    </div>
</template>
<script lang="ts" setup>
import interestActList from '@/components/List/interestActList.vue';
import { getDetaicurrentUserActivityListls } from '@/api/activity';
import refreshList from '@/components/refreshList/index.vue';
const Data = ref({
    tab: { active: 0, nav: [{ name: '全部' }, { name: '未开始', code: '1' }, { name: '进行中', code: '2' }, { name: '已结束', code: '3' }] },//活动状态 1：未开始; 2：进行中; 3：已结束;
    list: [
        // {title:'南充本部高坪健身队夜徒公告',group:'2024-11-19 08:00 至 2024-11-20 23:00',join:15,total:40,status:'end'},
        // {title:'南充本部高坪健身队夜徒公告',group:'2024-11-19 08:00 至 2024-11-20 23:00',join:25,total:80,status:'open'},
        // {title:'南充本部高坪健身队夜徒公告',group:'2024-11-19 08:00 至 2024-11-20 23:00',join:40,total:100,status:'wait'},
    ],
    pageNum: 1
})
//动态列表
const loadMoreRef = ref(null);
function getList() {
    getDetaicurrentUserActivityListls({
        activityCategory: 'interestGroup',
        pageSize: 10,
        pageNum: 1,
        progress: Data.value?.tab?.nav[Data.value.tab?.active]?.code,
    }).then(res => {
        if (res.code == 200) {
            if (Data.value.pageNum === 1) Data.value.list = [];
            Data.value.list = Data.value.list.concat(res.data);
            //重置刷新状态及 判断是否加载完成
            if (loadMoreRef.value) {
                loadMoreRef.value.onLoadSuc(Data.value.list.length, res.total);
            }
        }
    })
}
// 刷新
const onRefreshList = () => {
    Data.value.pageNum = 1;
    getList();
};
// 加载更多
const onLoadMore = () => {
    Data.value.pageNum++;
    getList();
};
function chooseTab(val) {
    onRefreshList()
}
onMounted(() => {
    getList()
})
</script>
<style lang="scss" scoped>
.creatTopicActivity {
    background: #F6F7F8;
    min-height: 100vh;

    :deep(.van-tab) {
        font-size: 32px;
        line-height: 1;
    }

    :deep(.van-tabs__wrap) {
        height: 78px;
    }
}
</style>