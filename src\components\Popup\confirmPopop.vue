<template>
    <van-popup v-model:show="props.show" position="center" round @click-overlay="cancel"
        class="popup box-border pt-30px pb-70px px-30px flex flex-col items-center ">
        <div class="tip text-#333 text-32px my-60px">{{ props.tips }}</div>
        <div class="handle_btn flex justify-center w-full px-30px">
            <div class="cancel_btn bg-[#EBEBEB] w-213px h-71px rounded-35px
            text-center leading-71px text-[#666] text-30px mr-30px" v-if="props.showCancel" @click="cancel">{{
                props.cancelText
                }}</div>
            <div class="comfirm_btn text-center leading-71px
            text-[#fff] text-30px w-213px h-71px rounded-35px" :style="{ backgroundColor: props.confirmColor }"
                v-if="props.showConfirm" @click="confirm">{{
                    props.confirmText }}</div>
        </div>
    </van-popup>
</template>
<script lang="ts" setup>
const props = defineProps({
    show: {
        type: Boolean,
        default: false
    },
    tips: {
        type: String,
        default: '确认执行吗？',
        required: true
    },
    showCancel: {
        type: Boolean,
        default: true,
    },
    showConfirm: {
        type: Boolean,
        default: true,
    },
    cancelText: {
        type: String,
        default: '取消',
    },
    confirmText: {
        type: String,
        default: '确认',
    },
    confirmColor: {
        type: String,
        default: '#7EB2FF',
    },
})
const emit = defineEmits(['update:show', 'cancel', 'confirm'])
const cancel = () => {
    emit('update:show', false)
    emit('cancel')
}
const confirm = () => {
    emit('confirm')
}
</script>
<style lang="scss" scoped>
.popup {
    width: calc(100% - 150px);
}
</style>