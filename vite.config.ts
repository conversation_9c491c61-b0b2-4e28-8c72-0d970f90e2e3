import { defineConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path'; // 引入方法
// 自动导入vue中hook reactive ref等
import AutoImport from 'unplugin-auto-import/vite';
import viteCompression from 'vite-plugin-compression';
import postcssPxToViewport from 'postcss-px-to-viewport';
import VueSetupExtend from 'vite-plugin-vue-setup-extend';
import UnoCSS from 'unocss/vite';
// import legacySWC from 'vite-plugin-legacy-swc';
import legacy from '@vitejs/plugin-legacy';
export default defineConfig(({ command, mode }) => {
  // 根据当前工作目录中的 `模式` 加载 .env 文件
  // 设置第三个参数为 '' 来加载所有环境变量，而不管是否有 `VITE_` 前缀。
  // 设置第三个参数为 'VITE_' 表示加载'VITE_'开头的环境变量
  const env = loadEnv(mode, process.cwd(), 'VITE_');
  console.log(env);
  const port: number = (env.VITE_APP_PORT as any) || 80;
  return {
    // 应用访问路径 例如使用前缀 /admin/
    base: '/', //env.VITE_APP_CONTEXT_PATH
    plugins: [
      vue(),
      viteCompression({
        threshold: 512, // 分割大小限制
      }),
      VueSetupExtend(),
      AutoImport({
        // 安装两行后你会发现在组件中不用再导入ref，reactive等
        imports: ['vue', 'vue-router'],
        // 存放的位置
        dts: 'src/auto-import.d.ts',
      }),
      UnoCSS(),
      // legacySWC(),
      legacy({
        targets: ['> 0.2%', 'not dead'],
        additionalLegacyPolyfills: [
          'core-js/stable',
          'regenerator-runtime/runtime',
        ], // 兼容 async/await
      }),
    ],
    // 配置别名
    resolve: {
      // https://cn.vitejs.dev/config/shared-options.html#resolve-alias
      alias: {
        // "~": path.resolve(__dirname, "./"), // ~代替./
        '@': resolve(__dirname, 'src'),
        '#': resolve(__dirname, 'types'),
      },
    },
    // https://cn.vitejs.dev/config/server-options
    server: {
      // 将此设置为 0.0.0.0 或者 true 将监听所有地址，包括局域网和公网地址
      host: true,
      // 配置启动并端口号
      // 注意：如果端口已经被使用，Vite 会自动尝试下一个可用的端口，所以这可能不是开发服务器最终监听的实际端口
      port: 8080,
      // 服务启动时是否自动打开浏览器
      open: true,
      // 代理配置
      proxy: {
        [env.VITE_WX_API]:{
           target: 'https://ncapp.nczgh.com/wxUrl',
           // 允许跨域,可以代理反向的地址
           changeOrigin: true,
           crossDomain: true,
           rewrite: path => path.replace(new RegExp('^' + env.VITE_WX_API), ''),
           bypass(req, res, options) {
            const proxyURL = options.target + options.rewrite(req.url);
            console.log(proxyURL, 'VITE_WX_API');
            res.setHeader('x-req-proxyURL', proxyURL); // 将真实请求地址设置到响应头中
           }
        },
        [env.VITE_BASE_ALI_API]:{
          target: 'https://ncadmin.nczgh.com/api-ali/',//http://192.168.101.23:23005
           // 允许跨域,可以代理反向的地址
           changeOrigin: true,
           crossDomain: true,
           rewrite: path => path.replace(new RegExp('^' + env.VITE_BASE_ALI_API), ''),
           bypass(req, res, options) {
            const proxyURL = options.target + options.rewrite(req.url);
            console.log(proxyURL, 'VITE_ALI_API');
            res.setHeader('x-req-proxyURL', proxyURL); // 将真实请求地址设置到响应头中
           }
        },
        [env.VITE_BASE_API]: {
          // 要代理的服务器地址 84
          // target: 'http://192.168.101.157:28000',
          target: 'http://192.168.101.50:28000',
          // 允许跨域,可以代理反向的地址
          changeOrigin: true,
          // 将代理前缀替换为空
          // 如：http://xxx.com/dev-api/login  替换成 -> http://xxx.com/login
          rewrite: path => path.replace(new RegExp('^' + env.VITE_BASE_API), ''),
          bypass(req, res, options) {
            const proxyURL = options.target + options.rewrite(req.url);
            console.log(proxyURL, 'proxyURL');
            res.setHeader('x-req-proxyURL', proxyURL); // 将真实请求地址设置到响应头中
          },
        }
      },
    },
    css: {
      // 适配移动端
      postcss: {
        plugins: [
          postcssPxToViewport({
            viewportWidth: 750, // 设计稿宽度，根据你的设计稿调整
            viewportUnit: 'vw', // 转换成的视窗单位
            unitPrecision: 5, // 转换后的精度
            propList: ['*'], // 指定需要转换的属性的单位，*代表所有
            viewportHeight: 1334, // 视窗的高度，如果需要的话
            fontViewportUnit: 'vw', // 字体使用的视窗单位
            minPixelValue: 1, // 小于或等于1px则不进行转换
            mediaQuery: false, // 是否在媒体查询的css代码中也进行转换
            replace: true, // 是否直接更换属性值
            exclude: /(\/|\\)(node_modules)(\/|\\)/, // 设置忽略文件
          }),
        ],
      },
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler',
        },
      },
    },
    build: {
      outDir: 'ncapp',
      chunkSizeWarningLimit: 1000,//规定触发警告的 chunk 大小
      rollupOptions: {
        output: {
          assetFileNames: assetInfo => {
            let info = assetInfo.name.split('.');
            let extType = info[info.length - 1];
            if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)) {
              extType = 'media';
            } else if (/\.(png|jpe?g|gif|svg)(\?.*)?$/.test(assetInfo.name)) {
              extType = 'img';
            } else if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name)) {
              extType = 'fonts';
            }
            return `${extType}/[name]-[hash][extname]`;
          },
          chunkFileNames: 'js/[name]-[hash].js',
          entryFileNames: 'js/[name]-[hash].js',
          // 最小化拆分包
          manualChunks(id) {
            if (id.includes('node_modules')) {
              return id.toString().split('node_modules/')[1].split('/')[0].toString();
            }
          },
        },
      },
      // 解决低版本浏览器白屏问题
      target: ['edge90', 'chrome90', 'firefox90', 'safari15', 'es2015'],
    },
    minify: 'terser',
    terserOptions: {
      //打包后移除console和注释
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
  };
});
