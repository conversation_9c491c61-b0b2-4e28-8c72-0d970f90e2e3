<template>
  <div class="pb-20px">
    <van-form ref="formRef" @submit="submit">
      <van-cell-group>
        <template v-for="(item, index) in formPrams" :key="item.key">
          <van-field
            :required="item.require"
            v-model="Data.formatData.introduce"
            :label="item.name"
            :rules="
              item.require
                ? [{ required: true, message: item.placeholder }]
                : []
            "
            input-align="right"
            error-message-align="right"
            label-width="30%"
            :placeholder="item.placeholder"
            v-if="item.itemType === 'input'"
          ></van-field>
          <div v-else-if="item.itemType === 'switch'">
            <van-field
              name="radio"
              :required="item.require"
              :label="item.name"
              input-align="right"
              error-message-align="right"
            >
              <template #input>
                <div class="flex flex-col">
                  <van-radio-group  v-model="item.value" direction="horizontal">
                    <van-radio
                      :name="e.dictCode"
                      icon-size="16px"
                      v-for="(e, i) in item.selArr"
                      :key="i"
                      >{{ e.dictName }}</van-radio
                    >
                  </van-radio-group>
                  <!-- 工作单位 -->
                  <div v-if="item.key === 'workUnit'">
                    <van-field
                      :required="item.require"
                      :rules="
                        item.require
                          ? [{ required: true, message: item.placeholder }]
                          : []
                      "
                      input-align="right"
                      error-message-align="right"
                      label-width="30%"
                      :placeholder="item.placeholder"
                    ></van-field>
                  </div>
                </div>
              </template>
            </van-field>
          </div>
          <van-field
            readonly
            clickable
            v-else-if="item.itemType === 'date'"
            name="picker"
            v-model="Data.formatData.applyTimeStart"
            :label="item.name"
            label-width="30%"
            :placeholder="item.placeholder"
            @click="showcPicker((state = 4), 'start')"
            input-align="right"
            :rules="
              item.require
                ? [{ required: true, message: item.placeholder }]
                : []
            "
            error-message-align="right"
            is-link
          />
          <van-field
            v-else-if="item.itemType === 'picker'"
            :label="item.name"
            @click="showPickerFn(item.selArr, item.key)"
            v-model="Data.formatData[item.key]"
            :required="item.require"
            :rules="
              item.require
                ? [{ required: true, message: item.placeholder }]
                : []
            "
            label-width="30%"
            :placeholder="item.placeholder"
            right-icon="arrow"
            readonly
            input-align="right"
            error-message-align="right"
          ></van-field>
        </template>

        <!-- <van-field required label="选择小组" @click="showPickerFn(Data.IndustryList, 'Industry')"
                v-model="Data.formatData.IndustryName" :rules="[{ required: true, message: '请选择' }]"
                label-width="25%" placeholder="请选择" right-icon="arrow" readonly input-align="right" error-message-align="right"></van-field>
                <van-field  label="选择话题" @click="showPickerFn(Data.IndustryList, 'Industry')"
                v-model="Data.formatData.IndustryName" :rules="[{ required: true, message: '请选择' }]"
                label-width="25%" placeholder="请选择" right-icon="arrow" readonly input-align="right" error-message-align="right"></van-field> -->
        <!-- <div class="textarea">
                    <van-field required v-model="Data.formatData.introduce" label="动态内容"  type="textarea"
                        :rules="[{ required: true, message: '请输入动态内容' }]" label-width="100%"
                        placeholder="请输入动态内容"></van-field>
                </div>
                <div class="p-28px">
                    <div class="text-28px text-#333 mb-20px"><span class="text-#CC3333 ">*</span>活动图片</div>
                    <van-uploader v-model="Data.imgList" reupload max-count="2" />
                    <div class="text-20px text-#333 mt-40px mb-20px">*请上传JPG/PNG/JPEG格式的图片，最多上传10张</div>
                    <div class="text-20px text-#333">*禁止上传手机截图、网络照片等无关图片，违者，停止使用相册功能</div>
                </div> -->
      </van-cell-group>
      <!-- <div
        class="btn w-65/100 mt-40px m-auto text-34px text-#fff py-23px text-center"
      >
        提交
      </div> -->
      <Button name="提交" class=" mt-80px m-auto w-450px h-78px" @click="onSubmit"/>
    </van-form>
    <van-popup v-model:show="Data.showPicker" position="bottom">
      <van-picker
        :columns="Data.columns"
        @confirm="onConfirm"
        @cancel="Data.showPicker = false"
      />
    </van-popup>
    <van-popup v-model:show="Data.showTime" position="bottom">
      <van-date-picker
        @confirm="onConfirm"
        @cancel="Data.showTime = false"
        type="date"
      />
    </van-popup>
    <TipPopup :showPop="Data.showPop" :icon="tjcg" tip="个人信息提交成功" btnTitle="返回" @closePop="Data.showPop=false"  />
  </div>
</template>
<script lang="ts" setup>
import formPrams from "./formParams.ts";
import Button from '@/components/Button/button.vue'
import tjcg from '@/assets/vitality/personalInfo/tjcg.png'
import TipPopup from "@/components/Popup/tipPopup.vue";

const Data = ref({
  formList: [],
  formatData: {},
  columns: [],
  showPicker: false,
  type: "",
  IndustryList: [],
  imgList: [],
  showTime: false,
  showPop:false
});
onMounted(() => {
  //   Data.value.formList = [...formPrams];
  //   console.log(Data.value.formList, 333);
  formPrams.forEach((e:any) => {
    Data.value.formatData[e.key] = e.value;
  });
});
const showcPicker = () => {
  Data.value.showTime = true;

};

//选择
const showPickerFn = (list:any, type:string) => {
  Data.value.columns = list;
  Data.value.showPicker = true;
  Data.value.type = type;
};
const onConfirm = ({ selectedOptions }) => {
  
  Data.value.showPicker = false;
};
//提交
function onSubmit() {
    Data.value.showPop = true
}
</script>
<style lang="scss">
.textarea {
  .van-field__control {
    background: #f6f7f8;
    border-radius: 8px;
    padding: 20px;
  }
  .van-field__body {
    margin-top: 15px;
  }
}
.van-radio-group{
    justify-content: flex-end !important;
}
.btn {
  background: url("@/assets/public/butto.png") no-repeat;
  background-size: 100% 100%;
}
</style>