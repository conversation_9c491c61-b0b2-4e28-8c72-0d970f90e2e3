<template>
  <div class="echart-box" style="width:100%;height:100%">
    <div ref="echartRef" class="w-full h-full"></div>
  </div>
</template>

<script lang="ts" setup>
// 饼图
import { useECharts } from '@/utils/useECharts'
import { Ref, ref, watch, onMounted } from 'vue'
import * as echarts from 'echarts'
import { getVwSize } from '../../data'
const props = defineProps({
  dataSource: {
    type: Object,
    default: {
      columnList: [
        '顺庆区',
        '高坪区',
        '嘉陵区',
        '阆中市',
        '南部县',
        '西充县',
        '仪陇县',
        '营山县',
        '蓬安县',
      ],
      registerUserList: [],
      unionUserList: [],
      readCountList: [],

    },
  },
})

const echartRef = ref<HTMLDivElement | null>(null)
const { setOptions } = useECharts(echartRef as Ref<HTMLDivElement>)

const initChart = () => {
  let data = { x: [], y1: <any[]>[], y2: <any[]>[], y3: <any[]>[] }
  data.x = props.dataSource?.columnList
  let maxNumReg = 0, maxNumUnion = 0, maxNumRead = 0
  let isFormated = false
  if (props.dataSource?.registerUserList) {
    maxNumReg = Math.max(...props.dataSource?.registerUserList)
    data.y1 = props.dataSource?.registerUserList
  }
  if (props.dataSource?.unionUserList) {
    maxNumUnion = Math.max(...props.dataSource?.unionUserList)
    data.y2 = props.dataSource?.unionUserList
  }
  if (props.dataSource?.readCountList) {
    maxNumRead = Math.max(...props.dataSource?.readCountList)
    data.y3 = props.dataSource?.readCountList
  }
  if ([maxNumReg, maxNumUnion].some(item => item >= 10000)) {
    isFormated = true
    data.y1 = data.y1.map((item: any) => parseFloat((item / 10000).toFixed(2)))
    data.y2 = data.y2.map((item: any) => parseFloat((item / 10000).toFixed(2)))
  }
  if (maxNumRead >= 10000) {
    data.y3 = data.y3.map((item: any) => parseFloat((item / 10000).toFixed(2)))
  }

  // 绘制图表
  var option = {
    tooltip: {
      show: true,
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      textStyle: {
        fontSize: getVwSize(20),
      },
      formatter: function (params: any) {
        let result = ''
        params.forEach((item: any) => {
          let value = item.value
          if (item.seriesIndex === 0) value = props.dataSource?.registerUserList[item.dataIndex]
          else if (item.seriesIndex === 1) value = props.dataSource?.unionUserList[item.dataIndex]
          else if (item.seriesIndex === 2) value = props.dataSource?.readCountList[item.dataIndex]
          result += item.marker + item.seriesName + ': ' + value + `<br/>`
        })
        return result
      },
    },
    grid: {
      top: '20%',
      left: '0%',
      right: '0%',
      bottom: '0%',
      containLabel: true,
    },
    legend: {
      show: true,
      icon: 'square',
      orient: 'horizontal',
      top: '0%',
      right: '0%',
      itemWidth: getVwSize(20),
      itemHeight: getVwSize(20),
      itemGap: getVwSize(60),
      textStyle: {
        color: '#666',
        fontSize: getVwSize(20),
      },
    },
    xAxis: [
      {
        type: 'category',
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitArea: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        boundaryGap: true,
        axisLabel: {
          show: true,
          interval: 0,
          rotate: 30,
          fontSize: getVwSize(20),
          lineHeight: getVwSize(30),
          margin: getVwSize(20),
          color: '#999',
        },
        data: data.x,
      },
    ],
    yAxis: [
      {
        name: `访问量${maxNumRead >= 10000 ? '(万人)' : '(人)'}`,
        nameTextStyle: {
          color: '#999',
          fontSize: getVwSize(20),
          padding: [0, 0, 0, getVwSize(50)]
        },
        type: 'value',
        min: 0,
        minInterval: 1,
        splitLine: {
          show: true,
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#E6E6E6',
          },
        },
        axisLabel: {
          textStyle: {
            color: '#999',
            fontSize: getVwSize(20)
          },
        },
        axisTick: {
          show: false,
        },
      },
      {
        name: `参与量${isFormated ? '(万人)' : '(人)'}`,
        nameTextStyle: {
          color: '#999',
          fontSize: getVwSize(20),
          padding: [0, getVwSize(50), 0, 0]
        },
        type: 'value',
        min: 0,
        minInterval: 1,
        splitLine: {
          show: true,
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#E6E6E6',
          },
        },
        axisLabel: {
          textStyle: {
            color: '#999',
            fontSize: getVwSize(20)
          },
        },
        axisTick: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: '注册会员',
        type: 'bar',
        barWidth: getVwSize(20),
        yAxisIndex: 1,
        label: {
          show: false,
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [{
            offset: 0,
            color: 'rgba(55, 131, 250, 1)'

          },
          {
            offset: 1,
            color: 'rgba(66, 201, 250, 1)'
          }
          ]),
          borderRadius: getVwSize(16)
        },
        data: data.y1,
      },
      {
        name: '工会会员',
        type: 'bar',
        barWidth: getVwSize(20),
        yAxisIndex: 1,
        label: {
          show: false,
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [{
            offset: 0,
            color: '#92E4DC'
          },
          {
            offset: 1,
            color: '#0AABB3'
          }
          ]),
          borderRadius: getVwSize(16)
        },
        data: data.y2,
      },
      {
        name: '访问量',
        type: 'line',
        symbol: 'circle',
        z: 1,
        label: {
          show: false,
        },
        lineStyle: {
          width: getVwSize(4),
          shadowColor: 'rgba(66, 163, 255, 0.1)',
          shadowBlur: getVwSize(6),
          shadowOffsetY: getVwSize(6)
        },
        itemStyle: {
          normal: {
            color: '#42A3FF',
          },
        },
        yAxisIndex: 0,
        data: data.y3,
      },
    ],
  }

  setOptions(option)
}

onMounted(() => {
  initChart()
})

// 监听dataSource
watch(
  () => props.dataSource,
  (newValue, oldValue) => {
    initChart()
  },
  { deep: true, immediate: true }
)
</script>
