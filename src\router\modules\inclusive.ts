export default [
    {
        path: "/inclusive",
        name: "inclusive",
        component: () => import("@/views/inclusive/index.vue"),
        meta: {
          title: "普惠",
          isShowTabBar: true,
          isBack:false,
          keepAlive:true,
          updatePath:['/','/activity','/home','/inclusive','/county','/countyDetail','/my']
        }
    },
    {
        path: "/inclusiveActivity",
        name: "inclusiveActivity",
        component: () => import("@/views/inclusive/inclusiveActivity/index.vue"),
        meta: {
          title: "普惠活动",
          isShowTabBar: false,
          isBack:true,
          keepAlive:true,
          updatePath:['/inclusive','/county','/countyDetail']
        }
    },
    // 购物车
    {
        path: "/shoppingCart",
        name: "shoppingCart",
        component: () => import("@/views/inclusive/shop/shoppingCart.vue"),
        meta: {
          title: "购物车",
          isShowTabBar: false,
          isBack:true,
          keepAlive:false,
        }
    },
    // 商城列表
    {
        path: "/inclusive/goodsList",
        name: "inclusiveGoodsList",
        component: () => import("@/views/inclusive/shop/list.vue"),
        meta: {
          title: "商城列表",
          isShowTabBar: false,
          isBack:true,
          keepAlive:false,
        }
    },
    {
      path: "/inclusive/merchant",
      name: "inclusiveMerchant",
      component: () => import("@/views/inclusive/shop/merchant.vue"),
      meta: {
        title: "商家主页",
        isShowTabBar: false,
        isBack:true,
        keepAlive:false,
      }
    },
    // 商品评分
    {
      path: "/inclusive/rating",
      name: "inclusiveRating",
      component: () => import("@/views/inclusive/shop/rating.vue"),
      meta: {
        title: "商品评分",
        isShowTabBar: false,
        isBack:true,
        keepAlive:false,
      }
    },
    {
      path: "/inclusive/goodsDetail",
      name: "inclusiveGoodsDetail",
      component: () => import("@/views/inclusive/shop/goodsDetail.vue"),
      meta: {
        title: "商品详情",
        isShowTabBar: false,
        isBack:true,
        keepAlive:false,
      }
    },
    {
      path: "/inclusive/create",
      name: "inclusivePay",
      component: () => import("@/views/inclusive/order/create.vue"),
      meta: {
        title: "创建订单",
        isShowTabBar: false,
        isBack:true,
        keepAlive:false,
      }
    }
]