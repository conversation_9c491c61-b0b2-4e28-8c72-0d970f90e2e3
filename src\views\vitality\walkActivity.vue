<template>
  <div class="bg-[#f5f5f5] min-h-100vh w-100vw flex flex-col">
    <img loading="lazy" :src="bannner" alt="" class="w-100% h-253px" />

    <div class="bg-#fff flex-1 p-30px box-border">
      <div class="tab-box mb-36px">
        <van-tabs v-model:active="Data.tab.active" sticky type="card" color="#F2F2F2" title-inactive-color="#666666"
          title-active-color="#5AA4FF" line-width="30" @click-tab="onClickTab">
          <van-tab :title="item.name" :name="item.code" v-for="(item, index) in Data.tab.nav" :key="index"
            title-class="tab-title"></van-tab>
        </van-tabs>
      </div>
      <refreshList @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
        <activityList :data="Data.list" @toDetails="toDetails">
          <template #subContent="{ item }">
            <div class="text-24px text-#999 mb-30px">
              <div class="mb-10px">
                活动时间：{{ dayjs(item?.activityStartTime).format('YYYY-MM-DD HH:MM') }} 至
                {{ dayjs(item?.activityEndTime).format('YYYY-MM-DD HH:MM') }}
              </div>
              <div class="flex items-center">
                <div class="flex-1">主办单位：{{ item?.companyName }}</div>
                <div class="text-#5088F0 flex items-center">
                  <van-icon name="eye-o" color="#5088F0" size="16" class="mr-7px" />{{ item?.readCount }}
                </div>
              </div>
            </div>
          </template>
          <template #status="{ item }">
            <div class="text-24px text-#fff px-15px py-8px box-border rounded-ss-16px rounded-br-16px"
              :style="{ background: dealStatus(item?.progress, 'bgColor') }">
              {{ dealStatus(item?.progress, "text") }}
            </div>
          </template>
        </activityList>
      </refreshList>
    </div>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs'
import bannner from "@/assets/vitality/jbz_default_img.jpg";
import activityList from "@/components/activity/list.vue";
import refreshList from '@/components/refreshList/index.vue';
import { activityInfoList } from "@/api/activity";
import { useUserStore } from "@/store/modules/user";
import { openMiniProgram, isApp } from "@/utils/utils";
import { getDetails } from '@/api/activity.ts';
const useStore = useUserStore();
import router from "@/router";
const Data = ref({
  tab: {
    active: '',
    nav: [
      { name: "全部", code: "" },
      {
        name: "进行中",
        code: "2",
        bgColor: "linear-gradient(90deg, #FD633F, #FE952E)",
      },
      {
        name: "未开始",
        code: "1",
        bgColor: "linear-gradient(90deg, #2FB095, #55D1AC)",
      },
      {
        name: "已结束",
        code: "3",
        bgColor: "linear-gradient(90deg, #999999, #CCCCCC)",
      },
    ],
  },

  list: [
  ],
  pageNum: 1,
});
// loadMoreRef
const loadMoreRef = ref(null);
onMounted(() => {
  getActList();
});
/**
 * 点击状态标签页的处理函数
 *
 * @param item 被点击的标签页对象
 */
function onClickTab(item: any) {
  Data.value.tab.active = item.name;
  onRefreshList();
}
function dealStatus(status: string, type: string) {
  if (type === "text")
    return Data.value.tab.nav.find((el: any) => el.code === status)?.name;
  if (type === "bgColor")
    return Data.value.tab.nav.find((el: any) => el.code === status)?.bgColor;
}
function toJump(path: string) {
  if (!path) return;
  router.push({ path });
}
function toDetails(item: any) {
  // 对接跳转小程序
  if (item.activityCategory === 'walk' && isApp()) {
    getDetails({ activityId: item.activityId })//用于记录浏览
    const path = `/pages/guidance/guidance?exActiviId=${item.activityId}&token=${useStore.getSaToken}`
    openMiniProgram('h5applet', 'gh_193b1d6dda72', '云上职工健步走', path)
  } else {
    router.push({
      path: "/activityHome/activityDetail",
      query: {
        activityId: item.activityId,
      }
    })
  }
}

// 刷新
const onRefreshList = () => {
  Data.value.pageNum = 1;
  Data.value.list = [];
  getActList();
};
// 加载更多
const onLoadMore = () => {
  Data.value.pageNum++;
  getActList();
};
async function getActList() {
  let res = await activityInfoList({
    activityCategory: "walk",
    pageSize: 10,
    pageNum: Data.value.pageNum,
    progress: Data.value.tab.active || ''
  });
  if (Data.value.pageNum === 1) Data.value.list = [];
  Data.value.list = Data.value.list.concat(res.data);
  //重置刷新状态及 判断是否加载完成
  if (loadMoreRef.value) {
    loadMoreRef.value.onLoadSuc(Data.value.list.length, res.total);
  }
}
</script>
<style lang="scss" scoped>
.tab-box {
  :deep(.van-tabs__nav--card) {
    border: none !important;
    margin: 0 !important;
  }

  :deep(.van-tab--card) {
    border-right: none !important;
    border-radius: 44px;
    background-color: #f2f2f2;
    color: #666;
    margin-right: 44px;
  }

  :deep(.van-tab--card):last-child {
    margin-right: 0;
  }

  :deep(.van-tab--active) {
    background-color: #f6faff !important;
    border: 1px solid #5aa4ff !important;
    font-weight: 500;
    font-size: 28px;
    color: #5aa4ff;
  }

  :deep(.tab-title) {
    font-weight: 400;
    font-size: 28px;
    color: #666;
  }
}
</style>