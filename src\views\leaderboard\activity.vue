<template>
    <div class="main">
        <img loading="lazy" class="banner" src="@/assets/leaderBoard/activity/banner.png" alt="" srcset="">
        <div class="box ">
            <div class="tab h-90px flex justify-around items-center" :class="{ 'tab-actived': tab.activityType }">
                <div class="tab-item1" :class="{ 'tab-color': !tab.activityType }" @click="changeType(0)">活动板块统计</div>
                <div class="tab-item1" :class="{ 'tab-color': tab.activityType }" @click="changeType(1)">单个活动统计</div>
            </div>
            <div v-show="!tab.activityType">
                <div class="tab-box">
                    <div class="text-22px text-#999 text-right mb-16px w-full">备注：年度选择年份</div>
                    <dateSelect :dateTypeArr="tab.list" :defaultType="tab.currentIndex"
                        v-model:default-time="tab.currentDate" :columnsType="tab.columnsType"
                        @changeType="changeDateType">
                    </dateSelect>
                </div>
                <div class="info ">
                    <div class="title flex items-center">
                        <img loading="lazy" class="icons mr-10px" src="@/assets/leaderBoard/icon_flag.png" alt=""
                            srcset="">
                        <span>活动数据统计</span>
                    </div>
                    <div class="content ph-activit mt-10px flex">
                        <div class="label">
                            <div class="text-[24px] text-[#666]">进行中(场)</div>
                            <div class="text-[35px] text-[#333] mt-10px">{{ totalObj?.afootCount || 0 }}</div>
                        </div>
                        <div class="label">
                            <div class="text-[24px] text-[#666]">发布量(场)</div>
                            <div class="text-[35px] text-[#333] mt-10px">{{ totalObj?.publishCount || 0 }}</div>
                        </div>
                        <div class="label">
                            <div class="text-[24px] text-[#666]">参与量{{ totalObj?.joinCount >= 10000 ? '(万人)' : '(人)' }}
                            </div>
                            <div class="text-[35px] text-[#333] mt-10px" v-formatNum="10000">{{ totalObj?.joinCount || 0
                            }}
                            </div>
                        </div>
                        <div class="label">
                            <div class="text-[24px] text-[#666]">访问量{{ totalObj?.joinCount >= 10000 ? '(万人)' : '(人)' }}
                            </div>
                            <div class="text-[35px] text-[#333] mt-10px" v-formatNum="10000">{{ totalObj?.readCount ||
                                0 }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="info ">
                    <div class="title flex items-center justify-between">
                        <div class="flex items-center">
                            <img loading="lazy" class="icons mr-10px" src="@/assets/leaderBoard/icon_pie.png" alt=""
                                srcset="">
                            <span>工会活动类型统计(场)</span>
                        </div>
                        <div class="select z-2 w-fit py-6px px-15px rounded-30px text-#6EAEFC"
                            @click="showPickerFn('region')">
                            <span class="mr-10px text-26px">{{ company?.companyName }}</span>
                            <van-icon name="arrow-down"></van-icon>
                        </div>
                    </div>
                    <div class="content w-full  h-330px mt-10px">
                        <PieAreaChart :dataSource="activityTypeEcharts" />
                    </div>
                </div>
                <div class="info ">
                    <div class="title flex items-center">
                        <img loading="lazy" class="icons mr-10px" src="@/assets/leaderBoard/icon_addr.png" alt=""
                            srcset="">
                        <span>区域会员行为统计</span>
                    </div>
                    <div class="content chart-box mt-10px">
                        <BarAreaChart :dataSource="memberBehaviorEcharts" />
                    </div>
                </div>
                <div class="info ">
                    <div class="title flex items-center">
                        <img loading="lazy" class="icons mr-10px" src="@/assets/leaderBoard/think/icon_user.png" alt=""
                            srcset="">
                        <span>会员行为趋势</span>
                    </div>
                    <div class="content chart-box mt-10px">
                        <unionTrendChart :dataSource="memberTrendEcharts" />
                    </div>
                </div>
                <!-- <div class="info ">
                    <div class="title flex items-center">
                        <img loading="lazy" class="icons mr-10px" src="@/assets/leaderBoard/icon_month.png" alt=""
                            srcset="">
                        <span>近半年统计用户参与情况</span>
                    </div>
                    <div class="content w-full h-500px mt-10px ">
                        <BarMonthChart />
                    </div>
                </div> -->
                <!-- <div class="info ">
                    <div class="title flex items-center">
                        <img loading="lazy" class="icons mr-10px" src="@/assets/leaderBoard/icon_hy.png" alt=""
                            srcset="">
                        <span>近半年统计数据分析</span>
                    </div>
                    <div class="content w-full h-500px mt-10px">
                        <LineYearChart />
                    </div>
                </div> -->
            </div>
            <div v-show="tab.activityType">
                <div class="tab-box1 flex justify-center items-center ">
                    <div class="tab-item !w-full !h-60px center" @click="showPickerFn('activity')">
                        {{ tab.activityName?.length > 20 ? tab.activityName?.slice(0, 20) + '...' : tab.activityName }}
                        <van-icon name="arrow-down" class="ml-5px" color="#76B7FB" />
                    </div>
                </div>
                <div class="info">
                    <div class="top mb-20px flex">
                        <div class="top-label ">
                            <img loading="lazy" class="w-40px" src="@/assets/leaderBoard/activity/icon_join.png" alt=""
                                srcset="">
                            <div class="text-[#666] text-[24px]">参与总人数：</div>
                            <div class="text-[#FAC858] text-[30px] font-blod">{{ totalObjSingle?.joinCount || 0 }}</div>
                        </div>
                        <div class="top-label bg1">
                            <img loading="lazy" class="w-40px" src="@/assets/leaderBoard/activity/icon_visit.png" alt=""
                                srcset="">
                            <div class="text-[#666] text-[24px]">访问总量：</div>
                            <div class="text-[#6FD4D9] text-[30px] font-blod">{{ totalObjSingle?.readCount || 0 }}</div>
                        </div>
                        <div class="top-label bg2">
                            <img loading="lazy" class="w-40px" src="@/assets/leaderBoard/activity/gift.png" alt=""
                                srcset="">
                            <div class="text-[#666] text-[24px]">总抽奖次数：</div>
                            <div class="text-[#4898FB] text-[30px] font-blod">{{ totalObjSingle?.lotteryCount || 0 }}
                            </div>
                        </div>
                    </div>
                    <div class="title flex items-center">
                        <img loading="lazy" class="icons mr-10px" src="@/assets/leaderBoard/icon_addr.png" alt=""
                            srcset="">
                        <span>区域会员参与情况</span>
                    </div>
                    <div class="content w-full h-500px mt-20px">
                        <AreaUserChart :dataSource="memberParticipationEcharts" />
                    </div>
                    <div class="title mt-20px flex items-center">
                        <img loading="lazy" class="icons mr-10px" src="@/assets/leaderBoard/icon_rl.png" alt=""
                            srcset="">
                        <span>活动每日访问情况</span>
                    </div>
                    <div class="content w-full h-600px mt-20px">
                        <LineDayChart :dataSource="activityVisitEcharts" />
                    </div>
                </div>
                <!-- 有奖品存在才展示 -->
                <div class="info" v-if="prizeInfo?.series?.length">
                    <div class="title flex items-center">
                        <img loading="lazy" class="icons mr-10px" src="@/assets/leaderBoard/icon_gift.png" alt=""
                            srcset="">
                        <span>活动奖品发放情况</span>
                    </div>
                    <div class="content w-full h-500px mt-30px">
                        <div class="select-box flex items-center relative z-10" @click="showPickerFn('prize')">
                            <div class="select-name">{{ currentPrizeChart?.series?.name ?
                                currentPrizeChart?.series?.name : '请选择奖品' }}</div>
                            <van-icon name="arrow-down" class="ml-5px" color="#76B7FB" />
                        </div>
                        <LineSingleChart :dataSource="currentPrizeChart" />
                    </div>
                </div>
                <div class="info">
                    <div class="title flex items-center">
                        <img loading="lazy" class="icons mr-10px" src="@/assets/leaderBoard/icon_gift.png" alt=""
                            srcset="">
                        <span>活动新增用户趋势</span>
                    </div>
                    <div class="content w-full h-500px mt-20px">
                        <LineUserAddChart :dataSource="newUserTrendEcharts" />
                    </div>
                </div>
            </div>
        </div>
        <img class="banner bottom" src="@/assets/leaderBoard/ph/bottom_bg.png" alt="" srcset="">
        <van-popup v-model:show="selectShow" position="bottom">
            <van-field v-model="searchName" placeholder="搜索关键词" v-show="selectType === 'activity'"
                class="search-input"></van-field>
            <van-picker title="选择" :columns="selectColumns" @cancel="selectShow = false" @confirm="onConfirmSelect" />
        </van-popup>
    </div>
</template>
<script setup lang="ts">
import { ref, unref } from 'vue'
import PieAreaChart from './components/charts/PieAreaChart.vue';
import BarAreaChart from './components/charts/BarAreaChart.vue';
import AreaUserChart from './components/charts/AreaUserChart.vue';
import BarMonthChart from './components/charts/BarMonthChart.vue';
import LineYearChart from './components/charts/LineYearChart.vue';
import LineDayChart from './components/charts/LineDayChart.vue';
import LineSingleChart from './components/charts/LineSingleChart.vue';
import LineUserAddChart from './components/charts/LineUserAddChart.vue';
import dateSelect from './components/dateSelect.vue'
import unionTrendChart from './components/charts/unionTrendChart.vue';
import {
    unionActivityInfo, unionActivityType,
    memberBehavior, memberBehaviorTrend, activityStaticData,
    activityPrizeInfo, activityNewUserTrend,
    activityMemberParticipation, activityEveryDayVisit
} from '@/api/leaderboard/activity'
import { activityInfoList } from '@/api/activity'

import { useDictionary } from '@/store/modules/dictionary';
const dictionaryStore = useDictionary()
import { useUserStore } from '@/store/modules/user';
const useStore = useUserStore();

const route = useRoute()
const currentMonth = new Date().getMonth() + 1 < 10 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1
const tab = ref({
    activityType: 0,//活动类型 0:活动，1：单个活动
    list: [
        { label: '年度', value: 'year' },
        { label: '月度', value: 'month' },
    ],
    columnsType: ['year', 'month'],
    currentIndex: 1,
    currentDate: [new Date().getFullYear(), currentMonth],
    activityId: '',
    activityName: '',
})
const changeDateType = (val: any) => {
    unref(tab).currentIndex = val
    if (val === 0) {
        unref(tab).columnsType = ['year']
        unref(tab).currentDate = [new Date().getFullYear()]
    }
    else {
        unref(tab).columnsType = ['year', 'month']
        unref(tab).currentDate = [new Date().getFullYear(), currentMonth]
    }
}


function changeType(value: number) {
    unref(tab).activityType = value;
}

const currentUserCode = computed(() => useStore.leaderCode)

// =====请求=====
// 活动数据统计
const totalObj = ref<any>({})
const activityStatistics = () => {
    unionActivityInfo({
        queryDate: unref(tab).currentDate.join('-'),
        companyId: currentUserCode.value,//当前用户的
        activityCategory: 'union'
    }).then(res => {
        if (res.code === 200 && res.data) totalObj.value = res.data
    })
}
// 活动类型
const activityTypeEcharts = ref<any>([])
const getUnionActivityType = () => {
    unionActivityType({
        queryDate: unref(tab).currentDate.join('-'),
        companyId: company.value.companyId,

    }).then(res => {
        if (res.code === 200 && res.data) {
            activityTypeEcharts.value = []
            Object.keys(res.data).forEach((t: any) => {
                activityTypeEcharts.value.push({
                    name: t,
                    value: res.data[t]
                })
            })
        }
    })
}
// 区域会员行为统计
const memberBehaviorEcharts = ref<any>([])
const getMemberBehavior = () => {
    memberBehavior({
        queryDate: unref(tab).currentDate.join('-'),
        companyId: currentUserCode.value,//当前用户的
    }).then(res => {
        if (res.code === 200 && res.data) {
            memberBehaviorEcharts.value = res.data
        }
    })
}

// 会员行为趋势统计
const memberTrendEcharts = ref<any>(null)
const getUnionActivityTypeTrend = () => {
    memberBehaviorTrend({
        queryDate: unref(tab).currentDate.join('-'),
        companyId: currentUserCode.value,//当前用户的
    }).then(res => {
        if (res.code === 200 && res.data) {
            memberTrendEcharts.value = res.data
        }
    })
}

// 单个活动
// 用户所属工会活动列表
const activityList = ref<any>([])
const getActivityInfoList = async () => {
    const res = await activityInfoList({
        companyId: currentUserCode.value,//当前用户的
        pageSize: 0,
    })
    if (res.code === 200 && res.data) {
        tab.value.activityId = res.data[0]?.activityId
        tab.value.activityName = res.data[0]?.activityName
        activityList.value = res.data.map((t: any) => {
            return { value: t?.activityId, text: t?.activityName }
        })
    }
}

const totalObjSingle = ref<any>({})
const activityStaticDataFn = () => {
    activityStaticData({
        activityId: tab.value.activityId,
    }).then(res => {
        if (res.code === 200 && res.data) totalObjSingle.value = res.data
    })
}

// 区域会员参与情况
const memberParticipationEcharts = ref<any>({})
const getMemberParticipation = () => {
    activityMemberParticipation({
        activityId: tab.value.activityId,
    }).then(res => {
        if (res.code === 200 && res.data) memberParticipationEcharts.value = res.data
    })
}
// 活动每日访问情况
const activityVisitEcharts = ref<any>({})
const getActivityVisit = () => {
    activityEveryDayVisit({ activityId: tab.value.activityId }).then(res => {
        if (res.code === 200 && res.data) activityVisitEcharts.value = res.data
    })
}
// 奖品发放情况
const prizeInfo = ref<any>({})
const currentPrizeChart = ref<any>({
    dataX: [],
    series: {
        data: [],
        name: ''
    }
})
const getActivityPrizeInfo = () => {
    activityPrizeInfo({ activityId: tab.value.activityId }).then(res => {
        if (res.code === 200 && res.data) prizeInfo.value = res.data
        currentPrizeChart.value.dataX = res.data?.dataX //x轴数据
        if (res.data?.series?.length > 0) currentPrizeChart.value.series = res.data.series[0]//series数据
    })
}

// 活动新增用户趋势
const newUserTrendEcharts = ref<any>({})
const getActivityNewUserTrend = () => {
    activityNewUserTrend({ activityId: tab.value.activityId }).then(res => {
        if (res.code === 200 && res.data) newUserTrendEcharts.value = res.data
    })
}


//=====end=====



// 区县下拉
const regionList = computed(() => dictionaryStore.dictionaryOBJmap?.['regionCode']?.map((t: any) => {
    const { dictName, remark } = t
    return { value: remark, text: dictName }
}))
// 区县信息
const company = ref({
    companyId: '',
    companyName: ''
})
const selectShow = ref(false)
const selectType = ref('')//下拉选择类型
const columns = ref<any>([])
const searchName = ref('')
const selectColumns = computed(() => {
    if (searchName.value) {
        return columns.value.filter((item: Recordable) => { return item.text.includes(searchName.value) })
    }
    else return columns.value
})

const showPickerFn = (type: string) => {
    selectType.value = type
    switch (type) {
        case 'region':
            columns.value = regionList.value
            break
        case 'activity':
            columns.value = activityList.value
            break
        case 'prize':
            columns.value = prizeInfo.value.series.map((t: any, index: any) => {
                return {
                    text: t.name,
                    value: index
                }
            })
            break
    }
    selectShow.value = !selectShow.value
}
const onConfirmSelect = ({ selectedOptions }) => {
    const text = selectedOptions[0].text
    const value = selectedOptions[0].value
    switch (selectType.value) {
        case 'region':
            company.value.companyId = value
            company.value.companyName = text
            getUnionActivityType()
            break
        case 'activity':
            tab.value.activityId = value
            tab.value.activityName = text
            activityStaticDataFn()
            getActivityPrizeInfo()
            getMemberParticipation()
            getActivityVisit()
            getActivityNewUserTrend()
            break
        case 'prize':
            currentPrizeChart.value.series = prizeInfo.value.series[value]
            break
    }
    selectShow.value = false
}
onBeforeMount(() => {
    if (currentUserCode.value) {
        company.value.companyId = currentUserCode.value as string
        company.value.companyName = regionList.value.find((t: any) => t.value === currentUserCode.value)?.text || ''
    } else {
        company.value.companyId = regionList.value[0].value
        company.value.companyName = regionList.value[0].text
    }

})
const initData = async (type: number) => {
    if (type === 1) {
        activityStatistics()
        getUnionActivityType()
        getMemberBehavior()
        getUnionActivityTypeTrend()
    }
    else if (type === 2) {
        await getActivityInfoList()
        activityStaticDataFn()
        getActivityPrizeInfo()
        getMemberParticipation()
        getActivityVisit()
        getActivityNewUserTrend()
    }
}

watch(() => unref(tab).currentDate, () => {
    initData(1)
})
onMounted(() => {
    initData(1)

    // 单个活动-相关接口
    setTimeout(() => {
        initData(2)
    }, 1000)

})
</script>
<style lang="scss" scoped>
.main {
    width: 100%;
    min-height: 100vh;
    background: linear-gradient(to bottom, #D8E9FE 0%, #F6FBFF 20%, #DCEEFB 100%);
    box-sizing: border-box;
    position: relative;

    .border {
        border: 1px solid red;
    }

    .banner {
        width: 100%;
        position: absolute;
        z-index: 1;
    }

    .bottom {
        position: absolute;
        bottom: 0;
    }

    .center {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .title {
        font-weight: 500;
    }

    .box {
        width: calc(100% - 56px);
        margin: auto;
        box-sizing: border-box;
        margin-top: 28px;
        position: relative;
        z-index: 5;

        .tab {
            background: #EEF8FF url(@/assets/leaderBoard/activity/tab_bg_l.png) no-repeat;
            background-size: 100% 100%;
            border-radius: 45px 45px 0px 0px;

            &.tab-actived {
                background: #EEF8FF url(@/assets/leaderBoard/activity/tab_bg_r.png) no-repeat;
                background-size: 100% 100%;
            }

            .tab-item1 {
                font-size: 34px;
                color: #79AAE7;

                &.tab-color {
                    color: #4898FB
                }
            }


        }

        .tab-box {
            padding: 30px 10px;
            background: linear-gradient(to bottom, #F6FBFF 0%, #E2EFFE 50%, transparent 100%);
            // background: linear-gradient(to bottom,  #F6FBFF 20%, #DCEEFB 100%);
        }

        .tab-box1 {
            padding: 30px 20px;
            background: linear-gradient(to bottom, #F6FBFF 0%, #E2EFFE 50%, transparent 100%);

            .tab-item {
                border-radius: 30px;
            }

        }

        .tab-item {
            width: 30%;
            height: 50px;
            font-size: 28px;
            color: #4898FB;
            border-radius: 23px;
            border: 1px solid #76B7FB;

            &.actived {
                background: #4898FB;
                color: white;
            }
        }

        .time {
            width: 30%;
            height: 50px;
            font-size: 28px;
            color: #4898FB;
            border-radius: 23px;
            border: 1px solid #76B7FB;

            &.gray {
                color: rgba(118, 183, 251, 0.5);
            }
        }

        .select-box {
            width: 55%;
            height: 55px;
            padding: 0 10px;
            font-size: 28px;
            color: #4898FB;
            justify-content: space-around;
            border-radius: 30px;
            border: 1px solid #76B7FB;
            position: absolute;
            top: -10px;
            right: 0;

            .select-name {
                width: 90%;
                height: 100%;
                line-height: 55px;
                text-align: center;
                white-space: nowrap;
                /* 强制文本不换行 */
                overflow: hidden;
                /* 隐藏溢出内容 */
                text-overflow: ellipsis;
            }

        }

        .top {
            justify-content: space-between;

            .top-label {
                width: 31%;
                height: 210px;
                padding: 25px;
                border-radius: 20px;
                border: 2px solid #FFFFFF;
                box-sizing: border-box;
                display: flex;
                flex-direction: column;
                justify-content: space-around;
                background: linear-gradient(0deg, #FFFFFF 54%, #FFF6E9 100%);

                &.bg1 {
                    background: linear-gradient(0deg, #FFFFFF 54%, #E2FCFF 100%);
                }

                &.bg2 {
                    background: linear-gradient(0deg, #FFFFFF 54%, #E5F0FF 100%);
                }
            }
        }

        .info {
            width: 100%;
            padding: 20px;
            margin-bottom: 25px;
            background: linear-gradient(to bottom, #E5F3FF 0%, #fff 35%, #FFFFFF 100%);
            border-radius: 20px;
            border: 2px solid #FFFFFF;
            box-sizing: border-box;

            .icons {
                width: 40px;
                height: 40px;
                object-fit: contain;
                position: relative;
                top: 2px;
            }

            .title {
                color: #333;
                font-weight: 500;
                font-size: 32px;
            }

            .ph-activit {
                height: 155px;
                background: linear-gradient(0deg, #FFFFFF 0%, #F5FAFF 100%);
                border-radius: 35px;
                justify-content: space-around;
                align-items: center;

                .label {
                    text-align: center;
                }
            }

            .content {
                position: relative;
            }

            .chart-box {
                width: 100%;
                height: 500px;
            }
        }

    }

    .select {
        border: 1px solid #76B7FB;
    }

    .search-input {
        margin: 20px;
        box-sizing: border-box;
        border: 1px solid #76B7FB;
        border-radius: 40px;
        line-height: 1;
        width: auto;
        font-size: 28px;
    }
}
</style>