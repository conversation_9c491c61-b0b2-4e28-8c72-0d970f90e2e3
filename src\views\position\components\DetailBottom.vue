<template>
  <div class="w-full h-[140px] fixed bottom-0 bg-[#fff] flex z-1">
    <div class="w-1/3 flex justify-evenly items-center text-[#333333]">
      <div class="text-center" @click="handleSc">
        <van-icon name="star-o" :class="`${ifSc ? 'puff-out-center' : 'puff-in-center'}`" class="text-[50px]"
          v-if="!ifSc" />

        <van-icon name="star" color="#ffb300" :class="`${ifSc ? 'puff-in-center' : 'puff-out-center'}`"
          class="text-[50px]" v-else />
        <span class="block">收藏</span>
      </div>
      <div class="text-center active:text-[#5ba5ff80]" @click="handleShare">
        <van-icon name="share-o" class="text-[50px]" />
        <span class="block">分享</span>
      </div>
    </div>
    <div class="w-2/3 flex justify-evenly items-center" :class="`${ifVr ? '' : 'px-5'}`">
      <div
        class="active:shadow-[0_0_10px_0_#5ba5ff] active:rounded-[36px] text-center py-2 border border-solid px-[54px] rounded-[36px] w-[230px]"
        v-if="ifVr" @click="handleClick">
        VR查看
      </div>

      <div
        class="bg-#5BA5FF text-center text-[#fff] rounded-[36px] py-2 active:shadow-[0_0_10px_0_#5ba5ff] active:rounded-[36px]"
        :class="`${ifVr ? 'w-[230px]' : 'w-full'}`" @click="handleQrcode">
        扫码签到
      </div>
    </div>

    <!-- 气泡样式 -->
    <div class="fixed bottom-140px z-100" :style="`left:${bubbleX}%`">
      <waterIntergral v-model:show="showBubble" :score="scoreNum"></waterIntergral>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { collectOperate, shareOperate } from '@/api/public';
import utils from '@/utils/utils';
import { showFailToast, showSuccessToast } from 'vant';
const router = useRouter();

const ifSc = ref(false);

const detailRecord = inject<Recordable>('detailRecord', {});

const ifVr = computed(() => unref(detailRecord)?.panoramaUrl);

const waterIntergral = defineAsyncComponent(() => import('@/components/Bubble/waterIntergral.vue'))

function handleClick() {
  router.push({
    path: '/vrShow', query: {
      linkUrl: ifVr.value
    }
  });
}

function handleQrcode() {
  utils.scan('USER', [{ Name: 'position', }])
  // router.push({ path: '/scanQRcode', query: {} });
}
// 气泡提示框参数设置
const bubbleX = ref(2);//2 15
const showBubble = ref(false);
const scoreNum = ref(0);
let isReq = false;

function handleShare() {
  utils.SharedWorker(unref(detailRecord)?.positionName, window.location.href, (isCan) => {
    if (isCan) {
      getShare()
    }
  })
  // utils.newsAction({ title: unref(detailRecord)?.positionName, dataId: unref(detailRecord)?.positionInfoId }, "share")
}

function handleSc() {
  if (isReq) return;
  isReq = true;

  collectOperate({ sourceId: unref(detailRecord)?.positionInfoId }).then(
    ({ code, data: { statefulFlowState, score }, message }) => {
      isReq = false;
      if (code === 200) {
        ifSc.value = statefulFlowState;
        showSuccessToast(`${unref(ifSc) ? '收藏成功' : '取消收藏'}！`);
        if (score) {
          scoreNum.value = score
          showBubble.value = true;
          bubbleX.value = 2;
        }
      } else {
        showFailToast(`收藏失败！${message || ''}`);
      }
    }
  )
    .catch(() => {
      isReq = false;
    })
}
//分享
const getShare = (dataid, code) => {
  if (isReq) return;
  isReq = true;
  shareOperate({ sourceId: unref(detailRecord)?.positionInfoId }).then(
    ({ code, data: { statefulFlowState, score }, message }) => {
      isReq = false;
      if (code === 200) {
        ifSc.value = statefulFlowState;
        if (score) {
          scoreNum.value = score
          showBubble.value = true;
          bubbleX.value = 15;
        }
      } else {
        showFailToast(`分享失败！${message || ''}`);
      }
    }
  )
    .catch(() => {
      isReq = false;
    })
}
onMounted(() => {
  // window.h5NewsShareReturn = getShare;
})
watch(
  () => detailRecord,
  () => {
    ifSc.value = unref(detailRecord)?.whetherCollect;
  },
  { deep: true }
);
</script>
