import { h5Http,dataCenterHttp,openHttp } from '@/utils/http/axios';
// ===== 签到 ====
export const userSign = (params:any) => {
  return dataCenterHttp.post({
    url:'/everydaySignRecord/everyDaySignIn',
    params
  })
}
// 签到抽奖
export const userSignLottery = () => {
  return h5Http.post({
    url:'/activityInfo/h5/signLottery',
    params:{
      platform:'app'
    }
  })
}
// 签到记录
export const userSignList = (params:any) => {
  return dataCenterHttp.get({
    url:'/everydaySignRecord/summarySignRecord',
    params
  })
}
//===== end =====

// ==== 积分 ====
// 积分统计
export const myIntegralNum = (params:any) => {
  return dataCenterHttp.get({
    url:'/customIntegral/getIntegralByUserId',
    params
  })
}

// 个人可用积分
export const getUsableIntegral = () => {
    return dataCenterHttp.get({
        url:'/customIntegral/getUsableIntegral',
    })
}
// 积分明细列表
export const myIntegralList = (params:any) => {
  return dataCenterHttp.get({
    url:'/customIntegral/findIntegralRecordVoList',
    params
  })
}
// 积分规则
export const integralRule = () => {
  return dataCenterHttp.get({
    url:'/customIntegral/getIntegralDescribe',
  })
}

// 积分活动
export const integralActivityList = (params:any) => {
  return h5Http.get({
    url:'/activityInfo/h5/integralActivityList',
    params
  })
}

// 抽奖活动详情
export const integralLotteryDetail = (params:any) => {
  return h5Http.get({
    url:'/activityInfo/h5/getDetailByActivityMode',
    params
  })
}

// 抽奖
export const getLottery = () => {
  return h5Http.post({
    url:'/activityInfo/h5/integralLottery',
    params:{platform: "app"}
  })
}//立即兑换
export const integralGoodsExchange = (params:any) => {
  return openHttp.post({
    url:'/customOrder/createIntegralExchange',
    params
 })
}

// 兑换记录
export const integralGoodsExchangeList = (params:any) => {
  return openHttp.get({
    url:'/integralExchangeRecord/getCurrentExchangeList',
    params
 })
}
// 获取商品核销二维码
export const getIntegralGoodsQrCode = (recordId:string) => {
  return openHttp.get({
    url:'/customOrder/getOrderQrCode',
    params:{
      recordId
    }
  })
}
// 获取线上订单详情
export const getIntegralOrder = (recordId:string) => {
  return openHttp.get({
    url:'/integralExchangeRecord/getCurrentExchangeDetails',
    params:{
      recordId
    }
  })
}
// 确认收货
export const confirmReceiveGoods = (recordId:any) => {
  return openHttp.post({
    url:'/customOrder/confirmReceipt',
    params:{recordId}
 })
}

// 核销
export const integralGoodsExchangeQrCode = (params:any) => {
  return openHttp.post({
    url:'/customOrder/writeOffOrderQrCode',
    params
 })
}
// 积分任务
export const integralTaskList = () => {
  return dataCenterHttp.get({
    url:'/customIntegral/getIntegralTask',
  })
}
export const integralGoodsList = (params:any) => {
  return openHttp.get({
    url:'/customProductInfo/integralProductFindVoList',
    params
 })
}
// ===== end =====

