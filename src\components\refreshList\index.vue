<template>
    <div class="list h-full">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh" :disabled="props.disabled"
            :style="props.fullScreen ? 'height: 100vh;' : 'height:100%'">
            <van-list v-model:loading="loading" :finished="finished"
                :finished-text="noData || noshowText ? '' : props.finishedText" error-text="请求失败，点击重新加载" @load="onLoad"
                :immediate-check="props.immeCheck">
                <slot></slot>
                <div v-if="noData" class="no-data-cont h-full" :class="props.noDataClass">
                    <!-- 暂无数据图片展示 -->
                    <Empty v-if="!props.slotEmpty">

                    </Empty>
                    <slot name="noData" v-else></slot>
                </div>
            </van-list>
        </van-pull-refresh>
    </div>
</template>

<script lang="ts" setup>
import Empty from '@/components/Empty/index.vue';
const props = defineProps(
    {
        immeCheck: { //立即执行
            type: Boolean,
            default: false
        },
        fullScreen: { // 是否需要让下拉区域始终为全屏
            type: Boolean,
            default: false
        },
        disabled: {
            type: Boolean,
            default: false
        },
        noDataClass: {
            type: String,
            default: ''
        },
        finishedText: {
            type: String,
            default: '没有更多了'
        },
        slotEmpty: {
            type: Boolean,
            default: false
        },
        noshowText: {
            type: Boolean,
            default: false
        }
    }
)
const refreshing = ref(false)//是否正在刷新
const loading = ref(false)//是否正在加载
const finished = ref(false)//是否加载完成
const noData = ref(false) //是否没有数据
const len = ref(0) //当前加载长度
const total = ref(0) //总数据长度
const emit = defineEmits(['onRefreshList', 'onLoadMore'])
// 刷新加载完成
const onRefreshSuc = () => {
    refreshing.value = false
    finished.value = false
}
// 重置状态
const resetStatus = () => {
    loading.value = true
    finished.value = false
    noData.value = false
}
const onRefresh = () => {
    // resetStatus()
    setTimeout(() => {
        emit('onRefreshList')
    }, 500)
}
// currentLength  已加载数据  totalLength 总数据
const onLoadSuc = (currentLength: number, totalLength: number) => {
    onRefreshSuc()
    loading.value = false
    len.value = currentLength
    total.value = totalLength
    if (totalLength > 0) {
        noData.value = false
        if (currentLength < totalLength) {
            finished.value = false
        } else {
            finished.value = true
        }

    } else {
        finished.value = true
        noData.value = true
    }

}
const onLoad = () => {
    setTimeout(() => {
        if (len.value < total.value) {
            emit('onLoadMore')
        } else {
            finished.value = true
        }
    }, 500)
}
onMounted(() => {
    // 是否立即执行
    if (props.immeCheck) {
        loading.value = true
        setTimeout(() => {
            onRefresh()
        }, 500)
    }
})
// 向父组件暴露的方法
defineExpose({
    onLoadSuc,//每次请求数据调用
    resetStatus,//切换标签页重置状态
})
</script>

<style lang="scss" scoped>
.list {
    --van-list-text-font-size: 24px;
    --van-loading-text-font-size: 26px;
    --van-pull-refresh-head-font-size: 26px;
    --van-pull-refresh-loading-icon-size: 40px;
}
</style>