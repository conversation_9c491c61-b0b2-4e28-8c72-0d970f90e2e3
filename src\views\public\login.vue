<template>
    <div class="login">
        <div class="content">
            <div class="flex items-center">
                <img loading="lazy" src="@/assets/public/title.png" alt="" class="title">
            </div>
            <van-form @submit="onSubmit">
                <van-field v-model="Data.form.p" name="手机号" type="number" placeholder="请输入手机号" :rules="Data.password">
                    <template #button v-show="!Data.isShow">

                        <van-button v-show="Data.isShow" class="send-code" size="small"
                            :disabled="Data.count == 0 ? false : true" @click="sendCode()" native-type="button">{{
                                Data.count === 0 ? '发送验证码' : `${Data.count}s后重新获取` }}</van-button>

                    </template>
                </van-field>
                <van-field v-if="Data.isShow" v-model="Data.form.code" name="验证码" placeholder="请输入短信验证码"
                    :rules="generalRules('请输入短信验证码', false)" />

                <van-field v-if="!Data.isShow" v-model="Data.form.code" type="password" name="密码" placeholder="请输入登录密码"
                    :rules="generalRules('请输入登录密码', false)" />

                <div class="login-btn-box">
                    <van-button class="login-btn h-80px rounded-38px mt-53px text-#fff text-30px tracking-widest" round
                        block type="info" native-type="submit">
                        {{ Data.loginState ? '登录' : '登录中' }}
                        <van-icon v-show="!Data.loginState" name="weapp-nav" />
                    </van-button>
                </div>
            </van-form>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { generalRules } from "@/utils/rulesValidator";
import { showFailToast, showToast } from "vant";
import { accountVerifyCode, accountCode } from "@/api/login";
import router from "@/router";
import { useUserStore } from '@/store/modules/user';
const Data = ref({
    isShow: true,
    form: {
        p: "",
    },
    rules: { generalRules },
    password: [{
        required: true,
        message: "手机号不能为空",
        trigger: "onBlur"
    },
    {
        // 自定义校验规则
        validator: value => {
            return /^(0|86|17951)?(13[0-9]|14[********]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/.test(value);
        },
        message: "请输入正确格式的手机号",
        trigger: "onBlur"
    }
    ],
    loginText: '登录',
    loginState: true,
    count: 0,
    timer: null
})
//倒计时
function getCode() {
    let TIME_COUNT = 60;
    if (!Data.value.timer) {
        Data.value.count = TIME_COUNT;
        Data.value.timer = setInterval(() => {
            if (Data.value.count > 0 && Data.value.count <= TIME_COUNT) {
                Data.value.count--;
            } else {
                clearInterval(Data.value.timer);
                Data.value.timer = null;
            };
        }, 1000);
    }
}
//发送验证码
function sendCode() {
    if (Data.value.form.p != "") {
        accountCode({
            p: Data.value.form.p
        }).then(res => {
            if (res.code == 200) {
                getCode()
            } else {
                showToast(res.message)
            }
        })
    }
    else {
        showFailToast("请输入手机号")
    }
}
function onSubmit(values) {
    Data.value.loginState = false;
    accountVerifyCode(Data.value.form).then(async (res) => {
        if (res.code == 200) {
            // 避免调用两次登录接口
            // const data = await useUserStore()?.login({
            //     token: res.data
            // })
            // if (data) {
            // router.back()
            // 只针对开发使用
            router.replace({
                path: '/welcome',//
                query: {
                    token: res.data
                }
            })
            // }
        } else {
            Data.value.loginState = true
            showFailToast(res.message)
        }
    })
}
</script>
<style scoped lang="scss">
.login {
    height: 100vh;
    width: 100%;
    background: url("@/assets/public/bg_login.png") no-repeat;
    background-size: 100% 100%;

    .content {
        background: url("@/assets/public/border.png") no-repeat;
        background-size: 100% 100%;
        width: 90%;
        // height: 562px;
        margin: 0 auto;
        position: relative;
        top: 35%;
        padding: 60px 45px;
        box-sizing: border-box;

        :deep(.van-button--disabled) {
            opacity: 1;
        }

        .title {
            width: 50%;
            margin: 0 auto;
        }

        :deep(.van-form) {
            margin-top: 50px;

            .van-cell {
                border-bottom: 1px solid rgba(197, 197, 197, 0.6);
                background: transparent;
                padding: 18px 10px 18px 28px;
                margin-top: 30px;

                .send-code {
                    // width: 145px;
                    width: auto;
                    height: 42px;
                    background: #6FAFFF;
                    border-radius: 21px;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    .van-button__text {
                        font-size: 22px;
                        font-weight: 400;
                        color: #fff;
                        line-height: 13px;
                    }
                }
            }

            .login-btn-box {
                display: flex;
                justify-content: center;
            }

            .login-btn {
                background: url("@/assets/public/button.png") no-repeat;
                background-size: 100% 100%;

                .van-icon-weapp-nav {
                    font-size: 30px;
                    margin-left: 5px;
                }
            }
        }

        .tab-btn {
            width: 100%;
            height: 24px;
            font-size: 30px;
            font-weight: 400;
            color: #0aa397;
            line-height: 24px;
            margin-top: 47px;
            text-align: center;
        }

        .van-button--info {
            border: 1px solid transparent;
        }
    }

}
</style>