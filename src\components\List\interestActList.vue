<template>
    <div class=" mb-20px bg-#fff p-20px rounded-20px" v-for="(item, index) of data" :key="index" @click="toPage(item)">
        <div class="flex hr pb-22px text-24px"> <img loading="lazy" :src="judgeStaticUrl(item.logo)" alt=""
                class="w-34px h-32px mr-11px">{{ item.groupName }}</div>
        <div class="flex pt-22px">
            <div class="relative">
                <img loading="lazy" src="@/assets/interest/banner.png" alt="" class="w-200px rounded-8px h-113px">
                <div class="text-#fff text-20px absolute top-0 left-0 w-70px h-30px text-center"
                    :class="item.progressMsg == '进行中' ? 'open' : item.progressMsg == '未开始' ? 'wait' : 'end'">{{
                        item.progressMsg }}
                </div>
            </div>
            <div class="flex-1 pl-29px right-box border-box">
                <div class="truncate text-32px">{{ item.activityName }}</div>
                <div class="text-#666666 text-20px mt-13px flex items-center"><van-icon name="clock-o" size="11"
                        class="mr-10px" />{{ dayjs(item?.activityStartTime).format('YYYY-MM-DD') }}至{{
                            dayjs(item?.activityEndTime).format('YYYY-MM-DD') }}</div>
                <div class="text-#666666 text-20px mt-13px flex items-center">
                    <img loading="lazy" src="@/assets/interest/icon_join.png" class="w-30px h-23px mr-10px">{{
                        item.signUpInfo?.signUpCount }}/{{ item.signUpInfo?.maxCount }}
                    <div class="w-70/100 ml-10px">
                        <van-progress :show-pivot="false"
                            :percentage="(item.signUpInfo?.signUpCount / item.signUpInfo?.maxCount) * 100"
                            stroke-width="8" color="#5AA4FF" />
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>
<script lang="ts" setup>
import router from '@/router';
import { useUserStore } from '@/store/modules/user';
import { judgeStaticUrl } from '@/utils/utils';
import dayjs from 'dayjs';
const props = defineProps({
    data: {
        type: Array,
        default: [],
    },
})
//详情
function toPage(item) {
    useUserStore().setActivityDetail()
    sessionStorage.setItem('activityId', item.activityId)
    router.push({
        path: '/activityHome/interestDetail',
        query: {
            activityId: item.activityId
        }
    })
}
</script>
<style lang="scss" scoped>
.right-box {
    width: calc(100% - 200px);
}

.hr {
    border-bottom: 1px solid #EBEBEB;
}

.wait {
    background: url("@/assets/interest/wait.png") no-repeat;
    background-size: 100% 100%;
}

.open {
    background: url("@/assets/interest/open.png") no-repeat;
    background-size: 100% 100%;
}

.end {
    background: url("@/assets/interest/end.png") no-repeat;
    background-size: 100% 100%;
}
</style>
