<template>
    <!-- 订单列表 -->
    <div class="my-order bg-[#F6F7F8] h-fit min-h-100vh box-border">
        <div class="headers bg-#fff overflow-hidden">
            <div class="flex items-center bg-[#F9F9F9] rounded-26px mx-30px my-20px py-10px px-30px">
                <img loading="lazy" src="@/assets/public/icon_s.png" class="w-28px">
                <van-field v-model="searchValue" placeholder="搜索商户名\商品名\规格名..." class="flex-1 ml-20px" clearable
                    @blur="searchFn" />
            </div>

            <van-tabs v-model:active="tabActive" sticky title-active-color="#5AA4FF" title-inactive-color="#333"
                @change="onRefreshList" v-show="showTab">
                <van-tab :title="item.label" v-for="item in tabs" :key="item.value" :name="item.value"
                    title-class="tab-title">
                </van-tab>
            </van-tabs>
        </div>
        <div class="list">
            <refreshList ref="loadMoreRef" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" :slotEmpty="true">
                <!-- 订单列表样式 -->
                <template v-if="tabActive !== 'refundOrder'">
                    <orderCell v-for="item, index in list" :content="item" :key="item.orderId + item.orderState"
                        @toDetail="toDetail(item, index, '')"
                        @handleControll="(typeInfo) => handleControll(typeInfo, item, index)">
                    </orderCell>
                </template>
                <!-- end -->
                <!-- 售后列表样式 -->
                <template v-else>
                    <afterSaleCell v-for="item, index in list" :content="item" :key="item.orderId + item.orderState"
                        @toDetail="toDetail(item, index, 'refund')"
                        @handleControll="(typeInfo) => handleControll(typeInfo, item, index)">
                    </afterSaleCell>

                </template>
                <!-- end -->
                <template #noData>
                    <van-empty :image="emptyImg" description="暂时没有订单哦" :image-size="['60%', 'auto']">
                    </van-empty>
                </template>
            </refreshList>
        </div>

        <!--确认收货弹窗 -->
        <confirmReceivePopup v-model:show="popupShow" @confirm="confirmReceiveFn" :bgCover="bgCover"
            :goodsNum="goodsNum">
        </confirmReceivePopup>
        <!-- 评价 -->
        <evaluatePopup v-model:show="evaluateShow"
            :productList="currentItem.snapshotVo?.transProductSnapshot?.goodsFlatten" @submit="getEvaluateInfo">
        </evaluatePopup>
    </div>
</template>
<script lang="ts" setup>
import { orderMySearchList, orderMyStateList, afterSaleList } from '@/api/mall/inclusive'
import emptyImg from '@/assets/integralMall/no_content.png'
import orderCell from './components/orderCell.vue';
import afterSaleCell from './components/afterSaleCell.vue';
import refreshList from '@/components/refreshList/index.vue';
import { useDictionary } from '@/store/modules/dictionary';
import { useMallStore } from '@/store/modules/mall';
import useRefreshFun from '@/hooks/app.ts'
import useOrderFn from '@/hooks/orderHooks.ts'
import { showToast } from 'vant';
const confirmReceivePopup = defineAsyncComponent(() => import('@/components/Popup/confirmReceivePopup.vue'))
const evaluatePopup = defineAsyncComponent(() => import('./components/evaluatePopup.vue'))

defineOptions({
    name: 'myOrders',
})

const dictionaryStore = useDictionary()
// 售后
const saleServiceState = computed(() => dictionaryStore.getDictionaryOpt?.['saleServiceState'])
const backAmountState = computed(() => dictionaryStore.getDictionaryOpt?.['backAmountState'])
// end
// 订单状态
const transOrderState = computed(() => dictionaryStore.getDictionaryOpt?.['transOrderState'])

const tabs = computed(() => {
    return [
        {
            label: '全部',
            value: '',
        },
        ...transOrderState.value,
        {
            label: '售后/退款',
            value: 'refundOrder'
        }
    ]
})

const router = useRouter()
const mallStore = useMallStore()

// 跳转详情
const toDetail = (item: any, index: number, type: string) => {
    refreshPart(index)
    router.push({
        path: '/order/detail',
        query: {
            orId: item.orderId,
            ty: type,
            sId: item.serviceGroupId || ''
        }
    })
}
// 按钮操作
const { orderCancelFn, orderConfirmFn, submitCommentFn, cancelAfterSaleFn } = useOrderFn()
const popupShow = ref(false)
const bgCover = ref('')
const goodsNum = ref(0)
const currentItem = ref<any>({})
// typeInfo:操作按钮类型 item:当前订单对象 index：当前订单索引
const handleControll = (typeInfo: any, item: any, index: number) => {
    switch (typeInfo.type) {
        case 'cancel':
            // 取消订单
            orderCancelFn(item.orderId, () => {
                refreshPart(index, true)
            })
            break;
        case 'pay':
            showToast('暂不支持支付')
            // 去支付
            break;
        case 'receive':
            // 确认收货
            goodsNum.value = 0
            bgCover.value = item.snapshotVo?.transProductSnapshot?.productInfoList[0]?.priceListInfo[0]?.productSubImg

            item.snapshotVo?.transProductSnapshot?.productInfoList.forEach((item: any) => {
                goodsNum.value += item.priceListInfo?.length || 0
            })
            popupShow.value = true
            currentItem.value = item
            currentItem.value.pageIndex = index
            break;
        case 'refund':
            // 申请退款
            refreshPart(index, false)
            router.push({
                path: '/order/refund',
                query: {
                    orId: item.orderId,
                    type: 'backOrder'
                }
            })
            break;
        case 'evaluate':
            //评价
            currentItem.value = item
            currentItem.value.pageIndex = index
            evaluateShow.value = true
            break;
        case 'cancelRefund':
            // 取消退款申请
            cancelAfterSaleFn({ serviceGroupId: item.serviceGroupId }, () => {
                refreshPart(index, true)
            })
            break;
        case 'upload':
            // 上传单号
            refreshPart(index, false)
            router.push({
                path: '/order/logistics',
                query: {
                    orId: item.orderId,
                    sId: item.serviceGroupId
                }
            })
            break;
        case 'resetRefund':
            // 重新申请退款
            refreshPart(index, false)
            router.push({
                path: '/order/refund',
                query: {
                    orId: item.orderId,
                    type: 'backOrder'
                }
            })
            break;
        default:
            break;
    }
}
// 确认收货
const confirmReceiveFn = () => {
    popupShow.value = false
    orderConfirmFn(currentItem.value.orderId, () => {
        refreshPart(currentItem.value.pageIndex, true)
    })
}
// 评价
const evaluateShow = ref(false)
const getEvaluateInfo = (inclusiveProductCommentList: any) => {
    submitCommentFn({
        orderId: currentItem.value.orderId,
        inclusiveProductCommentList: inclusiveProductCommentList.map((item: any) => {
            if (item.anonymousFlg) item.anonymousFlg = 'y'
            else item.anonymousFlg = 'n'
            return item
        })
    }, () => {
        evaluateShow.value = false
        refreshPart(currentItem.value.pageIndex, true)
    })
}

// 局部刷新
const refreshPart = (index: number, refresh: boolean = false) => {
    let currenPageNum = Math.floor(index / pageSize) + 1
    if (!refresh) return mallStore.setOrdersPageNum(currenPageNum)
    refreshHandle(currenPageNum)
}
const refreshHandle = (currenPageNum: number) => {
    if (currenPageNum > 1) {
        let endIndex = ((currenPageNum - 1) * pageSize) - 1
        list.value = list.value.slice(0, endIndex)
    }
    pageNum = currenPageNum
    loadMoreData()
}
// end

// 其他页面触发刷新执行事件
const savePageNum = computed(() => mallStore.getOrdersPageNum)
const changePage = () => {
    refreshHandle(savePageNum.value)
}
const { refresh } = useRefreshFun()
refresh([
    { name: 'changePage', funsName: [changePage] }
])
// end


const showTab = ref(true)
const tabActive = ref('')
const searchValue = ref('')
const searchFn = () => {
    if (searchValue.value) showTab.value = false
    else showTab.value = true
    onRefreshList()
}
const list = ref<any>([]);
let pageNum = 1;
const pageSize = 10;
const loadMoreRef = ref()
// 刷新
const onRefreshList = () => {
    pageNum = 1
    list.value = []
    loadMoreData()
}
// 加载更多
const onLoadMore = () => {
    pageNum++
    loadMoreData()
}
const loadMoreData = async () => {
    let rescode, resdata, restotal
    // 搜索订单列表接口
    if (searchValue.value) {
        const { code, data, total } = await orderMySearchList({
            transProductSnapshot: searchValue.value,
            pageSize,
            pageNum,
        })
        rescode = code
        resdata = data
        restotal = total
    }
    // 售后列表接口
    else if (tabActive.value === 'refundOrder') {
        const { code, data, total } = await afterSaleList({
            pageSize,
            pageNum,
        })
        rescode = code
        resdata = data
        restotal = total
    }
    // 订单列表接口
    else {
        const { code, data, total } = await orderMyStateList({
            orderBy: 'create_time',
            sortType: 'desc',
            pageSize,
            pageNum,
            orderState: tabActive.value
        })
        rescode = code
        resdata = data
        restotal = total
    }

    if (pageNum === 1) list.value = []

    if (rescode === 200) {
        // 对商品数量做处理和虚拟状态判断
        if (tabActive.value !== 'refundOrder') resdata.forEach((item: any) => {
            item.statusText = transOrderState.value.find((dic: any) => dic.value === item.orderState)?.label
            let goodsFlatten: any = [] // 商品列表扁平化处理
            item.snapshotVo.transProductSnapshot.productInfoList.forEach((goods: any) => {
                goodsFlatten.push(...goods.priceListInfo)
            })
            item.snapshotVo.transProductSnapshot.goodsFlatten = goodsFlatten
            // 商品类型 虚拟/商品  只要存在实物商品即为实物订单，否则为虚拟订
            let isActual = item.snapshotVo.transProductSnapshot.productInfoList.some((item: any) => item.productType === 'actual')
            if (isActual) item.snapshotVo.transProductSnapshot.productType = 'actual'
            else item.snapshotVo.transProductSnapshot.productType = 'virtual'
        })
        else {
            resdata.forEach((item: any) => {
                // 订单售后状态处理
                item.statusText = saleServiceState.value.find((dic: any) => dic.value === item.serviceState)?.label
                item.detailVOList.forEach((item: any) => {
                    item.backResultStateText = backAmountState.value.find((dic: any) => dic.value === item.backResultState)?.label
                })
            })
        }
        if (pageNum === 1) list.value = resdata
        else list.value = [...list.value, ...restotal]
    }
    //重置刷新状态及 判断是否加载完成
    if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(list.value.length, restotal)
    }
}
onMounted(() => {
    onRefreshList()
})
</script>
<style scoped lang="scss">
.my-order {
    .headers {
        :deep(.van-cell) {
            background-color: transparent;
            padding: 0;
        }

        :deep(.van-tabs--line .van-tabs__wrap) {
            height: 92px;
        }

        :deep(.van-tab) {
            line-height: 1;
            height: 100%;
        }

        :deep(.van-tabs__nav--line) {
            height: 100%;
            padding-bottom: 15px;
        }

        :deep(.van-tabs__line) {
            display: none;
        }

        :deep(.van-tab__text) {
            font-size: 30px;
        }

        :deep(.van-cell__value) {
            font-size: 28px;
        }
    }
}
</style>