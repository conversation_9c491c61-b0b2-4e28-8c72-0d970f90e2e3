<template>
    <div class="bg-[#f6f7f8] min-h-[100vh] overflow-y-scroll pb-[122px] bg-#FFF" ref="scrollBoxRef">
      <div v-for="(item, index) in Data.topicList" :key="index"
        class="mb-[20px] bg-[#fff] px-29px box-border pt-[39px] pb-[19px]">
        <shareList :data="[item]" :isShowDetail="false" :type="item.auditStatus=='refuse' ? 'examine': ''" />
      </div>
    </div>
  </template>
  <script lang="ts" setup>
  import { useRoute } from "vue-router";
  const route = useRoute();
  import shareList from "../components/shareList.vue";
  const conment = ref("");//评论
  const Data = ref({
    topicList: [],
    titleName: '',
    objInfo: {},
    pageNum: 1,
  });
  
  onMounted(() => {
    Data.value.topicList= [JSON.parse(route.query.info) ]
  });
  
  
  </script>
  <style lang="scss" scoped>

  </style>