<template>
    <div class="launch w-full h-100%">
        <van-popup v-model:show="props.isShow">
            <div class="box fixed top-1/2 left-1/2 -translate-1/2 flex flex-col w-80%">
                <van-swipe class="my-swipe w-100%" :autoplay="3000" :show-indicators='false'>
                    <van-swipe-item v-for="(image, index) in props.imgList" :key="index" @click="toPage(image)">
                        <img loading="lazy" :src="utils.judgeStaticUrl(image.launchPicUrl)" width="100%"
                            style="height: 100%;" />
                    </van-swipe-item>
                </van-swipe>
                <img loading="lazy" src="@/assets/public/close.png" alt=""
                    class="close fixed w-50px h-50px bottom-20px right-1/2 translate-x-1/2" @click="closeLaunch()">
            </div>
        </van-popup>
    </div>
</template>
<script lang="ts" setup>
import utils from '@/utils/utils';
const emit = defineEmits(['closeLaunch']);
import { encryptUserInfo } from '@/api/news';
import { useUserStore } from "@/store/modules/user";
const props = defineProps({
    isShow: {
        type: Boolean,
        default: false
    },
    imgList: {
        type: Array,
        default: []
    },
    isHome: {
        type: Boolean,
        default: false
    }

})
//关闭弹窗
function closeLaunch() {
    emit("closeLaunch", false)
}
// 详情跳转
function toPage(item) {
    if (item.isJump == 'y') {
        if (item.jumpType == "builtIn") {
            if (item.jumpRouter == 'news') {
                utils.openNewsLink({
                    title: item?.title,
                    url: item?.jumpLinkUrl,//window.location.origin+'/newsDetail?newsId='+item?.newsId+'&categoryCode='+props?.categoryCode,
                    shareTitle: item?.newsTitle,
                    shareUrl: item?.externalLinkAddress,//window.location.origin+'/newsDetail?newsId='+item?.newsId+'&categoryCode='+props?.categoryCode,
                    dataId: item?.jumpLinkUrl,
                }, item?.whetherExternalLink, item?.categoryCode)
            } else {
                useUserStore().setActivityDetail()
                sessionStorage.setItem('activityId', item?.jumpLinkUrl)
                utils.openActLink({
                    title: item.title,
                    url: '/activityHome/activityDetail?activityId=' + item?.jumpLinkUrl,
                    shareName: item.title,//'分享标题'
                    shareurl: '/activityHome/activityDetail?activityId=' + item?.jumpLinkUrl,//'分享地址'
                    dataid: item?.jumpLinkUrl,
                    type: 1,//类型 1-工会活动，2-普惠活动
                    uid: item.companyId,
                    win: item?.jumpLinkUrl,
                    isExternal: 'n'
                })
            }
        } else {
            getEncryption(item)
        }
    }
    emit("closeLaunch", false);
}
function getEncryption(item) {
    encryptUserInfo({
        recordId: item?.launchId,
        userInfo: useUserStore()?.getUserInfo,
    }).then(res => {
        let url = item.jumpLinkUrl; //返回的url
        url += item.jumpLinkUrl.indexOf("?") == -1 ? "?" : "&"; //判断是否有问号
        if (item.jumpRouter == 'news') {
            utils.openNewsLink({
                title: item?.title,
                url: url + "token=" + res.data,//window.location.origin+'/newsDetail?newsId='+item?.newsId+'&categoryCode='+props?.categoryCode,
                shareTitle: item?.newsTitle,
                shareUrl: url,//window.location.origin+'/newsDetail?newsId='+item?.newsId+'&categoryCode='+props?.categoryCode,
                dataId: item?.launchId,
            }, item?.whetherExternalLink, item?.categoryCode)
        } else {
            utils.openActLink({
                title: item.title,
                url: url + "token=" + res.data,
                shareName: item.title,//'分享标题'
                shareurl: url,//'分享地址'
                dataid: item?.launchId,
                type: 1,//类型 1-工会活动，2-普惠活动
                uid: item.companyId,
                win: item?.launchId,
                isExternal: 'y'
            })
        }
    })
}
</script>
<style lang="scss" scoped>
.launch {
    .van-popup {
        background-color: transparent;
        width: 100%;
        height: 100%
    }

    .van-overlay {
        background-color: rgba(0, 0, 0, .5);
    }
}
</style>