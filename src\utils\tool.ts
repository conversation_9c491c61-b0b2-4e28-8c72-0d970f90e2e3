// by den<PERSON><PERSON><PERSON>
/**
 * 脱敏手机号码
 *
 * @param content 待脱敏的手机号内容
 * @param fillChar 脱敏填充字符，默认为 "*"
 * @returns 脱敏后的手机号字符串
 */
export function desPhone(content:string, fillChar:string = "*"):string {
  if (!content) {
    return "";
  }

  // 非字符串转换为字符串
  content = content.toString();

  if (content.length < 11) {
    return content;
  }

  let index = 1;
  let result = "";

  for (let char of content) {
    if (index < 4 || index > content.length - 4) {
      result += char;
    } else {
      result += fillChar;
    }
    index++;
  }
  return result;
}

/**
* 通过身份证号码获取生日
*
* @param identityCard 身份证号码
* @returns 生日
*/
export function getBirthdate(identityCard:string) {
  if (identityCard.length === 15) {
    return `19${identityCard.substr(6, 2)}-${identityCard.substr(8, 2)}-${identityCard.substr(10, 2)}`
  } else if (identityCard.length === 18) {
    return `${identityCard.substr(6, 4)}-${identityCard.substr(10, 2)}-${identityCard.substr(12, 2)}`
  } else {
    return ''
  }
}
/**
* 通过身份证号码获取年龄
*
* @param identityCard 身份证号码
* @returns 生日
*/
export function getAge(identityCard:string) {
  let len = (identityCard + "").length;
  if (len == 0) {
    return '';
  } else {
    //身份证号码只能为15位或18位其它不合法
    if ((len != 15) && (len != 18))
    {
      return '';
    }
  }
  let strBirthday = "";
  if (len == 18)
  {
    strBirthday = identityCard.substr(6, 4) + "/" + identityCard.substr(10, 2) + "/" + identityCard.substr(12, 2);
  }
  if (len == 15) {
    strBirthday = "19" + identityCard.substr(6, 2) + "/" + identityCard.substr(8, 2) + "/" + identityCard.substr(10, 2);
  }
  //时间字符串里，必须是“/”
  const birthDate = new Date(strBirthday);
  const nowDateTime = new Date();
  let age = nowDateTime.getFullYear() - birthDate.getFullYear();
  if (nowDateTime.getMonth() < birthDate.getMonth() || (nowDateTime.getMonth() == birthDate.getMonth() && nowDateTime.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
}

/**
* 通过身份证号码获取性别
*
* @param identityCard 身份证号码
* @returns 性别value值 male 男，female 女
*/
export function getGenderByIdCard(identityCard:string){
  if (identityCard.length === 15) {
    if(Number(identityCard.substr(14, 1)) % 2 === 1) return 'male'
    else return 'female'

  } else if (identityCard.length === 18) {
    if(Number(identityCard.substr(16, 1)) % 2 === 1) return 'male'
    else return 'female'
  } else {
    return ''
  }
}

// 检查浏览器是否支持 ES6 模块
function isES6ModuleSupported() {
  try {
    // 尝试通过动态 import 检测模块支持
    new Function('import("")');
    return true;
  } catch (e) {
    return false;
  }
}

if (isES6ModuleSupported()) {
  console.log("浏览器支持 ES6 模块，应该能够运行 Vite 项目");
} else {
  console.log("浏览器不支持 ES6 模块，可能不支持 Vite");
}
// 检查浏览器是否支持现代 JavaScript 特性
function checkModernJavaScriptSupport() {
  try {
    // 测试 `let` 和箭头函数支持
    let test = () => {};
    test();
    return true;
  } catch (e) {
    return false;
  }
}

if (checkModernJavaScriptSupport()) {
  console.log("浏览器支持现代 JavaScript 语法，应该支持 Vite");
} else {
  console.log("浏览器不支持现代 JavaScript 语法，可能不支持 Vite");
}

// 检查 WebSocket 支持
function isWebSocketSupported() {
  return 'WebSocket' in window;
}

if (isWebSocketSupported()) {
  console.log("浏览器支持 WebSocket，应该能够支持 Vite 热更新");
} else {
  console.log("浏览器不支持 WebSocket，Vite 热更新可能无法正常工作");
}




