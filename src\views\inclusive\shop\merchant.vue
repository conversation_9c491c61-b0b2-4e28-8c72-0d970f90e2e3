<template>
    <div class="merchant bg-#F5F5F5 w-full min-h-100vh">
        <div class="merchant-banner w-full h-306px"></div>
        <div class="merchant-content mx-30px -mt-150px">
            <!-- 店铺卡片 -->
            <div class="merchant-card w-full bg-#fff px-28px py-34px rounded-28px">
                <van-skeleton title :row="5" :loading="loading">
                    <div class="cell-1 flex">
                        <div class="merchant-card-cover w-156px h-156px rounded-12px bg-#F1F1F1">
                            <img loading="lazy" :src="utils.judgeStaticUrl(merchantInfo?.companyIcon)"
                                class="w-full h-full rounded-12px object-cover">
                        </div>
                        <div class="merchant-card-info flex-1 ml-26px">
                            <div class="merchant-card-title text-28px flex items-center justify-between">
                                <div>
                                    <span class="text-34px">{{ merchantInfo?.companyName }}</span><van-icon
                                        name="arrow"></van-icon>
                                </div>
                                <div class="collect" @click="collectFn">
                                    <img loading="lazy" src="@/assets/inclusive/shop/icon_collect.png"
                                        class="w-125px block" v-if="!merchantInfo?.whetherCollect" />
                                    <img loading="lazy" src="@/assets/inclusive/shop/icon-collected.png"
                                        class="w-125px block" v-else />
                                </div>
                            </div>
                            <div class="merchant-card-info mt-20px">
                                <span
                                    class="card-tag bg-#E44845 text-#fff text-26px py-6px px-12px rounded-8px">美味特产</span>
                                <span class="card-collect ml-16px text-#666 text-24px">{{ merchantInfo?.collectCount ||
                                    0
                                    }}人收藏</span>
                            </div>
                            <div class="merchant-card-address mt-36px flex items-center" @click="toAddress">
                                <img loading="lazy" src="@/assets/inclusive/shop/icon_dz.png"
                                    class="w-28px h-28px block" />
                                <div class="text-#666 ml-10px text-24px">{{ merchantInfo?.address }}</div>
                            </div>
                            <div @click="contactPhone"
                                class="merchant-card-phone w-148px h-48px mt-25px text-#FF5237 flex items-center justify-center rounded-8px">
                                <img loading="lazy" src="@/assets/inclusive/shop/icon_dh.png" class="w-28px block" />
                                <span class="ml-9px text-22px">电话联系</span>
                            </div>
                        </div>
                    </div>
                    <div class="cell-2 pt-30px flex items-center">
                        <img loading="lazy" src="@/assets/inclusive/shop/coupon-text.png" class="w-78px block" />
                        <div class="inclusive-coupon-1 flex items-center 
                        ml-18px p-16px text-26px rounded-10px">
                            <div class="info-text">
                                满300减60
                            </div>
                            <div class="rec-text">领取</div>
                        </div>
                        <div class="inclusive-coupon-2 flex items-center 
                        ml-18px p-16px text-26px rounded-10px">
                            <div class="info-text">
                                8.8折优惠
                            </div>
                            <div class="rec-text">领取</div>
                        </div>
                        <div class="arrow-right flex-1 flex justify-end">
                            <van-icon name="arrow" class="text-30px text-#999"></van-icon>
                        </div>
                    </div>
                </van-skeleton>
            </div>

            <!-- 商家上传商品 -->
            <div class="goods-list mt-44px">
                <!-- tab选择 -->
                <div class="tab-list h-80px bg-[#F5F5F5] sticky top-0 z-99 flex items-center justify-between">
                    <div class="w-65% flex items-center justify-between">
                        <van-tabs v-model:active="tabActive" title-active-color="#333" title-inactive-color="#333"
                            background="transparent" @change="searchChange" class="flex-1">
                            <van-tab v-for="item, index in typeTabList" :name="item.value" :title="item.label"
                                :key="index"></van-tab>
                        </van-tabs>
                        <!-- <div class="price-type text-30px text-#333 ml-20px leading-1">
                            <span class="mr-10px">价格</span>
                            <van-icon name="arrow-down" color="#AEABAB"></van-icon>
                        </div> -->
                    </div>

                    <div class="filter-types flex items-center" @click="showFilter = true">
                        <div class="mr-10px text-28px">分类</div>
                        <img loading="lazy" src="@/assets/inclusive/shop/icon_fl.png" class="w-26px block" />
                    </div>
                </div>
                <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
                    <div class="flex flex-wrap justify-between">
                        <div v-for="(item, index) in list" :key="index"
                            class="w-336px mt-20px box-border overflow-hidden">
                            <goodsListCell :content="item"></goodsListCell>
                        </div>
                    </div>
                </refreshList>
            </div>
        </div>
        <!-- 筛选弹窗 -->
        <filterPopup v-model:show="showFilter" :filterSearch="inclusiveProductColumn" :typeArry="productCategory"
            @confirm="filterConfirm">
        </filterPopup>
        <!-- 气泡效果 -->
        <div class="fixed left-50% top-50% -translate-50% z-100">
            <waterIntergral v-model:show="showBubble" :score="scoreNum" bigSize="150px" midSize="62px" smallSize="50px"
                scorefontSize="40px"></waterIntergral>
        </div>
    </div>
</template>
<script lang="ts" setup>
import utils from '@/utils/utils';
import { inclusiveMerchantsDetails, inclusiveGoodsList, inclusiveGoodsSales } from '@/api/mall/inclusive'
import { collectOperate } from "@/api/public"
import { showToast } from 'vant';
const waterIntergral = defineAsyncComponent(() => import('@/components/Bubble/waterIntergral.vue'))
const filterPopup = defineAsyncComponent(() => import('../components/filterPopup.vue'))

import goodsListCell from '../components/goodsCell.vue';
import refreshList from '@/components/refreshList/index.vue';
import { useDictionary } from '@/store/modules/dictionary';
const dictionaryStore = useDictionary()

const tabActive = ref('0')
// tab标签列表
const typeTabList = computed(() => {
    return [
        {
            label: '销量',
            value: '0',
        },
        {
            label: '新品',
            value: '1',
        },
    ]
})
// 商品分类
const productCategory = computed(() => dictionaryStore.getDictionaryOpt?.['inclusiveProductColumn'])
const showFilter = ref(false)
const inclusiveProductColumn = ref('')
const filterConfirm = (val: any) => {
    inclusiveProductColumn.value = val
    onRefreshList()
}

const route = useRoute()
const companyId = route.query?.companyId as string
const merchantInfo = ref<any>({})
const loading = ref(true)
const getInfo = () => {
    inclusiveMerchantsDetails(companyId).then(({ code, data }) => {
        if (code === 200) {
            merchantInfo.value = data
        }
        loading.value = false
    })
        .catch(() => {
            loading.value = false
        })
}
const list = ref<any>([]);
let pageNum = 1;

const searchChange = () => {
    onRefreshList()
}
const loadMoreRef = ref()
// 刷新
const onRefreshList = () => {
    pageNum = 1
    loadMoreData()
}
// 加载更多
const onLoadMore = () => {
    pageNum++
    loadMoreData()
}
const loadMoreData = async () => {
    const params: any = {
        companyId,
        pageSize: 10,
        pageNum,
        inclusiveProductColumn: inclusiveProductColumn.value
    }
    let resCode, resTotal, resData

    if (tabActive.value === '0') {
        const { code, data, total } = await inclusiveGoodsSales(params)
        resCode = code
        resTotal = total
        resData = data
    }
    else if (tabActive.value === '1') {
        params.orderBy = 'create_time'
        params.sortType = 'desc'
        params.systemQueryType = 'h5'

        const { code, data, total } = await inclusiveGoodsList(params)
        resCode = code
        resTotal = total
        resData = data
    }


    if (pageNum === 1) list.value = []
    if (resCode === 200) {
        if (pageNum === 1) list.value = resData
        else list.value = [...list.value, ...resData]
    }
    //重置刷新状态及 判断是否加载完成
    if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(list.value.length, resTotal)
    }
}


// 收藏
// 气泡提示框参数设置
const showBubble = ref(false);
const scoreNum = ref(0);
let isReq = false;
const collectFn = async () => {
    if (isReq) return
    isReq = true
    const { code, data } = await collectOperate({ sourceId: companyId })
    isReq = false

    if (code !== 200) return showToast('操作失败')
    const { statefulFlowState, score } = data
    if (statefulFlowState === true) {
        merchantInfo.value.whetherCollect = true
        merchantInfo.value.collectCount = merchantInfo.value.collectCount + 1
    }
    else if (statefulFlowState === false) {
        merchantInfo.value.whetherCollect = false
        merchantInfo.value.collectCount = merchantInfo.value.collectCount - 1
    }

    if (score) {
        scoreNum.value = score
        showBubble.value = true;
    }
}
// 导航
const toAddress = () => {
    utils.mapNav({
        win: route.fullPath,
        name: merchantInfo.value?.address,
        lat: merchantInfo.value?.addressCoordinate?.split(',')[1] || '',
        lon: merchantInfo.value?.addressCoordinate?.split(',')[0] || ''
    })
}
// 拨打电话
const contactPhone = () => {
    utils.getTel({
        number: merchantInfo.value?.contractPhone
    })
}
onMounted(() => {
    getInfo()
    loadMoreData()
})
</script>
<style scoped lang="scss">
.merchant-banner {
    background-image: url('@/assets/inclusive/shop/merchant-banner.png');
    background-size: 100% auto;
    background-repeat: no-repeat;
}

.merchant-card {
    .merchant-card-cover {
        border: 1px solid #FF4344;
    }

    .merchant-card-phone {
        border: 1px solid #FF5237;
    }

    .cell-1 {
        border-bottom: 1px solid #EFEFEF;
        padding-bottom: 25px;
    }

    .inclusive-coupon-1 {
        color: #FE3C5E;
        background-color: #FFF0F3;
        border: 1px solid #FE3C5E;
    }

    .inclusive-coupon-2 {
        color: #FF6342;
        background-color: #FFF1E7;
        border: 1px solid #FF6342;
    }
}

.goods-list {
    .header {
        .search {
            border: 2px solid #FF4344;
            background-color: #fff;

            :deep(.van-cell) {
                padding: 0;
                height: 100%;

            }

            :deep(.van-field__body) {
                height: 100%;
                font-size: 24px;
            }

            --van-field-clear-icon-size: 25px;
        }
    }

    .tab-list {
        :deep(.van-tabs--line .van-tabs__wrap) {
            height: 80px;
        }

        :deep(.van-tab) {
            line-height: 1;
            height: 100%;
            font-size: 30px;
        }

        :deep(.van-tab--active) {
            font-size: 32px;
        }

        :deep(.van-tabs__nav--line) {
            height: 100%;
            padding-bottom: 15px;
        }

        :deep(.van-tabs__line) {
            background-color: #FF4344;
            width: 40px;
            height: 6px;
            bottom: 15px;
            border-radius: 3px
        }

        // :deep(.van-tab__text) {
        //     font-size: 28px;
        // }
    }
}
</style>