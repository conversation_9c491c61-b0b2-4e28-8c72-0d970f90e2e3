import { defineStore } from 'pinia';
import { DictionaryModal } from '@/api/dictionary';
import { has } from 'lodash-es';
import { queryDictionary } from '@/api/dictionary';
interface DictionaryStore {
  dictionaryMap: Nullable<Recordable>;
  dictionaryOBJmap: Nullable<Recordable>;
  dictionaryOpt: Nullable<Recordable>;
}

export const useDictionary = defineStore({
  id: 'dictionary',
  persist: true,
  state: (): DictionaryStore => ({
    dictionaryMap: null,
    dictionaryOBJmap: null,
    dictionaryOpt: null,
  }),
  getters: {
    getDictionaryMap(state): Nullable<Recordable> {
      return state.dictionaryMap;
    },
    getDictionaryOBJMap(state): Nullable<Recordable> {
      return state.dictionaryOBJmap;
    },
    getDictionaryOpt(state): Nullable<Recordable> {
      return state.dictionaryOpt;
    },
  },
  actions: {
    setDictionaryMap(map: Nullable<Recordable>) {
      this.dictionaryMap = map;
    },
    setDictionaryOBJMap(map: Nullable<Recordable>) {
      this.dictionaryOBJmap = map;
    },
    setDictionaryOptMap(map: Nullable<Recordable>) {
      this.dictionaryOpt = map;
    },
    async setDictionary(res: DictionaryModal[]): Promise<void> {
      const map: Recordable = {};
      const objMap: Recordable = {};
      const optMap: Recordable = {};

      res.map(v => {
        map[v.groupCode + '_' + v.dictCode] = v;

        if (has(objMap, v.groupCode as string)) {
          objMap[v.groupCode]?.push(v);
          //选择框
          optMap[v.groupCode]?.push({
            label: v.dictName,
            value: v.dictCode,
          });
        } else {
          const arr: DictionaryModal[] = [];
          const optArr: Recordable[] = [];
          arr.push(v);
          optArr.push({
            label: v.dictName,
            value: v.dictCode,
          });
          objMap[v.groupCode] = arr;
          optMap[v.groupCode] = optArr;
        }
      });

      this.setDictionaryMap(map);
      this.setDictionaryOBJMap(objMap);
      this.setDictionaryOptMap(optMap);
    },
    // 请求字典
    async getQueryDictionary(){
      queryDictionary().then(res => {
        this.setDictionary(res);
      })
    }
  },
});
