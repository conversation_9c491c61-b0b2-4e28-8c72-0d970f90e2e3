<template>
    <div class="rules bg-[#6db247]">
        <!-- 活动交互描述 -->
        <div class="static_rule bg-[#6db247]">
            <img loading="lazy" src="@/assets/tree/intergral_rule_1.jpg" alt="" class="w-full block" />
            <img loading="lazy" src="@/assets/tree/intergral_rule_2.jpg" alt="" class="w-full block" />
        </div>
        <!-- 活动规则 -->
        <div class="rule_view px-60px box-border -mt2px relative">
            <div class="rule_header mx-auto flex items-center justify-center
            text-36px text-[#fff] font-bold leading-0">
                活动规则
            </div>
            <div class="introduce relative z-2">
                <div class="text-[#609358] text-32px font-bold title relative">活动玩法
                </div>
                <div v-html="rules.introduce" v-if="rules.introduce"></div>
                <div v-else>-</div>
            </div>
            <div class="rule relative z-2">
                <div class="text-[#609358] text-32px font-bold title relative">换和发货说明

                </div>
                <div v-html="rules.rule" v-if="rules.rule"></div>
                <div v-else>-</div>
            </div>
            <div class="absolute left-0 right-0 bottom-0 flex items-end z-1">
                <img loading="lazy" src="@/assets/tree/intergral_rule_5.png" class="w-full" />
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
defineOptions({
    name: 'integralTreeRule'
})
import { treeAcDetail } from "@/api/integralTree";
onMounted(() => {
    getRules();
});
const rules = ref<any>({});
const getRules = () => {
    treeAcDetail().then((res) => {
        rules.value.introduce = res.data?.activityContent;
        rules.value.rule = res.data?.activityRules;
    })
}

</script>
<style scoped lang="scss">
.rule_view {
    width: 100%;
    min-height: 521px;
    height: fit-content;
    background-image: url('@/assets/tree/intergral_rule_3.png'),
        url('@/assets/tree/intergral_rule_4.png');
    background-position: 0px 0px, 0% 300px;
    background-repeat: no-repeat, repeat-y;
    background-size: 750px 304px, 750px 487px;
    background-color: #6db246;
    padding-bottom: 98px;
    padding-top: 26px;

    .rule_header {
        width: 274px;
        height: 67px;
        background: linear-gradient(0deg, #B4D15D 0%, #8AEF5B 100%);
        border-radius: 0 0 35px 35px;
        padding-bottom: 2px;
    }

    .title::after {
        display: block;
        content: ' ';
        width: 136px;
        height: 9px;
        background: linear-gradient(0deg, #B4D15D 0%, #8AEF5B 100%);
        opacity: 0.3;
        margin-top: -5px;
    }
}
</style>