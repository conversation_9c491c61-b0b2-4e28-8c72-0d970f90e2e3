<template>
    <div class="yearGift flex flex-col">
        <div class="header flex items-center justify-center" style="flex:0.2">
            <img loading="lazy" src="@/assets/yearGift/benefit_title.png" alt="" class="w-60%" />
        </div>
        <div class="coupons mx-54px relative" style="flex:0.6">
            <img loading="lazy" src="@/assets/yearGift/coupon_border.png" class="w-full" />
            <div class="absolute left-0 right-0px top-60px bottom-160px mx-65px overflow-scroll-y">
                <refreshList key="relist" @onRefreshList="onRefreshList" ref="loadMoreRef">
                    <div class="tips text-right text-#999 text-20px mb-10px">下滑查看更多</div>
                    <div class="coupon-items relative mb-20px" v-for="item, index in list" :key="index">
                        <img loading="lazy" src="@/assets/yearGift/coupon_bg.png" class="w-full" />
                        <div class="absolute left-0 w-full top-0 h-full
                        flex justify-between items-center text-#fff">
                            <div class="left flex items-baseline justify-center flex-1 px-10px flex-wrap">
                                <template v-if="item.couponType === 'noLimit' || item.couponType === 'fullDecrement'">
                                    <div class="text-35px">￥</div>
                                    <div class="text-60px">{{ item.discountAmount || '-' }}</div>
                                </template>
                                <template v-else-if="item.couponType === 'noLimit'">
                                    <div class="text-35px">￥</div>
                                    <div class="text-60px">30</div>
                                </template>
                                <template v-else-if="item.couponType === 'discount'">
                                    <div class="text-60px">{{ item.discountPercent || '-' }}</div>
                                    <div class="text-35px">折</div>
                                </template>
                            </div>
                            <div class="right text-center flex-1 px-10px flex-wrap">
                                <div class="text-26px leading-none">{{ item.couponName }}</div>
                                <div class="text-24px mt-10px leading-none">
                                    <template
                                        v-if="item.couponType === 'fullDecrement' || item.couponType === 'discount'">
                                        满{{ item.amountLimit || '-' }}可使用
                                    </template>
                                </div>
                                <div class="bg-#fff text-#f1495b text-22px leading-none
                                rounded-20px py-10px px-5px mt-15px mx-25px" @click="receiveCoupon(item)">立即领券</div>
                            </div>
                        </div>
                    </div>
                </refreshList>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
defineOptions({
    name: 'yearGiftBenefits'
})
import { BirthdayCardCouponList, getCardCoupon } from "@/api/yearGift";
import { showToast } from 'vant'
import refreshList from '@/components/refreshList/index.vue';
onMounted(() => {
    getCouponList();
});
const list = ref<any>([]);
const loadMoreRef = ref<any>(null);
const onRefreshList = () => {
    getCouponList();
}
const getCouponList = async () => {
    const { code, data } = await BirthdayCardCouponList()
    if (code === 200) list.value = data
    else {
        list.value = []
    }
    loadMoreRef.value.onLoadSuc(list.value.length, list.value.length)
};
// 领取优惠券
const receiveCoupon = (item: any) => {
    getCardCoupon({
        couponId: item.couponId,
        platform: 'app'
    }).then((res) => {
        if (res.code === 200) {
            showToast('领取成功')
            getCouponList()
        }
        else showToast(res.message ? res.message : '领取失败')
    })
}
</script>

<style scoped lang="scss">
.yearGift {
    background-image: url("@/assets/yearGift/benefit_bg.png");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}
</style>