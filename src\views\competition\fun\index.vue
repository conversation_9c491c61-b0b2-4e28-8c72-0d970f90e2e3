<template>
  <div class="bg-#F5F5F5 min-h-100vh">
    <img loading="lazy" :src="banner" alt="" class="w-100% h-280px block" />


    <div class="tab-box">
      <van-tabs v-model:active="Data.tab.active" sticky line-width="20" @click-tab="onClickTab">
        <van-tab :title="item.name" v-for="(item, index) in Data.tab.nav" :key="index"
          title-class="tab-title"></van-tab>
      </van-tabs>
    </div>
    <div class="p-30px box-border" v-if="Data.tab.active === 0">
      <refreshList key="actList" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
        <List :data="Data.newsList" @toNewsDetail="toNewsDetail"></List>
      </refreshList>

    </div>
    <div class="p-30px box-border" v-else-if="Data.tab.active === 1">
      <div class="two-tab-box mb-30px">
        <van-tabs type="card" v-model:active="Data.twoTab.active" sticky line-width="20" @click-tab="onClickTwoTab">
          <van-tab :title="item.name" v-for="(item, index) in Data.twoTab.nav" :key="index"
            title-class="tab-title"></van-tab>
        </van-tabs>
      </div>
      <refreshList key="actList" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
        <div v-for="(item, index) in Data.activityList" @click="toDetail(item)" :key="index"
          class="bg-#fff px-24px py-22px box-border rounded-16px mb-20px relative">
          <div class=" flex items-center h-100%">
            <img loading="lazy" :src="item.appCover ? item.appCover : defaultImg"
              class="w-200px h-136px rounded-16px mr-25px" alt="">
            <div class="flex flex-col justify-around min-h-136px w-full">
              <div class="text-#333333 text-28px text-ellipsis line-clamp-2 whitespace-nowrap text-wrap">{{
                item.activityName }}</div>
              <div class="flex items-center justify-between text-#999999 text-22px">
                <div class="flex items-center mr-46px">
                  <img loading="lazy" src="@/assets/vitality/volunteerService/icon_bmsj.png" alt=""
                    class="w-22px h-22px mr-10px" />{{ item.activityStartTime }} - {{ item.activityEndTime }}
                </div>
                <div class="flex items-center">
                  <van-icon name="eye-o" size="15" class="z-1 mr-10px" color="#999999" />
                  {{ item.readCount }}
                </div>
              </div>
            </div>
          </div>
          <!-- <div class="text-#999999 text-22px flex items-center">
                <img loading="lazy" src="@/assets/vitality/volunteerService/icon_bmsj.png" alt="" class="w-22px h-22px mr-10px" />{{ item.activityStartTime }} - {{ item.activityEndTime }}
              </div> -->
          <div class="flex items-center w-full mt-28px justify-between" v-if="item.signUpInfo">
            <div class="text-#999999 text-22px">报名人数：<span class="text-#4489FF">{{ item.signUpInfo.signUpCount
                }}</span>/{{ item.signUpInfo.maxCount }}</div>
            <div class="text-26px px-30px py-10px box-border rounded-23px text-#fff"
              :style="{ background: item.progress === '2' ? 'linear-gradient(90deg, #5BA5FF, #5CB6FF)' : '#CCCCCC' }">
              {{ item.progress === '3' ? '报名结束' : '立即报名' }}
            </div>
          </div>
          <div
            class="absolute left-0 top-0 text-#FFFFFF text-24px px-14px py-6px box-border rounded-ss-16px rounded-br-16px"
            :style="{ background: item.progress === '1' ? 'linear-gradient(90deg, #FF8F4F 0%, #FF6D19 100%)' : item.progress === '2' ? ' linear-gradient(90deg, #80DD9D 0%, #4AC67E 100%)' : 'linear-gradient(90deg, #CCCCCC 0%, #999999 100%)' }">
            {{ item.progressMsg }}
          </div>
        </div>
      </refreshList>
    </div>
    <div v-else-if="Data.tab.active === 2">
      <refreshList key="actList" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
        <div class="p-30px box-border w-100% flex items-center flex-wrap">

          <div v-for="(item, index) of Data.newsList" :key="index" class="cgzs-item-box bg-#fff rounded-16px mb-20px"
            :class="{ 'mr-20px': index % 2 == 0 }" @click="toNewsDetail(item)">
            <div class="h-198px w-100% relative">
              <!-- 视频类型 -->
              <div
                class="absolute rounded-16px w-100% h-100% left-0 top-0 bg-[rgba(0,0,0,.3)] flex justify-center items-center"
                v-if="item?.displayType == 'videoCoverImage'">
                <img loading="lazy" src="@/assets/competition/icon_play.png" alt="" class="w-60px h-60px">
              </div>
              <img loading="lazy" :src="item.newsCoverUrl" alt="" class="h-100% w-100% rounded-16px" />
            </div>
            <div class="px-15px box-border">
              <div class="text-ellipsis line-clamp-2 whitespace-nowrap text-wrap my-15px text-#3F4853 text-28px">
                {{ item.newsTitle }}
              </div>
            </div>
          </div>
        </div>
      </refreshList>
    </div>

  </div>
</template>
<script lang="ts" setup>
import banner from "@/assets/competition/qwjs_banner.jpg";
import List from "../components/List.vue";
import refreshList from "@/components/refreshList/index.vue";
import { activityInfoList } from "@/api/activity.ts";
import { getCategoryInfo, getNewsList } from '@/api/news/index';
import utils from "@/utils/utils";
import { readOperate } from '@/api/public';
import { toDetail } from "@/hooks/useValidator";

import defaultImg from "@/assets/competition/default.jpg";
const Data = ref({
  tab: {
    active: 0,
    nav: [
      { name: "承办报名", code: 'cheng_ban_bao_ming' },
      { name: "竞赛报名", },
      { name: "成果展示", code: 'cheng_guo_zhan_shi' }
    ],
  },
  //   status: 1-未开始  2-进行中  3-已结束
  list: [{ cover: banner, status: '1' }, { cover: banner, status: '2' }, { cover: banner, status: '3' }],
  twoTab: {
    active: 0,
    nav: [
      { name: '全部', value: '' },
      { name: '未开始', value: '1' },
      { name: '进行中', value: '2' },
      { name: '已结束', value: '3' },
    ]
  },
  activityList: [],
  pageNum: 1,
  activityCategory: 'funCompetition',
  tabData: {},
  newsList: []
})
function onClickTab(val: { name: number }) {
  Data.value.tab.active = val.name;
  onRefreshList()
}
function onClickTwoTab(val: { name: number }) {
  Data.value.twoTab.active = val.name;
  Data.value.pageNum = 1;
  getActList()
}
//获取栏目
async function getColumn() {
  getCategoryInfo({
    categoryCode: Data.value.tab.nav[Data.value.tab.active].code,
    platformType: 30,
  }).then(res => {
    Data.value.tabData = res.data;
    getLists();
  });
}
//新闻列表
async function getLists() {
  let res = await getNewsList({
    categoryCode: Data.value.tabData?.categoryCode,
    platformType: 30,
    pageNum: Data.value.pageNum,
    pageSize: 10,
  });
  res.data?.forEach((item: Recordable) => {
    item.newsCoverUrl = utils.judgeStaticUrl(item.newsCoverUrl)
  })
  if (Data.value.pageNum === 1) Data.value.newsList = [];
  Data.value.newsList = Data.value.newsList.concat(res.data);
  //重置刷新状态及 判断是否加载完成
  if (loadMoreRef.value) {
    loadMoreRef.value.onLoadSuc(Data.value.newsList.length, res.total);
  }
}
onMounted(() => {
  getColumn()
});
//详情
function toNewsDetail(item) {
  if (item.content?.whetherExternalLink) {
    readOperate({ sourceId: item?.newsId }).then(res => { })
  }
  utils.openNewsLink({
    title: item?.newsTitle,
    url: item?.externalLinkAddress,//window.location.origin+'/newsDetail?newsId='+props.content?.newsId+'&categoryCode='+props?.categoryCode,
    shareTitle: item?.newsTitle,
    shareUrl: item?.externalLinkAddress,//window.location.origin+'/newsDetail?newsId='+props.content?.newsId+'&categoryCode='+props?.categoryCode,
    dataId: item?.newsId,
  }, item?.whetherExternalLink, item?.categoryCode)

}
//获取活动列表
const loadMoreRef = ref(null)
function getActList() {
  activityInfoList({
    activityCategory: Data.value.activityCategory,
    progress: Data.value.twoTab.nav[Data.value.twoTab.active].value ?? undefined,
    pageSize: 10,
    pageNum: unref(Data).pageNum,
  }).then(res => {
    if (res.code == 200) {
      if (Data.value.pageNum === 1) {
        Data.value.activityList = [];
      }
      res.data = res.data?.map(t => {
        const { activityStartTime, activityEndTime } = t
        t.activityStartTime = activityStartTime?.split(' ')[0] ?? ''
        t.activityEndTime = activityEndTime?.split(' ')[0] ?? ''
        return t
      })
      Data.value.activityList = Data.value.activityList.concat(res.data);
      //重置刷新状态及 判断是否加载完成
      if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(Data.value.activityList.length, res.total);
      }
    }

  })
}
// 刷新
const onRefreshList = () => {
  Data.value.twoTab.active = 0
  Data.value.pageNum = 1;
  if (Data.value.tab.active === 1) {
    getActList();
  } else {
    getColumn()
  }
};
// 加载更多
const onLoadMore = () => {
  Data.value.pageNum++;
  if (Data.value.tab.active === 1) {
    getActList();
  } else {
    getColumn()
  }
};

</script>
<style lang="scss" scoped>
.tab-box {
  :deep(.van-tab) {
    padding-bottom: 20px;
  }

  :deep(.van-tabs__line) {
    background: linear-gradient(86deg, #5aa4ff 0%, #c7e0ff 100%);
    border-radius: 3px;
    bottom: 50px;
  }

  :deep(.tab-title) {
    font-weight: 400;
    font-size: 30px;
    color: #808080;
  }

  :deep(.van-tab--active) {
    font-weight: 500;
    font-size: 32px;
    color: #333333;
  }
}

.two-tab-box {
  :deep(.van-tabs__nav--card) {
    border-radius: 29px !important;
    margin: 0 !important;
    border: none !important;
    padding: 6px;
    box-sizing: border-box;


  }

  :deep(.van-tab--card) {
    border-right: none !important;
  }

  :deep(.van-tab--active) {
    border-radius: 29px !important;
    color: #FFFFFF !important;

  }

  :deep(.tab-title) {
    font-weight: 400;
    font-size: 26px;
    color: #666666;
  }
}

.cgzs-item-box {
  width: calc((100% - 20px) / 2);
}
</style>
