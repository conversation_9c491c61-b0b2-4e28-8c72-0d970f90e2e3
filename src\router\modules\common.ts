import { KeepAlive } from 'vue';

export default [
    {
        path: '/paySuccess',
        name: 'PaySuccess',
        component: () => import('@/views/common/paySuccess.vue'),
        meta:{
            title:'支付成功',
            isShowTabBar: false,
            isBack:true
        }
    },
    {
        path: '/nestedWindow',
        name: 'nestedWindow',
        component: () => import('@/views/public/nestedWindow.vue'),
        meta:{
            title:'我的窗口',
            isShowTabBar: false,
            isBack:true,
        }
    }
]