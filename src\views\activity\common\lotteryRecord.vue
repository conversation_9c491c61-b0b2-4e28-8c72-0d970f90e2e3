<template>
  <div class="prize-records bg-[#F9F9F9] w-full overflow-x-hidden flex flex-col">
    <div class="tab-box h-100px bg-white sticky top-0 z-99" v-if="tabs && tabs.length > 1">
      <van-tabs v-model:active="active" sticky line-width="20" @click-tab="onClickTab">
        <van-tab :title="item.label" v-for="item in tabs" :key="item.value" :name="item.value"
          title-class="tab-title"></van-tab>
      </van-tabs>
    </div>
    <div class="two-tab-box h-100px bg-white p-30px py-20px box-border" v-if="active == 7" :class="{'mt-36px':tabs && tabs.length > 1}">
      <van-tabs type="card" v-model:active="twoTab.active" sticky line-width="20" @click-tab="onClickTwoTab">
        <van-tab :title="item.name" v-for="(item, index) in twoTab.nav" :key="index" title-class="tab-title"></van-tab>
      </van-tabs>
    </div>
    <div class="prizeRecord-container bg-#fff p-30px box-border mt-20px flex-1">
      <div class="prize-records-tabs">
        <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
          <div class="tasklist-card flex justify-between items-center  bg-[#fff] mb-25px"
            v-for="item in prizeRecordList" :key="item.autoId">
            <!-- 红包 -->
            <template v-if="item.prizeType == '5'">
              <div @click="toDrawMoney(item)"
                class="flex justify-between items-center w-full p-20px py-25px bg-#f7f8f9 rounded-15px">
                <div class="flex justify-center items-center">
                  <div class="tasklist-card-left">
                    <img loading="lazy" src="@/assets/activity/public/icon_choujianghongbao.png" alt="" />
                  </div>
                  <div class="tasklist-card-middle">
                    <div>{{ item.prizeName }} </div>
                    <div>{{ item.createTime }} </div>
                  </div>
                </div>
                <div class="tasklist-card-right">
                  <div class="flex justify-center items-center">+{{ item.prizeContent }}元</div>
                  <div :class="{
                    received: item.receiveState == '1',
                    flex: true,
                    'justify-center': true,
                    'items-center': true,
                  }">
                    <template v-if="item.receiveState === '1'"> 已领取 </template>
                    <template v-if="item.receiveState === '0'">未领取</template>
                  </div>
                </div>
              </div>
            </template>
            <!-- 积分 -->
            <template v-if="item.prizeType == '2'">
              <div @click="toDrawMoney(item)"
                class="flex justify-between items-center w-full p-20px py-25px bg-#f5f5f5 rounded-15px">
                <div class="flex justify-center items-center">
                  <div class="tasklist-card-left">
                    <img loading="lazy" src="@/assets/activity/public/integral-icon.png" alt="" />
                  </div>
                  <div class="tasklist-card-middle">
                    <div>{{ item.prizeName }} </div>
                    <div>{{ item.createTime }} </div>
                  </div>
                </div>
                <div class="tasklist-card-right">
                  <div class="flex justify-center items-center">+{{ item.prizeContent }}</div>
                </div>
              </div>
            </template>
            <!-- 实物奖品 -->
            <template v-if="item.prizeType == '3'">
              <!-- 自提 -->
              <div class="w-full p-20px py-25px bg-#f5f5f5 rounded-15px" v-if="item.receiveType === '1'">
                <div class="flex flex-1 items-center mb-20px">
                  <div class="w-120px h-120px mr-20px flex-shrink-0">
                    <img loading="lazy" :src="item.prizeImg ? judgeStaticUrl(item.prizeImg) : prize_img" alt=""
                      class="w-full h-full rounded-14px" />
                  </div>
                  <div class="w-full">
                    <div class="text-31px text-[#333] font-550 mb-20px flex justify-between mb-20px">
                      <div class="leading-38px">{{ item.prizeName || '-' }}</div>
                    </div>
                    <div class="text-29px text-[#6D6D6D]"> 中 奖 人 ： <span>{{ item.userName }}</span>
                    </div>
                    <div class="text-29px text-[#6D6D6D]">中奖时间：<span>{{ item.createTime }}</span>
                    </div>
                  </div>
                </div>
                <div
                  class="w-full text-25px text-[#999] pt-20px leading-40px border-t-1px border-t-[#e0e0e0] border-t-dashed">
                  <div class="text-25px text-[#6D6D6D] mb-15px">
                    * 线下自提奖品，具体领取规则见活动详情
                  </div>

                </div>
              </div>
              <!-- 邮寄 -->
              <div class="w-full p-20px bg-#f5f5f5 rounded-15px" v-else>
                <div class="flex flex-1 items-center">
                  <div class="w-150px h-150px mr-20px flex-shrink-0">
                    <img loading="lazy" :src="item.prizeImg ? judgeStaticUrl(item.prizeImg) : prize_img" alt=""
                      class="w-full h-full rounded-14px" />
                  </div>
                  <div class="w-full">
                    <div class="text-31px text-[#333] font-550 mb-20px flex justify-between mb-20px">
                      <div class="leading-38px">{{ item.prizeName || '-' }}</div>
                    </div>
                    <div class="text-29px text-[#6D6D6D] mb-15px">中奖时间：<span>{{ item.createTime }}</span>
                    </div>
                    <div class="flex flex-col justify-between items-center text-29px text-[#6D6D6D]">
                      <div class="flex items-center justify-between w-full">
                        <div>
                          发货状态：<span class="text-[#FF7E00]">{{ item.remark || '未发货' }}</span>
                        </div>
                        <van-button
                          class="w-190px h-40px rounded-20px text-[#fff] text-24px address-btn p-0 border-0 flex-shrink-0"
                          @click.stop="toEditAddress(item)" v-if="!item.consignee">
                          填写收货地址
                        </van-button>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="w-full text-25px text-[#999] pt-20px leading-40px" v-if="item.consignee">
                  <div><van-icon name="phone-o" />
                    {{ item.consignee }} {{ item.consigneeMobile }}</div>
                  <div><van-icon name="location-o" />
                    {{ item.provinceCode }} {{ item.address }}</div>
                </div>
              </div>
            </template>
            <!-- 普惠票券 -->
            <template v-if="active == '7'" class="p-30px ">
              <div class="mycoupon-card relative w-full border rounded-26px border-solid "
                :class="item.myCouponState ? 'border-#CCCCCC' : 'border-#FF8167'" @click="useCoupon(item)">
                <div class="mycoupon-card-title flex justify-between">
                  <div class="w-full flex card-title-container ">
                    <div class="coupon-money flex justify-center flex-col items-center w-26%"
                      :class="{ overDate: item.myCouponState }">
                      <div :class="{ overDate: item.myCouponState }" class="text-right">
                        <template v-if="item.couponInfo.couponType === 'discount'">
                          <span :class="{ overDate: item.myCouponState }"> {{ Number(item.couponInfo.discountPercent)
                            || '-' }} <span class="!text-28px" :class="{ overDate: item.myCouponState }">折</span></span>
                        </template>
                        <template v-else>
                          <!-- <span class="!text-36px" :class="{ overDate: item.myCouponState }">¥</span> -->
                          <span :class="{ overDate: item.myCouponState }">{{ Number(item.couponInfo.discountAmount) ||
                            '-' }}</span>
                        </template>
                      </div>

                    </div>
                    <div class="p-26px flex w-74%   pl-40px box-border"
                      :class="item.myCouponState ? 'bg-used' : 'bg-wait'">
                      <div class="flex-shrink-0 w-72%">
                        <div class="mycoupon-card-name mb-5px truncate" :class="{
                          overDate: item.myCouponState
                        }">
                          {{ item.couponName || '-' }}
                        </div>
                        <div class="text-#FF4344 font-500 text-26px truncate my-8px"
                          :class="{ overDate: item.myCouponState }">
                          {{ item.couponInfo.couponType === 'noLimit' ? '无门槛优惠券' :
                            `满${Number(item.couponInfo.amountLimit)}减${Number(item.couponInfo.discountAmount)}元` }}
                          {{
                            item.couponInfo.couponType === 'discount' && item.couponInfo.discountAmount ?
                              `，最高减${Number(item.couponInfo.discountAmount)}元` : ''
                          }}
                        </div>
                        <div class="mycoupon-card-state" :class="{ overDate: item.myCouponState }">
                          {{ item.useStartTime }}至{{ item.useEndTime || '-' }}
                        </div>
                      </div>
                      <div class=" mycoupon-card-bottom">
                        <van-button class="btn" v-if="!item.overDate && item.state === 'n'">
                          去使用
                        </van-button>
                      </div>
                    </div>

                  </div>
                </div>
                <!-- <div class="flex justify-between items-center mycoupon-card-bottom">
                <div> 在对应普惠商家按照核销规则进行消费抵扣</div>
                <van-button class="btn" v-if="!item.overDate && item.state === 'n'" @click="useCoupon(item)">
                  去使用
                </van-button>
                <div v-else-if="item.overDate" class="used">
                  {{ item.state === 'n' ? '已过期' : '已使用' }}
                </div>
              </div> -->
                <img loading="lazy" src="@/assets/activity/icon_expired.png" alt="" class="absolute w-124px h-115px bottom-0 right-0"
                  v-if="item.overDate && item.state === 'n'" />
                <img loading="lazy" src="@/assets/activity/icon_used.png" alt="" class="absolute w-124px h-115px bottom-0 right-0"
                  v-if="item.state === 'y'" />
              </div>
            </template>
          </div>
        </refreshList>
      </div>
    </div>

  </div>
</template>

<script setup>
defineOptions({
  name:'lotteryRecord'
})
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { findMyCouponList, getPrizeRecordList, addMailInfo } from '@/api/activity';
import refreshList from '@/components/refreshList/index.vue';
import { useUserStore } from "@/store/modules/user";
import prize_img from '@/assets/activity/public/prize.png'
const useStore = useUserStore();
const route = useRoute();
import { useMallStore } from '@/store/modules/mall';
import { showToast } from 'vant';
const mallStore = useMallStore();
import {judgeStaticUrl} from '@/utils/utils';
const active = ref('5')
/**
 *     {value:'2',label:'积分'},
 *     {value:'3',label:'实物奖品'},
 *     {value:'4',label:'普惠商品'},
 *     {value:'5',label:'红包'},
 *     {value:'6',label:'兑换奖品'},
 *     {value:'7',label:'优惠券'},
 * @type {*[]}
 */
const twoTab = ref({
  active: 0,
  nav: [
    { name: "待使用", code: '1' },
    { name: "已使用", code: '2' },
    { name: "已过期", code: '3' },
  ]
})
const defaultTypes = [
  { value: '5', label: '红包' },
  { value: '7', label: '优惠券' },
  { value: '3', label: '实物奖品' },
  { value: '2', label: '积分' },
]
const tabs = ref([])
// 响应式数据
const prizeRecordList = ref([]); // 中奖记录
const pageSize = 20;
const pageNum = ref(1);
const error = ref(false);
const loadMoreRef = ref()
const activityDetail = computed(() => useStore.activityDetail || {});
watch(activityDetail, () => {
  if (route.query.activityId || activityDetail.value.activityId) {
    getPrizeRecords();
  }
})

const onClickTab = (item) => {
  active.value = item.name;
  prizeRecordList.value = [];
  onRefreshList()
};
function onClickTwoTab(val) {
  twoTab.value.active = val.name;
  onRefreshList()
}
const addressInfo =ref(null)
onActivated(()=>{
  if (JSON.stringify(mallStore.getAddressInfo) !== '{}') {
    addressInfo.value = mallStore.getAddressInfo;
    getAddAddressInfo()
  }
})

// 生命周期钩子
onMounted(() => {
  console.log( '活动详情')
  //参数传入需要展示的奖品类型
  if (route.query.prizeTypes) {
    const split = route.query.prizeTypes.split(',')
    tabs.value = defaultTypes.filter(t => split.includes(t.value));

  } else {
    tabs.value = defaultTypes
  }
  active.value = route.query.active ?? tabs.value[0].value

  getPrizeRecords();
});
//新增地址信息
const getAddAddressInfo = async () => {
  let params = {
    consignee: addressInfo.value?.receiverName,
    consigneeMobile: addressInfo.value?.receiverPhone,
    provinceCode: addressInfo.value?.detailArea,
    address: addressInfo.value?.detailAddress,
    autoId: addressInfo.value?.id
  }
    const { code } = await addMailInfo(params); // 假设 yourApiFunction 是你的 API 调用函数
    if (code === 200) {
      showToast({
        message: '保存成功',
        duration: 1000,
        type: 'success',
      });
      mallStore.setAddressInfo({})
      getPrizeRecords();
    }
}
  

// 方法
const router = useRouter();
const toEditAddress = (item) => {
  router.push({
    path: '/address',
    query: { autoId: item.autoId },
  });
};
//去使用
const useCoupon = ({ couponId, recordId }) => {
  router.push({
    path: '/couponRecordDetail',
    query: { couponId, recordId },
  });
}

// 领取红包
const toDrawMoney = async (record) => {
  // 你的逻辑代码
};

// 刷新
const onRefreshList = () => {
  pageNum.value = 1;
  getPrizeRecords();
};
// 加载更多
const onLoadMore = () => {
  pageNum.value++;
  getPrizeRecords();
};

// 中奖记录列表
const getPrizeRecords = async () => {
  let res = {};
  //优惠券
  if (active.value === '7') {
    res = await findMyCouponList({
      activityId: route.query.activityId ?? useStore.activityDetail?.activityId,
      orderBy: 'create_time',
      sortType: 'desc',
      pageSize: pageSize,
      pageNum: pageNum.value,
      useState: twoTab.value?.nav[twoTab.value?.active].code
    })
    res.data = res.data.map(t => {
      if (new Date().format('yyyy-MM-dd') > t.useEndTime && t.state === 'n') {
        t.overDate = true
      }
      t.myCouponState = (t.overDate && t.state === 'n') || t.state === 'y'
      return t
    })
  } else {
    res = await getPrizeRecordList({
      userId: useStore.userInfo.userId,
      activityId: route.query.activityId ?? useStore.activityDetail.activityId,
      prizeType: active.value,
      orderBy: 'create_time',
      sortType: 'desc',
      pageSize: pageSize,
      pageNum: pageNum.value,
    });
  }
  const { code, data, total } = res
  if (code == 200) {
    if (pageNum.value === 1) {
      prizeRecordList.value = [];
    }

    prizeRecordList.value = prizeRecordList.value.concat(data);
    //重置刷新状态及 判断是否加载完成
    if (loadMoreRef.value) {
      loadMoreRef.value.onLoadSuc(prizeRecordList.value.length, total);
    }
  }
};
// onBeforeRouteLeave(to => {
//   router.getRoutes().forEach(item => {
//     // if(item.meta.keepAlive?.value){
//     //     item.meta.keepAlive.value=false
//     // }
//     if (item.name === 'lotteryRecord' && item.meta.keepAlive) {
//       item.meta.keepAlive.value = to.name === 'Address';
//     }
//     console.log(item);
    
//   });
// })

</script>

<style lang="scss" scoped>
.prize-records {
  min-height: 100%;
  // background-color: #ffffff;

  .prizeRecord-container {}

  .bg-used {
    background: url('@/assets/activity/bg_used.png') no-repeat;
    background-size: 100% 100%;
  }

  .bg-wait {
    background: url('@/assets/activity/bg-wait.png') no-repeat;
    background-size: 100% 100%;
  }

  .prize-records-tabs {
    .tasklist-card {
      width: 100%;
      // padding: 0px 10px;
      // margin-bottom: 25px;
      // border-bottom: 1px solid #e9e9e9;

      .tasklist-card-left {
        margin-right: 26px;

        img {
          width: 65px;
          height: 65px;
        }
      }

      .tasklist-card-middle {
        div {
          &:first-child {
            font-size: 31px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #222222;
          }

          &:last-child {
            margin-top: 10px;
            font-size: 24px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #999999;
          }
        }
      }

      .tasklist-card-right {
        div {
          &:last-child {
            font-size: 24px;
            font-family: Source Han Sans SC;
            font-weight: 400;
            color: #f6bb1b;
            margin-top: 10px;
          }

          &:first-child {
            font-size: 31px;
            font-family: Arial;
            color: #920005;
          }
        }

        .received {
          color: #999999;
        }
      }

      .mycoupon-card {
        // background: #ffffff;
        // box-shadow: -1px 3px 10px 0px rgba(148, 148, 148, 0.37);
        // border-radius: 14px;

        .mycoupon-card-title {
          // padding: 25px 20px;
          // padding-bottom: 0;

          .card-title-container {
            // border-bottom: 1px dashed #cacaca;
            // padding-bottom: 20px;
          }

          .mycoupon-card-name {
            font-size: 29px;
            font-family: Alibaba PuHuiTi;
            font-weight: 500;
            color: #FF4344;
          }

          .mycoupon-card-state {
            font-size: 24px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #FF4344;
            border-radius: 6px;
            padding: 2px 10px;
          }

          .coupon-money {
            div {
              &:first-child {
                line-height: 50px;
                font-size: 33px;
                font-family: Roboto;
                font-weight: bold;
                color: #ff4315;

                // background: linear-gradient(0deg, #ff0000 0%, #fb5840 100%);
                // -webkit-background-clip: text;
                // -webkit-text-fill-color: transparent;
                span {
                  font-size: 60px;
                  font-family: Arial;
                  font-weight: bold;
                  color: #ff4315;
                  // background: linear-gradient(0deg, #ff0000 0%, #fb5840 100%);
                  // -webkit-background-clip: text;
                  // -webkit-text-fill-color: transparent;
                }
              }

              &:last-child {
                font-size: 24px;
                font-family: Source Han Sans CN;
                font-weight: 400;
                color: #ed4921;
              }
            }
          }
        }

        .mycoupon-card-bottom {
          padding: 18px 20px;

          >div {
            &:first-child {
              font-size: 24px;
              font-family: Source Han Sans CN;
              font-weight: 400;
              color: #999999;
            }
          }

          .btn {
            width: 118px;
            height: 48px;
            background: #FF4344;
            border-radius: 26px;
            border: none;
            font-size: 24px;
            font-family: Source Han Sans CN;
            font-weight: 500;
            color: #ffffff;
            padding: 0;
          }

          .used {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 118px;
            height: 48px;
            background: #f5f5f5;
            border: 1px solid #999999;
            border-radius: 8px;
            font-size: 24px;
            font-family: Source Han Sans CN;
            font-weight: 500;
            color: #999999;
          }
        }

        .coupon-code {
          font-size: 35px;
          font-family: Alibaba PuHuiTi;
          font-weight: 500;
          color: #333333;
        }

        .overDate {
          color: #999999 !important;
        }
      }
    }
  }

  .two-tab-box {
    :deep(.van-tabs__nav) {
      background: transparent !important;
    }

    :deep(.van-tabs__nav--card) {
      border-radius: 29px !important;
      margin: 0 !important;
      border: none !important;
      box-sizing: border-box;
      height:90%;
    }

    :deep(.van-tab--card) {
      border-right: none !important;
    }

    :deep(.tab-title) {
      font-weight: 400;
      font-size: 30px;
      color: #666666;
    }    
    :deep(.van-tab--active) {
      border-radius: 29px !important;
      color: #5aa4ff !important;
      background: #EEF6FF !important;
      font-size: 32px;
    }
    :deep(.van-tabs__wrap) {
      height: 70px;
    }

    :deep(.van-tab) {
      line-height: 1;
      height: 100%;
    }
  }

  .gift-bottom {
    text-align: center;
    font-size: 25px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #4c4c4c;
    line-height: 40px;
  }

  :deep(.van-popup) {
    overflow-y: unset;
    width: 70%;
    border-radius: 42px;
    background-color: transparent;
  }

  .shortRecordId {
    font-size: 35px;
    font-family: Alibaba PuHuiTi;
    font-weight: 500;
    color: #333333;
    background: #fff3ed;
    border-radius: 11px;
    width: 347px;
    height: 58px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .address-btn {
    background: linear-gradient(-90deg, #FA4528 0%, #FF9454 100%);
    // color: #e0e0e0;
  }
  .tab-box{
    :deep(.van-tabs--line .van-tabs__wrap) {
      height: 100px;
    }

    :deep(.van-tab) {
      line-height: 1;
      height: 100%;
    }

    :deep(.van-tabs__nav--line) {
      height: 100%;
      padding-bottom: 15px;
    }

    :deep(.van-tabs__line) {
      background: linear-gradient(86deg, #5aa4ff 0%, #c7e0ff 100%);
      width: 54px;
      height: 6px;
      z-index: 1;
    }

    :deep(.van-tab__text) {
      font-size: 32px;
      z-index: 2;
    }
  }
}
</style>
