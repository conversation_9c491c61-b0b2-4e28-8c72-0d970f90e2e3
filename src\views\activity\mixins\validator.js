import { showDialog } from 'vant'
import {
  activityTimeValidator,
  qualificationValidator,
  voteUploadWordsTimeValidator,
} from '@/utils/actRulesValidator.js'
import { usePopup } from '@/hooks/hooks'
export default {
  computed: {
    activityDetail() {
      return this.$store.activityDetail
    },
    isLogin() {
      return !!this.$store.userInfo
    },
  },
  methods: {
    // 详情 登录 活动时间 地区校验
    activityValidator() {
      if (!this.validator()) return false
      let userInfo = this.$store.userInfo
      // 地区校验
      let { state, message } = qualificationValidator(this.activityDetail, userInfo)
      if (!state) {
        showDialog({
          title: '温馨提示',
          message,
          confirmButtonText: '我知道了',
          lockScroll: false,
        })
        return false
      }
      return true
    },
    validatorUploadWorks() {
      if (!this.validator()) return false
      const { state, message } = voteUploadWordsTimeValidator(this.activityDetail);
      
      if (!state) {
        showDialog({
          title: '温馨提示',
          message,
          confirmButtonText: '我知道了',
          lockScroll: false,
        })
        return false
      }
      return true
    },

    // 详情 登录 活动时间校验
    validator() {
      if (!this.activityDetail) {
        showDialog({
          title: '温馨提示',
          message: '当前活动太火爆,请稍后重试或尝试重新进入活动页面。',
          confirmButtonText: '我知道了',
          lockScroll: false,
        })
        return false
      }
      if (!this.isLogin) {
        showDialog({
          title: '温馨提示',
          message: '暂未查询到您的用户信息,请到川工之家APP注册或尝试重新登录。',
          confirmButtonText: '我知道了',
          lockScroll: false,
        })
        return false
      }
      // 活动时间
      let { state: result, message: msg } = activityTimeValidator(this.activityDetail)
      if (!result) {
        // Dialog({
        //   title: '提示',
        //   message: msg,
        //   confirmButtonText: '我知道了',
        //   lockScroll: false,
        // })
        let userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
        usePopup({ state: true, userInfo, message: msg })
        return false
      }
      return true
    },
    // 详情 登录校验
    defaultValidator() {
      if (!this.activityDetail) {
        showDialog({
          title: '温馨提示',
          message: '当前活动太火爆,请稍后重试或尝试重新进入活动页面。',
          confirmButtonText: '我知道了',
          lockScroll: false,
        })
        return false
      }
      if (!this.isLogin) {
        showDialog({
          title: '温馨提示',
          message: '暂未查询到您的用户信息,请到川工之家APP注册或尝试重新登录。',
          confirmButtonText: '我知道了',
          lockScroll: false,
        })
        return false
      }
      return true
    },
    // 活动详情校验
    activityDetailValidator() {
      if (!this.activityDetail) {
        showDialog({
          title: '温馨提示',
          message: '当前活动太火爆,请稍后重试或尝试重新进入活动页面。',
          confirmButtonText: '我知道了',
          lockScroll: false,
        })
        return false
      }
      return true
    },
    toLotteryRecordValidator() {
      if (!this.defaultValidator()) return
      this.$router.push('/common/lotteryRecord')
    },
  },
}
