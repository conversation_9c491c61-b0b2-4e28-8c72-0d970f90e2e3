<template>
  <van-popup v-model:show="props.show" position="center" @click-overlay="close">
    <div class="confirm w-690px m-auto h-1200px box-border px-40px pt-120px">
      <div class="my-40px font-[Source Han Sans CN] text-28px leading-6 h-780px overflow-y-auto">
        <div v-if="description?.explainList?.length"
          class="text-44px text-#333 ml-40px font-[YEFONTAoYeHei] leading-8 pb-20px">
          征集说明
        </div>
        <div class="pb-5 indent-[2em] leading-1.2em text-28px " v-for="item in description?.explainList">
          {{ item.content }}</div>
        <div v-if="description?.requirementList?.length"
          class="text-44px text-#333 ml-40px font-[YEFONTAoYeHei] leading-8 pb-20px">
          作品要求
        </div>
        <div class="pb-5 indent-[2em] leading-1.2em text-28px" v-for="item in description?.requirementList">
          {{ item.content }}</div>
      </div>
      <div class="text-28px text-#333">
        <!-- <van-radio-group v-model="checked">
          <van-radio :name="true">
            我已阅读
            <template #icon="props">
              <van-icon
                size="16"
                name="passed"
                :color="props.checked ? '#198DFA' : '#999'"
              />
            </template>
</van-radio>
</van-radio-group> -->
        <van-checkbox v-model="checked" :color="checked ? '#198DFA' : '#999'">
          <span :style="checked ? 'color:#198DFA' : 'color:#999'">我已阅读</span>
        </van-checkbox>
      </div>
      <div
        class="button w-400px h-85px text-white font-[Source Han Sans CN] box-border text-34px flex items-center justify-center cursor-pointer mt-30px mx-auto"
        @click="closePopup">
        继续发布
      </div>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
// 弹窗根据类型 展示不同模板样式
import { ref, watch } from 'vue';
import { uploadVideoDesc } from '@/api/video/index';
import { showFailToast } from 'vant';
import { useRouter } from 'vue-router';
const router = useRouter();

defineOptions({
  name: 'YearGiftPopup',
});
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['update:show']);

const checked = ref(false);
const description = ref<any>({});

watch(
  () => props.show,
  val => {
    if (val)
      uploadVideoDesc({})
        .then(({ code, data, message }) => {
          if (code === 200) {
            description.value = data;
          } else {
            closePopup();
            showFailToast(message || '获取作品要求失败');
          }
        })
        .catch(() => console.log('获取作品要求失败'));
  }
);

// 关闭弹窗
async function closePopup() {
  if (!checked.value) return showFailToast('请确认阅读并同意作品要求');
  router.push({ path: '/video-uploadWork' });
  emit('update:show', false);
}
const close = () => {
  emit('update:show', false);
};
</script>
<style scoped lang="scss">
.confirm {
  background: url(@/assets/video/ask_bg.png) no-repeat center/100% 100%;

  // 正常button样式
  .button {
    background: url(@/assets/video/add_btn_bg.png) no-repeat center/100% 100%;
  }

  --van-radio-checked-icon-color: transparent;
  --van-radio-border-color: transparent;

  :deep(.van-checkbox__label) {
    line-height: unset;
  }
}

.van-popup {
  // width: 100vw;
  background: transparent;
  // max-width: 100vw;
}
</style>
