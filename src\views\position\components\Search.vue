<template>
  <div class="p-[10px] pb-0 flex items-center">
    <van-field v-model="val" :center="true" :clearable="true" placeholder="请输入关键字" class="!py-1 w-5/6 !pr-10px">
      <template #button>
        <van-button size="small" class="w-[85px]" @click="handleClick">
          搜索
        </van-button>
      </template>
      <template #left-icon>
        <img loading="lazy" :src="iconSearch" class="w-[24px] h-[24px]" />
      </template>
    </van-field>
    <div class="flex items-center justify-center w-1/6 text-[#999999] text-[28px]" @click="handleOpenArea">
      <!-- :class="{'!text-#5AA4FF':areaCodeName && areaCodeName !=='全部'}" -->
      <img loading="lazy" :src="searchIcon" class="w-[40px] h-[40px]" />
      {{ areaCodeName && areaCodeName !== '全部' ? areaCodeName : '筛选' }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import iconSearch from '@/assets/workers-voice/icon-search.png';
import searchIcon from '@/assets/position/search-icon.png';

const emit = defineEmits(['button-click', 'show-area']);
const props = defineProps({
  areaCodeName: {
    type: String,
    default: ''
  }
});
// search
const val = ref<string>();

function handleClick() {
  emit('button-click', { value: unref(val) });
}

function handleOpenArea() {
  emit('show-area', true);
}
</script>

<style lang="scss" scoped>
:deep(.van-cell:after) {
  border: none !important;
}
</style>