<template>
  <div class="my-activity w-full">
    <div class="tab-box h-80px bg-white sticky top-0 z-99">
      <van-tabs v-model:active="Data.tab.active" sticky line-width="20" @click-tab="onClickTab">
        <van-tab :title="item.name" v-for="(item, index) in Data.tab.nav" :key="index"
          title-class="tab-title"></van-tab>
      </van-tabs>
    </div>

    <div class="two-tab-box h-80px w-45% m-auto">
      <van-tabs type="card" v-model:active="Data.twoTab.active" sticky line-width="20" @click-tab="onClickTwoTab">
        <van-tab :title="item.name" v-for="(item, index) in Data.twoTab.nav" :key="index"
          title-class="tab-title"></van-tab>
      </van-tabs>
    </div>
    <div class="p-30px">
      <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">

        <div v-if="Data.twoTab.active == 1">
          <div class="flex justify-between w-full flex-wrap">
            <div v-for="(item, index) of Data.activityList" :key="index"
              class="bg-#FFF rounded-20px list-items mb-22px w-48%" @click="toDetail(item)">
              <div class="h-160px w-full relative">
                <img loading="lazy" :src="item.appCover ? judgeStaticUrl(item.appCover) : defaultCover" alt=""
                  class="w-full h-full appCover">
              </div>
              <div class="p-20px box-border w-full">
                <div class="truncate w-100% text-30px">{{ item.activityName }}</div>
                <div class="text-#999999 text-22px py-12px pb-5px">{{ item.activityStartTime }} - {{
                  item.activityEndTime }}
                </div>
                <div class="text-24px text-#ccc pb-12px">
                  <van-icon name="location" class="mr-12px" />{{ item.companyName }}
                </div>
                <div class="flex justify-between items-center text-24px text-#ccc">
                  <div class="rounded-10px text-22px border-1px border-solid px-6px"
                    :class="item.progressMsg == '未开始' ? 'text-#FBAA46  border-#FBAA46' : item.progressMsg == '进行中' ? 'text-#5AA4FF  border-#5AA4FF' : 'text-#999999  border-#999999'">
                    {{ item.progressMsg }}
                  </div>
                  <div><van-icon name="friends" class="mr-12px" size="14" />{{ item.readCount }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else>
          <div v-for="(item, index) of Data.activityList" :key="index" class="bg-#FFF rounded-20px list-items mb-22px"
            @click="toDetail(item)">
            <div class="h-300px w-full relative rounded-20px overflow-hidden">
              <img loading="lazy" :src="item.appCover ? judgeStaticUrl(item.appCover) : defaultCover" alt=""
                class="w-full h-full">
              <div class="act_status_box absolute right-0 z-99 text-#fff text-24px top-0">
                <div
                  :class="item.progressMsg == '未开始' ? 'act_status_no' : item.progressMsg == '进行中' ? 'act_status_ing' : ''">
                  {{ item.progressMsg }}
                </div>
              </div>
            </div>
            <div class="p-20px box-border w-full">
              <div class="truncate w-100% text-30px">{{ item.activityName }}</div>
              <div class="text-#666 text-26px py-12px flex items-center"><van-icon name="underway"
                  class="mr-12px text-[#CCCCCC]" size="14" />
                {{ item.activityStartTime }} - {{ item.activityEndTime }}
              </div>
              <div class="flex justify-between text-24px text-#ccc">
                <div><van-icon name="location" class="mr-12px" size="14" />{{ item.companyName }}</div>
                <div><van-icon name="friends" class="mr-12px" size="14" />{{ item.readCount }}</div>
              </div>
            </div>
          </div>
        </div>
      </refreshList>
    </div>
  </div>
</template>
<script lang="ts" setup>
defineOptions({
  name: 'myActivity'
})
import dayjs from 'dayjs';
import { getDetaicurrentUserActivityListls } from "@/api/activity.ts";
import refreshList from '@/components/refreshList/index.vue';
import defaultCover from '@/assets/activity/tu.png'
import { judgeStaticUrl } from '@/utils/utils';
import { toDetail } from "@/hooks/useValidator";
const Data = ref({
  tab: {
    tabActive: 0,
    nav: [
      { name: '全部', value: '' },
      { name: '进行中', value: '1', code: '2' },
      { name: '未开始', value: '2', code: '1' },
      { name: '已结束', value: '3', code: '3' },
    ],
  },

  twoTab: {
    active: 0,
    nav: [
      { name: '工会活动', value: '0', activityCategory: 'union' },
      { name: '普惠活动', value: '1', activityCategory: 'inclusive' },
    ]
  },
  pageNum: 1,
  activityList: []
})
/**
 * 点击状态标签页的处理函数
 *
 * @param item 被点击的标签页对象
 */
function onClickTab(item: any) {
  Data.value.tab.tabActive = item.name;
  Data.value.pageNum = 1;
  getActList()
}
function onClickTwoTab(item: any) {
  Data.value.twoTab.active = item.name;
  Data.value.pageNum = 1;
  getActList()
}
//获取活动列表
const loadMoreRef = ref(null)
function getActList() {
  getDetaicurrentUserActivityListls({
    activityCategory: Data.value.twoTab.nav[Data.value.twoTab.active].activityCategory,
    pageSize: 10,
    pageNum: unref(Data).pageNum,
    progress: Data.value.tab.nav[Data.value.tab.tabActive].code
  }).then(res => {
    if (res.code == 200) {
      if (Data.value.pageNum === 1) {
        Data.value.activityList = [];
      }
      res.data = res.data?.map(t => {
        const { activityStartTime, activityEndTime } = t
        t.activityStartTime = dayjs(activityStartTime).format('YYYY.MM.DD') ?? '';
        t.activityEndTime = dayjs(activityEndTime).format('YYYY.MM.DD') ?? '';

        // t.activityStartTime = activityStartTime?.split(' ')[0] ?? ''
        // t.activityEndTime = activityEndTime?.split(' ')[0] ?? ''
        return t
      })
      Data.value.activityList = Data.value.activityList.concat(res.data);
      //重置刷新状态及 判断是否加载完成
      if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(Data.value.activityList.length, res.total);
      }
    }

  })
}
// 刷新
const onRefreshList = () => {
  // Data.value.params = {}
  Data.value.pageNum = 1;
  getActList()
};
// 加载更多
const onLoadMore = () => {
  Data.value.pageNum++;
  getActList()
};
onMounted(() => {
  getActList()
})

</script>

<style lang="scss" scoped>
.my-activity {
  background: #F6F7F8;
  height: 100vh;

  .tab-box {
    background: #fff;

    :deep(.van-tab) {
      line-height: 1;
      height: 100%;
    }

    :deep(.van-tabs__line) {
      background: linear-gradient(86deg, #5aa4ff 0%, #c7e0ff 100%);
      border-radius: 3px;
      height: 6px;
      width: 54px;
    }

    :deep(.tab-title) {
      font-weight: 400;
      font-size: 30px;
      color: #808080;
    }

    :deep(.van-tab--active) {
      font-weight: 500;
      font-size: 32px;
      color: #333333;
    }

    :deep(.van-tabs--line .van-tabs__wrap) {
      height: 80px;
    }

    :deep(.van-tabs__nav--line) {
      height: 100%;
      padding-bottom: 15px;
    }
  }

  .two-tab-box {
    margin-top: 28px;

    :deep(.van-tabs__nav--card) {
      border-radius: 29px !important;
      margin: 0 !important;
      border: none !important;
      box-sizing: border-box;
      height: 90%;

    }

    :deep(.van-tab--card) {
      border-right: none !important;

    }

    :deep(.van-tab--active) {
      border-radius: 29px !important;
      color: #5AA4FF !important;
      background: rgba(90, 164, 255, 0.1);

    }

    :deep(.van-tabs__nav) {
      background: transparent !important;
    }

    :deep(.tab-title) {
      font-weight: 400;
      font-size: 26px;
      color: #666666;
      width: 138px;
    }

    :deep(.van-tabs__wrap) {
      height: 70px;
    }

    :deep(.van-tab) {
      line-height: 1;
      height: 100%;
    }
  }

  .appCover {
    border-radius: 20px 20px 0px 0px;
  }

  .list-items {
    box-shadow: 0px 3px 10px 0px rgba(128, 153, 162, 0.1);

    .act_status_box {
      >div {
        background-image: url('@/assets/activity/end.png');
        background-size: 100% 100%;
        background-position: center center;
        width: 119px;
        height: 44px;
        line-height: 44px;
        /*box-shadow: 3px 1px 1px 0px #f04128;*/
        /*border-radius: 28px 0px;*/
        text-align: center;
      }

      .act_status_no {
        background-image: url('@/assets/activity/wait.png') !important;

      }

      .act_status_ing {
        background-image: url('@/assets/activity/open.png') !important;
      }
    }
  }
}
</style>
