<template>
  <div class="w-100vw min-h-100vh bg-#f6f7f8">
    <div class="px-30px box-border">
      <div class="flex items-center w-100% my-30px">
        <div :class="{ 'mr-20px': ind === 0 }" v-for="(it, ind) in Data.card" :key="ind"
          :style="{ backgroundImage: `url(${it.bg})` }"
          class="bg-no-repeat bg-cover bg-center w-1/2 h-140px flex flex-col justify-center pl-20px box-border"
          @click="onCard(it.path)">
          <div class="text-32px text-#333 mb-10px">{{ it.name }}</div>
          <div class="text-24px" :style="{ color: it.color }">{{ it.tip }}</div>
        </div>
      </div>

      <div class="tab-box mb-36px">
        <van-tabs v-model:active="Data.tab.active" sticky line-width="20" @click-tab="onClickTab">
          <van-tab :title="item.label" v-for="(item, index) in Data.tab.nav" :key="index"
            title-class="tab-title"></van-tab>
        </van-tabs>
      </div>
      <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
        <interestGroupList :data="Data.groupList" :showBtn="true" @details="toDetail"
          v-if="Data.groupList && Data.groupList.length">
          <template #showBtn="{ item }">
            <!--  -->
            <div class="w-90px py-6px rounded-19px text-#fff text-center text-24px" @click.stop="join(item)" :class="{
              'bg-#CCCCCC': item.joinFlag == 'pass',
              'text-#4899ff  border-#a6cdff border-solid border-1px bg-#FFF':
                item.joinFlag == 'wait',
              'bg-#5AA4FF': item.joinFlag == null,
              'cursor-not-allowed opacity-50':
                (item.userId == useStore.getUserInfo?.userId && item.joinFlag == 'pass') ||
                item.joinSum == item.maxPeople,
            }">
              <span v-if="item.joinFlag == 'pass'">退出</span>
              <span v-else-if="item.joinFlag == 'wait'">审核中</span>
              <span v-else>加入</span>
              <!-- {{ item.joinFlag ? '退出' : '加入' }} -->
            </div>
          </template>
          <template #dataPart="{ item }">
            <div class="flex items-center my-[20px]">
              <div class="flex items-center text-[#666] text-[24px]">
                <img loading="lazy" src="@/assets/interest/icon_num.png" alt="" class="w-22px h-20px mr-10px" />
                成员：{{ item.joinSum }}/{{ item.maxPeople }}
              </div>
            </div>
          </template>
        </interestGroupList>
      </refreshList>
    </div>
    <van-popup :show="Data.showPop" round class="popup1">
      <van-form @submit="joinSubmit">
        <div class="text-center text-32px text-#333 mb-50px">加入原因</div>
        <div class="text-#666 text-28px">姓名</div>
        <div class="text-input mt-15px">
          <van-field required v-model="Data.joinObj.addUserName" label="" label-width="0"
            :rules="[{ required: true, message: '请输入姓名' }]"></van-field>
        </div>
        <div class="text-#666 text-28px mt-[28px]">加入原因</div>
        <div class="text-input mt-15px">
          <van-field v-model="Data.joinObj.addCause" label="" rows="3" type="textarea" label-width="0"></van-field>
        </div>
        <van-button type="primary" block
          class="btn w-65/100 mt-40px m-auto text-34px text-#fff py-23px text-center border-none"
          native-type="submit">提交</van-button>
        <img loading="lazy" src="@/assets/public/close.png" alt=""
          class="fixed -bottom-15 w-60px h-60px left-1/2 -translate-x-1/2" @click="closePopup" />
      </van-form>
    </van-popup>
  </div>
</template>
<script lang="ts" setup>
import card_cjxz from '@/assets/digitalSchool/card_cjxz.png';
import card_wdxz from '@/assets/digitalSchool/card_wdxz.png';
// import icon_zzzb from '@/assets/digitalSchool/icon_zzzb.png';
import interestGroupList from '@/components/List/interestGroupList.vue';
import refreshList from '@/components/refreshList/index.vue';
import { findOneJoinRecordVoList, joinGroups, exitGroup } from '@/api/digitalSchools/group';
import { useDictionary } from '@/store/modules/dictionary';
import { useUserStore } from '@/store/modules/user';
import { showToast, showConfirmDialog, showFailToast, showSuccessToast } from 'vant';
import router from '@/router';
const dictionary = useDictionary();
const useStore = useUserStore();
const userInfo = computed(() => useStore.userInfo);
const Data = ref({
  card: [
    {
      name: '创建小组',
      tip: '促进互助学习',
      bg: card_cjxz,
      color: '#F0A233',
      path: '/digitalSchool/studyGroup/createGroup',
    },
    {
      name: '我的小组',
      tip: '学习小分队',
      bg: card_wdxz,
      color: '#32A0D3',
      path: '/digitalSchool/studyGroup/myGroups',
    },
  ],
  tab: {
    active: 0,
    nav: [
      {
        name: '骨干培训',
      },
      {
        name: '工会精神',
      },
      {
        name: '系统操作',
      },
      {
        name: '技能提升',
      },
    ],
  },
  groupList: [],
  showPop: false,
  introduce: '',
  pageNum: 1,
  joinObj: {
    groupBizId: '',
    addUserName: userInfo.value?.nickname || '',
    addCause: '',
  },
});

const loadMoreRef = ref('');

onMounted(() => {
  Data.value.tab.nav = dictionary.getDictionaryOpt?.['courseSection'];
  getGroupList();
});

//学习小组列表
function getGroupList() {
  findOneJoinRecordVoList({
    currTypeId: Data.value.tab.nav[Data.value.tab.active].value,
    pageNum: Data.value.pageNum,
    pageSize: 10,
  }).then(res => {
    if (res.code == 200) {
      if (Data.value.pageNum == 1) {
        Data.value.groupList = res.data || [];
      } else {
        Data.value.groupList = Data.value.groupList.concat(res.data);
      }
      Data.value.groupList.forEach(item => {
        item.logo = item.groupCover;
      });
      //重置刷新状态及 判断是否加载完成
      if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(Data.value.groupList.length, res.total);
      }
    }
  });
}

// 刷新
const onRefreshList = () => {
  Data.value.pageNum = 1;
  getGroupList();
};
// 加载更多
const onLoadMore = () => {
  Data.value.pageNum++;
  getGroupList();
};
function onCard(path: string, query: object) {
  if (!path) return;
  router.push({ path, query });
}
function onClickTab(item: { name: number }) {
  Data.value.tab.active = item.name;
  Data.value.pageNum = 1;
  if (loadMoreRef.value) loadMoreRef.value.resetStatus();
  onRefreshList();
}
function toDetail(item: any) {
  if (item.joinFlag != 'pass') {
    showFailToast('您还没有加入该小组,请先加入该小组');
    return;
  }
  onCard('/digitalSchool/studyGroup/groupDetails', {
    groupBizId: item.groupBizId,
    groupId: item.groupId,
  });
}
function join(item) {
  if (item.userId == useStore.getUserInfo?.userId && item.joinFlag == 'pass') {
    showToast('小组管理员不可退出哦');
    return false;
  }

  if (item.joinSum == item.maxPeople) {
    showToast('小组人员已满了哦');
    return false;
  }

  if (item.joinFlag == 'pass') {
    showConfirmDialog({
      title: '提示',
      message: `确认退出${item.groupName}学习小组?`,
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      className: 'close',
    }).then(async () => {
      exitGroup({ groupBizId: item.groupBizId }).then(res => {
        if (res.code == 200) {
          showToast('退出成功');
          item.joinFlag = null;
        } else {
          showFailToast(res.message);
        }
      });
    });
  } else if (item.joinFlag == null) {
    for (const key in Data.value.joinObj) {
      Data.value.joinObj[key] = '';
    }
    Data.value.joinObj.addUserName = userInfo.value?.nickname || '';
    Data.value.showPop = true;
    Data.value.joinObj.groupBizId = item.groupBizId;
  } else if (item.joinFlag == 'wait') {
    showToast('正在审核中，请耐心等待！');
  }
}
function joinSubmit() {
  joinGroups(Data.value.joinObj).then(res => {
    if (res.code == 200) {
      showSuccessToast('提交成功,等待审核');
    } else {
      showFailToast(res.message);
    }
  });

  Data.value.showPop = false;
}
function closePopup() {
  Data.value.showPop = false;
}
</script>
<style lang="scss" scoped>
.tab-box {
  background: transparent;

  :deep(.van-tabs__nav--card) {
    border: none !important;
    margin: 0 !important;
  }

  :deep(.van-tabs__nav) {
    background: #f5f6f7 !important;
  }

  :deep(.van-tabs__line) {
    background: linear-gradient(86deg, #c7e0ff 0%, #5aa4ff 100%);
    border-radius: 3px;
  }

  :deep(.tab-title) {
    font-weight: 400;
    font-size: 28px;
    color: #666;
  }

  :deep(.van-tab--active) {
    font-weight: 500;
    font-size: 32px;
    color: #333;
  }
}

.item-box {
  box-shadow: 0px 0px 10px 0px #f6f7f8;
  width: calc((100% - 19px) / 2);
}

.van-overlay {
  background: rgba(0, 0, 0, 0.5);
}

.popup1 {
  width: 85vw;
  min-height: 500px;
  padding: 50px 40px;
  box-sizing: border-box;
  top: 40%;
  overflow-y: visible;
}

.btn_ok {
  background: linear-gradient(0deg, #a1cbff 0%, #5aa4ff 100%);
}

.text-input {
  :deep(.van-cell) {
    padding: 0;
  }

  :deep(.van-field__control) {
    background: #f6f7f8;
    border-radius: 10px;
    padding: 20px 35px;
    box-sizing: border-box;
  }

  :deep(.van-field__body) {
    margin-top: 15px;
  }
}

.btn {
  background: url('@/assets/public/butto.png') no-repeat;
  background-size: 100% 100%;
}
</style>
