<template>
    <div class="year-gift w-100vw h-100vh">
        <yearGiftCard :content="cardInfo" v-if="isshow" />
    </div>
</template>
<script setup lang="ts">
import yearGiftCard from "./components/card.vue";
import { getUserBirthdayCard } from "@/api/yearGift"
const route = useRoute();
onMounted(() => {
    getCardInfo()
})
const cardInfo = ref<any>({})
const isshow = ref(false)
const getCardInfo = async () => {
    const { data, code } = await getUserBirthdayCard(route.query?.autoId)
    if (code === 200) {
        cardInfo.value = data
        isshow.value = true
    }
}
</script>
<style scoped lang="scss"></style>