<template>
  <van-form @submit="onSubmit" class="uploadForm w-full" error-message-align="right">
    <van-cell-group inset>
      <van-field v-model="from.title" name="视频标题" label="视频标题" placeholder="视频标题" required input-align="right"
        :rules="[{ required: true, message: '请填写视频标题' }]" />
      <van-field v-model="from.describes" type="textarea" name="视频描述" label="视频描述" placeholder="视频描述" label-align="top"
        required :autosize="{
          minHeight: 100,
        }" :rules="[{ required: true, message: '请填写视频描述' }]" />
      <van-field v-model="areaName" required label="所属区域" placeholder="请选择所属区域" label-align="left"
        :rules="[{ required: true, message: '请选择所属区域' }]" right-icon="arrow" @click="showPickerFn" readonly
        input-align="right" error-message-align="right">
      </van-field>
      <van-field label="视频封面" required label-align="top" error-message-align="left"
        :rules="[{ required: true, message: '请上视频封面' }]">
        <template #input>
          <van-uploader v-model="from.pictures" reupload max-count="1" accept="image/*" :after-read="afterRead">
            <div class="uploader w-140px">
              <img loading="lazy" src="@/assets/public/upload_icon.png" class="w-full" />
            </div>
          </van-uploader>
        </template>
      </van-field>
      <van-field label="视频文件" required label-align="top" error-message-align="left"
        :rules="[{ required: true, message: '请上视频文件' }]">
        <template #input>
          <van-uploader v-model="from.linkUrl" reupload max-count="1" accept="video/*" :after-read="afterRead">
            <div class="uploader w-140px h-140px bg-#F2F2F2 flex items-center justify-center rounded-md">
              <img loading="lazy" src="@/assets/public/video_icon.png" class="w-60px" />
            </div>
          </van-uploader>
        </template>
      </van-field>

      <!-- <van-field v-model="from.userName" label="开卡人姓名" placeholder="开卡人姓名" :readonly="isHave"
        :rules="[{ required: true, message: '请填写开卡人姓名' }]" name="userName" required input-align="right" />

      <van-field v-model="from.cardNumber" label="卡号" placeholder="卡号" name="cardNumber" :rules="bankCardRules" required
        input-align="right" :readonly="isHave"></van-field>
      <van-field v-model="from.issuingBank" :readonly="isHave" label="开户行"
        :rules="[{ required: true, message: '请填写开户行' }]" placeholder="开户行" name="issuingBank" required
        input-align="right"></van-field> -->
    </van-cell-group>
    <div class="my-16px">
      <van-button class="button w-350px h-85px m-auto text-32px" round block type="primary" native-type="submit">
        提交
      </van-button>
    </div>
  </van-form>
  <van-popup v-model:show="showPicker" position="bottom">
    <van-picker :columns="areaList" @confirm="pickerConfirm" :columns-field-names="{
      text: 'dictName',
      value: 'remark',
    }" @cancel="showPicker = false" />
  </van-popup>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import { uploadFile } from '@/api/public';
import { showFailToast, showSuccessToast } from 'vant';
import { uploadVideo, getCardInfo, saveCardInfo } from '@/api/video';
import { useRouter } from 'vue-router';
const areaList = ref<any[]>([])
const router = useRouter();
const from = reactive<any>({});
const areaName = ref('');
import { useDictionary } from '@/store/modules/dictionary';
const dictionary = useDictionary()
const isHave = ref(false)
onMounted(() => {
  // 获取银行卡信息
  getCardInfo().then(({ data }) => {
    if (data) {
      const { userName, cardNumber, issuingBank } = data;
      from.userName = userName;
      from.cardNumber = cardNumber;
      from.issuingBank = issuingBank;
      isHave.value = true
    }
  });
  // end
  areaList.value = [...dictionary.getDictionaryOBJMap?.['regionCode']];
})
const showPicker = ref(false)
function pickerConfirm({ selectedOptions }) {
  showPicker.value = false;
  from.areaId = selectedOptions[0].remark;
  areaName.value = selectedOptions[0].dictName;
}
function onSubmit(values: any) {
  uploadVideo({
    title: from.title,
    describes: from.describes,
    pictures: from.pictures[0]?.url,
    linkUrl: from.linkUrl[0]?.url,
    areaId: from.areaId
  }).then(({ message, code }) => {
    if (code === 200) {
      showSuccessToast('提交成功,等待管理员审核!');
      router.back();
    } else {
      showFailToast(`提交失败！${message || ''}`);
    }
  });
  if (!isHave.value) {
    saveCardInfo({
      userName: from.userName,
      cardNumber: from.cardNumber,
      issuingBank: from.issuingBank,
    }).then(({ code, message }) => {
      if (code !== 200) showFailToast(`提交失败！${message || ''}`);
    })
      .catch((error) => {
        showFailToast(`提交失败！${error?.message || ''}`);
      })
  }
}

const showPickerFn = () => {
  showPicker.value = true;
};
function afterRead(file: any) {
  let filedata = {
    operateType: '82', //操作模块类型
    file: file.file,
  };
  if (file.file.type.indexOf('video') >= 0 && file.file.size > 50 * 1024 * 1024) {
    return showFailToast('上传视频文件大小不能超过50M!');
  }
  file.status = 'uploading';
  uploadFile(filedata).then(res => {
    if (res.code == 200 && res.data) {
      file.status = 'success';
      file.url = res.data[0];
      showSuccessToast('上传成功');
    } else {
      file.status = 'failed';
      showFailToast(res.message);
    }
  });
}
const bankCardRules = ref([
  {
    require: true,
    message: '请填写卡号',
  },
  {
    validator: (val: any) => val.length >= 16 && val.length <= 19,
    message: '卡号长度不小于16位且不大于19位',
  },
  {
    validator: (val: any) => /^(?!.*[\u4e00-\u9fff]).*$/.test(val),
    message: '卡号格式不正确',
  },
])

</script>

<style lang="scss" scoped>
.uploadForm {
  .button {
    background: url(@/assets/video/add_btn_bg.png) no-repeat center/100% 100%;
    --van-button-border-width: 0;
  }

  :deep(.van-cell-group--inset) {
    margin: 0 !important;
  }

  :deep(.van-field__body) {
    textarea {
      background-color: #f6f7f8;
      padding: 10px;
      border-radius: 10px;
    }

    font-size: 30px;
  }

  :deep(.van-field__label) {
    font-size: 32px !important;
  }

  :deep(.van-cell) {
    padding-top: 26px;
    padding-bottom: 26px;
  }

  :deep(.van-field__error-message) {
    font-size: 26px !important;
  }
}
</style>
