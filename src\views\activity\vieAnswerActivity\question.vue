<template>
  <div class="vieAnswerActivity-question w-full min-h-full px-30px pt-50px flex flex-col items-center pb-30px">
    <div
      class="fixed top-15px right-50px z-9999 timer w-80px h-96px flex justify-center items-center text-30px text-[#FF881C] font-600 pt-10px"
      v-if="time > 0">
      {{ time }}
    </div>
    <div class="question-header pl-38px pr-38px pt-30px pb-30px w-full ">
      <div class="question-order flex justify-center mb-20px">
        <span>第{{ currentTopicIndex + 1 || '-' }}题</span>共{{ topicList.length || '-' }}题
      </div>
      <div class="schedule flex justify-evenly">
        <div v-for="(item, index) in topicList" :key="index">
          <div class="w-full h-full rounded-14px" :class="{
            submited: currentTopicIndex >= index,
          }">
          </div>
        </div>
      </div>
    </div>
    <div class="w-full  bg-[#fff] rounded-20px relative py-50px px-30px mb-80px flex flex-col items-center">
      <!-- 题目 -->
      <div class="text-33px text-[#333] leading-50px mb-30px">
        {{
          topicList && topicList.length
            ? (currentTopicIndex + 1) + '、' + (topicList[currentTopicIndex].optionType === 'select' ? '（多选）' : '') +
            topicList[currentTopicIndex].topicContent
            : '--'
        }}
      </div>
      <!-- 选项 -->
      <div
        class="w-full px-30px py-30px bg-[#F5F1F1] rounded-14px text-31px text-[#333] mb-40px leading-40px flex justify-between items-center"
        :class="{
          select: topicList[currentTopicIndex].select && topicList[currentTopicIndex].select.split(',').includes(option.optionNo),
          right:
            topicList[currentTopicIndex].isSubmited &&
            option.correct &&
            (topicList[currentTopicIndex].optionType === 'radio' ||
              (topicList[currentTopicIndex].select &&
                topicList[currentTopicIndex].select.split(',').includes(option.optionNo) &&
                topicList[currentTopicIndex].optionType === 'select')),
          error:
            topicList[currentTopicIndex].isSubmited &&
            topicList[currentTopicIndex].select &&
            topicList[currentTopicIndex].select.split(',').includes(option.optionNo) &&
            !option.correct,
        }" :key="topicList[currentTopicIndex].topicOptionId"
        v-for="(option, i) in topicList?.[currentTopicIndex]?.options || []"
        @click="onSelect(topicList[currentTopicIndex], option)">
        {{ option.optionContent.slice(0, 1) }}、{{ option.optionContent.slice(2) }}
        <img loading="lazy" src="@/assets/activity/error.png" alt="" class="w-38px h-38px" v-if="topicList[currentTopicIndex].isSubmited &&
          topicList[currentTopicIndex].select &&
          topicList[currentTopicIndex].select.split(',').includes(option.optionNo) &&
          !option.correct
        " />
        <img loading="lazy" src="@/assets/activity/right.png" alt="" class="w-38px h-38px"
          v-if="topicList[currentTopicIndex].isSubmited && option.correct" />
      </div>
      <van-button class="next-btn flex justify-center items-center border-0 mt-50px"
        @click="submit(topicList[currentTopicIndex])">
        {{ currentTopicIndex === topicList.length - 1 ? '提交' : '下一题' }}
      </van-button>
    </div>

    <!-- 答题提交弹窗 -->
    <van-popup v-model:show="show" :close-on-click-overlay="false" :lock-scroll="true"
      class="w-full flex justify-center bg-transparent">
      <div class="flex justify-center items-center relative w-650px min-h-700px">
        <!-- 配置有抽奖 -->
        <template v-if="activityDetail.luckDraw === 'y'">
          <!-- 答题成功 -->
          <template v-if="isSuccess">
            <img loading="lazy" src="@/assets/activity/jiangbei.png" alt="" class="w-full" />
            <div class="absolute top-50px w-90/100 flex justify-center flex-col items-center">
              <div class="mb-250px text-center w-full">
                <p class="text-[#fff] text-50px mb-20px">恭喜您!</p>
                <p class="text-[#FFE57C] text-38px">获得1次抽奖机会</p>
              </div>
              <div class="flex justify-center w-60/100 h-80px items-center text-35px text-[#fff] font-500 pb-10px"
                :style="{ backgroundImage: `url(${draw_btn})`, backgroundSize: '100% 100%' }"
                @click="router.replace('/activityHome/lottery')">

                去抽奖
              </div>
            </div>
          </template>
          <!-- 答题失败 -->
          <template v-else>
            <img loading="lazy" src="@/assets/activity/err_ren.png" alt="" class="w-60/100" />
            <div class="absolute top-25px w-90/100 flex justify-center flex-col items-center">
              <div class="mb-330px text-center w-full">
                <p class="text-[#fff] text-34px mb-20px">答题失败~</p>
                <p class="text-[#FFE57C] text-38px">答对{{ lotteryCondition || 3 }}题即可抽奖！</p>
              </div>
              <div class="flex justify-center w-60/100 h-80px items-center text-35px text-[#fff] font-500 pb-10px"
                :style="{ backgroundImage: `url(${draw_btn})`, backgroundSize: '100% 100%' }" @click="backFn()">
                我知道了
              </div>
            </div>
          </template>
        </template>
        <!-- 无抽奖 -->
        <template v-else>
          <img loading="lazy" src="@/assets/activity/jiangbei.png" alt="" class="w-full" />
          <div class="absolute top-50px w-90/100 flex justify-center flex-col items-center">
            <div class="mb-250px text-center w-full">
              <p class="text-[#fff] text-38px mb-20px">感谢您的参与</p>
              <p class="text-[#FFE57C] text-38px">您总共答对{{ rightQuestionNum || '0' }}题!</p>
            </div>
            <div class="flex justify-center w-60/100 h-80px items-center text-35px text-[#fff] font-500 pb-10px"
              :style="{ backgroundImage: `url(${draw_btn})`, backgroundSize: '100% 100%' }" @click="backFn()">
              我知道了
            </div>
          </div>
        </template>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import draw_btn from '@/assets/activity/draw_btn.png'
import { showDialog, showToast } from 'vant'
import { getVieAnswerState, getTopicList, vieAnswerSubmit } from '@/api/activity';
import { activityValidator, } from '@/hooks/useValidator'
import { useRouter, useRoute } from "vue-router";
import { useUserStore } from "@/store/modules/user";
import { commonBackFn } from '@/utils/utils';
const useStore = useUserStore();
const router = useRouter();
const route = useRoute()

const show = ref(false)
const isSuccess = ref(false)
const currentTopicIndex = ref(0); // 当前题目
const topicList = ref([]); // 题目数据
const startTime = ref(new Date().getTime()); // 答题开始时间
const clickState = ref(true); // 防止频繁点击
const autoId = ref(null); // 首次答题记录，防止中途退出
const rightQuestionNum = ref(0); // 答对数量
const score = ref(0); // 本次答题得分
const isNeedAutoId = ref(false); // 是否校验中途退出
const vieAnswerNum = ref(0); // 今日答题次数
const isSubmited = ref(false); // 是否提交

const activityDetail = computed(() => {
  return useStore.activityDetail || {};
});
watch(activityDetail, () => {
  // if (activityDetail.value.activityId) {
  //   getVieAnswerCount();
  // }
  getTopicLists(false)
})
onMounted(() => {
  // if (activityDetail.value.activityId) {
  //   getVieAnswerCount()
  // }
  getTopicLists(false)

})

onBeforeRouteLeave((to, from, next) => {
  clearInterval(timer)
  next()
})

const time = ref(0)
const initTime = ref(0)
let timer = null
const isNeedTime = ref(false)

// 倒计时
const setTimer = () => {
  time.value = unref(initTime)
  timer = setInterval(() => {
    if (time.value > 0) {
      time.value--
      if (time.value == 0) {
        showToast({
          message: currentTopicIndex.value === topicList.value.length - 1 ? '答题超时' : '答题超时，即将进入下一题',
          duration: 1000,
          forbidClick: true,
          onClose: () => {
            if (!topicList.value[currentTopicIndex.value].select) {
              topicList.value[currentTopicIndex.value].select = '-1'
            }
            submit(topicList.value[currentTopicIndex.value])
          }
        });
      }
    }
  }, 1000)
}

const getTopicLists = async (autoId) => {
  const res = await getTopicList({
    activityId: activityDetail.value.activityId
  });
  if (res.code === 200) {
    topicList.value = res.data;
    topicList.value.forEach(item => {
      item.options?.forEach(i => {
        i.optionNo = i.optionNo + '';
      });
    });
    if (activityDetail.value.vieAnswerInfo.timeUse) {
      isNeedTime.value = true
      initTime.value = activityDetail.value.vieAnswerInfo.timeUse
      setTimer()
    }
    // 是否需要校验中途退出
    if (autoId) {
      isNeedAutoId.value = false;
      startQuestion();
    }
  }
}
const onSelect = (item, option) => {
  if (item.isSubmited) return;
  if (item.optionType === 'radio') {
    item.select = option.optionNo;
  } else if (item.optionType === 'select') {
    let selectList = [];
    if (item.select && item.select.split(',').includes(option.optionNo)) {
      // 取消选中
      selectList = item.select.split(',').filter(i => i && i != option.optionNo);
    } else {
      // 选中
      item.select = item.select ? item.select + ',' + option.optionNo : option.optionNo;
      selectList = item.select.split(',');
    }
    selectList.sort((a, b) => a - b);
    item.select = selectList.join(',');
  }
  topicList.value = [...topicList.value]; // 更新题目列表
};
const startQuestion = async () => {
  const params = {
    activityId: activityDetail.value.activityId,
  };
  const { code, data } = await vieAnswerSubmit(params);
  if (code === 200) {
    autoId.value = data.autoId;
  } else {
    setTimeout(() => {
      backFn();
    }, 2000);
  }
};
const submit = async (item, index) => {
  if (!activityValidator()) return;
  if (!clickState.value) return;
  if (isNeedAutoId.value) {
    if (!autoId.value) {
      showToast({
        message: '暂未获取到题目信息,请稍后重试',
        duration: 2000,
      });
      return;
    }
  }
  if (!item.select) {
    showToast({
      message: '请先选择答案',
      duration: 1500,
    });
    return;
  }
  item.isSubmited = true;

  topicList.value = [...topicList.value];
  if (isNeedTime.value) {
    clearInterval(timer)
  }
  if (currentTopicIndex.value === topicList.value.length - 1) {
    // 提交
    const params = {
      activityId: activityDetail.value.activityId,
      totalTime: (new Date().getTime() - startTime.value) / 1000,
      topicAnswers: {},
      autoId: isNeedAutoId.value ? autoId.value : undefined,
    };
    rightQuestionNum.value = 0;
    score.value = 0;
    topicList.value.forEach(item => {
      // 计算分数
      if (item.answer == item.select) {
        rightQuestionNum.value++;
        score.value += Number(item.score);
      }
      params.topicAnswers[item.topicInfoId] = item.select;
    });
    isSubmited.value = true;
    clickState.value = false;
    const res = await vieAnswerSubmit(params);
    if (res.code === 200) {
      isSuccess.value = res.data;
      time.value = 0
      setTimeout(() => {
        show.value = true;
        clickState.value = true;
      }, 1000);
    } else {
      showToast(res.message)
      setTimeout(() => {
        clickState.value = true;
      }, 1000);
    }
  } else {
    // 下一题
    clickState.value = false;
    setTimeout(() => {
      currentTopicIndex.value++;
      clickState.value = true;
      if (isNeedTime.value) {
        setTimer()
      }
    }, 800);
  }
};
// 获取答题次数
const getVieAnswerCount = async () => {
  const res = await getVieAnswerState({
    activityId: activityDetail.value.activityId,
  });
  if (res.code == 200) {
    vieAnswerNum.value = res.data || 0;
    const { numberPerDay } = activityDetail?.value.vieAnswerInfo || {}
    if (vieAnswerNum.value < numberPerDay) {
      getTopicLists(false)
    } else {
      showDialog({
        title: "温馨提示",
        message: `每日可参与答题${numberPerDay}次,您今日已无答题次数,明日再来吧~`,
        confirmButtonText: "我知道了",
      }).then(() => {
        backFn()
      });
    }
  }
}

const backFn = () => {
  // 通过外链打开,需要调用app返回方法
  commonBackFn(route)
}
</script>

<style lang="scss" scoped>
.vieAnswerActivity-question {
  background-color: #f7b492;

  .timer {
    background-image: url("@/assets/activity/public/timer.png");
    background-size: 100% 100%;
  }

  .question-num {
    background-image: url('@/assets/activity/ans_frame.png');
    background-size: 100% 100%;
  }

  .question-header {
    background-color: #fff;
    border-radius: 20px;
    margin-bottom: 25px;

    .question-order {
      color: #333333;
      font-size: 33px;

      span {
        color: #FF7D00;
        margin-right: 20px;
      }
    }

    .schedule {
      &>div {
        width: 91px;
        height: 28px;
        border-radius: 14px;
        background: #e5e5e5;
        overflow: hidden;
      }

      .submited {
        background-color: #FF7D00;
      }

      .error {
        background: #cb0209;
      }

    }
  }

  .select {
    background-color: #ffeaca;
  }

  .right {
    color: #fff;
    background-color: #1bc85f;
  }

  .error {
    color: #fff;
    background-color: #F84D1E
  }

  .next-btn {
    width: 380px;
    height: 75px;
    background: linear-gradient(0deg, #E03A27 0%, #EE6631 100%);
    border-radius: 38px;
    font-size: 35px;
    color: #FFFFFF;
  }

  .thanks {
    background-image: url('@/assets/activity/thanks-popup.png');
  }

  .winning {
    background-image: url('@/assets/activity/envelopes.png');
  }

  .oncemore {
    background: linear-gradient(0deg, #F43D30 0%, #F95919 0%, #F58356 100%);
    font-size: 32px;
    color: #FFFFFF;
  }
}
</style>
