<template>
  <div class="echart-box" style="width:100%;height:100%">
    <div ref="echartRef" class="w-full h-full"></div>
  </div>
</template>

<script lang="ts" setup>
// 饼图
import { useECharts } from '@/utils/useECharts'
import { Ref, ref, watch, onMounted } from 'vue'
import * as echarts from 'echarts'
import { getVwSize } from '../../data'
const props = defineProps({
  dataSource: {
    type: Object,
    default: () => {
      return [
        { name: '竞答活动', value: 100 },
        { name: '抽奖活动', value: 23 },
        { name: '健步走活动', value: 45 },
      ]
    },
  }, //color
})

const echartRef = ref<HTMLDivElement | null>(null)
const { setOptions } = useECharts(echartRef as Ref<HTMLDivElement>)

const initChart = () => {
  let data = props.dataSource
  let colors = [
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
      offset: 0,
      color: '#FDA453'
    },
    {
      offset: 1,
      color: '#FFCE60'
    }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
      offset: 0,
      color: '#58E7C2'
    },
    {
      offset: 1,
      color: '#00C9A0'
    }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
      offset: 0,
      color: '#A4EBFF'
    },
    {
      offset: 1,
      color: '#42A3FF'
    }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
      offset: 0,
      color: '#feb493'
    },
    {
      offset: 1,
      color: '#FF7336'
    }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
      offset: 0,
      color: '#bbabff'
    },
    {
      offset: 1,
      color: '#8669FF'
    }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
      offset: 0,
      color: '#b2f8fc'
    },
    {
      offset: 1,
      color: '#0AABB3'
    }
    ]),
  ]
  // 绘制图表
  var option = {
    tooltip: {
      show: true,
      formatter: function (params: any) {
        let result = ''
        result += params.marker + params.name + ': ' + params.value + `<br>占比：${params.percent}%`
        return result
      },
      textStyle: {
        fontSize: getVwSize(20),
      },
    },
    grid: {
      top: '0%',
      left: '0%',
      right: '0%',
      bottom: '0%',
    },
    legend: {
      show: true,
      icon: 'square',
      top: "center",
      left: '60%',
      itemWidth: getVwSize(20),
      itemHeight: getVwSize(20),
      itemGap: getVwSize(30),
      formatter: function (name) {
        let obj = data.find(v => v.name == name)
        return `{title|${name}：}{value|${obj.value}}`
      },
      textStyle: {
        rich: {
          title: {
            fontSize: getVwSize(22),
            lineHeight: getVwSize(30),
            color: "#666"
          },
          value: {
            fontSize: getVwSize(20),
            lineHeight: getVwSize(20),
            color: "#333"
          }
        }
      },
    },
    series: [
      {
        name: '',
        type: 'pie',
        clockWise: false,
        center: ["25%", "50%"],
        radius: ['45%', '72%'],
        hoverAnimation: false,
        clockwise: true,
        avoidLabelOverlap: true,
        color: colors,
        label: {
          show: false,
        },

        data: data
      }
    ],
  }

  setOptions(option)
}

onMounted(() => {
  initChart()
})

// 监听dataSource
watch(
  () => props.dataSource,
  (newValue, oldValue) => {
    initChart()
  },
  { deep: true, immediate: true }
)
</script>
<style scoped lang="scss">
.echart-box {}
</style>
