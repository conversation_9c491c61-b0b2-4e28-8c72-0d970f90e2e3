/* 弹窗 */
// html,
// body {
//     width: 100%;
//     min-height: 100vh;
//     box-sizing: border-box;
//     padding-top: constant(safe-area-inset-top);
//     padding-top: env(safe-area-inset-top);
//     padding-bottom: constant(safe-area-inset-bottom);
//     padding-bottom: env(safe-area-inset-bottom);

// }

.shadow-drop-2-center {
  -webkit-animation: shadow-drop-2-center 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  animation: shadow-drop-2-center 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

/**
 * ----------------------------------------
 * animation shadow-drop-2-center
 * ----------------------------------------
 */
@-webkit-keyframes shadow-drop-2-center {
  0% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  }

  100% {
    -webkit-transform: translateZ(50px);
    transform: translateZ(50px);
    -webkit-box-shadow: 0 0 3px 0px rgba(0, 0, 0, 0.1);
    box-shadow: 0 0 3px 0px rgba(0, 0, 0, 0.1);
  }
}

@keyframes shadow-drop-2-center {
  0% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  }

  100% {
    -webkit-transform: translateZ(50px);
    transform: translateZ(50px);
    -webkit-box-shadow: 0 0 3px 0px rgba(0, 0, 0, 0.1);
    box-shadow: 0 0 3px 0px rgba(0, 0, 0, 0.1);
  }
}

/**
 * ----------------------------------------
 * animation puff-out-center
 * ----------------------------------------
 */
.puff-out-center {
  -webkit-animation: puff-out-center 1s cubic-bezier(0.165, 0.84, 0.44, 1) both;
  animation: puff-out-center 1s cubic-bezier(0.165, 0.84, 0.44, 1) both;
}

@-webkit-keyframes puff-out-center {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-filter: blur(0px);
    filter: blur(0px);
    opacity: 1;
  }

  100% {
    -webkit-transform: scale(2);
    transform: scale(2);
    -webkit-filter: blur(4px);
    filter: blur(4px);
    opacity: 0;
  }
}

@keyframes puff-out-center {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-filter: blur(0px);
    filter: blur(0px);
    opacity: 1;
  }

  100% {
    -webkit-transform: scale(2);
    transform: scale(2);
    -webkit-filter: blur(4px);
    filter: blur(4px);
    opacity: 0;
  }
}

/**
 * ----------------------------------------
 * animation puff-in-center
 * ----------------------------------------
 */
.puff-in-center {
  -webkit-animation: puff-in-center 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) both;
  animation: puff-in-center 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) both;
}

@-webkit-keyframes puff-in-center {
  0% {
    -webkit-transform: scale(2);
    transform: scale(2);
    -webkit-filter: blur(4px);
    filter: blur(4px);
    opacity: 0;
  }

  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-filter: blur(0px);
    filter: blur(0px);
    opacity: 1;
  }
}

@keyframes puff-in-center {
  0% {
    -webkit-transform: scale(2);
    transform: scale(2);
    -webkit-filter: blur(4px);
    filter: blur(4px);
    opacity: 0;
  }

  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-filter: blur(0px);
    filter: blur(0px);
    opacity: 1;
  }
}

/**
 * ----------------------------------------
 * animation tracking-in-expand
 * ----------------------------------------
 */
.tracking-in-expand {
  -webkit-animation: tracking-in-expand 0.7s cubic-bezier(0.215, 0.61, 0.355, 1) both;
  animation: tracking-in-expand 0.7s cubic-bezier(0.215, 0.61, 0.355, 1) both;
}

@-webkit-keyframes tracking-in-expand {
  0% {
    letter-spacing: -0.5em;
    opacity: 0;
  }

  40% {
    opacity: 0.6;
  }

  100% {
    opacity: 1;
  }
}

@keyframes tracking-in-expand {
  0% {
    letter-spacing: -0.5em;
    opacity: 0;
  }

  40% {
    opacity: 0.6;
  }

  100% {
    opacity: 1;
  }
}

/**
 * ----------------------------------------
 * animation slide-in-left
 * ----------------------------------------
 */
@-webkit-keyframes slide-in-left {
  0% {
    -webkit-transform: translateX(-750px);
    transform: translateX(-750px);
    opacity: 0;
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-left {
  0% {
    -webkit-transform: translateX(-750px);
    transform: translateX(-750px);
    opacity: 0;
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
  }
}

.scale-in-top {
  -webkit-animation: scale-in-top 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  animation: scale-in-top 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

/**
 * ----------------------------------------
 * animation slide-in-right
 * ----------------------------------------
 */
@-webkit-keyframes slide-in-right {
  0% {
    -webkit-transform: translateX(750px);
    transform: translateX(750px);
    opacity: 0;
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-right {
  0% {
    -webkit-transform: translateX(750px);
    transform: translateX(750px);
    opacity: 0;
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
  }
}

/**
 * ----------------------------------------
 * animation slide-out-bottom cubic-bezier(0.55, 0.085, 0.68, 0.53)
 * ----------------------------------------
 */

.slide-out-bottom {
  -webkit-animation: slide-out-bottom 0.5s both;
  animation: slide-out-bottom 0.5s both;
}

@-webkit-keyframes slide-out-bottom {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }

  100% {
    -webkit-transform: translateY(37.5vh);
    transform: translateY(37.5vh);
  }
}

@keyframes slide-out-bottom {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }

  100% {
    -webkit-transform: translateY(37.5vh);
    transform: translateY(37.5vh);
  }
}

/**
 * ----------------------------------------
 * animation slide-in-bottom cubic-bezier(0.25, 0.46, 0.45, 0.94)
 * ----------------------------------------
 */
.slide-in-bottom {
  -webkit-animation: slide-in-bottom 0.5s both;
  animation: slide-in-bottom 0.5s both;
}

@-webkit-keyframes slide-in-bottom {
  0% {
    -webkit-transform: translateY(1000px);
    transform: translateY(1000px);
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

@keyframes slide-in-bottom {
  0% {
    -webkit-transform: translateY(1000px);
    transform: translateY(1000px);
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

/*****animation end*****/

.bg-button-long {
  background-image: url('@/assets/workers-voice/bg-button-long.png');
  background-size: 560px 100%;
  background-repeat: no-repeat;
  background-position: center;
  width: 100%;
  height: 76px;
}

// 处理富文本样式
.rich_text {
  :deep(p) {
    max-width: 100% !important;
    img {
      max-width: 100% !important;
      height: auto !important;
    }
  }
  p{
    max-width: 100% !important;
  }
  img, image{
    max-width: 100% !important;
    height: auto !important;
    display: block;
  }
  &::after {
    content: '';
    display: table;
    clear: both;
  }
  /* 基础容器限制 */
  max-width: 100% !important;
  overflow-x: hidden !important;
  word-break: break-word !important;
  
  /* 处理所有子元素 */
  * {
    max-width: 100% !important; /* 强制覆盖行内样式 */
    height: auto !important;
    box-sizing: border-box !important;
  }

  /* 表格溢出滚动 */
  table {
    width: 100%;
    max-width: 100% !important;
    overflow: hidden !important;
    display: block;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    box-sizing: border-box;
    th {
      width: 100% !important; /* 防止自动缩小 */
      display: flex !important;
      box-sizing: border-box;
    }
    tr{
      padding:10px;
      width: 100% !important; /* 防止自动缩小 */
      display: flex !important;
      box-sizing: border-box;
    }
    td{
      width: fit-content !important;
      flex: 1 1 15% !important;
      max-width: 46% !important;
      box-sizing: border-box;
      text-align: left;
    }
  }

  /* 代码块处理 */
  pre {
    white-space: pre-wrap;
    overflow-x: auto;
  }

  /* 视频响应式 */
  iframe {
    width: 100% !important;
    aspect-ratio: 16/9;
  }
}
// 处理底部安全距离
.safe_area_bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

* {
  box-sizing: border-box;
}

/* 添加其他字体格式和路径（如 woff、woff2）以增强兼容性 */

// @font-face {
//   font-family: 'Source Han Sans CN Regular';
//   src: url('/path/NotoSansSC-Regular.otf') format('truetype');
// }


@-webkit-keyframes ball-beat {
  50% {
      opacity: 0.2;
      -webkit-transform: scale(0.75);
      transform: scale(0.75);
  }

  100% {
      opacity: 1;
      -webkit-transform: scale(1);
      transform: scale(1);
  }
}

@keyframes ball-beat {
  50% {
      opacity: 0.2;
      -webkit-transform: scale(0.75);
      transform: scale(0.75);
  }

  100% {
      opacity: 1;
      -webkit-transform: scale(1);
      transform: scale(1);
  }
}

// 气泡弹窗调整-需要写在公共文件
.floating-bubble-back {
  background-color: transparent !important;
  width: 105px !important;
  height: 105px !important;
  .van-icon__image {
    width: 105px;
    height: 105px;
  }
}

.move_single_btn_class {
    background-color: transparent !important;
    border-radius: 0 !important;
    overflow: visible;
    width: 250px;
}
.moveSingleBtnClass {
  background-color: transparent !important;
  overflow: visible;
  .van-icon__image{
    width: 142px;
    height: 140px;
  }
}

.floating-bubble-phone {
  background-color: transparent !important;
  .van-icon__image {
    width: 85px;
    height: 106px;
  }
}
.move_cart_btn_class{
  background-color: transparent !important;
    border-radius: 0 !important;
    overflow: visible;
    width: 155px;
    height: 157px;
}

// end
.van-empty__description{
  font-size: 26px !important;
}
.van-field__error-message--right,.van-field__error-message{
  font-size: 24px !important;
}
.van-picker__cancel, .van-picker__confirm{
  font-size: 30px !important;
}
.van-picker-column{
  font-size: 28px !important;
}
.van-picker__title{
  font-size: 30px !important;
  line-height: normal !important;
}
// .van-back-top {
//   background-color:transparent;
//   background: url('@/assets/public/icon_top.png') no-repeat;
//   background-size: cover;
//   --van-back-top-size:90px;
// }
// .van-back-top__icon{
//     display: none;
// }
// .van-icon-back-top:before{
//   display: none !important;
// }
