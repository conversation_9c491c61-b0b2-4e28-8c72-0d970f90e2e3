<template>
  <div class="exchange bg-[#F9F9F9] h-fit min-h-full box-border">
    <div class="tabs h-100px bg-white sticky top-0 z-99">
      <van-tabs
        v-model:active="activeValue"
        class="myTabs"
        @click-tab="changeActive"
      >
        <van-tab
          :title="item.label"
          :name="item.value"
          v-for="(item, index) in tabs"
          :key="index"
          title-class="tab-title"
        >
        </van-tab>
      </van-tabs>
    </div>
    <div class="rounded-16px mt-15px list_view bg-white">
      <refreshList
        ref="loadMoreRef"
        :immeCheck="true"
        @onRefreshList="onRefreshList"
        @onLoadMore="onLoadMore"
      >
        <div class="list min-h-full bg-white">
          <div
            class="cell p-26px"
            v-for="(item, index) in list"
            :key="index"
          >
            <div class="flex">
              <div
                class="left_item w-200px h-136px bg-[#F5F5F5] rounded-11px bg-cover bg-center"
                :style="{ backgroundImage: `url(${userStore.getPrefix}${item.pictures})` }"
              >
              </div>
              <div class="right_item text-32px w-460px flex-1 ml-20px">
                <div class="title text-[30px] text-#333 truncate"> {{ item.title }} </div>
                <div
                  class="title text-[26px] text-#666 overflow-hidden text-overflow-ellipsis line-clamp-2"
                >
                  {{ item.describes }}
                </div>
                <div class="bottom_cell flex justify-between items-center">
                  <div class="text-[#A1A1A1] text-24px"> {{ item.createTime }} </div>
                  <van-icon
                    name="delete"
                    color="#999"
                    @click="deleteVideo(item)"
                  ></van-icon>
                </div>
              </div>
            </div>
            <div
              v-if="item.auditState == 'refuse'&&item.remark"
              class="text-#666 text-26px flex mt-2 p-2 bg-#F6F7F8 rounded-lg"
            >
              未通过原因：<div class="flex-1 text-#FF4344">{{ item.remark }}</div>
            </div>
          </div>
        </div>
      </refreshList>
    </div>
  </div>
</template>
<script lang="ts" setup>
import refreshList from '@/components/refreshList/index.vue';
import { getPersonalList, deleteFunVideo } from '@/api/video/index';
import { useUserStore } from '@/store/modules/user';
import { showToast, showConfirmDialog } from 'vant';

const route = useRoute();

const activeValue = ref('pass');
watch(route,(val) => {
  if(val.path === '/my/manuscript' && val.query.status) activeValue.value = val.query.status as string;
},{immediate:true})
const tabs = ref([
  { label: '已通过', value: 'pass' },
  { label: '待审核', value: 'wait' },
  { label: '未通过', value: 'refuse' },
]);
const userStore = useUserStore();
const changeActive = () => {
  if (loadMoreRef.value) loadMoreRef.value.resetStatus();
  onRefreshList();
};
// 列表
const list = ref<any>([]);
const pageNum = ref(1);
const loadMoreRef = ref(null);
const onRefreshList = () => {
  pageNum.value = 1;
  loadMoreData();
};
// 加载更多
const onLoadMore = () => {
  pageNum.value++;
  loadMoreData();
};
const loadMoreData = async () => {
  const { data, code, total } = await getPersonalList({
    pageNum: pageNum.value,
    pageSize: 10,
    auditState: activeValue.value,
  });

  if (code === 200) {
    if (pageNum.value === 1) list.value = data;
    else list.value = [...list.value, ...data];
  }
  //重置刷新状态及 判断是否加载完成
  if (loadMoreRef.value) {
    loadMoreRef.value.onLoadSuc(list.value.length, total);
  }
};

function deleteVideo(item: any) {
  showConfirmDialog({
    title: '提示',
    message: '是否要删除视频作品？',
  })
    .then(async () => {
      let { code, message } = await deleteFunVideo({
        shortVideoId: item.shortVideoId,
      });
      if (code == 200) {
        list.value = list.value.filter(e => e.autoId !== item.autoId);
        showToast('删除成功');
      } else {
        showToast('删除失败！' + message);
      }
    })
    .catch(() => {
      showToast('已取消删除');
    });
}
</script>
<style lang="scss" scoped>
.exchange {
  .tabs {
    :deep(.van-tabs--line .van-tabs__wrap) {
      height: 100px;
    }

    :deep(.van-tab) {
      line-height: 1;
      height: 100%;
    }

    :deep(.van-tabs__nav--line) {
      height: 100%;
      padding-bottom: 15px;
    }

    :deep(.van-tabs__line) {
      background: linear-gradient(86deg, #5aa4ff 0%, #c7e0ff 100%);
      width: 54px;
      height: 6px;
      z-index: 1;
    }

    :deep(.van-tab__text) {
      font-size: 32px;
      z-index: 2;
    }
  }

  .list_view {
    min-height: calc(100vh - 160px);
  }

  .list {
    .cell {
      box-shadow: 0px 3px 10px 0px rgba(119, 151, 203, 0.15);
    }
  }
}
</style>
