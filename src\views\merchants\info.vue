<template>
    <div class="merchants-writeOff bg-#F6F7F8 w-full min-h-100vh px-30px py-40px flex flex-col">

        <div class="store-info bg-#fff p-30px rounded-20px">
            <van-skeleton title :row="5" :loading="loading">
                <div class="store flex pb-22px">
                    <img loading="lazy" :src="utils.judgeStaticUrl(campanyDetail?.companyIcon)"
                        class="w-160px h-160px rounded-20px object-cover"
                        v-previewImg="utils.judgeStaticUrl(campanyDetail?.companyIcon)" />
                    <div class="left-info ml-20px" @click="toDetail">
                        <div class="text-32px">
                            {{ campanyDetail.companyName }}
                            <van-icon name="arrow" class="text-[32px] text-#B3B3B3 ml-10px" />
                        </div>
                        <div class="text-#999999 text-28px mt-30px">
                            <img loading="lazy" src="@/assets/public/icon_address-gray.png" class="w-22px h-26px" />
                            <span class="ml-12px">{{ campanyDetail.address }}</span>
                        </div>
                        <div class="text-#999999 text-28px mt-20px">
                            <img loading="lazy" src="@/assets/public/icon_phone-line.png" class="w-24px h-24px" />
                            <span class="ml-12px">{{ campanyDetail.contractPhone }}</span>
                        </div>
                    </div>
                </div>
                <div class="time pt-28px">
                    <div class="">
                        <span class="text-#808080 text-30px">营业时间：</span>
                        <span class="text-#333 text-30px">{{ campanyDetail.openTime }}-{{ campanyDetail.closeTime
                            }}</span>
                    </div>
                    <div class="mt-22px">
                        <span class="text-#808080 text-30px">商家介绍：</span>
                        <span class="text-#333 text-30px">{{ campanyDetail.introduce }}</span>
                    </div>
                </div>
            </van-skeleton>
        </div>
        <div class="write-off bg-[#fff] rounded-30px relative mt-30px flex-1">
            <div class="tabs w-full h-[60px] flex overflow-visiable">
                <div v-for="item in tabArr" :key="item.value"
                    class="w-1/2 relative  flex justify-center items-center text-[30px] tab"
                    :class="`${active === item.value ? 'tab--active font-medium  h-[70px] -top-10px text-32px text-#333' : 'text-30px text-#637180'} ${item.clazz}`"
                    @click="onClickTab(item)">
                    <div class="tracking-in-expand">{{ item.title }}</div>
                </div>
            </div>
            <div v-show="active == 'writeOff'">
                <!-- <div class="coupon-input bg-#F6F6F6 h-90px rounded-12px mx-30px mt-60px flex items-center">
                <van-field v-model="couponInput" center clearable placeholder="请输入优惠券兑换码">
                    <template #button>
                        <div class="bg-#65AAFF text-#fff text-32px px-36px rounded-8px h-70px leading-70px">确定</div>
                    </template>
</van-field>
</div> -->
                <div class="mt-64px flex flex-col items-center">
                    <div class="sancode bg-#F6F6F6 w-300px h-300px rounded-12px flex items-center justify-center">
                        <img loading="lazy" src="@/assets/merchants/sancode.png" alt="" class="w-99px h-92px" />
                    </div>
                    <div class="text-#808080 text-30px mt-38px">扫一扫二维码核销更方便</div>
                </div>
                <!-- 扫码核销 -->
                <div class="sancode-btn w-400px h-78px mx-auto text-center leading-none 
            mt-76px text-#fff text-32px flex items-center justify-center mb-26px"
                    @click="utils.scan('USER', [{ Name: 'scan' }]);">
                    扫码核销
                </div>
            </div>
            <div v-show="active == 'record'">
                <div class="nav-box w-full h-[60px] flex items-center justify-around mt-10px">
                    <div class="nav-item w-50% flex items-center justify-center" @click="onClickNav(index)"
                        :class="{ on: navActive == index }" v-for="(item, index) in navArr" :key="item.value">{{
                            item.title
                        }}</div>
                </div>

                <div class="p-25px">
                    <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef"
                        :slotEmpty="true">
                        <div class="recode-item py-25px" v-for="(item, index) in navArr[navActive].List" :key="index">
                            <div class="flex justify-between items-center mb-25px">
                                <div class="text-28px text-[#333] font-500">{{ item.couponName }}</div>
                                <span v-if="item.couponInfo.couponType === 'discount'"
                                    class="text-28px text-[#E63535] font-500">{{
                                        Number(item.couponInfo.discountPercent)
                                        || '-' }} 折</span>
                                <span v-else class="text-28px text-[#E63535] font-500">{{
                                    Number(item.couponInfo.discountAmount)
                                        ? `￥${item.couponInfo.discountAmount}` : ''
                                }}</span>

                            </div>
                            <div class="text-24px text-[#808080] mb-5px">核销时间：{{ item.updateTime }}</div>
                            <div class="text-24px text-[#808080]">核销用户：{{ item.userName }}</div>

                        </div>
                        <template #noData>
                            <van-empty :image="emptyImg" description="暂时没有记录哦" :image-size="['60%', 'auto']">
                            </van-empty>
                        </template>
                    </refreshList>


                </div>
            </div>
        </div>

    </div>
</template>
<script lang="ts" setup>
const active = ref('writeOff');
import emptyImg from '@/assets/integralMall/no_content.png'
import { getCompanyInfoByAccount, findRecordList, getVerificationRecord } from '@/api/merchants';
import { useUserStore } from "@/store/modules/user";
import refreshList from '@/components/refreshList/index.vue';
import utils from '@/utils/utils';
//动态列表
const loading = ref(true);
const loadMoreRef = ref(null);
const useStore = useUserStore();
const router = useRouter();
const tabArr = [
    { title: '商家核销', value: 'writeOff', clazz: 'write-info slide-in-left', },
    { title: '核销记录', value: 'record', clazz: 'record-info' },
];
const navActive = ref(0)
const navArr = ref([
    { title: '票券', value: 'ticket', clazz: '', pageNum: 1, List: [] },
    { title: '积分商品', value: 'shop', clazz: '', pageNum: 1, List: [] },
])
const onClickTab = ({ value }: Recordable) => {
    active.value = value;
};
const onClickNav = (index: any) => {
    navActive.value = index;
    onRefreshList()
};
const couponInput = ref('')
const campanyDetail = ref({})
const toDetail = () => {
    router.push({
        path: '/merchants/detail'
    });
}
const getDetail = () => {
    getCompanyInfoByAccount().then(res => {
        if (res.data) {
            campanyDetail.value = res.data
            loading.value = false;
        }
    }).catch(() => { })
}
const getTicketList = () => {
    findRecordList({
        pageSize: 10,
        pageNum: navArr.value[navActive.value].pageNum
    }).then(res => {
        console.log(res.total);

        if (navArr.value[navActive.value].pageNum === 1) {
            navArr.value[navActive.value].List = [];
        }
        navArr.value[navActive.value].List = navArr.value[navActive.value].List.concat(res.data);
        //重置刷新状态及 判断是否加载完成
        console.log(loadMoreRef.value);

        if (loadMoreRef.value) {

            loadMoreRef.value.onLoadSuc(navArr.value[navActive.value].List.length, res.total);
        }
    })
}
const getVerificationRecordList = () => {
    getVerificationRecord({
        pageSize: 10,
        pageNum: navArr.value[navActive.value].pageNum
    }).then(res => {
        res.data.forEach(item => {
            item.couponInfo = {
                couponType: ''
            }
            item.couponName = item.productName
            item.updateTime = item.usedTime
            item.userName = item.writeOffUserName
        })
        if (navArr.value[navActive.value].pageNum === 1) {
            navArr.value[navActive.value].List = [];
        }
        navArr.value[navActive.value].List = navArr.value[navActive.value].List.concat(res.data);
        //重置刷新状态及 判断是否加载完成
        console.log(loadMoreRef.value);

        if (loadMoreRef.value) {

            loadMoreRef.value.onLoadSuc(navArr.value[navActive.value].List.length, res.total);
        }
    })
}
getVerificationRecord
const getList = () => {
    if (navActive.value == 0) {
        getTicketList();
    } else {
        getVerificationRecordList();

    }
}
// 刷新
const onRefreshList = () => {
    navArr.value[navActive.value].pageNum = 1;
    getList();
};
// 加载更多
const onLoadMore = () => {
    navArr.value[navActive.value].pageNum++;
    getList();
};
onBeforeMount(
    () => {
        getDetail()
        getList()
    }
)
</script>
<style scoped lang="scss">
.merchants-writeOff {
    background-image: url('@/assets/merchants/store_bg.png');
    background-size: 100% auto;
    background-repeat: no-repeat;

    .store-info {
        .store {
            border-bottom: 1px solid #E6E6E6;
        }
    }
}

.nav-box {
    .nav-item {
        font-weight: 400;
        font-size: 28px;
        color: #333333;

        &.on {
            font-weight: 500;
            font-size: 28px;
            color: #5AA4FF;
        }
    }
}

.recode-item {
    border-bottom: 1px solid #E6E6E6;
}

.write-off {
    .tabs {
        background: linear-gradient(0deg, #FFFFFF 0%, #E3F2FF 100%);
        border-radius: 30px 30px 0 0;
    }

    .write-info.tab--active {
        background-image: url('@/assets/merchants/active_tab_left.png');
        background-repeat: no-repeat;
        background-size: 100% 70px;
    }

    .record-info.tab--active {
        background-image: url('@/assets/merchants/active_tab_right.png');
        background-repeat: no-repeat;
        background-size: 100% 70px;
    }

    .coupon-input {
        :deep(.van-cell) {
            background-color: transparent;
        }

        :deep(.van-field__control) {
            font-size: 30px;
        }

        :deep(.van-cell) {
            padding: 10px;
            padding-left: 20px;
        }
    }

    .sancode-btn {
        background-image: url('@/assets/public/butto.png');
        background-size: 100% auto;
    }
}
</style>