<template>
  <!-- 搜索框灰色背景 -->
  <div class="search flex justify-between items-center mb-28px">
    <van-search v-model="searchVal" show-action :placeholder="placeholder" @search="onSearch" :clearable="false"
      :left-icon="getIcon()" :class="props.showBtn ? '' : '!w-100%'" input-align="left">
      <template #action>
        <div @click="onSearch" class="search-btn">搜索</div>
      </template>
    </van-search>
    <div class="mr-10px text-24px text-#fff publish w-160px h-60px flex items-center justify-end pr-18px border-box"
      @click="btnPublish()" v-if="props.showBtn">{{ title }}</div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  showBtn: {
    type: Boolean,
    default: true
  },
  placeholder: {
    type: String,
    default: '请输入搜索关键词'
  }
})
//获取图标
const getIcon = () => {
  let img = new URL(`@/assets/public/icon_search.png`, import.meta.url).href
  return img
}

const emit = defineEmits(['onSearch', 'btnPublish']);
const searchVal = ref(null)
//搜索
function onSearch() {
  emit("onSearch", searchVal.value);
}
//发布操作
function btnPublish() {
  emit("btnPublish");
}
</script>

<style lang="scss" scoped>
.search {
  .search-btn {
    width: 104px;
    height: 54px;
    text-align: center;
    background: rgba(90, 164, 255, .1);
    border-radius: 27px;
    line-height: 54px;
    font-size: 26px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #5AA4FF;
  }

  :deep(.van-search) {
    padding: 0px;
    padding-right: 16px;
    background-color: #FFFFFF;
    border-radius: 34px;
    width: 70%;

    .van-search__content {
      border-radius: 34px;
      background-color: #FFFFFF;
    }

    .van-search__action {
      padding-right: 0;
    }

    .van-field__left-icon {
      display: flex;
      align-items: center;

      img {
        width: 25px;
        height: 25px;
      }
    }
  }

  .publish {
    background: url("@/assets/public/publish.png") no-repeat;
    background-size: 100% 100%;
  }

  :deep(.van-field__control) {
    font-size: 28px;
  }

  :deep(.van-search__field) {
    height: 100%;
  }
}
</style>