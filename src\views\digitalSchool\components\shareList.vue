<template>
    <div class="topic mb-25px p-28px  bg-#fff mb-21px text-#333 rounded-20px text-28px"
        :class="type == 'examine' ? 'text-#999' : ''" v-for="(item, index) of data" :key="index" @click="toPage(item)">
        <div class="flex mb-30px">
            <img loading="lazy" src="@/assets/public/head_default.png" alt="" class="w-76px h-76px topic-icon">
            <div class="topic-right pl-33px flex-1">
                <div>{{ item.userName }}</div>
                <div class=" text-20px mt-16px text-#999">{{ item.createTime }}</div>
            </div>
            <div class="rounded-18px border-solid border-#999 border-1px text-22px font-400 w-111px h-38px flex items-center justify-center text-#999"
                v-if="props.isShowDetail">查看详情</div>
        </div>
        <slot name="centerContent" :item="item">
        </slot>
        <div class="text-28px mt-30px" :class="type == 'examine' ? 'text-#999' : 'text-#666'">
            <div :class="isShowDetail ? 'line-clamp-3' : ''">
                {{ item.describes }}
            </div>
        </div>
        <div class="flex justify-between">
            <img loading="lazy" :src="judgeStaticUrl(src)" alt="" v-for="(src, i) of item?.file?.split(',')" :key="i"
                class="w-200px max-h-full mt-20px rounded-15px" @click.stop="previewImage(i, item)">
        </div>
        <slot name="bottomContent" :item="item">
            <div class="bg-#f6f7f8 rounded-10px px-30px py-20px box-border text-28px text-#666 mt-[22px]"
                v-if="props.type == 'examine' && item.auditRemarks"><span class="text-#999">拒绝理由：</span>{{
                    item.auditRemarks }}</div>
        </slot>

    </div>
</template>
<script lang="ts" setup>
import { showImagePreview } from 'vant';
import { judgeStaticUrl } from '@/utils/utils';
const props = defineProps({
    data: {
        type: Array,
        default: [],
    },
    type: {
        type: String,
        default: 'normal',//examine-审核信息展示审核信息
    },
    isShowDetail: {
        type: Boolean,
        default: true
    }

})
const val = ref(null)
const emit = defineEmits(['detail']);
//详情
function toPage(item) {
    emit('detail', item)
}
// 图片预览
const previewImage = (index: number, item: any) => {
    showImagePreview({
        images: imageFilter(item?.file),
        startPosition: index,
    })
}
const imageFilter = (imgUrl: any) => {
    if (!Array.isArray(imgUrl)) {
        return imgUrl.split(',').map((item: string) => {
            return judgeStaticUrl(item)
        })
    }
}
</script>
<style lang="scss">
.topic {
    .topic-icon {
        border-radius: 50%;
    }
}
</style>