<template>
    <div class="integral_details w-full pt-23px min-h-100vh box-border">
        <div class="card relative mx-30px">
            <img loading="lazy" src="@/assets/integralMall/integral_details_bg.png" class="w-full" />
            <div class="absolute top-70px left-70px text-center">
                <div class="num text-52px text-[#fff] mb-10px">{{ userIntegral }}</div>
                <div class="tags text-[#0F85FF] text-28px bg-[#fff] px-20px py-11px rounded-24px">可用积分</div>
            </div>
        </div>
        <div class="tabs px-30px h-80px flex items-center justify-between bg-[#fff] sticky top-0 z-99">
            <div class="tab flex-1">
                <van-tabs v-model:active="activetab" background="transparent" title-inactive-color="#333333"
                    title-active-color="#333333" @click-tab="onClickTab">
                    <van-tab class="flex-1" v-for="item, index in tabs" :name="item.value" :title="item.name"
                        :key="index">
                    </van-tab>
                </van-tabs>
            </div>

            <div class="date_select rounded-23px py-10px px-15px ml-25px flex items-center" @click="handleDatePicker">
                <span class="text-[#333] text-24px leading-none">
                    {{ currentDate[0] }}年{{ currentDate[1] }}月
                </span>
                <div class="rotate-90 text-[#A1A1A1] leading-none mb-5px">
                    <van-icon name="play" />
                </div>
            </div>
            <div class="filters flex items-center ml-15px" @click="filterShow" v-show="activetab === '1'">
                <img loading="lazy" src="@/assets/integralMall/filter_icon_blue.png" class="w-28px">
                <span class="text-#418DEA text-26px">筛选</span>
            </div>
        </div>
        <!-- 列表 -->
        <div class="card_list mb-80px rounded-16px mx-30px mt-20px">
            <refreshList ref="loadMoreRef" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore">
                <template v-if="activetab == '0'">
                    <integralCell :content="item" titleKey="remark" v-for="item, index in list" :key="index" />
                </template>
                <template v-else>
                    <orderMallCell v-for="item, index in list" :key="index" :showBottom="index < list.length - 1"
                        :content="item" @click="lookDetail(item, index)">
                        <template #exchangeTime>
                            <div class="text-#666 text-22px">兑换时间：{{ item?.createTime }}</div>
                        </template>
                    </orderMallCell>
                </template>
            </refreshList>
        </div>
        <van-popup v-model:show="dateSelectShow" position="bottom">
            <!-- 日期选择器 -->
            <van-date-picker v-model="targetDate" title="选择年月" :min-date="minDate" :max-date="maxDate"
                :formatter="formatter" :columns-type="columnsType" @confirm="confirmDate" />
        </van-popup>
        <codePopup v-model:show="qrCodeShow" :url="qrCodeUrl"></codePopup>

        <!-- 订单搜索项 -->
        <van-popup v-model:show="orderFilters" position="bottom" round>
            <div class="filters_list">
                <div class="title text-28px flex items-center px-28px h-103px">
                    <div class="text-34px font-500 text-#333 flex-1">订单筛选</div>
                    <div class="text-28px text-#5AA4FF" @click="orderFilters = false">取消</div>
                </div>
                <div class="px-30px">
                    <div class="text-#636569 text-28px">发货方式</div>
                    <div class="flex items-center">
                        <div class="text-#636569 bg-#F2F3F5 text-28px h-70px 
                        rounded-40px px-45px flex items-center leading-none mr-20px mt-20px"
                            @click="exchangeActive = item.value; orderActive = ''"
                            :class="{ '!bg-#5AA4FF !text-#fff': exchangeActive == item.value }"
                            v-for="item, index in exchangeTabs" :key="index">
                            {{ item.title }}
                        </div>
                    </div>
                </div>
                <div class="px-30px mt-30px">
                    <div class="text-#636569 text-28px">兑换来源</div>
                    <div class="flex items-center">
                        <div class="text-#636569 bg-#F2F3F5 text-28px h-70px 
                        rounded-40px px-45px flex items-center leading-none mr-20px mt-20px"
                            @click="sourceActive = item.value;"
                            :class="{ '!bg-#5AA4FF !text-#fff': sourceActive == item.value }"
                            v-for="item, index in sourceList" :key="index">
                            {{ item.label }}
                        </div>
                    </div>
                </div>
                <div class="px-30px mt-30px" v-show="exchangeActive">
                    <div class="text-#636569 text-28px">订单状态</div>
                    <div class="flex flex-wrap items-center">
                        <div class="text-#636569 bg-#F2F3F5 text-28px h-70px 
                        rounded-40px px-45px flex items-center leading-none mr-20px mt-20px"
                            :class="{ '!bg-#5AA4FF !text-#fff': orderActive == item.value }"
                            @click="orderActive = item.value" v-for="item, index in orderStatus[exchangeActive]"
                            :key="index">
                            {{ item.title }}
                        </div>
                    </div>
                </div>

                <div class="flex px-70px items-center justify-between my-80px">
                    <div @click="resetFilter"
                        class="reset-btn rounded-40px h-78px text-34px flex-1 mr-48px flex items-center justify-center leading-none">
                        重置
                    </div>
                    <div @click="comfirmFilter"
                        class="comfirm-btn rounded-40px h-78px text-34px flex-1 flex items-center justify-center leading-none">
                        确认
                    </div>
                </div>
            </div>
        </van-popup>
    </div>
</template>
<script setup lang="ts">
import integralCell from './components/integralCell.vue'
import orderMallCell from "./components/orderMallCell.vue";
import refreshList from '@/components/refreshList/index.vue';
import { myIntegralNum, myIntegralList, integralGoodsExchangeList, getIntegralGoodsQrCode } from '@/api/mall/integral'
import { useUserStore } from '@/store/modules/user';
import codePopup from './components/codePopup.vue';
import { closeToast, showLoadingToast, showToast } from 'vant';
import useRefreshFun from '@/hooks/app.ts'
import { useDictionary } from '@/store/modules/dictionary';
const dictionary = useDictionary()

onMounted(() => {
    initalPage()
})
const route = useRoute()
const useStore = useUserStore();
const userInfo = computed(() => useStore.userInfo)

const tabs = [
    {
        name: '积分明细',
        value: '0'
    },
    {
        name: '兑换记录',
        value: '1'
    },
]
const orderFilters = ref(false)
// 兑换记录
const exchangeActive = ref('')
const exchangeTabs = [
    {
        title: "全部",
        value: ''
    },
    {
        title: "线上发货",
        value: '1'
    },
    {
        title: "线下兑换",
        value: '2'
    }
]

const sourceList = computed(() => {
    return [
        { label: '全部', value: '' },
        ...dictionary.getDictionaryOpt?.['exchangeSource']
    ]

})
const sourceActive = ref(sourceList.value[0].value)

const orderActive = ref('')
const orderStatus = {
    '1': [
        {
            title: "全部",
            value: ''
        },
        {
            title: "未发货",
            value: 'deliver'
        },
        {
            title: "待收货",
            value: 'receive'
        },
        {
            title: "已完成",
            value: 'over'
        },
    ],
    '2': [
        {
            title: "全部",
            value: ''
        },
        {
            title: "未核销",
            value: 'wait'
        },
        {
            title: "已核销",
            value: 'used'
        },
    ]
}
const filterShow = () => {
    exchangeActive.value = integralPayment.value
    if (exchangeActive.value === '1') orderActive.value = deliveryStatus.value
    else if (exchangeActive.value === '2') orderActive.value = state.value
    else {
        orderActive.value = ''
    }
    orderFilters.value = true
}
const resetFilter = () => {
    exchangeActive.value = ''
    orderActive.value = ''
    sourceActive.value = sourceList.value[0].value
}

const comfirmFilter = () => {
    integralPayment.value = exchangeActive.value
    if (integralPayment.value === '1') {
        deliveryStatus.value = orderActive.value
        state.value = ''
    }
    else if (integralPayment.value === '2') {
        state.value = orderActive.value
        deliveryStatus.value = ''
    }
    else {
        deliveryStatus.value = ''
        state.value = ''
    }
    orderFilters.value = false
    if (loadMoreRef.value) loadMoreRef.value.resetStatus()
    onRefreshList()
}
const onClickTab = () => {
    if (loadMoreRef.value) loadMoreRef.value.resetStatus()
    onRefreshList()
}
// end


const activetab = ref<any>('0')
const isSet = ref(false)
watch(() => route.query, (val) => {
    if (isSet.value) return
    activetab.value = val.type
    isSet.value = true
}, { immediate: true, deep: true })

const userIntegral = ref<any>('')
// 初始化页面
const initalPage = async () => {
    loadMoreData()
    const { code, data } = await myIntegralNum({
        userId: userInfo.value?.userId
    })
    if (code === 200) userIntegral.value = data?.userIntegral
}
// 列表
const list = ref<any>([]);
const pageNum = ref(1);
const loadMoreRef = ref<any>(null)
const deliveryStatus = ref('')
const state = ref('')
const integralPayment = ref('')
const onRefreshList = () => {
    pageNum.value = 1
    list.value = []
    loadMoreData()
}
// 加载更多
const onLoadMore = () => {
    pageNum.value++
    loadMoreData()
}
const loadMoreData = async () => {
    let mycode, mytotal, mydata

    if (activetab.value === '0') {
        const { code, data, total } = await myIntegralList({
            userId: userInfo.value?.userId,
            queryTime: currentDate.value.join('-') + '-01',
            pageNum: pageNum.value,
            pageSize: 10,
            orderBy: 'create_time',
            sortType: 'desc',
        })
        mycode = code
        mytotal = total
        mydata = data
    }
    else {
        const { code, data, total } = await integralGoodsExchangeList({
            queryTime: currentDate.value.join('-') + '-01',
            pageNum: pageNum.value,
            pageSize: 10,
            integralPayment: integralPayment.value,
            state: state.value,
            exchangeSource: sourceActive.value,
            deliveryStatus: deliveryStatus.value,
            orderBy: 'create_time',
            sortType: 'desc',
        })
        mycode = code
        mytotal = total
        mydata = data
    }

    if (mycode === 200) {
        if (pageNum.value == 1) list.value = mydata
        else list.value = [...list.value, ...mydata]
    }
    //重置刷新状态及 判断是否加载完成
    if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(list.value.length, mytotal)
    }
}
// 查看详情
const qrCodeShow = ref(false)
const qrCodeUrl = ref('')
let btnClick = false
const router = useRouter()
const lookDetail = async (item: any, index: number) => {
    // 线上发货
    if (item.integralPayment === '1') {
        router.push({
            path: '/integralMall/order/detail',
            query: {
                recordId: item.recordId,
            }
        })
        currentIndex = index //点击详情记录索引值
    }
    else {
        if (item.state === 'used') {
            // 存储当前核销信息
            router.push({
                path: '/integralMall/order/writeOff',
                query: {
                    recordId: item.recordId,
                }
            })
            return
        }
        if (btnClick) return;
        btnClick = true
        currentIndex = index
        showLoadingToast({
            message: '获取核销码...',
            forbidClick: true
        })
        // const { code, data, message } = await getIntegralGoodsQrCode(item.recordId)
        // if (code === 200) {
        //     qrCodeUrl.value = data
        //     qrCodeShow.value = true
        //     currentIndex = index //索引值
        //     qrCodeShowClose()
        // }
        // else {
        //     showToast({
        //         message,
        //         icon: 'none'
        //     })
        // }
        await qrCodeShowClose()
        btnClick = false
        closeToast()
    }
}
// 获取核销码状态
// 隐藏关闭
watch(() => qrCodeShow.value, (val) => {
    if (!val) {
        // clearInterval(timer.value)
        isPolling = false;
        currentIndex = '';
        isPolling = false;
        clearTimeout(timer.value)
    }
})
const timer = ref<any>(null)
let currentIndex:any = 0
let isPolling = false;
let maxWaitTime = 5 * 60 * 1000; //最大等待时间（毫秒）
const pollInterval = 10000; //轮询间隔时间（毫秒）
let waitTime = 0; //当前等待时间（毫秒）
const qrCodeShowClose = async () => {
    // if (timer.value) clearInterval(timer.value)
    // timer.value = setInterval(async () => {
    //     if (isPolling) return;
    //     isPolling = true;
    //     try {
    //         const { code, data } = await getIntegralGoodsQrCode(list.value[currentIndex].recordId)
    //         if (!data) {
    //             qrCodeShow.value = false
    //             qrCodeUrl.value = ''
    //             list.value[currentIndex].state = 'used'
    //             clearInterval(timer.value)
    //         }
    //     }
    //     catch (err) {
    //         clearInterval(timer.value)
    //     }
    //     finally {
    //         isPolling = false;
    //     }

    // }, 5000)
    if (isPolling) return;
    isPolling = true;
    let isWait = true//是否开始轮询
    try{
        const { code, data,message } = await getIntegralGoodsQrCode(list.value[currentIndex].recordId)
        if (code === 200) {
            qrCodeUrl.value = data
            qrCodeShow.value = true
        }
        else if (!data) {
            showToast({
                message,
                icon: 'none'
            })
            qrCodeShow.value = false
            qrCodeUrl.value = ''
            list.value[currentIndex].state = 'used'
            isWait = false//停止轮询
        }
    }
    catch(err){
        isWait = false//停止轮询
    }
    finally{
        clearTimeout(timer.value)
        if(isWait && waitTime < maxWaitTime ) {
            timer.value = setTimeout(() => {
                waitTime += pollInterval; //每次轮询增加等待时间
                qrCodeShowClose()
            }, pollInterval)
        } else {
            waitTime = 0;
        }
        isPolling = false;
    }
}
// end

// 日期展示
const currentMonth = new Date().getMonth() + 1 < 10 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1
const currentDate = ref([new Date().getFullYear(), currentMonth])
const targetDate = ref<any>()
const columnsType = ['year', 'month']
const minDate = new Date(1949, 0, 1)
// 最大月份当月
const getYear = new Date().getFullYear()
const getMonth = new Date().getMonth()
const maxDate = new Date(getYear, getMonth, 1)

const formatter = (type, option) => {
    if (type === 'year') {
        option.text += '年';
    }
    if (type === 'month') {
        option.text += '月';
    }
    return option;
};


const dateSelectShow = ref(false)
const handleDatePicker = () => {
    targetDate.value = currentDate.value
    dateSelectShow.value = true
}
const confirmDate = ({ selectedValues }) => {
    currentDate.value = selectedValues
    dateSelectShow.value = false
    onRefreshList()
}
// 局部更新数据列表 更改收货状态
const changeOrderConfirm = () => {
    list.value[currentIndex].deliveryStatus = 'over'
}
// 其他页面触发刷新执行事件
const { refresh } = useRefreshFun()
refresh([
    { name: 'initalPage', funsName: [initalPage] },
    { name: 'changeOrderConfirm', funsName: [changeOrderConfirm] }
])
// end

</script>
<style lang="scss" scoped>
.date_select {
    border: 2px solid #CCCCCC;
}

.tabs {
    :deep(.van-tabs--line .van-tabs__wrap) {
        height: 80px;
    }

    :deep(.van-tabs__line) {
        width: 40px;
        height: 6px;
        background: linear-gradient(86deg, #5AA4FF 0%, #C7E0FF 100%);
        border-radius: 3px;
    }

    :deep(.van-tab__text) {
        font-size: 30px;
    }

    :deep(.van-tabs__nav--line) {
        height: 100%;
        padding-bottom: 15px;
    }

    :deep(.van-tab) {
        line-height: 1;
        height: 100%;
    }
}

.card_list {
    box-shadow: 0px 3px 10px 0px rgba(119, 151, 203, 0.15);
    min-height: 60vh;
}

.reset-btn {
    border: 1px solid #63A6FE;
    color: #5AA4FF;
}

.comfirm-btn {
    background: linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%);
    color: #fff;
}
</style>