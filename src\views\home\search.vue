<template>
    <div class="search w-full">
        <div class="px-30px py-44px">
            <Search @on-search="searchVal" :showBtn="false" />
        </div>
        <div class="content bg-#fff px-30px  min-h-85vh">
            <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef"
                v-if="Data.list.length">
                <div v-for="(item, index) of Data.list" :key="index" class="py-33px flex items-center"
                    @click="toDetail(item)">
                    <img loading="lazy" src="@/assets/public/icon_search.png" alt="" class="w-25px h-25px mr-22px">
                    <div class="text-28px truncate w-85%" v-html="item.newsName"></div>
                </div>
            </refreshList>
            <div class="pt-20px" v-if="!Data.list.length">
                <div class="text-#5AA4FF text-30px">热门搜索</div>
                <div class="rank-list flex justify-between text-28px items-center" v-for="(item, index) in Data.hotList"
                    :key="index" @click="toModule(item)">
                    <span class="w-38px h-38px rounded-6px text-#666 text-center">{{ index + 1 }}</span>
                    <p class="truncate flex-1 color-#333 ml-22px">{{ item.dataName }}</p>
                </div>
            </div>
            <Empty v-if="!Data.hotList.length" />
        </div>
    </div>
</template>
<script lang="ts" setup>
import { homePageSearchGetNewsList, getPopularSearches, addPopularity } from '@/api/news';
import Search from '@/components/Search/index.vue';
import refreshList from '@/components/refreshList/index.vue';
import { showToast } from 'vant';
import { readOperate } from '@/api/public';
import utils from '@/utils/utils';
// import dayjs from 'dayjs';
import Empty from '@/components/Empty/index.vue';
const Data = ref({
    searchVal: null,
    pageNum: 1,
    list: [],
    hotList: []
})
//搜索
function searchVal(val) {
    if (!val) {
        showToast("请输入关键字查询");
        return
    }
    Data.value.searchVal = val;
    onRefreshList()
}
// 刷新
const onRefreshList = () => {
    Data.value.pageNum = 1;
    getList();
};
// 加载更多
const onLoadMore = () => {
    Data.value.pageNum++;
    getList();
};
//搜索列表
const loadMoreRef = ref(null)
function getList() {
    homePageSearchGetNewsList({
        platformType: 30,
        pageNum: Data.value.pageNum,
        pageSize: 10,
        searchTitle: Data.value.searchVal
    }).then(res => {
        if (res.code == 200) {
            res.data.map(el => {
                el.newsName = el.newsTitle.replace(Data.value.searchVal, `<text style="color:#5AA4FF">${Data.value.searchVal}</text>`);
            })
            if (Data.value.pageNum === 1) Data.value.list = [];
            Data.value.list = Data.value.list.concat(res.data);
            //重置刷新状态及 判断是否加载完成
            if (loadMoreRef.value) {
                loadMoreRef.value.onLoadSuc(Data.value.list.length, res.total);
            }
        }
    })
}
//详情
function toDetail(item) {
    addPopularity({ redisKey: item.newsId }).then(res => { })
    if (item?.whetherExternalLink) {
        readOperate({ sourceId: item?.newsId }).then(res => { })
    }
    // 2025-1-8 外部链接在新闻详情页嵌套展示
    utils.openNewsLink({
        title: item?.newsTitle,
        url: item?.externalLinkAddress,
        shareTitle: item?.newsTitle,
        shareUrl: item?.externalLinkAddress,
        dataId: item?.newsId,
    }, false, item?.categoryCode)

    return false;
    utils.openNewsLink({
        title: item?.newsTitle,
        url: item?.externalLinkAddress,//window.location.origin+'/newsDetail?newsId='+item?.newsId+'&categoryCode='+props?.categoryCode,
        shareTitle: item?.newsTitle,
        shareUrl: item?.externalLinkAddress,//window.location.origin+'/newsDetail?newsId='+item?.newsId+'&categoryCode='+props?.categoryCode,
        dataId: item?.newsId,
    }, item?.whetherExternalLink, item?.categoryCode)

}
//热门搜索跳转
function toModule(item) {
    addPopularity({ redisKey: item.redisKey }).then(res => { });
    toDetail(item.dataObject)

}
//热门搜索
function getHotList() {
    getPopularSearches({}).then(res => {
        if (res.code == 200) {
            Data.value.hotList = res.data
        }
    })
}
onMounted(() => {
    getHotList();
})
</script>
<style lang="scss" scoped>
.search {
    background: url('@/assets/home/<USER>') no-repeat;
    background-size: 100%, 100%;

    .content {
        border-radius: 30px 30px 0 0;
    }

    .rank-list:nth-child(2) {
        span {
            color: #ffffff;
            background: #e6503c;
        }
    }

    .rank-list:nth-child(3) {
        span {
            color: #ffffff;
            background: #f98f31;
        }
    }

    .rank-list:nth-child(4) {
        span {
            color: #ffffff;
            background: #fcc651;
        }
    }
}
</style>