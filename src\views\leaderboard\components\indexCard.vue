<template>
    <div class="active-card py-38px px-30px rounded-20px mt-24px">
        <div class="head-title flex items-center">
            <img :src="getIcon()" alt="" />
            <div class="text-32px text-#333 ml-18px">{{ cardItem?.title }}</div>
        </div>
        <div class="text-24px text-#666 flex items-end mt-42px" v-for="item, index in cardItem?.data" :key="index">
            <div class="rank bg-#EDF7FF text-#63A8FF px-12px py-14px text-24px rounded-10px">第{{ item.rank }}名</div>
            <div class="flex-1 ml-20px">
                <div class="num flex justify-between">
                    <div class="text-24px text-#666">{{ item.title }}</div>
                    <div class="text-30px text-#333" style="font-family: Arial">{{ item.num }}</div>
                </div>
                <div class="progress h-14px w-full bg-#F0F0F0 mt-10px rounded-8px">
                    <div class="progress-bar" :style="`width:${item.progress}%`"></div>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>


const props = defineProps({
    cardItem: {
        type: Object,
        default: () => ({
            title: '用户活跃度指数',
            icon: 'lively',
            data: [
                {
                    rank: 3,
                    title: '用户访问量',
                    num: 136765,
                    progress: 40
                }
            ]
        })
    }
})
//获取图标
const getIcon = () => {
    let img = new URL(`../../../assets/leaderBoard/ali/icon_${props.cardItem?.icon}.png`, import.meta.url).href
    return img
}
</script>
<style scoped lang="scss">
.active-card {
    background: linear-gradient(0deg, #FFFFFF 54%, #E5F3FF 100%);

    .progress-bar {
        width: 70%;
        height: 100%;
        background: linear-gradient(90deg, #A1CBFF, #5AA4FF);
        border-radius: 8px;
    }
}
</style>