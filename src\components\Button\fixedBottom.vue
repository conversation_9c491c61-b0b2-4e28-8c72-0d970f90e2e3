<template>
  <div
    class="button fixed h-140px bottom-0 left-0 w-100% bg-#fff px-95px py-30px box-border"
  >
    <div
      class="bg-no-repeat flex items-center justify-center text-34px text-#fff bg-center w-100% h-100% rounded-40px"
      :style="{
        backgroundImage: `url(${disable ? button_grey : button})`,
        backgroundSize:'contain'
      }"
      style="backgroundsize: 100% 100%;backgroundposition: center;"

    >
      {{ name }}
    </div>
  </div>
</template>
  <script lang="ts" setup>
import button from "@/assets/public/button.png";
import button_grey from "@/assets/public/button_grey.png";
const props = defineProps({
  name: {
    type: String,
    default: "",
  },
  disable: {
    type: Boolean,
    default: false,
  },
});
</script>
  <style lang="scss" scoped>
.button {
  box-shadow: 0px -2px 10px 0px rgba(183, 192, 204, 0.2);
}
</style>