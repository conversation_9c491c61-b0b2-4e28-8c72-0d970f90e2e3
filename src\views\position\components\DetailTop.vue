<template>
  <div class="max-w-100vw px-[30px] relative top-[-100px]" :class="$style['detail-top']">
    <div class="bg-[#fff] p-[20px] rounded-[16px]">
      <div class="flex-col flex">
        <van-skeleton title :row="4" :loading="loading">
          <div class="text-[#333333] text-[30px] font-bold text-left w-full truncate">
            {{ detailRecord.positionName || '' }}
          </div>
          <div class="star flex items-center">
            <img loading="lazy" :src="item <= detailRecord?.positionScore ? starOrange : starGray" v-for="item in 5"
              :key="`star-${item}`" class="w-[26px] h-[26px] mr-1" />
            <span class="px-3 text-24px">{{ point }}</span>

            <van-tag color="#FCF0E2" class="text-[#A94800] px-[5px] py-[5px] text-24px">
              {{ detailRecord.typeName || '' }}
            </van-tag>
          </div>
          <div class="flex items-center text-[24px]">
            <img loading="lazy" :src="icon_time" class="w-[24px] h-[24px] mr-1" />
            <span class="text-[#808080] my-0.5">开放时间：</span>
            <span class="text-[#333333]">{{ fixOpenWeekDay(detailRecord) }}</span>
            &nbsp;&nbsp;

            <div class="text-left text-[24px] text-[#333333]">
              <span v-if="detailRecord.morningOpenTime && detailRecord.morningCloseTime">
                {{ exchangeTime(detailRecord.morningOpenTime) }} -
                {{ exchangeTime(detailRecord.morningCloseTime) }}
              </span>
              &nbsp;
              <span v-if="detailRecord.afterOpenTime && detailRecord.afterCloseTime">
                {{ exchangeTime(detailRecord.afterOpenTime) }} -
                {{ exchangeTime(detailRecord.afterCloseTime) }}
              </span>
            </div>
          </div>

          <div class="flex items-center text-[24px]" v-if="detailRecord?.manager || detailRecord?.phone">
            <img loading="lazy" :src="icon_fzr" class="w-[24px] h-[24px] mr-1" />
            <span class="text-[#808080]">负责人：</span>
            <span class="text-[#333333] mx-1 my-0.5">{{ detailRecord?.manager || '' }}</span>
            <span class="text-[#5BA5FF]">{{ detailRecord?.phone || '' }}</span>
          </div>
          <div class="flex items-center text-[24px]" v-if="detailRecord?.capacity > 0">
            <img loading="lazy" :src="icon_people" class="w-[24px] h-[24px] mr-1" />
            <span class="text-[#808080]">容纳人数：</span>
            <span class="text-[#333333] mt-0.5">{{ detailRecord?.capacity || 0 }} 人</span>
          </div>
        </van-skeleton>
      </div>
      <div class="line my-2" :style="{ borderBottom: '1px solid #EBEBEB' }" />
      <div class="flex items-center justify-between" @click="mapNav()">
        <div class="w-4/5">
          <div class="text-[#333333] text-[30px] font-bold text-left w-full">
            {{ detailRecord.address || '' }}
          </div>
          <div class="flex items-center text-[24px]">
            <span class="text-[#808080]">距您{{ fixDistance(detailRecord?.actualDistance) }}</span>
          </div>
        </div>
        <img loading="lazy" :src="navigation"
          class="w-[60px] h-[60px] relative right-3 active:shadow-[0_0_5px_0_#5ba5ff] active:rounded-1/2" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import icon_people from '@/assets/position/icon_people.png';
import icon_time from '@/assets/position/icon_time.png';
import icon_fzr from '@/assets/position/icon_fzr.png';
import starGray from '@/assets/position/star-gray.png';
import starOrange from '@/assets/position/star-orange.png';
import navigation from '@/assets/position/navigation.png';
import { fixDistance, fixOpenWeekDay, exchangeTime } from '../utils';
import { isEmpty } from 'lodash-es';
import utils from '@/utils/utils';

const detailRecord = inject<Recordable>('detailRecord', {});

const point = computed(() => ((unref(detailRecord)?.positionScore as number) || 0).toFixed(1));

const loading = computed(() => isEmpty(unref(detailRecord)));
function mapNav() {
  let marker = unref(detailRecord)?.coordinate.split(',');
  utils.mapNav({
    win: unref(detailRecord)?.autoId,
    name: unref(detailRecord)?.positionName,
    lat: marker[1],
    lon: marker[0],
  });
}
</script>

<style lang="less" module>
.detail-top {
  :global {}
}
</style>
