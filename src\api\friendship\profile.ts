// 个人信息api
import { h5Http } from '@/utils/http/axios';
// 获取标签
export function getLabelList(params:any) {
  return h5Http.get({ url: '/singleLabel/findVoList', params });
}
// 获取协议
export function getAgreement() {
  return h5Http.get({ url: '/singleInfo/singleText'});
}
// 获取申请记录
export function getSingleRecord(params:any) {
  return h5Http.get({ url: '/singleInfo/queryApplyRecord',params });
}
// 新增单身联谊档案
export function saveSingleForm(params:any) {
  return h5Http.post({ url: '/singleInfo/applySingle', params });
}
// 编辑单身联谊档案
export function updateSingleForm(params:any) {
  return h5Http.post({ url: '/singleInfo/updateSingle', params });
}

// 动态修改或发布
export function pushPost(params:any) {
  return h5Http.post({ url: '/singlePost/pushPost', params });
}
