<template>
  <div class="examine-page">
    <div class="tab-box">
      <van-tabs v-model:active="Data.tab.active" sticky color="#5AA4FF" title-active-color="#5AA4FF"
        title-inactive-color="#333333" line-width="30" @click-tab="onClickTab">
        <van-tab :title="item.name" v-for="(item, index) in Data.tab.nav" :key="index"
          title-class="tab-title"></van-tab>
      </van-tabs>
    </div>
    <div class="px-[40px] bg-[#fff] box-border two-tab-box">
      <van-tabs v-model:active="Data.twoTab.active" sticky type="card" title-active-color="#5AA4FF"
        title-inactive-color="#333333" @click-tab="onClickTwoTab">
        <van-tab :title="item.name" v-for="(item, index) in Data.twoTab.nav" :key="index"></van-tab>
      </van-tabs>
    </div>
    <div class="px-[30px] box-border">
      <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
        <div v-for="(item, index) in Data.list" :key="index"
          class="bg-[#fff] rounded-[20px] mb-[20px] p-[25px] box-border" @click="toDetails(item)">
          <div class="flex items-center">
            <img loading="lazy" :src="dealType(item, 'icon')" alt="" class="w-[46px] h-[46px] mr-[23px]" />
            <p class="flex-1 text-[30px] text-[#333] font-medium">
              {{ dealType(item, "text") }}
            </p>
            <span class="text-[24px] text-[#999]">{{ dayjs(item.createTime).format('YYYY-MM-DD hh:mm') }}</span>
          </div>
          <div v-if="item.sourceType === 'join'">
            <!-- 小组申请 -->
            <div class="flex items-start text-[28px] text-[#333]">
              申请加入
              <div class="text-[#666] flex-1 ml-[16px]">“{{ item.title }}”</div>
            </div>
            <div class="flex items-start text-[28px] text-[#333] mt-[20px]">
              申请原因：
              <div class="text-[#666] flex-1">{{ item.content }}</div>
            </div>
            <div class="flex items-start text-[28px] text-[#333] mt-[20px]" v-if="Data.tab.active === 2">
              失败原因：
              <div class="text-[#666] flex-1">{{ item.auditOpinion }}</div>
            </div>
            <div v-if="Data.tab.active === 0 && Data.twoTab.active === 1"
              class="flex items-center pt-[20px] mt-[20px] box-border border-t-[1px] border-t-[#EBEBEB] border-t-solid">
              <div class="flex items-center justify-end w-[100%]">
                <van-button block @click.stop="onRefuse(item, 'refuse')"
                  class="w-[128px] h-[48px] rounded-[39px] justify-center flex items-center text-[30px] text-[#5AA4FF] border-[1px] border-[#5AA4FF] border-solid mr-[30px]">
                  拒绝
                </van-button>
                <van-button block
                  class="w-[130px] h-[50px] rounded-[39px] justify-center flex items-center text-[30px] text-[#fff]"
                  style="
                  background: linear-gradient(0deg, #a1cbff 0%, #5aa4ff 100%);
                " @click.stop="onPass(item, 'pass')">
                  通过
                </van-button>
              </div>
            </div>
          </div>

          <div v-else>
            <div v-if="Data.tab.active === 2">
              <div class="flex items-start text-[28px] text-[#333] mt-[20px]">
                {{ dealType(item, "name") }}：
                <van-rate v-if="item.score" :readonly="true" v-model="item.score" :size="20" color="#ffd21e"
                  void-icon="star" void-color="#BFBFBF" class="pt-20px" />
                <div class="text-[#666] flex-1">{{ item.content }}</div>
              </div>
              <div class="flex items-start text-[28px] text-[#333] mt-[20px]">
                失败原因：
                <div class="text-[#666] flex-1">{{ item.auditOpinion }}</div>
              </div>
            </div>
            <div class="text-[28px] text-[#333]" v-else>
              <van-rate v-if="item.score" :readonly="true" v-model="item.score" :size="20" color="#ffd21e"
                void-icon="star" void-color="#BFBFBF" class="pt-20px" />
              {{ item.content }}
            </div>
            <div v-if="Data.tab.active === 0 && Data.twoTab.active === 1"
              class="flex items-center pt-[20px] mt-[20px] box-border border-t-[1px] border-t-[#EBEBEB] border-t-solid">
              <div class="flex items-center justify-end w-[100%]">
                <van-button block @click.stop="onRefuse(item, 'refuse')"
                  class="w-[128px] h-[48px] rounded-[39px] justify-center flex items-center text-[30px] text-[#5AA4FF] border-[1px] border-[#5AA4FF] border-solid mr-[30px]">
                  拒绝
                </van-button>
                <van-button block
                  class="w-[130px] h-[50px] rounded-[39px] justify-center flex items-center text-[30px] text-[#fff]"
                  style="
                  background: linear-gradient(0deg, #a1cbff 0%, #5aa4ff 100%);
                " @click.stop="onPass(item, 'pass')">
                  通过
                </van-button>
              </div>
            </div>
            <div v-else
              class="flex items-center pt-[20px] mt-[20px] box-border border-t-[1px] border-t-[#EBEBEB] border-t-solid text-[24px] ">
              <div class="text-[24px] text-[#666666] flex-1">查看详情</div>
              <van-icon name="arrow" color="#999" />
            </div>
          </div>
        </div>
      </refreshList>


    </div>
    <Popup :show="Data.showPop" :titleName="Data.titleName" :placeHolder="'请输入' + Data.titleName"
      @submit-content="submitContent" @close-popup="closePopup" />
  </div>
</template>
<script lang="ts" setup>
import dayjs from "dayjs";
import Popup from "@/components/Popup/popup.vue";
import { showToast } from "vant";
import router from "@/router";
import icon_xzsq from "@/assets/interest/examine/icon_xzsq.png";
import icon_xzdt from "@/assets/interest/examine/icon_xzdt.png";
import icon_dtpj from "@/assets/interest/examine/icon_dtpj.png";
import icon_hdpj from "@/assets/interest/examine/icon_hdpj.png";
import icon_hdbm from "@/assets/interest/examine/icon_hdbm.png";
import icon_cjxz from "@/assets/interest/examine/icon_cjxz.png";
import icon_xzpj from "@/assets/interest/examine/icon_xzpj.png";
import icon_hdsh from "@/assets/interest/examine/icon_hdsh.png";
import { h5FindAuditList, h5FindMyApplyList, groupAudit } from "@/api/interestGroup";
import refreshList from '@/components/refreshList/index.vue';
const Data = ref({
  tab: {
    active: 0,
    nav: [
      { name: "待审核", code: "wait" },
      { name: "已通过", code: "pass" },
      { name: "未通过", code: "refuse" },
    ],
  },
  twoTab: {
    active: 0,
    nav: [
      { name: "我的提交", code: "submission" },
      { name: "我的审核", code: "review" },
    ],
  },
  list: [
    //   ("group","申请创建小组"),("join", "小组申请"),("groupComment", "小组评价"),("dynamic", "小组动态"),("activityComment","活动评价"),
    //("activityDynamic","活动动态"),("signUp","活动报名"),("reply","动态评价"),
    //type(接口直接平替就好)  1-小组申请   2-小组动态   3-动态评价  4-活动评价   5-活动报名  6-创建小组
    // { type: "1", cont: "南充自行车骑行小组" },
    // {
    //   type: "2",
    //   cont: "非常喜欢骑行小组，大家都很有爱，互相帮助，运动 健身的同时收获了这么多好友~",
    // },
    // { type: "3", cont: "评价了“阆中街景真的好美，摄影技术好专业~”" },
    // {
    //   type: "4",
    //   cont: "评价了“本次活动举办的非常成功，还领取了很多福 利，期待下次获得举办~”",
    // },
    // { type: "5", cont: "报名了“南充市职工健步走活动~”" },
    // { type: "6", cont: "创建了“美食摄影小组”" },
  ],
  showPop: false,
  pageNum: 1,
  objInfo: {},
  titleName: ''
});

// 常量配置文件
const MESSAGE_TYPES = {
  JOIN: 'join',
  DYNAMIC: 'dynamic',
  REPLY: 'reply',
  ACTIVITY_COMMENT: 'activityComment',
  SIGN_UP: 'signUp',
  GROUP: 'group',
  GROUP_COMMENT: 'groupComment',
  ACTIVITY: 'activity'
};

const TYPE_CONFIGS = {
  [MESSAGE_TYPES.GROUP_COMMENT]: {
    icon: icon_xzdt,
    name: '',
    text: '小组评价',
    path: '/groupExamine/dynamics',
    type: 'xzpj'
  },
  [MESSAGE_TYPES.JOIN]: {
    icon: icon_xzsq,
    name: '',
    text: '小组申请',
    path: '/groupExamine/applyGroupInfo',
    type: 'xzsq'
  },
  [MESSAGE_TYPES.DYNAMIC]: {
    icon: icon_xzdt,
    name: '发布内容',
    text: '小组动态',
    path: '/groupExamine/dynamics',
    type: 'xzdt'
  },
  [MESSAGE_TYPES.REPLY]: {
    icon: icon_dtpj,
    name: '评价内容',
    text: '动态评价',
    path: '/groupExamine/dynamics',
    type: 'dtpj'
  },
  [MESSAGE_TYPES.ACTIVITY_COMMENT]: {
    icon: icon_hdpj,
    name: '评价内容',
    text: '活动评价',
    path: '/groupExamine/activity',
    type: 'hdpj'
  },
  [MESSAGE_TYPES.SIGN_UP]: {
    icon: icon_hdbm,
    name: '活动名称',
    text: '活动报名',
    path: '/groupExamine/activity',
    type: 'hdbm'
  },
  [MESSAGE_TYPES.GROUP]: {
    icon: icon_cjxz,
    name: '小组名称',
    text: '创建小组',
    path: '/groupExamine/group',
    type: 'group'
  },
  [MESSAGE_TYPES.ACTIVITY]: {
    icon: icon_hdsh,
    text: '活动审核',
    path: '/groupExamine/activityExamine',
    type: 'hdpj',
    name: '活动审核'
  }
};
/**
 * 根据类型返回对应的图标或名称
 * @param {Object} item - 包含sourceType属性的对象
 * @param {string} key - 需要返回的值类型（"icon"、"name"、"text"）
 * @returns {*} 根据类型和key返回对应的图标、名称或类型文字
 */
// function dealType(item: { type: string }, key: string) {

//   //   ("group","申请创建小组"),("join", "小组申请"),("groupComment", "小组评价"),("dynamic", "小组动态"),("activityComment","活动评价"),
//   //("activityDynamic","活动动态"),("signUp","活动报名"),("reply","动态评价"),
//    //type(接口直接平替就好)  1-小组申请   2-小组动态   3-动态评价  4-活动评价   5-活动报名  6-创建小组
//   switch (item.sourceType) {
//     case "groupComment":
//      return key === "icon" ? icon_xzpj : key === "name" ? "" : "小组评价";
//     case "join":
//       return key === "icon" ? icon_xzsq : key === "name" ? "" : "小组申请";
//     case "dynamic":
//       return key === "icon"
//         ? icon_xzdt
//         : key === "name"
//         ? "发布内容"
//         : "小组动态";
//     case "reply":
//       return key === "icon"
//         ? icon_dtpj
//         : key === "name"
//         ? "评价内容"
//         : "动态评价";
//     case "activityComment":
//       return key === "icon"
//         ? icon_hdpj
//         : key === "name"
//         ? "评价内容"
//         : "活动评价";
//     case "signUp":
//       return key === "icon"
//         ? icon_hdbm
//         : key === "name"
//         ? "活动名称"
//         : "活动报名";
//     case "group":
//       return key === "icon"
//         ? icon_cjxz
//         : key === "name"
//         ? "小组名称"
//         : "创建小组";
//     default:
//       break;
//   }
function dealType(item, key) {
  const config = TYPE_CONFIGS[item.sourceType];
  return config ? config[key] : undefined;
}

/**
 * 根据传入的item对象的sourceType属性跳转到不同的详情页面
 * @param {Object} item - 包含sourceType、autoId、groupId等属性的对象
 */
function toDetails(item) {
  const config = TYPE_CONFIGS[item.sourceType];
  if (!config) return;

  const isExamine = Data.value.tab.active === 0 && Data.value.twoTab.active === 1;

  const query = {
    autoId: item.autoId,
    groupId: item.groupId,
    state: item.state,
    ...(item.activityId && { activityId: item.activityId }),
    ...(config.type && { type: config.type }),
    ...(isExamine && { isExamine: 1 }),
    ...(item.sourceId && { activityId: item.sourceId })
  };

  router.push({
    path: `${config.path}`,
    query
  });
}
/**
 * 点击状态标签页的处理函数
 *
 * @param item 被点击的标签页对象
 */
function onClickTab(item: any) {
  Data.value.tab.active = item.name;
  Data.value.pageNum = 1;
  if (Data.value.twoTab.active == 1) {
    getLists()
  } else {
    getApplyLists();
  }
}
/**
 * 点击类型标签页时触发的事件处理函数
 *
 * @param item 标签页项对象
 */
function onClickTwoTab(item: any) {
  Data.value.twoTab.active = item.name;
  Data.value.pageNum = 1;
  if (Data.value.twoTab.active == 1) {
    getLists()
  } else {
    getApplyLists();
  }
}
/**
 * 点击拒绝  处理函数
 *
 * @param item 请求项，可以是任意类型的值
 */
function onRefuse(item: any, type) {
  Data.value.showPop = true;
  item.type = type;
  Data.value.titleName = '失败原因'
  Data.value.objInfo = item;

}
//通过
function onPass(item, type) {
  item.type = type;
  Data.value.objInfo = item;
  Data.value.titleName = '通过原因'
  Data.value.showPop = true;
}
//确认提交  拒绝
function submitContent(val: string) {
  // if (!val && Data.value.objInfo.type == 'refuse') {
  //   showToast({
  //     message: "请输入拒绝原因",
  //     icon: "none",
  //   });
  //   return;
  // }
  groupAudit({
    autoId: Data.value.objInfo.autoId,
    state: Data.value.objInfo.type,
    auditOpinion: val
  }).then(res => {
    if (res.code == 200) {
      showToast("审核成功");
      getLists();
      Data.value.showPop = false;
    }
  })

}
//关闭拒绝弹窗
function closePopup() {
  Data.value.showPop = false;
}

//获取列表
const loadMoreRef = ref(null);
// 刷新
const onRefreshList = () => {
  Data.value.pageNum = 1;
  if (Data.value.twoTab.active == 1) {
    getLists()
  } else {
    getApplyLists();
  }
};
// 加载更多
const onLoadMore = () => {
  Data.value.pageNum++;
  if (Data.value.twoTab.active == 1) {
    getLists()
  } else {
    getApplyLists();
  }
};
//我审核
async function getLists() {
  let res = await h5FindAuditList({
    state: Data.value?.tab?.nav[Data.value.tab?.active]?.code,
    pageNum: Data.value.pageNum,
    pageSize: 10,
    sortType: 'desc',
    orderBy: 'create_time'
  });
  if (Data.value.pageNum === 1) Data.value.list = [];
  Data.value.list = Data.value.list.concat(res.data);
  //重置刷新状态及 判断是否加载完成
  if (loadMoreRef.value) {
    loadMoreRef.value.onLoadSuc(Data.value.list.length, res.total);
  }
}
//我提交的
async function getApplyLists() {
  let res = await h5FindMyApplyList({
    state: Data.value?.tab?.nav[Data.value.tab?.active]?.code,
    pageNum: Data.value.pageNum,
    pageSize: 10,
    sortType: 'desc',
    orderBy: 'create_time'
  });
  if (Data.value.pageNum === 1) Data.value.list = [];
  Data.value.list = Data.value.list.concat(res.data);
  //重置刷新状态及 判断是否加载完成
  if (loadMoreRef.value) {
    loadMoreRef.value.onLoadSuc(Data.value.list.length, res.total);
  }
}
onMounted(() => {
  getApplyLists()
})
</script>
<style lang="scss" scoped>
.examine-page {
  background: #f6f7f8;
  min-height: 100vh;
  padding-bottom: 30px;
  box-sizing: border-box;

  .tab-box {
    :deep(.tab-title) {
      font-weight: 400;
      font-size: 32px;
      color: #333333;
    }

    :deep(.van-tab--active) {
      font-weight: 400;
      font-size: 32px;
      color: #5aa4ff;
    }

    :deep(.van-tab) {
      line-height: 1;
      height: 100%;
    }

    :deep(.van-tabs--line .van-tabs__wrap) {
      height: 78px;
    }
  }

  .two-tab-box {
    width: calc(100% - 80px);
    margin: 20px auto 30px;
    padding: 10px;

    :deep(.van-tab--active) {
      background-color: #fff;
    }

    :deep(.van-tabs__nav--card) {
      border: none;
      height: 44px;
    }

    :deep(.van-tab--card) {
      border-right: none;
    }

    :deep(.van-tab--card):nth-child(1) {
      position: relative;

      &::after {
        content: "";
        width: 1px;
        height: 29px;
        background: #e5e5e5;
        position: absolute;
        right: 0;
      }
    }

    :deep(.tab-title) {
      font-weight: 400;
      font-size: 28px;
      color: #333333;
    }

    :deep(.van-tab) {
      font-size: 28px;
    }

    :deep(.van-tab--active) {
      font-weight: 400;
      font-size: 30px;
      color: #5aa4ff;
    }

    :deep(.van-tab) {
      line-height: 1;
      height: 100%;
    }
  }
}
</style>
