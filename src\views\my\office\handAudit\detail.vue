<template>
    <div class="p-30px">
        <div class="rounded-20px bg-[#fff] shadow-drop-2-center">
            <div class="flex justify-between text-[30px] p-[var(--van-cell-group-inset-padding)] py-16px items-center" >
            <span class="text-[#2f2f2f] font-bold">{{detailInfo?.nickname}}申请入会</span>
            <span :class="detailInfo?.auditState=='return'?'text-#E72740':detailInfo?.auditState=='pass'?'text-#5DD1AE':'text-#5AA4FF'">
                {{fixState(detailInfo)?.dictName || ''}}
            </span>
            </div>
            <div :style="{ border: '1px solid #f4f4f4' }"></div>
            <van-form :readonly="true"  input-align="right" >
                <van-cell-group inset>
                    <van-field  v-model="detailInfo.nickname"  label="申请人"  />
                    <van-field v-model="detailInfo.phone" name="phone" label="联系电话" />
                    <van-field v-model="detailInfo.identityNumber"  name="identityNumber"  label="身份证号"  />
                    <van-field v-model="detailInfo.createTime"  name="createTime"  label="申请时间"  />
                    <van-field v-model="detailInfo.auditCompanyName" name="auditCompanyName" label="所入工会" />
                    <van-field v-model="detailInfo.belongCompanyName" name="belongCompanyName" label="单位名称" />
                    <div class="p-20px">
                        <van-cell class="!px-30px !py-20px bg-[#f0f0f0] !rounded-8px"
                            v-show="detailInfo.auditState === 'cancel'||detailInfo.auditState === 'return'" >
                            <div class="text-[#CC3333] text-[28px] text-left">* {{detailInfo.auditState === 'return'?'驳回':'取消'}}原因：</div>
                            <div class="text-left text-#333">{{ detailInfo.auditRemark||'--' }}</div>
                     </van-cell>
                </div>
                </van-cell-group>
            </van-form>
            <!-- 按钮 -->
            <div v-if="detailInfo?.auditState=='wait'" :style="{ 'border-top': '1px solid #f4f4f4' }" class="p-21px flex justify-center w-full box-border">
                <div  class="w-40% h-[70px] rounded-[40px] justify-center flex items-center text-[30px] text-[#63A6FE] border-1 border-#63A6FE border-solid mr-20px" @click="auditJoin('return')">不通过</div> 
                <div  class="w-40% h-[70px] rounded-[40px] justify-center flex items-center text-[30px] text-[#fff]" style="background: linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%);" @click="auditJoin('pass')">通过</div> 
            </div>
        </div>
        <Popup :show="Data.showPop" :titleName="Data.titleName+'原因'" :placeHolder="'请输入'+Data.titleName+'原因'" @submit-content="joinContent" @close-popup="closePopup"/>
    </div>
</template>
<script lang="ts" setup>
import { view,getAudit } from '@/api/joinUnion';
import { useUserStore } from '@/store/modules/user';
import { useDictionary } from '@/store/modules/dictionary';
import Popup from '@/components/Popup/popup.vue';
import { closeToast, showFailToast, showToast } from 'vant';
const dictionary = useDictionary();
const route = useRoute();
const detailInfo =ref({})
const fixState = (item: Recordable) =>
  dictionary.getDictionaryMap?.[`comAuditStateShow_${item.auditState}`];
const auditRemark =ref('');
const Data=ref({
    showPop:false,
    type:'',
    titleName:''
})
function getDetail(autoId) {
    view({ autoId:autoId }).then(res=>{
        detailInfo.value=res;
    })
}
function closePopup() {
    Data.value.showPop=false;
}
//审核
function auditJoin(type) {
    Data.value.type=type;
    Data.value.showPop=true;
    Data.value.titleName=type=='return'?'不通过':'通过'
}
function joinContent(val) {
    if(!val&&Data.value.type=='return'){
        showToast("请输入不通过原因");
        return
    }
    showToast({
        message: '数据审核中...',
        forbidClick: true,
        duration: 0
    })
    getAudit({
        todoIds:[detailInfo.value.autoId],
        auditState:Data.value.type,//审核只有两种结果，通过用pass 不通过用：return
        auditRemark:val//驳回原因必填
    }).then(res=>{
        if(res.code==200){
            closeToast()
            showToast("审核成功");
            Data.value.showPop=false;
            getDetail(route.query?.autoId)
        }else{
            showFailToast(res.message)
        }
    })
}
onMounted(()=>{
    const autoId = route.query?.autoId;
    getDetail(autoId);
    
})
</script>
<style lang="scss" scoped>
:deep(.van-cell-group--inset){
    margin: 0 !important;
}
</style>