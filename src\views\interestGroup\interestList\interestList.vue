<template>
    <div class=" box-border w-100%">
        <div class="bg">
            <div class="p-20px">
                <Search @btn-publish="publish" @on-search="searchVal" :title="'发布活动'"/>
                <Tab :data="Data.tab" @choose-index="chooseIndex" class="mb-20px" />
            </div>
        
       </div>
       <div class="p-30px">
            <div class="h-80vh">
                <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
                    <interestList :data="Data.interestList"/>
                </refreshList>
            </div>
        </div>
        <div class="fixed fix-btn w-80/100 mt-40px m-auto text-26px text-#fff py-23px text-center">发布兴趣动态#分享记录生活#</div>
    </div>
</template>
<script lang="ts" setup>
import Search from "@/components/Search/index.vue";
import Tab from "@/components/Tab/tab.vue";
import interestList from "@/components/List/interestList.vue";
import router from "@/router";
import refreshList from '@/components/refreshList/index.vue';
import { useUserStore } from '@/store/modules/user';
import { activityInfoList } from "@/api/activity";
const useStore = useUserStore();
const Data=ref({
    tab:{
        active:0,
        nav:[
            {labelName:'全部'},
        ]
    },
    interestList:[],//兴趣爱好
    pageNum:1,
    groupId:undefined,
    searchVal:undefined,
    companyClassicIds:''
})
//获取活动列表
const loadMoreRef=ref(null)
function getActList() {
    activityInfoList({
        activityCategory:'interestGroup',
        pageSize:10,
        pageNum:Data.value.pageNum,
        activityType:Data.value.groupId,
        activityName:Data.value.searchVal,
        // companyClassicIds:Data.value.companyClassicIds
    }).then(res=>{
        if(res.code==200){
            if (Data.value.pageNum === 1) Data.value.interestList = [];
            Data.value.interestList = Data.value.interestList.concat(res.data);
            //重置刷新状态及 判断是否加载完成
            if (loadMoreRef.value) {
                loadMoreRef.value.onLoadSuc(Data.value.interestList.length, res.total);
            }
        }
        
    })
}
// 刷新
const onRefreshList = () => {
  Data.value.pageNum = 1;
  getActList();
};
// 加载更多
const onLoadMore = () => {
  Data.value.pageNum++;
  getActList();
};
//发布按钮
function publish() {
    router.push('/releaseActivity')
}
//搜索
function searchVal(val) {
    Data.value.searchVal=val;
    Data.value.pageNum = 1
    getActList();
}
//筛选
function chooseIndex(item){
    Data.value.tab.active=item.activIndex;
    Data.value.groupId=item.autoId;
    Data.value.pageNum = 1
    getActList();
}
//区域筛选
function chooseArea(item) {
    Data.value.companyClassicIds=item.remark;
    Data.value.pageNum = 1
    getActList();
}
onMounted(()=>{
    Data.value.tab.nav=Data.value.tab.nav.concat(useStore.getLabelData);
    getActList();
})

</script>
<style lang="scss" scoped>
 .fix-btn{
    background: url("@/assets/public/button.png") no-repeat;
    background-size: 100% 100%;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    
}
:deep(.van-tabs__nav){
    background: transparent;
}
.bg{
    background: url("@/assets/interest/bg_cover.png") no-repeat;
    background-size: 100% 100%;
}
</style>