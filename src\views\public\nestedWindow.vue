<template>
  <div class="w-full h-100% flex flex-col relative" id="mian_view" :class="$style.custom">
    <van-skeleton title :row="8" :loading="loading">
      <div id="myView" class=" flex-1 overflow-y-scroll" v-html="custom" v-if="showCustom" @click.stop="handleClick">
      </div>
      <!-- 外链不是微信公众号打开方式 -->
      <iframe id="myframe" :src="url" frameborder="0" :width="width" :height="height" style="border:none" v-else>
      </iframe>
    </van-skeleton>
  </div>
</template>
<script setup lang="ts">
import { showToast, showConfirmDialog } from 'vant'
import utils from '@/utils/utils'
// 页面宽高
const height = window.innerHeight;
const width = window.innerWidth;
// https://mp.weixin.qq.com/s/NptTP-4wlMTflfAgzZXuDA
const props = defineProps({
  url: {
    type: String,
    default: 'https://mp.weixin.qq.com/s/REnYIHHLGlNLYlifZ6fI7w',
    // https://mp.weixin.qq.com/s/Oqa8avj4atjG2ZK6cwZeyQ
  }
})

const loading = ref(true)
import { queryNewsLink } from "@/api/public"
const custom = ref()
const showCustom = ref(true)
// 此图片来自微信公众平台未经润许不可使用问题
const setMetaNever = () => {
  let meta = document.createElement('meta');
  // 设置meta元素的name属性为"referrer" content属性为"never"
  meta.setAttribute('name', 'referrer');
  meta.setAttribute('content', 'never');
  // 获取文档的head元素
  let head = document.getElementsByTagName('head')[0];
  head.appendChild(meta);
}
const getResource = async (url: string) => {
  if (url.indexOf('mp.weixin') !== -1) {
    showCustom.value = true
    let res = await queryNewsLink(url)
    if (res) {
      res = res.replace(/strict-origin-when-cross-origin/g, "never");
      res = res.replace(/origin-when-cross-origin/g, "never");
      res = res.replace(/data-src/g, "src");
      res = res.replace(/&amp;from=appmsg/g, "");// 去掉多余参数，防止打不开

      // 地图加载不出来问题
      let mapArr = findAllOccurrences(res, '<mp-common-poi')
      mapArr.map((key, index) => {
        let endIndex = index > 0 ? mapArr[index + 1] : res.length
        let keyStr = res.substring(key, endIndex)
        let imgLink = findDataAttr(keyStr, '</mp-common-poi>', 'data-img="', '"')
        let address = findDataAttr(keyStr, '</mp-common-poi>', 'data-address="', '"')
        let latitude = findDataAttr(keyStr, '</mp-common-poi>', 'data-latitude="', '"')
        let longitude = findDataAttr(keyStr, '</mp-common-poi>', 'data-longitude="', '"')
        let name = findDataAttr(keyStr, '</mp-common-poi>', 'data-name="', '"')
        let html = `<div class="custom-map-info relative" style="background-color:#fafafa;padding:10px">
                      <div>
                          <div class="custom-map-name" style="font-weight:bold" data-latitude="${latitude}" data-longitude="${longitude}" data-address="${address}">${name}</div>
                          <div class="custom-map-address" data-latitude="${latitude}" data-longitude="${longitude}" data-address="${address}">${address}</div>
                      </div>
                      <img loading="lazy" src="${imgLink}" data-latitude="${latitude}" data-longitude="${longitude}" data-address="${address}" style="width:100%;height:180px !important;object-fit:cover" class="custom-map-img" />
                    </div>`

        res = res.slice(0, key) + html + res.slice(key)
      })
      // end

      // 处理svg图片加载不出来的问题
      // let svgArr = findAllOccurrences(res, '<svg space="default"')
      // if (svgArr.length > 0) {
      //   const urlArr: any = []
      //   svgArr.map((startIndex, index) => {
      //     let endIndex = svgArr.length > 1 ? svgArr[index + 1] : res.length
      //     let str = res.substring(startIndex, endIndex)
      //     let svgEndIndex = str.indexOf('</svg>')
      //     if (svgEndIndex > -1) {
      //       let currentSvg = str.substring(0, svgEndIndex)
      //       // 找路径
      //       let firstEndIndex = currentSvg.indexOf('background-image: url(&quot;')
      //       if (firstEndIndex > -1) {
      //         let newUrl = str.substring(firstEndIndex, currentSvg.length)
      //         let urlEnd = newUrl.indexOf('&quot;)')
      //         const url = newUrl.substring(28, urlEnd)
      //         if (url) {
      //           urlArr.push(url)
      //           let styleindex = currentSvg.indexOf('style="')
      //           if (styleindex > -1) {
      //             let newStyle = currentSvg.substring(0, styleindex + 7) + 'display:none !important;' + currentSvg.substring(styleindex + 7)
      //             res = res.slice(0, startIndex) + newStyle + res.slice(startIndex + svgEndIndex)
      //           }
      //         }
      //       }
      //     }
      //   })
      //   if (urlArr.length > 0) {
      //     let str = ''
      //     urlArr.forEach((url: string) => {
      //       let imgDom = document.createElement('img')
      //       imgDom.style.width = '100%'
      //       imgDom.style.display = 'block'
      //       imgDom.src = url
      //       str += imgDom.outerHTML

      //     })
      //     res = res.slice(0, svgArr[0]) + str + res.slice(svgArr[0])
      //   }
      // }
      // console.log(res.slice(svgArr[0]))
      // end

      // 处理背景图无法加载的问题
      let arr = findAllOccurrences(res, 'background-image: url(&quot;')
      if (arr.length > 0) {
        arr.map((key, index) => {
          let endIndex = index > 0 ? arr[index + 1] : res.length
          let str = res.substring(key + 28, endIndex)
          let firstEndIndex = str.indexOf('&quot;)')
          if (firstEndIndex > -1) {
            let newUrl = str.substring(0, firstEndIndex)
            // 此处是为了解决微信图片加载不出来的问题
            const bodyDom = document.getElementById('mian_view')
            let imgDom = document.createElement('img')
            imgDom.className = 'custom-current-img'
            imgDom.src = newUrl
            bodyDom?.appendChild(imgDom)
          }
        })
        // 图片元素添加好后 渲染富文本，移除图片元素
        nextTick(() => {
          custom.value = res
          let imgs = document.querySelectorAll('.custom-current-img')
          const bodyDom = document.getElementById('mian_view')
          imgs.forEach((img, index) => {
            bodyDom?.removeChild(img)
          })
        })
      } else {
        custom.value = res
      }
      // end





    }
    loading.value = false

  } else {
    showCustom.value = false
    loading.value = false
    nextTick(() => {
      let iframeDocument = document.getElementById('myframe')?.contentDocument || document.getElementById('myframe')?.contentWindow?.document;
      iframeDocument.body.style.minWidth = 'unset !important'; // 修改样式
      iframeDocument.body.style.width = '100% !important'; // 修改样式
    })
  }
}
// end

// 查找字符串某个属性值
function findDataAttr(keyStr, tagEnd, dataKeyStart, dataKeyEnd) {
  let tagIndex = keyStr.indexOf(tagEnd)
  let imgIndex = keyStr.indexOf(dataKeyStart)
  let imgStr = keyStr.substring(imgIndex + dataKeyStart.length, tagIndex)
  let firstEndIndex = imgStr.indexOf(dataKeyEnd)
  let attr = decodeURIComponent(imgStr.substring(0, firstEndIndex))
  return attr
}

// 查找字符串中所有出现的位置
function findAllOccurrences(str, searchElement) {
  // 初始化存储出现位置的数组
  let occurrences = [];
  let startIndex = str.indexOf(searchElement);// 查找第一个出现位置
  if (startIndex < 0) return [];
  // 循环查找出现位置，直到找不到为止
  while (true) {
    const foundIndex = str.indexOf(searchElement, startIndex);
    if (foundIndex === -1) break;
    occurrences.push(foundIndex);
    startIndex = foundIndex + searchElement.length;
  }
  return occurrences;
}
// end
const route = useRoute()
// 处理富文本中存在链接的情况
function handleClick(event) {
  let classNames = event.target.classList.toString()
  if (classNames.indexOf('custom-map-name') > -1 || classNames.indexOf('custom-map-address') > -1 || classNames.indexOf('custom-map-img') > -1) {
    let latitude = event.target.getAttribute('data-latitude')
    let longitude = event.target.getAttribute('data-longitude')
    let address = event.target.getAttribute('data-address')
    if (latitude && longitude) {
      showConfirmDialog({
        message: '确定开启导航吗？',
      }).then(() => {
        utils.mapNav({
          win: route.fullPath,
          lon: longitude,
          lat: latitude,
          name: address,
        })
      })
    }
    return
  }



  const target = event.target.closest('a') || event.target.closest('img');
  if (target) {
    let type = event.target.closest('a') ? 'file' : event.target.closest('img') ? 'img' : ''
    // 阻止默认行为，例如链接跳转
    let url = target.href || target.src
    event.preventDefault();
    // 处理a标签点击事件
    if (!url) {
      showToast('路径错误或不存在')
      return
    }
    let n = url.lastIndexOf('.')
    let Suffix = url.substring(n + 1)
    // 证明是文件
    if (Suffix) {
      if (type === 'file') utils.filePreview(url, target.title || '')
      else utils.imagePreview([url])
    } else {
      // 不是文件
      utils.openActLink({
        title: target.title,
        url: url,
        type: 1,
        win: Math.floor(Math.random() * 100),
        isExternal: 'y',
        dataid: Math.floor(Math.random() * 100)
      })
    }
  }
}
// end

onBeforeMount(() => {
  setMetaNever()
})

onMounted(() => {
  getResource(props.url)
})
</script>

<style lang="less" module>
.custom {
  :global {
    * {
      visibility: visible !important;
      opacity: 1 !important;
    }

    .rich_media_tool_area {
      display: none !important;
    }

    .rich_media_title {
      display: none !important;
    }

    .rich_media_meta_list {
      display: none !important;
    }

    .van-skeleton {
      background-color: #fff !important;
      height: 100%;
    }

    // 图片隐藏处理
    .custom-current-img {
      position: absolute;
      opacity: 0;
      width: 1px;
      height: 1px;
      top: 100%;
      left: 100%;
    }
  }
}
</style>