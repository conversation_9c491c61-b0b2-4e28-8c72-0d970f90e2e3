<template>
    <div class="bg-[#f5f5f5] min-h-100vh w-100vw flex flex-col">
      <div class="bg-#fff flex-1 p-30px box-border">
        <div class="tab-box mb-36px">
          <van-tabs
            v-model:active="Data.tab.active"
            sticky
            line-width="20"
            @click-tab="onClickTab"
          >
            <van-tab
              :title="item.name"
              v-for="(item, index) in Data.tab.nav"
              :key="index"
              title-class="tab-title"
            ></van-tab>
          </van-tabs>
        </div>
        <activityList :data="Data.list" @toDetails="toDetails">
          <template #status="{ item }">
            <div
              class="text-24px text-#fff px-15px py-8px box-border rounded-ss-16px rounded-ss-16px"
              :style="{ background: dealStatus(item?.progress, 'bgColor') }"
            >
              {{ dealStatus(item?.progress, "text") }}
            </div>
          </template>
        </activityList>
      </div>
    </div>
  </template>
    <script lang="ts" setup>
  import cover from "@/assets/vitality/volunteerService/cover.jpg";
  import activityList from "@/components/activity/list.vue";
  import router from "@/router";
  const Data = ref({
    tab: {
      active: 0,
      nav: [
        {
          name: "已通过",
          code: "2",
          bgColor: "linear-gradient(90deg, #FD633F, #FE952E)",
        },
        {
          name: "未通过",
          code: "1",
          bgColor: "linear-gradient(90deg, #2FB095, #55D1AC)",
        },
        {
          name: "待审核",
          code: "3",
          bgColor: "linear-gradient(90deg, #999999, #CCCCCC)",
        },
      ],
    },
    statusList: [
      {
        name: "进行中",
        code: "2",
        bgColor: "linear-gradient(90deg, #FD633F, #FE952E)",
      },
      {
        name: "未开始",
        code: "1",
        bgColor: "linear-gradient(90deg, #2FB095, #55D1AC)",
      },
      {
        name: "已结束",
        code: "3",
        bgColor: "linear-gradient(90deg, #999999, #CCCCCC)",
      },
    ],
  
    list: [
      // status  1-未开始  2-进行中 3-已结束
      {
        name: "有福社区低碳环保宣传活动协助志愿服务",
        cover: cover,
        status: "1",
      },
      {
        name: "有福社区低碳环保宣传活动协助志愿服务",
        cover: cover,
        status: "2",
      },
      {
        name: "有福社区低碳环保宣传活动协助志愿服务",
        cover: cover,
        status: "3",
      },
    ],
  });
  /**
   * 点击状态标签页的处理函数
   *
   * @param item 被点击的标签页对象
   */
  function onClickTab(item: any) {
    Data.value.tab.active = item.name;
  }
  function dealStatus(status: string, type: string) {
    if (type === "text")
      return Data.value.statusList.find((el) => el.code === status)?.name;
    if (type === "bgColor")
      return Data.value.statusList.find((el) => el.code === status)?.bgColor;
  }
  function toDetails(item: any) {
    router.push(
      { path:'/activityHome/vitality/volunteerService/activityDetail',
        query:{
          activityId:item.activityId
        } 
      })
  }
  </script>
    <style lang="scss" scoped>
  .tab-box {
    :deep( .van-tabs__nav--card ){
      border: none !important;
      margin: 0 !important;
    }
    :deep( .van-tabs__line) {
      background: linear-gradient(86deg, #c7e0ff 0%, #5aa4ff 100%);
      border-radius: 3px;
    }
  
    :deep( .tab-title) {
      font-weight: 400;
      font-size: 28px;
      color: #666;
    }
    :deep( .van-tab--active) {
      font-weight: 500;
      font-size: 32px;
      color: #333;
    }
  }
  </style>
    