
/**
 * 获取当前、前n天|月、后n天|月
 * @param currentDate 当前日期|当前月 格式 xxxx-xx-xx | xxxx-xx
 * @param val pre：前n天|前一月，last：后n天|后一月
 * @param type date：日期 | month：月份
 * @param idx 默认7天
 * @returns 返回日期格式 xxxx-xx-xx | xxxx-xx
 * */
 export function getNowFormatDate(currentDate:string = '',val:string = '',type:string = 'date',idx:number = 7) {
    //val:1当天|月，2：前n天|月，3：后qi天|月
    let curDate = !currentDate ? new Date() : new Date(currentDate)
    // console.log(curDate.getMonth()+1,'curDate')
    // console.log( new Date().getMonth()+1,'curDate1')
    let date = ''

    if(type === 'date'){
        date = val === '' ? curDate.getMonth()+1  === new Date().getMonth()+1? new Date() : curDate //如果是传入的日期月份是当月的话返回当前日期
        : val === 'pre' ? new Date(curDate.getTime() - 24 * 60 * 60 * 1000 * idx)
        : val === 'last' ? new Date(curDate.getTime() + 24 * 60 * 60 * 1000 * idx)
        : new Date()
    }
    else if(type === 'month'){
        date = val === '' ? curDate
        : val === 'pre' ? new Date(curDate.getFullYear(), curDate.getMonth() - 1)
        : val === 'last' ? new Date(curDate.getFullYear(), curDate.getMonth() + 1)
        : new Date()
    }

    let year = date.getFullYear(), //获取完整的年份(4位)
        month = date.getMonth() + 1, //获取当前月份(0-11,0代表1月)
        strDate = date.getDate() // 获取当前日(1-31)

    if (month < 10) month = `0${month}` // 如果月份是个位数，在前面补0
    if (strDate < 10) strDate = `0${strDate}` // 如果日是个位数，在前面补0

    return type === 'date'? `${year }-${month}-${strDate}`:`${year }-${month}`
}

/**
 * 获取某个日期一周||月内日期
 * @param date 当前日期格式为YYYY-MM-DD
 * @param type date：日期 | month：月份
 * @returns 返回数组格式[{week:'星期几',day:'日期'}]
*/
// 返回类型定义
export type WeekDay = {
    week: string; //星期几
    day: string; //日期
}
export function getWeekDates(date:string = '',type:string = 'date') {
    if(type === 'month') date = date + '-01'

    let now = date? new Date(date) :new Date();
    let dayOfWeek = now.getDay();
    let dayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;

    // 通过偏差值计算出周一的日期
    let monday = new Date(now);
    monday.setDate(monday.getDate() + dayOffset);
    const weekDayArr:WeekDay[] = []
    let formatLength = type === 'date'? 7 : 42
    for(let i = 0;i < formatLength;i++) {
        let weekday = new Date(monday);
        weekday.setDate(weekday.getDate() + i);
        weekDayArr.push({
            week:formatWeekday(weekday),
            day:formatDate(weekday)
        })
    }
    // 格式化日期为YYYY-MM-DD
    function formatDate(date:any) {
        let year = date.getFullYear()
        let month = ('0' + (date.getMonth() + 1)).slice(-2)
        let day = ('0' + date.getDate()).slice(-2)
        return `${year}-${month}-${day}`
    }
    function formatWeekday(date:any) {
        let weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        return weekdays[date.getDay()];
    }
    return weekDayArr;
}