<template>
  <div class="lottery flex flex-col pb-30px items-center relative justify-evenly pt-50px"
    :style="{ backgroundImage: `url(${activityDetail.activityMode === 'birthday' ? bg2 : bg1})` }">

    <div class="w-80/100 mb-80px" v-if="activityDetail && activityDetail.activityMode === 'birthday'">
      <img loading="lazy" :src="title2" alt="" class="w-full h-full">
    </div>
    <div class="w-60/100 h-114px mb-80px" v-else>
      <img loading="lazy" :src="title1" alt="" class="w-full h-full">
    </div>
    <div class="lottery-main h-845px w-95vw p-80px pr-78px pt-42px pl-82px flex justify-center"
      :style="{ backgroundImage: `url(${activityDetail.activityMode === 'birthday' ? zp2 : zp1})` }">
      <LuckyWheel ref="luckyWheel" :prizes="prizes" :default-style="defaultStyle" :blocks="blocks" :buttons="buttons"
        @end="endCallBack" @start="startCallBack" style="margin: 0 auto; transform: scale(1)"
        :default-config="defaultConfig" class="LuckyWheel !w-full">
      </LuckyWheel>
    </div>
    <div></div>
    <!-- 抽奖弹窗 -->
    <van-popup v-model:show="show" :close-on-click-overlay="false" :lock-scroll="true"
      class="flex justify-center bg-transparent flex-col items-center">
      <div
        class="flex items-center relative w-520px h-574px flex-col relative winning pt-70px pb-50px mb-30px px-50px text-center justify-between">
        <template v-if="luckDrawObj?.prizeType != '1'">
          <div class="text-[#D1302D] font-500 text-36px">恭喜您获得</div>
          <div>
            <div class="text-[#D1302D] font-550 text-42px mb-20px">{{ luckDrawObj?.prizeName || '-' }}</div>
            <img loading="lazy" :src="prize_img" alt="" class="w-120px">
          </div>
          <van-button
            class="w-308px h-80px flex justify-center items-center text-[36px] text-[#fff] bg-[#fe4d2b] rounded-40px border-none"
            @click="toRecord" v-if="luckDrawObj.prizeType != '3'">
            我知道了
          </van-button>
          <van-button
            class="w-308px h-80px flex justify-center items-center text-[36px] text-[#fff] bg-[#fe4d2b] rounded-40px border-none"
            @click="toRecord" v-else-if="luckDrawObj.prizeType == '3'">
            {{ luckDrawObj.receiveType === '1' ? '请到指定地点领取' : '去填写地址' }}
          </van-button>
        </template>
        <template v-else>
          <div class="w-full text-[#D1302D] font-500 text-34px">
            谢谢参与，奖品与您擦肩而过，请再接再厉~
          </div>
          <img loading="lazy" src="@/assets/activity/lottery-fail.png" alt="" class="w-180px">
          <van-button
            class="w-308px h-80px flex justify-center items-center text-[36px] text-[#fff] bg-[#fe4d2b] rounded-40px border-none"
            @click="iKnow">
            我知道了
          </van-button>
        </template>
      </div>
      <img loading="lazy" src="@/assets/activity/public/close-icon.png" alt="" class="w-60px" @click="iKnow">
    </van-popup>
  </div>
</template>

<script setup>
import { commonBackFn } from '@/utils/utils';
import { LuckyWheel } from '@lucky-canvas/vue'
import lotteryBtn from '@/assets/activity/cj_btn.png'
import face_img from '@/assets/activity/face.png'
import prize_img from '@/assets/activity/public/prize.png'
import { luckDrawStart } from '@/api/activity';
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from "vue-router";
import { useUserStore } from "@/store/modules/user";
import { validator } from '@/hooks/useValidator'
import { showToast } from 'vant';
import bg1 from '@/assets/activity/lottery-bg.png'
import bg2 from '@/assets/activity/birthday/bg.png'
import zp1 from '@/assets/activity/zhuanpan-bg.png'
import zp2 from '@/assets/activity/birthday/circle_wheel.png'
import title1 from '@/assets/activity/lottery-title.png'
import title2 from '@/assets/activity/birthday/title.png'

const useStore = useUserStore();
const router = useRouter();
const route = useRoute();
const show = ref(false);
const clickState = ref(false);
const luckyWheel = ref(null)
const luckDrawObj = ref({});
const prizes = ref([]);
const defaultStyle = ref({
  background: '#ffdfd5'
});
const defaultConfig = ref({
  decelerationTime: 5000,
  speed: 12,
  stopRange: 0,
  gutter: 2
});
const blocks = ref([
  {
    background: '#e8513b',
    // padding: '0.85rem',
    imgs: [],
  },
]);
const buttons = ref([
  {
    radius: '45px',
    fonts: [
      {
        text: '',
        fontSize: '0px',
        top: '0px',
        fontColor: '#F6DF8D',
      },
    ],
    imgs: [
      {
        src: lotteryBtn, // 转盘圆心指针图片
        width: '85%',
        height: '100%',
        top: '-110%',
      },
    ],
  },
]);
const activityDetail = computed(() => useStore.activityDetail || {});
// 初始化转盘
function init() {

  prizes.value = activityDetail.value.luckDrawInfo.prizeInfos.map((item) => {
    let prizeImg = { top: '20%', height: '2rem', width: '2rem' }
    // prizeType 1 谢谢参与
    if (item.prizeType === '1') {
      prizeImg.src = item.prizeImg ? useStore.getPrefix + item.prizeImg : face_img
    } else {
      prizeImg.src = item.prizeImg ? useStore.getPrefix + item.prizeImg : prize_img
    }
    let prizeObj = {
      fonts: [
        {
          text: item.prizeName,
          fontSize: '0.9rem',
          top: '65%',
          fontColor: '#D5332B',
          fontWeight: 550,
        },
      ],
      prizeInfoId: item.prizeInfoId,
    }
    if (prizeImg.src) {
      prizeObj.imgs = [prizeImg]
    }
    return prizeObj
  })
}

function toRecord() {
  show.value = false
  router.replace({
    path: '/activityHome/lotteryRecord',
    query: {
      active: luckDrawObj.value.prizeType
    }
  })
}
// 回调方法：点击“我知道了”
function iKnow() {
  show.value = false
  commonBackFn(route)
  // router.go(-1)
}

// 跳转到编辑地址页
function toEditAddress() {
  router.replace({
    path: '/activityHome/editAddress',
    query: {
      autoId: luckDrawObj.value.recordAutoId,
    },
  })
}

// 关闭弹窗
function closePopup() {
  show.value = false
}

// 发起抽奖
async function onLuckDrawStart() {
  const res = await luckDrawStart({
    awardPoolName: 'none',
    activityId: useStore.activityDetail.activityId,
  })
  if (res.code === 200) {
    luckDrawObj.value = res.data || {}
    // 假设 $refs.LuckyWheel 是转盘组件
    // 你可能需要使用 refs 来获取 DOM 或子组件
    // 例如：$refs.LuckyWheel.play() 应该替换为 refs.LuckyWheel.play()
    luckyWheel.value.play()
    prizes.value.forEach((item, index) => {
      if (item.prizeInfoId === res.data.prizeInfoId) {
        luckyWheel.value.stop(index)
      }
    })
  } else {
    showToast(res.message ?? '网络繁忙，请稍后再试~')
  }
}

// 发起抽奖开始时的回调
function startCallBack() {
  if (!validator()) return
  if (clickState.value) return
  clickState.value = true
  onLuckDrawStart()
}

// 抽奖结束后的回调
function endCallBack() {
  setTimeout(() => {
    show.value = true
    clickState.value = false
  }, 100)
}
watch(activityDetail, () => {
  if (activityDetail.value.activityId) {
    init()
  }
})
onMounted(() => {
  if (activityDetail.value.activityId) {
    init()
  }
})
</script>

<style lang="scss" scoped>
.lottery {
  width: 100%;
  min-height: 100%;
  //background-image: url('@/assets/activity/lottery-bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;

  .lottery-main {
    //background-image: url('@/assets/activity/zhuanpan-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }

  .winning {
    background-image: url('@/assets/activity/lottery-popup.png');
    background-size: 100% 100%;
  }

  .thanks {
    background-image: url('@/assets/activity/thanks-popup.png');
    background-size: 100% 100%;
  }

  .oncemore {
    background: linear-gradient(0deg, #F43D30 0%, #F95919 0%, #F58356 100%);
    font-size: 32px;
    color: #FFFFFF;
  }
}
</style>
