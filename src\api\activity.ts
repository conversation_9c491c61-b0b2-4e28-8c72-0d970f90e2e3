
import { h5Http } from '@/utils/http/axios';
//获取活动列表
export const activityInfoList = (params) => {
    return h5Http.get({
        url: '/activityInfo/h5/findList',
        params
    });
}

//工会活动首页统计
export const statisticsUnionActivity = (params) => {
    return h5Http.get({
        url: '/activityInfo/h5/statisticsUnionActivity', params
    });
}
//获取活动详情
export const getDetails = (params) => {
    return h5Http.get({
        url: '/activityInfo/h5/getDetails',
        params
    });
}
//获取我的活动
export const getDetaicurrentUserActivityListls = (params) => {
    return h5Http.get({
        url: '/activityInfo/h5/currentUserActivityList',
        params
    });
}
//活动报名
export const signUp = (params) => {
    return h5Http.post({
        url: '/activityInfo/h5/signUp',
        params
    });
}
//活动报名
export const signUpRecord = (params) => {
    return h5Http.get({
        url: '/activityInfo/h5/signUpRecord/details',
        params
    });
}
// 每日抽奖次数
export const getAwardState = (params) => {
    return h5Http.get({ url: '/activityInfo/h5/check/award', params })
}
// 中奖记录列表
export const getPrizeRecordList = (params) => {
    return h5Http.get({ url: '/activityInfo/h5/luckDraw/winningRecord', params, })
}
//发起抽奖
export const luckDrawStart = (params) => {
    return h5Http.post({ url: '/activityInfo/h5/luckDraw/start', params, })
}
// 获取题目
export const getTopicList = (params) => {
    return h5Http.get({ url: '/activityInfo/h5/findTopicList', params, })
}
// 答题提交
export const vieAnswerSubmit = (params) => {
    return h5Http.post({ url: '/activityInfo/h5/vieAnswer', params, })
}

// 添加邮寄地址信息
export const addMailInfo = (params) => {
    return h5Http.post({ url: '/activityInfo/h5/addMailInfo', params, })
}

// 发送评论
export const addComment = (params) => {
    return h5Http.post({ url: '/activityInfo/h5/addComment', params, })
}

// 评论列表
export const getCommentList = (params) => {
    return h5Http.get({ url: '/activityInfo/h5/findCommentList', params, })
}

// 每日答题次数
export const getVieAnswerState = (params) => {
    return h5Http.get({ url: '/activityInfo/h5/check/vieAnswer', params, })
}

//获取报名记录
export const applicationRecord = (params) => {
    return h5Http.get({ url: '/activityInfo/h5/signUpRecord/details', params, })
}
// 已提交问卷次数
export const questionCount = (params) => {
    return h5Http.get({ url: '/activityInfo/h5/question/count', params, })
}

// 问卷活动提交
export const questionnaireSubmit = (params) => {
    return h5Http.post({ url: '/activityInfo/h5/question', params, })
}

// 问卷活动提交
export const receiveCoupon = (params) => {
    return h5Http.post({ url: '/activityInfo/h5/coupon/receive', params, })
}

// 我的票券列表
export const findMyCouponList = (params) => {
    return h5Http.get({ url: '/activityInfo/h5/coupon/findMyList', params, })
}

// 我的票券详情
export const getMyCouponDetail = (params) => {
    return h5Http.get({ url: '/activityInfo/h5/coupon/h5GetDetail', params, })
}
// 我的票券核销
export const destroyQrCode = (params) => {
    return h5Http.get({ url: '/activityInfo/h5/coupon/destroyQrCode', params, })
}
// 活动-扫码签到
export const signIn = (params) => {
    return h5Http.post({ url: '/activityInfo/h5/signIn', params, })
}
// 答题排行榜
export const recordRank = (params) => {
    return h5Http.get({ url: '/activityInfo/answer/recordRank', params, })
}
//个人得分、排名信息
export const scoreInfo = (params) => {
    return h5Http.get({ url: '/activityInfo/h5/answer/scoreInfo', params, })
}
//投稿
export const voteApply= (params) => {
    return h5Http.post({ url: '/activityInfo/h5/vote/apply', params, })
}
//我的投稿
export const myApplyList= (params) => {
    return h5Http.get({ url: '/activityInfo/h5/vote/myApplyList', params, })
}
//我的作品详情
export const myVoteDetails =(params) => {
    return h5Http.get({ url: '/activityInfo/h5/vote/details', params, })
}
//我的投稿次数
export const myApplyCount =(params) => {
    return h5Http.get({ url: '/activityInfo/h5/vote/myApplyCount', params, })
}
//我的投票次数
export const checkCount =(params) => {
    return h5Http.get({ url: '/activityInfo/h5/check/vote', params, })
}
//投票列表
export const findOpusesList =(params) => {
    return h5Http.get({ url: '/activityInfo/h5/vote/findOpusesList', params, })
}
//数据统计
export const statistics =(params) => {
    return h5Http.get({ url: '/activityInfo/h5/vote/statistics', params, })
}
//排行榜
export const findRankList =(params) => {
    return h5Http.get({ url: '/activityInfo/h5/vote/findRankList', params, })
}
//投票
export const opusesAddRecord =(params) => {
    return h5Http.post({ url: '/activityInfo/h5/vote/addRecord', params, })
}



