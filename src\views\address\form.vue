<template>
    <div class="address_form bg-[#F9F9F9] min-h-full pt-43px box-border flex flex-col">
        <div class="flex-1 px-26px">
            <van-form ref="formRef" @submit="submit">
                <van-field label="姓名" v-model="form.receiverName" name="receiverName" placeholder="请输入姓名"
                    :rules="rules.receiverName" maxlength="10"></van-field>
                <van-field label="电话" v-model="form.receiverPhone" name="receiverPhone" placeholder="请输入联系电话"
                    :rules="rules.receiverPhone"></van-field>
                <van-field label="所在地区" v-model="form.detailArea" name="detailArea" placeholder="请选择所在地区" readonly
                    right-icon="arrow" @click="showAreaSelect" :rules="rules.detailArea"></van-field>

                <van-field label="详细地址" v-model="form.detailAddress" name="detailAddress" placeholder="小区楼栋/乡镇村名称"
                    :rules="rules.detailAddress">
                    <template #right-icon>
                        <div class="w-26px" @click="getLocaltion">
                            <img loading="lazy" src="@/assets/public/icon_address_a.png" class="w-100%" />
                        </div>
                    </template>
                </van-field>

                <van-field label="设为默认" v-model="form.defaultFlag" name="defaultFlag">
                    <template #input>
                        <div class="check_select text-right w-full" @click="checkFlag">
                            <img loading="lazy" src="@/assets/public/check.png" class="w-32px"
                                v-show="form.defaultFlag === 'n'" />
                            <img loading="lazy" src="@/assets/public/checked.png" class="w-32px"
                                v-show="form.defaultFlag === 'y'" />
                        </div>
                    </template>
                </van-field>
            </van-form>
        </div>
        <div class="bottom_btn h-140px bg-[#fff] w-full flex items-center justify-center relative" @click="saveAddress">
            <img loading="lazy" src="@/assets/public/button.png" class="w-75%" />
            <div class="text-#fff absolute left-50% top-50% -translate-50% 
            flex items-center justify-center">
                <span class="leading-none text-32px">保存</span>
            </div>
        </div>
        <!-- 弹窗 -->
        <van-popup v-model:show="areaShow" position="bottom">
            <van-cascader v-model="cascaderValue" title="请选择所在地区" :options="areaColumns" @close="areaShow = false"
                @finish="onFinish" />
        </van-popup>
    </div>
</template>
<script lang="ts" setup>
import scAreaList from "@/staticData/scAddress.js";
import { telRules } from '@/utils/rulesValidator'
import { addOrUpdateAddress, getAddressDetail } from '@/api/address'
import { showConfirmDialog, showToast } from "vant";
import utils from '@/utils/utils';
import { useUserStore } from "@/store/modules/user";
const useStore = useUserStore();
const route = useRoute()
if (route.query.autoId) {
    window.document.title = '编辑地址'
} else {
    window.document.title = '新增地址'
}
onMounted(() => {
    initalPage()
})

const initalPage = () => {
    if (route.query.autoId) {
        getAddressDetail(route.query.autoId).then((res: any) => {
            form.value = res.data
        })
    }
}

//获取定位信息
const getLocaltion = () => {
    utils.getPosition(route.fullPath, (code: any, content: any) => {
        if (code == 200 && content) {
            let position = JSON.parse(content);
            form.value.detailAddress = position?.placeName || '' //详细地址
            useStore.setLocationInfo(JSON.parse(content));
        } else {
            showToast('获取定位信息失败')
        }
    })
}
const areaShow = ref(false)
const areaColumns = scAreaList
const cascaderValue = ref([])
const form = ref({
    detailArea: '',
    detailAddress: '',
    defaultFlag: 'n',
    receiverName: "",
    receiverPhone: "",
})
const formRef = ref(null)
const rules = ref({
    receiverName: [
        { required: true, message: '请输入姓名', trigger: 'blur' },
    ],
    receiverPhone: telRules,
    detailArea: [
        { required: true, message: '请选择所在地区', trigger: 'change' },
    ],
    detailAddress: [
        { required: true, message: '请输入详细地址', trigger: 'blur' },
    ],
})

const showAreaSelect = () => {
    areaShow.value = true
}
const onFinish = ({ selectedOptions }) => {
    areaShow.value = false
    if (!selectedOptions) return
    form.value.detailArea = selectedOptions[0]?.text + ' ' + selectedOptions[1]?.text + ' ' + selectedOptions[2]?.text
}
const checkFlag = () => {
    form.value.defaultFlag = form.value.defaultFlag === 'n' ? 'y' : 'n'
}
const router = useRouter()
const submit = (val: any) => {
    if (val) {
        if (route.query.autoId) {
            val.autoId = route.query.autoId
        }
        addOrUpdateAddress(val).then((res: any) => {
            if (res.code === 200) {
                showConfirmDialog({
                    title: '提示',
                    message: '保存成功'
                }).then(() => {
                    router.go(-1)
                })
            } else {
                showConfirmDialog({
                    title: '提示',
                    message: res.message
                })
            }
        })
    }
}
const saveAddress = () => {
    formRef.value?.submit()
}
</script>

<style lang="scss" scoped>
.van-field {
    background-color: #fff;
    margin-bottom: 20px;
    border-radius: 16px;
}

:deep(.van-field__label) {
    color: #666;
    font-size: 32px;
}

:deep(.van-field__body) {
    display: flex;
    justify-content: flex-end;
}

:deep(.van-field__control) {
    text-align: right;
    font-size: 30px;
}

:deep(.van-field__control::placeholder) {
    color: #666;
}

:deep(.van-field__error-message) {
    text-align: right;
}

:deep(.van-cascader__option) {
    font-size: 30px;
}

:deep(.van-tab) {
    font-size: 32px;
}

.van-field__right-icon .van-icon {
    color: #666;
}
</style>