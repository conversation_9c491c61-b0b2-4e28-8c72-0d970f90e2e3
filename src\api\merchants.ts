import { openHttp } from '@/utils/http/axios';
import { h5Http } from '@/utils/http/axios';



// 商家入驻表单
export const applyFormIn = (params:any) => {
    return openHttp.post({
        url:'/openCompanyApplyRecord/apply',
        params
    });
}
// 查询商家详情
export const companyInfo = (companyId:any) => {
    return openHttp.get({
        url:'/openCompanyInfo/getVoByCompanyId',
        params:{companyId}
    });
}
// 获取当前商家详情
export const getCompanyInfoByAccount = () => {
    return openHttp.get({
        url:'/openCompanyInfo/getCompanyInfoByAccount',
    });
}

// 商家分类列表
export const categoryList = (params:any) => {
    return openHttp.get({
        url:'/merchantsType/findVoList',
        params
    });
}
// 商家申请记录
export const applyRecord = (params:any) => {
    return openHttp.get({
        url:'/openCompanyApplyRecord/getCurrentRecentlyRecord',
        params:{
            companyType:'merchant',
            ...params
        }
    });
}
// 商家核销记录
export const findRecordList = (params:any) => {
    return h5Http.get({
        url:'/activityInfo/h5/coupon/findRecordList',
        params:{
            ...params
        }
    });
}
// app商家人员获取核销记录
export const getVerificationRecord = (params:any) => {
    return openHttp.get({
        url:'/customOrder/appMerchantUserGetVerificationRecord',
        params:{
            ...params
        }
    });
}