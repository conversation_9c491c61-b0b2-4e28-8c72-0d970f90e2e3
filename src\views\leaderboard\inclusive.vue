<template>
    <div class="main">
        <img loading="lazy" class="banner" src="@/assets/leaderBoard/ph/banner.png" alt="" srcset="">
        <div class="box p-28px ">
            <dateSelect :dateTypeArr="tab.list" :defaultType="tab.currentIndex" v-model:default-time="tab.currentDate"
                :columnsType="tab.columnsType" @changeType="changeType">
            </dateSelect>
            <div class="info ">
                <div class="flex justify-between">
                    <div class="title flex items-center">
                        <img loading="lazy" class="icons mr-10px" src="@/assets/leaderBoard/icon_pie.png" alt=""
                            srcset="">
                        <span>普惠活动统计</span>
                    </div>
                    <div class="select z-2 w-fit py-6px px-15px rounded-30px text-#6EAEFC"
                        @click="showPickerFn('region')">
                        <span class="mr-10px text-26px">{{ company?.companyName }}</span>
                        <van-icon name="arrow-down"></van-icon>
                    </div>
                </div>
                <div class="content ph-activit mt-10px flex">
                    <div class="label">
                        <div class="text-[24px] text-[#666]">进行中</div>
                        <div class="text-[35px] text-[#333] mt-10px">{{ inclusiveObj?.afootCount || 0 }}</div>
                    </div>
                    <div class="label">
                        <div class="text-[24px] text-[#666]">总发布量</div>
                        <div class="text-[35px] text-[#333] mt-10px">{{ inclusiveObj?.publishCount || 0 }}</div>
                    </div>
                    <div class="label">
                        <div class="text-[24px] text-[#666]">
                            参与总人数{{ inclusiveObj.joinCount >= 10000 ? '(万)' : '' }}
                        </div>
                        <div class="text-[35px] text-[#333] mt-10px" v-formatNum="10000">
                            {{ inclusiveObj.joinCount || 0 }}
                        </div>
                    </div>
                </div>
            </div>
            <div class="info ">
                <div class="title flex items-center">
                    <img loading="lazy" class="icons mr-10px" src="@/assets/leaderBoard/icon_gift.png" alt="" srcset="">
                    <span>普惠活动展开情况</span>
                </div>
                <div class="content chart-box mt-10px">
                    <LineChart :dataSource="activityEcharts" />
                </div>
            </div>
            <div class="info ">
                <div class="title flex items-center">
                    <img loading="lazy" class="icons mr-10px" src="@/assets/leaderBoard/icon_ticket.png" alt=""
                        srcset="">
                    <span>工会优惠券发放情况</span>
                </div>
                <div class="content w-full h-500px mt-10px">
                    <TicketChart :dataSource="couponEcharts" />
                </div>
            </div>
            <div class="info ">
                <div class="title flex items-center">
                    <img loading="lazy" class="icons mr-10px" src="@/assets/leaderBoard/icon_shop.png" alt="" srcset="">
                    <span>普惠商家入驻情况</span>
                </div>
                <div class="content chart-box mt-10px">
                    <BarChart :dataSource="merchantEcharts" />
                </div>
            </div>
            <div class="info ">
                <div class="title flex items-center">
                    <img loading="lazy" class="icons mr-10px" src="@/assets/leaderBoard/icon_type.png" alt="" srcset="">
                    <span>普惠商家类型统计</span>
                </div>
                <div class="content w-full h-330px mt-10px ">
                    <PieChart :dataSource="merchantEcharts?.pieData" />
                </div>
            </div>
            <!-- <div class="info ">
                <div class="title flex items-center">
                    <img loading="lazy" class="icons mr-10px" src="@/assets/leaderBoard/icon_order.png" alt=""
                        srcset="">
                    <span>订单量数据统计</span>
                </div>
                <div class="content w-full mt-10px">
                    <Progress :type="1" />
                </div>
            </div> -->

        </div>
        <img loading="lazy" class="banner bottom" src="@/assets/leaderBoard/ph/bottom_bg.png" alt="" srcset="">

        <van-popup v-model:show="selectShow" position="bottom">
            <van-picker title="选择" :columns="columns" @cancel="selectShow = false" @confirm="onConfirmSelect" />
        </van-popup>
    </div>
</template>
<script setup lang="ts">
import { ref, unref } from 'vue'
import LineChart from './components/charts/LineChart.vue';
import BarChart from './components/charts/BarChart.vue';
import PieChart from './components/charts/PieChart.vue';
import TicketChart from './components/charts/TicketChart.vue';
import unionTrendChart from './components/charts/unionTrendChart.vue';
import Progress from './components/charts/Progress.vue';
import dateSelect from './components/dateSelect.vue'
import { getMonthActivity } from '@/api/leaderboard'
import { inclusiveActivityInfo, unionCouponInfo, inclusiveMerchantInfo } from '@/api/leaderboard/activity'
import { useDictionary } from '@/store/modules/dictionary';

import { useUserStore } from '@/store/modules/user';
const useStore = useUserStore();

const dictionaryStore = useDictionary()
const currentMonth = new Date().getMonth() + 1 < 10 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1
const tab = ref({
    list: [
        { label: '年度', value: 'year' },
        { label: '月度', value: 'month' },
    ],
    columnsType: ['year', 'month'],
    currentIndex: 1,
    currentDate: [new Date().getFullYear(), currentMonth],
})
const changeType = (val: any) => {
    unref(tab).currentIndex = val
    if (val === 0) {
        unref(tab).columnsType = ['year']
        unref(tab).currentDate = [new Date().getFullYear()]
    }
    else {
        unref(tab).columnsType = ['year', 'month']
        unref(tab).currentDate = [new Date().getFullYear(), currentMonth]
    }
}
watch(() => unref(tab).currentDate, () => {
    activityStatistics()
    getActivityEcharts()
    getCouponEcharts()
})

// 区县下拉
const regionList = computed(() => dictionaryStore.dictionaryOBJmap?.['regionCode']?.map((t: any) => {
    const { dictName, remark } = t
    return { value: remark, text: dictName }
}))
// 区县信息
const company = ref({
    companyId: '',
    companyName: ''
})
const currentUserCode = computed(() => useStore.leaderCode)

// 普惠活动统计
const inclusiveObj = ref<any>({})
const activityStatistics = () => {
    getMonthActivity({
        queryDate: unref(tab).currentDate.join('-'),
        companyId: company.value.companyId,
        activityCategory: 'inclusive'
    }).then(res => {
        if (res.code === 200 && res.data) {
            inclusiveObj.value = res.data
        }
    })
}
// end
// 活动开展
const activityEcharts = ref<any>({
    columnList: [
        '本市级',
        '顺庆区',
        '高坪区',
        '嘉陵区',
        '阆中市',
        '南部县',
        '西充县',
        '仪陇县',
        '营山县',
        '蓬安县',
      ],
      publishCountList: [0,0, 0, 0, 0, 0, 0, 0, 0, 0],
      readCountList: [0,0, 0, 0, 0, 0, 0, 0, 0, 0],
})
const getActivityEcharts = () => {
    inclusiveActivityInfo({
        queryDate: unref(tab).currentDate.join('-'),
        activityCategory: 'inclusive',
        companyId: currentUserCode.value,
    }).then(res => {
        if (res.code === 200 && res.data
        && JSON.stringify(res.data) !== '{}'
        && res.data.publishCountList
        && res.data.readCountList

        ) {
            activityEcharts.value = res.data

        }
    })
}
// end
// 优惠券发放情况
const couponEcharts = ref<any>({
    columnList: [
        '本市级',
        '顺庆区',
        '高坪区',
        '嘉陵区',
        '阆中市',
        '南部县',
        '西充县',
        '仪陇县',
        '营山县',
        '蓬安县',
      ],
      publishCountList: [0,0, 0, 0, 0, 0, 0, 0, 0, 0],
      assignCountList: [0,0, 0, 0, 0, 0, 0, 0, 0, 0],
      usedCountList: [0,0, 0, 0, 0, 0, 0, 0, 0, 0],
})
const getCouponEcharts = () => {
    unionCouponInfo({
        queryDate: unref(tab).currentDate.join('-'),
        companyId: currentUserCode.value,
    }).then(res => {
        if (res.code === 200 && res.data && JSON.stringify(res.data) !== '{}') {
            couponEcharts.value = res.data
        }
    })
}

// 普惠商家入驻、类型统计
const merchantEcharts = ref<any>({
    pieData: []
})
const getMerchantEcharts = () => {
    inclusiveMerchantInfo({
        queryDate: unref(tab).currentDate.join('-'),
        companyId: currentUserCode.value,
    }).then(res => {
        if (res.code === 200 && res.data) {
            merchantEcharts.value = res.data
            merchantEcharts.value.pieData = res.data.typeNameList.map((t: any, index: number) => {
                return {
                    name: t,
                    value: res.data.typeTotalList[index]
                }
            })
        }
    })
}

// end


const selectShow = ref(false)
const selectType = ref('')//下拉选择类型
const columns = ref<any>([])
const showPickerFn = (type: string) => {
    selectType.value = type
    switch (type) {
        case 'region':
            columns.value = regionList.value
            break

    }
    selectShow.value = !selectShow.value
}
const onConfirmSelect = ({ selectedOptions }) => {
    const text = selectedOptions[0].text
    const value = selectedOptions[0].value
    switch (selectType.value) {
        case 'region':
            company.value.companyId = value
            company.value.companyName = text
            activityStatistics()

            break
    }
    selectShow.value = false
}


onMounted(() => {
    if (currentUserCode.value) {
        company.value.companyId = currentUserCode.value as string
        company.value.companyName = regionList.value.find((t: any) => t.value === currentUserCode.value)?.text || ''
    } else {
        company.value.companyId = regionList.value[0].value
        company.value.companyName = regionList.value[0].text
    }
    activityStatistics()
    getActivityEcharts()
    getCouponEcharts()
    getMerchantEcharts()
})

</script>
<style lang="scss" scoped>
.main {
    width: 100%;
    min-height: 100vh;
    background: linear-gradient(to bottom, #D8E9FE 0%, #F6FBFF 20%, #F6FBFF 80%, #DCEEFB 100%);
    box-sizing: border-box;
    position: relative;

    .border {
        border: 1px solid red;
    }

    .banner {
        width: 100%;
        position: absolute;
        z-index: 1;
    }

    .bottom {
        position: absolute;
        bottom: 0;
    }

    .center {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .title {
        font-weight: 500;
    }

    .box {
        width: 100%;
        box-sizing: border-box;
        margin-top: 130px;
        position: relative;
        z-index: 5;

        .tab-box {
            margin-bottom: 40px;
        }

        .tab-item {
            width: 30%;
            height: 50px;
            font-size: 28px;
            color: #4898FB;
            border-radius: 23px;
            border: 1px solid #76B7FB;

            &.actived {
                background: #4898FB;
                color: white;
            }
        }

        .time {
            width: 30%;
            height: 50px;
            font-size: 28px;
            color: #4898FB;
            border-radius: 23px;
            border: 1px solid #76B7FB;

            &.gray {
                color: rgba(118, 183, 251, 0.5);
            }
        }

        .info {
            width: 100%;
            padding: 20px;
            margin: 25px 0;
            background: linear-gradient(0deg, #FFFFFF 54%, #E5F3FF 100%);
            border-radius: 20px;
            border: 2px solid #FFFFFF;
            box-sizing: border-box;

            .icons {
                width: 40px;
                height: 40px;
                object-fit: contain;
                position: relative;
                top: 2px;
            }

            .title {
                color: #333;
                font-weight: 500;
                font-size: 32px;
            }

            .ph-activit {
                height: 155px;
                background: linear-gradient(0deg, #FFFFFF 0%, #F5FAFF 100%);
                border-radius: 35px;
                justify-content: space-around;
                align-items: center;

                .label {
                    text-align: center;
                }
            }

            .chart-box {
                width: 100%;
                height: 500px;
            }
        }
    }

    .select {
        border: 1px solid #76B7FB;
    }
}
</style>