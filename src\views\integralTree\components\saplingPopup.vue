<template>
    <!-- 领取树苗弹窗 -->
    <van-popup v-model:show="props.show" position="center" round class="popup box-border" @click-overlay="close">
        <div class="text-center relative box-border mx-auto w-60%" @click="close">
            <img loading="lazy" src="@/assets/tree/sapling_popup.png" class="w-full">
            <div class="text_content absolute top-57% bottom-0 left-8% right-8% flex flex-col">
                <div class="flex-1">
                    <div class="title text-[#64AD13] font-medium text-2.2vh">
                        -{{ props.integralNum }}积分
                    </div>
                    <div class="tips_text text-[#999] text-1.5vh mt-1% ">
                        {{ props.tipsText ? props.tipsText : `使用${props.integralNum}积分，参与爱心助农` }}
                    </div>
                </div>
                <div class="my-10%" @click.stop="confirm">
                    <img loading="lazy" src="@/assets/tree/sapling_btn.png" class="w-74%" />
                </div>
            </div>
        </div>
    </van-popup>
</template>
<script lang="ts" setup>
import { applyTree } from '@/api/integralTree'
import { showLoadingToast, closeToast, showToast } from 'vant';

const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    integralNum: {
        type: Number,
        default: 0,
    },
    activityId: {
        type: String,
        default: '',
        required: true
    },
    tipsText: {
        type: String,
        default: ''
    }
});
const emit = defineEmits(['update:show', 'refresh']);
// 确认执行操作
let handleClick = false
const confirm = async () => {
    if(handleClick) return;
    handleClick = true
    showLoadingToast({
        message: '树苗领取中,请耐心等待...',
        forbidClick: true,
        duration: 0
    })
    const { code, message } = await applyTree({
        platform: 'app',
        activityId: props.activityId
    })
    closeToast()

    if (code === 200) {
        emit('update:show', false);
        emit('refresh', {
            type: 'sappling'
        });
    } else {
        showToast(message)
    }
    handleClick = false;
};
// 关闭弹窗
const close = () => {
    emit('update:show', false);
}
</script>

<style scoped lang="scss">
.van-popup {
    background: transparent;
}
</style>