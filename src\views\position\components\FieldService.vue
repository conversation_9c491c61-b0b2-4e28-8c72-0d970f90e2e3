<template>
  <Empty v-if="isEmpty(venueServiceTypes)" />

  <div class="pb-2" v-else>
    <div v-for="item in venueServiceTypes" class="w-1/4 inline-flex my-2 justify-center items-center flex-col">
      <img loading="lazy" v-if="item.iconCode" :src="userStore.getPrefix + item.iconCode" class="w-[60px] h-[60px]" />
      <div class="text-[#808080]">{{ item.serviceTypeName }}</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store/modules/user';
import { isEmpty } from 'lodash-es';
import Empty from '@/components/Empty/index.vue';

const userStore = useUserStore();

const detailRecord = inject<Recordable>('detailRecord', {});

const venueServiceTypes = computed(() => unref(detailRecord)?.venueServiceTypes);
</script>
