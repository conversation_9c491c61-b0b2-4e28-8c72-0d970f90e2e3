import { cloudManageHttp } from '@/utils/http/axios';

// 个人站内消息
export const userMessageList = (params:any) => {
  return cloudManageHttp.get({
    url:'/messageBasic/queryH5SysList',
    params
  })
}
// 已读操作
export const readMessage = (params:any) => {
  return cloudManageHttp.post({
    url:'/messageBasic/updateMesReadFlagH5',
    params
  })
}
// 站内未读消息数量
export const unReadMessageNum = (params:any) => {
  return cloudManageHttp.get({
    url:'/messageBasic/queryH5SysCount',
    params
  })
}
