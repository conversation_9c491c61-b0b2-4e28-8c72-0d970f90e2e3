<template>
    <div class="pb-20px dynamic">
        <van-form ref="formRef" @submit="submit">
            <van-cell-group>
                <van-field required label="选择小组" @click="showPickerFn(Data.groupList, 'group')"
                    v-model="Data.formatData.groupName" :rules="[{ required: true, message: '请选择' }]" label-width="25%"
                    placeholder="请选择" right-icon="arrow" readonly input-align="right"
                    error-message-align="right"></van-field>
                <van-field label="选择话题" @click="showPickerFn(Data.topicList, 'topic')"
                    v-model="Data.formatData.topicName" label-width="25%" placeholder="请选择" right-icon="arrow" readonly
                    input-align="right" error-message-align="right"></van-field>
                <div class="textarea">
                    <van-field required v-model="Data.formatData.introduce" label="动态内容" type="textarea"
                        :rules="[{ required: true, message: '请输入动态内容' }]" label-width="100%"
                        placeholder="请输入动态内容"></van-field>
                </div>
                <div class="p-28px">
                    <div class="text-32px text-#333 mb-20px">活动图片</div>
                    <van-uploader v-model="Data.imgList" reupload max-count="10" accept="image/*"
                        :after-read="afterRead" />
                    <div class="text-28px text-#333 mt-40px mb-20px">*请上传JPG/PNG/JPEG格式的图片，最多上传10张</div>
                    <div class="text-28px text-#333">*禁止上传手机截图、网络照片等无关图片，违者，停止使用相册功能</div>
                </div>
            </van-cell-group>
            <van-button type="primary" block
                class="btn w-65/100 mt-40px m-auto text-34px text-#fff py-23px text-center border-none"
                native-type="submit">提交</van-button>
        </van-form>
        <van-overlay :show="Data.show" />
        <van-popup v-model:show="Data.showPicker" position="bottom">
            <van-picker :columns="Data.columns" @confirm="onConfirm" @cancel="Data.showPicker = false">
                <template #option="option">
                    {{ option.label }}
                </template>
            </van-picker>
        </van-popup>
    </div>
</template>
<script lang="ts" setup>
import { interestGroupTopic, myGroupList, interestGroupComments } from '@/api/interestGroup';
import { useUserStore } from '@/store/modules/user';
import { uploadFile } from '@/api/public';
import { closeToast, showFailToast, showLoadingToast, showSuccessToast } from 'vant';
import router from "@/router";
import { log } from 'util';
const useStore = useUserStore();

const Data = ref({
    formatData: {
        groupName: '',
        groupNumber: '',
        activityNumber: '',
        introduce: '',
        reason: '',
        join: '',
        topicId: '',
        topicName: ''
    },
    columns: [],
    showPicker: false,
    type: '',
    groupList: [],
    imgList: [],
    topicList: [],
    show: false
})

//选择
const showPickerFn = (list, type) => {
    Data.value.columns = list;
    Data.value.showPicker = true
    Data.value.type = type
}
const onConfirm = ({ selectedOptions }) => {
    switch (Data.value.type) {
        case 'group':
            Data.value.formatData.groupName = selectedOptions[0]?.label;
            Data.value.formatData.groupId = selectedOptions[0]?.value;
            break;
        case 'topic':
            Data.value.formatData.topicName = selectedOptions[0]?.label;
            Data.value.formatData.topicId = selectedOptions[0]?.value;
            break;
    }
    Data.value.showPicker = false
}
//话题列表
function getGroupTopic() {
    interestGroupTopic({
        sortType: 'desc'
    }).then(res => {
        if (res.code == 200) {
            res.data.map(el => {
                el.label = el.topicTitle;
                el.value = el.autoId;
            })
            Data.value.topicList = res.data;
        }
    })
}
//获取兴趣小组列表
const getGroupList = () => {
    myGroupList({
        pageSize: 0
    }).then((res) => {
        if (res.code == 200) {
            res.data.map(el => {
                el.label = el.groupName;
                el.value = el.groupId;
            })
            Data.value.groupList = res.data;
        }


    })
}
//文件上传
function afterRead(file) {
    showLoadingToast({
        message: '上传中...',
        forbidClick: true,
    });
    Data.value.show = true;
    let filedata = {
        operateType: "161", //操作模块类型
        file: file.file,
    };
    uploadFile(filedata).then((res) => {
        if (res.code == 200 && res.data) {
            closeToast()
            Data.value.show = false;
            file.status = "success";
            file.url = res.data[0];
            let arr = [];
            Data.value.imgList.forEach((item) => {
                arr.push(item.url);
            });
            Data.value.formatData.images = arr.join(",");
        } else {
            file.status = "failed";
            showFailToast(res.message);
            closeToast();
            Data.value.show = false;
        }
    });
}
//提交
function submit() {
    let params = {
        content: Data.value.formatData.introduce,
        images: Data.value.formatData.images,
        topicId: Data.value.formatData.topicId,//话题id
        groupId: Data.value.formatData.groupId,
        dataSources: "group", //评论来源 : group:小组  activity：活动
        commentType: "dynamic" //评论类型 （groupComment：小组评价，dynamic：小组动态，activityComment：活动评论，activityDynamic：活动评价 ）
    }
    interestGroupComments(params).then(res => {
        if (res.code == 200) {
            showSuccessToast("提交成功,等待审核");
            router.go(-1)
        }
    })
}
onMounted(() => {
    getGroupTopic();
    getGroupList();
})
</script>
<style lang="scss" scoped>
:deep(.textarea) {
    .van-field__control {
        background: #F6F7F8;
        border-radius: 8px;
        padding: 20px;
    }

    .van-field__body {
        margin-top: 25px;
        font-size: 28px;
    }
}

.dynamic {
    :deep(.van-field__label) {
        font-size: 30px !important;
    }

    :deep(.van-cell__value) {
        font-size: 28px;
    }

    :deep(.van-field__right-icon .van-icon) {
        font-size: 26px;
    }

    :deep(.van-cell) {
        line-height: 1;
        padding-top: 26px;
        padding-bottom: 26px;
    }

    --van-uploader-size: 100px;

}

.btn {
    background: url("@/assets/public/butto.png") no-repeat;
    background-size: 100% 100%;
}
</style>
