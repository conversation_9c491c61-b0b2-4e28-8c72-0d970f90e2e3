<template>
    <div class="myFriendship h-100vh w-full overflow-x-hidden">
        <!-- 干部信息 -->
        <div class="flex items-center py-58px px-36px box-border flex title-bg h-260px relative">
            <img loading="lazy" :src="cadreInfo?.gender === 1
                ? male
                : cadreInfo?.gender === 2
                    ? female
                    : defaultIcon
                " alt="" class="w-90px h-90px rounded-50% mr-17px">
            <div class="text-#fff">
                <div class="text-32px font-500">
                    {{ cadreInfo?.cadreName }}
                    <span class="ml-15px text-26px font-400 pl20px opacity-80"
                        :class="{ 'border-l-1px border-l-#fff border-l-solid': cadreInfo?.postName }">
                        {{ cadreInfo?.postName || '' }}</span>
                </div>
                <div class="text-26px font-400 opacity-80 mt15px">
                    单位：{{ cadreInfo?.workCompanyName }}
                </div>
            </div>
        </div>
        <!-- end -->
        <van-tabs v-model:active="tabActive" sticky color="#5AA4FF" title-active-color="#5AA4FF"
            title-inactive-color="#333333" line-width="30" @click-tab="onClickTab" class="-mt-60px px30px">
            <van-tab :title="item.name" v-for="(item, index) in tab" :key="index" title-class="tab-title"></van-tab>
        </van-tabs>
        <div class="relative z-1  pb150px box-border my-views">
            <div class="my34px flex justify-between px24px items-center">
                <div class="px27px h-48px text-28px rounded-24px flex items-center justify-center"
                    :class="twoTabActive == index ? 'two_tab_active' : 'two_tab'" v-for="(item, index) in twoTab"
                    :key="index" @click="onClickTwoTab(item, index)">
                    <!-- 大于99 做99+处理 -->
                    {{ item.label }} {{ item.count > 99 ? '99+' : item.count }}
                </div>
                <!-- 更多筛选项 -->
                <div class="flex flex-col items-center h-fit" @click="filtershow = !filtershow">
                    <img loading="lazy" src="@/assets/public/icon_sx.png" class="w-30px h-30px" />
                </div>
            </div>
            <div class="dialog-activity" v-show="filtershow">
                <van-popup :show="filtershow" round position="top" @click-overlay="filtershow = false">
                    <div class="bg-#fff p-20px">

                        <div v-if="cadreInfo?.unionId === '6650f8e054af46e7a415be50597a99d5'">
                            <div class="text-30px mb-20px">下级工会</div>
                            <div class="flex flex-wrap">
                                <div class="min-w-100px dropdown_menu_item box-border
                            px-20px py-6px mb-15px rounded-25px bg-#f7f7f7 text-#4c4c4c mr-15px text-28px"
                                    :class="{ '!bg-#A1CBFF !text-#fFF': item.value == queryCompanyId }"
                                    @click.stop="queryCompanyId = item.value" v-for="item, index in queryCompanyList"
                                    :key="index">
                                    {{ item.label }}
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center mt-20px text-28px">
                            <span class="text-28px mr-10px">包含下级</span>
                            <van-checkbox v-model="nextLevelFlag" shape="square"></van-checkbox>
                        </div>
                    </div>
                </van-popup>
            </div>
            <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
                <!-- 会员档案 -->
                <div v-if="tabActive == 0" class="px-30px">
                    <div class="bg-#fff rounded-20px px22px mb-22px" v-for="(item, index) in list"
                        @click="toPage(item, index)">
                        <div class="flex py-30px border-b-solid border-b-1px border-b-#F6F7F8 items-center">
                            <div class="font-500 text-28px text-#333 mr-16px">
                                {{ item.nickname }}
                            </div>
                            <div class="rounded-18px flex items-center justify-center w-85px h-36px text-#fff text-24px"
                                :class="item.gender == 'male' ? 'bg_blue' : 'bg_pink'">
                                <img loading="lazy" :src="item.gender == 'male' ? iconMale : iconFemale"
                                    class="mr7px h-23px w-auto" alt="" srcset="">
                                {{ item.age }}
                            </div>
                            <div class="ml-auto text-28px"
                                :class="item.auditStatus == 'wait' ? 'text-#f97619' : item.auditStatus == 'pass' ? 'text-#5dd1ae' : ' text-#e72740'">
                                {{ dictionary.getDictionaryMap?.[`newsCommentAuditState_${item.auditStatus}`]?.dictName
                                }}
                                <van-icon name="arrow" color="#999"></van-icon>
                            </div>
                        </div>
                        <div class="mt39px pb36px">
                            <div class="flex items-center justify-between mb29px font-400 text-28px "
                                v-for="(el, i) in row">
                                <div class="text-#666">
                                    {{ el.name }}
                                </div>
                                <div class="text-#333">
                                    {{ item[el.key] || "" }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 广场动态  广场评论-->
                <div v-if="tabActive == 1 || tabActive == 2" class="overflow-x-hidden">
                    <div class="flex " v-for="(item, index) in list" @click="clickSelect(item)">
                        <img loading="lazy" :src="item.selected ? iconXz : iconWxz" alt="" srcset=""
                            class="w-30px h-30px ml66px mr30px mt33px" v-if="showTool">
                        <div
                            class="min-w-680px box-border bg-#fff rounded-20px px22px py18px mb-22px mx-auto w-[calc(100%-60px)]">
                            <div class="flex items-center">
                                <img loading="lazy" :src="defaultIcon" class="w-55px h-55px rounded-50% mr20px" alt=""
                                    srcset="">
                                <div>
                                    <span class="text-#333 font-500 text-28px mr-20px">
                                        {{ item.nickname }}
                                    </span>
                                </div>
                                <div class="ml-auto text-28px"
                                    :class="item.auditStatus == 'wait' ? 'text-#f97619' : item.auditStatus == 'pass' ? 'text-#5dd1ae' : ' text-#e72740'">
                                    {{
                                        dictionary.getDictionaryMap?.[`newsCommentAuditState_${item.auditStatus}`]?.dictName
                                    }}
                                </div>
                            </div>
                            <div class="mt-10px">
                                <span class="font-400 text-24px text-#b2b2b2">
                                    发布于{{ item.createTime }}
                                </span>

                            </div>
                            <!-- 广场动态 -->
                            <div class="mt30px mb-26px  leading-38px text-#333 text-28px font-400"
                                v-if="tabActive == 1">
                                {{ item.content }}
                            </div>
                            <div class="flex flex-wrap" v-if="item.imgUrl && tabActive == 1">
                                <img loading="lazy" :src="judgeStaticUrl(el)"
                                    :class="item.imgUrl.split(',').length == 1 ? 'img_one' : item.imgUrl.split(',').length == 2 ? 'img_two' : 'img_three'"
                                    alt="" srcset="" v-for="el in item.imgUrl.split(',')">
                            </div>
                            <!-- 广场评论 -->
                            <div class="mt30px mb-26px bg-#f6f7f8 px29px py27px rounded-12px text-#333 text-28px font-500 leading-38px "
                                v-if="tabActive == 2">
                                {{ item.content }}
                            </div>
                            <div class="pt23px border-t-solid border-t-1px border-t-#F6F7F8">
                                <div class="flex items-center justify-between mb29px font-400 text-28px"
                                    v-if="item.auditStatus == 'pass' || item.auditStatus == 'refuse'">
                                    <div class=" text-#666">
                                        审核时间:
                                    </div>
                                    <div class="text-#333">
                                        {{ item.auditTime }}
                                    </div>
                                </div>
                                <div class="flex items-center justify-between mb29px font-400 text-28px"
                                    v-if="item.auditStatus == 'refuse'">
                                    <div class=" text-#666">
                                        驳回原因:
                                    </div>
                                    <div class="text-#333 text-#e72740">
                                        {{ item.auditRemarks }}
                                    </div>
                                </div>
                            </div>
                            <div class="audit_btn flex items-center px20px justify-between"
                                v-if="item.auditStatus == 'wait'">
                                <div class="refuse_btn" @click.stop="clickRefuse(item)">
                                    不通过
                                </div>
                                <div class="agree_btn" @click.stop="clickAgree(item)">
                                    通过
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </refreshList>
        </div>
        <div class="w-full relative z-2" v-if="tabActive !== 0 && twoTab[twoTabActive]?.value == 'wait' && list.length">
            <fixedBottom name="一键审核" @click="clickAudit" v-if="!showTool"></fixedBottom>
            <div class="fixed left-0 bottom-0 w-full" v-else>

                <div class="audit_btn fixed_btn flex items-center px30px pt22px pb39px justify-between">
                    <div class="flex flex-col mr25px">
                        <img loading="lazy" :src="isSelectAll ? iconXz : iconWxz" alt="" class="w-30px h-30px mb11px"
                            @click="selectAll">
                        <div class="text-22px font-400 text-#b2b2b2">
                            全选
                        </div>
                    </div>
                    <div class="text-26px font-400 text-#b2b2b2" @click="cancleBatch">取消</div>

                    <div class="refuse_btn !h-79px !w-250px !rounded-40px" @click="clickRefuse('')">
                        不通过
                    </div>
                    <div class="agree_btn !h-79px !w-250px !rounded-40px" @click="clickAgree('')">
                        通过
                    </div>
                </div>
            </div>
        </div>
        <popup :showPop="showPop" @close-popup="showPop = false" @submit-popup="clickSubmitPopup"></popup>
    </div>
</template>

<script lang="ts" setup>
defineOptions({
    name: 'myFriendship'
})
import useRefreshFun from '@/hooks/app.ts';
import defaultIcon from '@/assets/public/head_default.png';
import iconFemale from "@/assets/friendship/audit/icon_female.png"
import iconMale from "@/assets/friendship/audit/icon_male.png"
import male from '@/assets/public/male.png';
import female from '@/assets/public/female.png';
import iconWxz from "@/assets/friendship/audit/icon_wxz.png"
import iconXz from "@/assets/friendship/audit/icon_xz.png"
import popup from './compontents/popup.vue';
import refreshList from '@/components/refreshList/index.vue'
import fixedBottom from '@/components/Button/fixedBottom.vue';
import { findRecordAuditList, singlePostFindVoList, singlePostCommentList, queryTableCount, postAudit, commentAudit } from "@/api/friendship/audit"
import { useDictionary } from '@/store/modules/dictionary';
import { useUserStore } from '@/store/modules/user';
import { judgeStaticUrl } from "@/utils/utils";
import { showFailToast, showSuccessToast } from 'vant';
const useStore = useUserStore()
const cadreInfo = computed(() => useStore.getCadreInfo)//干部信息
const dictionary = useDictionary()
const router = useRouter()
const tab = ref([
    { name: '会员档案', api: findRecordAuditList, code: 'single' },
    { name: '广场动态', api: singlePostFindVoList, auditApi: postAudit, code: 'post', idKey: "postId" },
    { name: '广场评论', api: singlePostCommentList, auditApi: commentAudit, code: 'comment', idKey: "autoId" }
])
const tabActive = ref(0)
const twoTab = ref([
])
const twoTabActive = ref(0)
const loadMoreRef = ref(null)
const pageNum = ref(1)
const list = ref<any>([])
const row = ref([
    { name: "联系电话", key: 'phone' },
    { name: "现居住地", key: 'currentAddress' },
    { name: "提交时间", key: 'createTime' }
])
const formData = ref({
    auditList: [],
    auditStatus: "pass",
    auditRemarks: ""
})
const showTool = ref(false)
const isSelectAll = ref(false)
const showPop = ref(false)
const route = useRoute()

// 筛选条件
const filtershow = ref(false)
const nextLevelFlag = ref(true) //是否包含下级 默认包含
const queryCompanyId = ref(null) // 查询工会id
const queryCompanyList = computed(() => {
    let arr = dictionary.getDictionaryOpt?.[`unionsInfo`]
    if (arr?.length) arr = arr.filter((el: any) => el.value !== '6650f8e054af46e7a415be50597a99d5')
    arr.unshift({ label: '全部', value: '' })
    return arr
})
watch(filtershow, (val) => {
    if (filtershow.value) return
    onRefreshList()
})

onMounted(() => {
    twoTab.value = dictionary.getDictionaryOpt?.['newsCommentAuditState'];

    if (route.query.status) {
        tabActive.value = Number(route.query.active)
        twoTabActive.value = Number(route.query.status)
    }

    countList()
    getList()
})


//选中事件
function clickSelect(item) {
    if (showTool.value) {
        item.selected = !item.selected
    }
    let count = list.value.filter((el: any) => el.selected).length
    if (count == list.value.length) {
        isSelectAll.value = true
    } else {
        isSelectAll.value = false
    }
}
// 全选事件
function selectAll() {
    isSelectAll.value = !isSelectAll.value
    list.value.forEach((item: any) => {
        item.selected = isSelectAll.value
    })

}

// 一键审核
function clickAudit() {
    showTool.value = !showTool.value
}
const cancleBatch = () => {
    showTool.value = false
    isSelectAll.value = false
    list.value.forEach((item: any) => {
        item.selected = false
    })
}

// 不通过事件
function clickRefuse(item) {
    let activeOneObj = tab.value[tabActive.value]
    if (item) {
        formData.value.auditList = [item[activeOneObj.idKey]]
    } else {
        let arr = []
        list.value.forEach(el => {
            if (el.selected) {
                arr.push(el[activeOneObj.idKey])
            }
        })
        if (arr.length == 0) {
            showFailToast("请选择审核项")
            return;
        }
        formData.value.auditList = arr
    }
    showPop.value = true
    formData.value.auditStatus = "refuse"
}

// 通过事件
function clickAgree(item) {
    let activeOneObj = tab.value[tabActive.value]
    if (item) {
        formData.value.auditList = [item[activeOneObj.idKey]]
    } else {
        let arr = []
        list.value.forEach(el => {
            if (el.selected) {
                arr.push(el[activeOneObj.idKey])
            }
        })
        if (arr.length == 0) {
            showFailToast("请选择审核项")
            return;
        }
        formData.value.auditList = arr
    }
    formData.value.auditStatus = "pass"
    audit()
}

// 审核弹窗提交事件
function clickSubmitPopup({ auditRemarks }) {
    formData.value.auditRemarks = auditRemarks
    audit()
}

function audit() {
    let activeOneObj = tab.value[tabActive.value]
    activeOneObj['auditApi'](formData.value).then(res => {
        if (res.code == 200) {
            showSuccessToast("审核成功")
        } else {
            showFailToast(res.message)
        }
        showPop.value = false
        onRefreshList()
    })
}


function countList() {
    let activeOneObj = tab.value[tabActive.value]
    queryTableCount({
        nextLevelFlag: nextLevelFlag.value,
        queryCompanyId: queryCompanyId.value
    }).then(res => {
        if (res.code == 200) {
            if (res.data[activeOneObj.code]) {
                twoTab.value.forEach(item => {
                    item.count = res.data[activeOneObj.code]?.[item.value] || 0
                })
            }
        }
    })
}

function getList() {
    let activeOneObj = tab.value[tabActive.value]
    let activeTwoObj = twoTab.value[twoTabActive.value]

    activeOneObj['api']({
        auditStatus: activeTwoObj?.value,
        nextLevelFlag: nextLevelFlag.value,
        queryCompanyId: queryCompanyId.value,
        pageNum: pageNum.value,
        pageSize: 10
    }).then(res => {
        if (res.code == 200) {
            if (pageNum.value === 1) list.value = [];
            list.value = [...list.value, ...res.data]
        }
        //重置刷新状态及 判断是否加载完成
        if (loadMoreRef.value) {
            loadMoreRef.value.onLoadSuc(list.value.length, res.total);
        }
    })
}
function toPage(item: any, index: number) {
    router.push({
        path: '/my/friendship/detail', query: {
            singleUserId: item.singleUserId
        }
    })
}

const changePage = () => {
    onRefreshList()

}
// 定义刷新属性
const { refresh } = useRefreshFun()
refresh([
    { name: 'changePage', funsName: [changePage] }
])


const onClickTab = (item) => {
    tabActive.value = item.name
    showTool.value = false
    onRefreshList()
}

const onClickTwoTab = (item, index) => {
    twoTabActive.value = index
    onRefreshList()
}

// 刷新
const onRefreshList = () => {
    pageNum.value = 1;
    showTool.value = false
    countList()
    getList();
};
// 加载更多
const onLoadMore = () => {
    pageNum.value++;
    getList();
};

</script>

<style lang="scss" scoped>
.myFriendship {
    background: url("@/assets/my/office/list-bg.png"), #F6F7F8;
    background-size: 100% auto;
    background-repeat: no-repeat;
    background-position: bottom center;

    .title-bg {
        background: url("@/assets/friendship/audit/banner.jpg") no-repeat;
        background-size: 100% 100%;
    }

    :deep(.tab-title) {
        font-weight: 400;
        font-size: 32px;
        color: #333333;
    }

    :deep(.van-tab--active) {
        font-weight: 400;
        font-size: 32px;
        color: #5aa4ff;
    }

    :deep(.van-tabs__wrap) {
        height: 110px;
        border-radius: 20px;
    }

    .two_tab {
        background: #FFFFFF;
        border-radius: 24px;
        font-weight: 400;
        color: #666666;
    }

    .two_tab_active {
        background: #F6FAFF;
        border: 1px solid #5AA4FF;
        font-weight: 500;
        color: #5AA4FF;
    }

    .bg_blue {
        background: linear-gradient(90deg, #9FC9FF, #4196FF);
    }

    .bg_pink {
        background: linear-gradient(90deg, #FAA8AB, #FF7097);
    }

    .img_one {
        border-radius: 20px;
        width: 100%;
        height: 226px;
        margin-bottom: 22px;
        object-fit: cover;
    }

    .img_two {
        border-radius: 20px;
        width: 48%;
        height: 226px;
        margin-right: 20px;
        margin-bottom: 22px;
        object-fit: cover;

        &:last-child {
            margin-right: 0px;

        }
    }

    .img_three {
        border-radius: 20px;
        width: 31%;
        height: 226px;
        margin-right: 20px;
        margin-bottom: 22px;
        object-fit: cover;

        &:nth-child(3n+3) {
            margin-right: 0px;

        }
    }

    .audit_btn {
        >div {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .refuse_btn {
            width: 46%;
            height: 60px;
            background: #FFFFFF;
            border-radius: 30px;
            border: 1px solid #5BA5FF;
            font-weight: 400;
            font-size: 28px;
            color: #4297FF;
        }

        .agree_btn {
            width: 46%;
            height: 60px;
            background: linear-gradient(0deg, #A0CAFF 0%, #5AA3FF 100%);
            border-radius: 30px;
            font-weight: 400;
            font-size: 28px;
            color: #FFFFFF;
        }
    }

    .fixed_btn {
        background: #FFFFFF;
        box-shadow: 0px -2px 10px 0px rgba(183, 192, 204, 0.2);
    }

    .select-bars {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
    }

    .dialog-activity {

        /* 活动主页 tab切换 下拉的弹窗  */
        .van-popup {
            position: absolute;
            top: 0px;
            left: 0;
            right: 0;
            background-color: #fff;
            z-index: 1000;
        }

        .van-overlay {
            background: rgba($color: #000000, $alpha: 0.3);
            top: 0px;
            bottom: 0;
            left: 0;
            right: 0;
            position: absolute;
        }

        .dropdown_menu_box {
            border-radius: 0px 0px 12px 12px;

            .dropdown_menu_item {}
        }

        :deep(.van-checkbox) {
            transform: scale(0.8);
        }

    }

    .my-views {
        min-height: calc(100vh - 360px);
    }
}
</style>