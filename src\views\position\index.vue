<template>
  <div class="relative" :class="$style.position">
    <BMap width="100%" height="100vh" ak="DbJrhrxGlSAoIsnUnNcWNnL9VPHBXcNO" :enableScrollWheelZoom="true"
      :enableDragging="true" :enableInertialDragging="true" :enablePinchToZoom="true" ref="mapRef"
      :center="lngLat || location.point || undefined" @initd="initMap" :mapType="mapType" :zoom="15">
      <BMarker :position="marker.position" :key="`bm-marker-${index}`" :icon="{
        imageUrl: marker.icon,
        size: {
          width: 23,
          height: 30,
        },
      }" v-for="(marker, index) in lnglatList" />

      <BLabel :content="marker.content" :position="marker.position" v-for="(marker, index) in lnglatList" :style="{
        color: '#fff',
        backgroundColor: 'rgba(1, 27, 45, 0.36)',
        border: 'none',
        borderRadius: '26px',
        fontSize: '14px',
        padding: '0 8px',
      }" :offset="{ x: -30, y: -35 }" />
    </BMap>

    <ScrollIndex class="z-999" />

    <Content :showMap="showMap" :lnglat="lnglat" :dataTabs="dataTabs" @data-change="handleDataChange">
      <template #right-click>
        <div class="fixed top-[-130px] flex justify-between items-end w-full pointer-events-none">
          <div class="pointer-events-auto">
            <img loading="lazy" :src="appointmentIcon" class="w-[121px] h-[107px]" @click="toRecordList" />
          </div>
          <div @click="showMap = !showMap"
            class="pr-2 right-0 text-[26px] bg-[#fff] h-[46px] border border-[#5AA4FF] py-1 border-solid rounded-full flex items-center pointer-events-auto text-#505B66 text-26px">
            <img loading="lazy" :src="showMap ? down : up" class="w-[45px] h-[45px]" />
            {{ showMap ? '收起' : '展开' }}列表
          </div>
        </div>
      </template>
    </Content>
  </div>
</template>

<script lang="ts" setup>
import { BMap, BMarker, BLabel, MapType, Point, useBrowserLocation } from 'vue3-baidu-map-gl';
import ScrollIndex from './components/ScrollIndex.vue';
import Content from './components/Content.vue';
import down from '@/assets/position/down.png';
import up from '@/assets/position/up.png';
import appointmentIcon from '@/assets/position/appointment-icon.png';
import { find, map, toNumber } from 'lodash-es';
import { useUserStore } from '@/store/modules/user';
import { findVenueTypeManageVOList } from '@/api/position';
import all from '@/assets/position/all.png';
import allClicked from '@/assets/position/all-clicked.png';
import utils from '@/utils/utils';
import router from '@/router';
const userStore = useUserStore();

// data
const lngLat: Point = { lng: 106.089957, lat: 30.800315 };

const mapRef = ref();

const mapType = ref<MapType>('BMAP_NORMAL_MAP');

const showMap = ref<boolean>(true);

const lnglat = ref<Recordable>({ point: { lng: '104.054638', lat: '30.681797' } });

const lnglatList = ref<Recordable[]>([]);

const dataTabs = ref<Recordable[]>([]);

// fn
const initMap = (args) => {
  if (utils.isApp()) {
    utils.getPosition('public_nav', (code: any, content: any) => {
      if (code == 200) {
        const position = JSON.parse(content);
        lnglat.value.point.lng = position.longitude
        lnglat.value.point.lat = position.latitude
        unref(mapRef).resetCenter();
      }
    })
  } else {
    get(args);
  }
}
const { get, location } = useBrowserLocation(null, () => {
  lnglat.value = location;
  unref(mapRef).resetCenter();
});

function handleDataChange(dataList: Recordable[]) {
  lnglatList.value = map(dataList, v => ({
    position: {
      lng: toNumber(v.coordinate?.split(',')?.[0]),
      lat: toNumber(v.coordinate?.split(',')?.[1]),
    },
    icon:
      userStore.getPrefix +
      find(unref(dataTabs), ['typeManageId', v.typeManageId] as Recordable)?.iconCode,

    content: v.positionName,
  }));
}

function toRecordList() {
  router.push({ path: '/reserveRecord' });
}

onMounted(async () => {
  dataTabs.value = [
    { labelName: '全部', value: '', showIcon: all, clickIcon: allClicked },
    ...map((await findVenueTypeManageVOList({ pageSize: 0 })) || [], v => ({
      labelName: v.typeName,
      value: v.typeManageId,
      ...v,
    })),
  ];
});
</script>

<style lang="less" module>
.position {
  :global {
    overflow: hidden;

    .van-field {
      background-color: #f6f7f8;
      border-radius: 50px;

      .van-button {
        border: unset !important;
        color: #5aa4ff;
        background-color: rgba(90, 164, 255, 0.1);
        border-radius: 40px;
        font-size: 24px;
      }
    }

    .btn-confirm {
      background-image: url('@/assets/workers-voice/bg-button-long.png');
      background-size: 560px 100%;
      background-repeat: no-repeat;
      background-position: center;
      width: 100%;
      height: 76px;
    }

    .anchorBL {
      img {
        display: none;
      }
    }
  }
}
</style>
