<template>
  <div class="overflow-x-hidden border-box" :class="$style['place-detail']">
    <div class="p-3 rounded-xl">
      <van-swipe class="rounded-xl" :autoplay="3000">
        <van-swipe-item v-for="image in placeRecord?.images" :key="image">
          <img loading="lazy" :src="image" class="h-[300px] rounded-xl w-full" />
        </van-swipe-item>
        <template #indicator="{ active, total }">
          <div class="custom-indicator px-[10px] py-1 flex items-center">
            <img loading="lazy" :src="iconImage" class="w-[25px] h-[22px] mr-1" />
            <span>{{ active + 1 }}/{{ total }}</span>
          </div>
        </template>
      </van-swipe>
    </div>
    <PlaceDetailTop />
    <div class="h-[15px] w-full bg-[#f5f5f5]" />

    <div class="p-3 pl-[50px]">
      <div class="flex items-center">
        <span class="h-[31px] line-title w-[5px] inline-block mr-1 rounded-lg" />
        <span> 场所介绍 </span>
      </div>
      <div v-html="placeRecord?.venueContent" />
    </div>
    <PlaceDetailBottom />
  </div>
</template>

<script lang="ts" setup>
import iconImage from '@/assets/position/icon-image.png';
import PlaceDetailTop from './components/PlaceDetailTop.vue';
import PlaceDetailBottom from './components/PlaceDetailBottom.vue';
import { venueInfoDetail } from '@/api/position';
import { map, split, isEmpty } from 'lodash-es';
import { useUserStore } from '@/store/modules/user';
import defaultPlace from '@/assets/position/default-place.jpg';

const route = useRoute();

const userStore = useUserStore();

const placeRecord = ref<Recordable>();

provide('placeRecord', placeRecord);

onMounted(async () => {
  const data = await venueInfoDetail({ autoId: route.query.autoId });
  placeRecord.value = {
    ...data,
    images: isEmpty(data.venueImages)
      ? [defaultPlace]
      : map(split(data.venueImages, ','), i => userStore.getPrefix + i),
  };
});
</script>

<style lang="less" module>
.place-detail {
  :global {
    overflow-y: auto;

    * {
      box-sizing: border-box;
    }

    .van-swipe-item {
      border-radius: 0.75rem;
    }

    .custom-indicator {
      position: absolute;
      transform: translateX(580px);
      bottom: 30px;
      font-size: 24px;
      color: #fff;
      border-radius: 19px;
      background: rgba(0, 0, 0, 0.5);
    }

    .line-title {
      background: linear-gradient(to top, #5ba5ff, #5cb6ff);
    }
  }
}
</style>
