<template>
    <van-popup :show="props.show" position="bottom" @click-overlay="close">
        <div class="goodsSpecify px-30px pt-40px pb-87px bg-[#fff]">
            <div class="controll-btn flex items-center justify-between text-30px mb-30px" v-if="sourceType === '3'">
                <div class="text-#999" @click="close">取消</div>
                <div class="confirm-btn text-[#FF4344]" @click="confirm">确认</div>
            </div>
            <div class="info flex items-center justify-between">
                <div class="img-cover w-105px h-105px rounded-15px bg-cover center"
                    v-previewImg="judgeStaticUrl(props.list[defaultIndex]?.productSubImg)"
                    :style="`background-image: url(${judgeStaticUrl(props.list[defaultIndex]?.productSubImg)})`">
                </div>
                <div class="text-[#FF4344]" v-if="sourceType !== '3'">
                    <span class="text-40px">{{ sourceType === '2' ? props.list[props.defaultIndex]?.discountIntegral
                        : props.list[props.defaultIndex]?.nowIntegral }}</span>
                    <span class="tetx-30px">积分</span>
                    <!-- 现金未做 -->
                </div>
                <!-- 普惠商城 -->
                <div class="text-[#FF4344]" v-else>
                    <span class="tetx-30px">￥</span>
                    <span class="text-40px">{{ props.list[props.defaultIndex]?.nowPrice }}</span>
                </div>
            </div>
            <div class="lists mt-30px">
                <div class="item flex items-center justify-between bg-[#F6F7F8] p-14px
                 rounded-14px" :class="{ 'item-active': currentIndex === index }" v-for="item, index in props.list"
                    :key="index" @click="handleSelect(index)">
                    <div class="flex items-center">
                        <div class="img-cover w-65px h-65px rounded-10px bg-cover center"
                            :style="`background-image: url(${judgeStaticUrl(item?.productSubImg)})`"
                            v-previewImg="judgeStaticUrl(item?.productSubImg)">
                        </div>
                        <div class="ml-20px text-28px whitespace-nowrap">
                            {{ item?.productSubName }}
                        </div>
                    </div>
                    <div class="mr-20px text-#333" v-if="sourceType !== '3'">x1</div>
                    <div v-else-if="sourceType === '3' && currentIndex === index" class="flex items-center justify-end">
                        <div class="w-40% rounded-20px ml-5px flex items-center">
                            <img src="@/assets/inclusive/icon_reduce.png" alt="" class="w-35px h-35px"
                                @click.stop="handleCount('decrement')" />
                            <div class="text-28px ml-20px mr-20px leading-none input-line rounded-10px flex-1">
                                <van-field v-model="currentProductCount" type="number" class="" />
                            </div>
                            <img src="@/assets/inclusive/icon_add.png" alt="" class="w-35px h-35px"
                                @click.stop="handleCount('increment')" />
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </van-popup>
</template>

<script lang="ts" setup>
import { judgeStaticUrl } from '@/utils/utils';
const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    list: {
        type: Array,
        default: () => [] as any[],
    },
    defaultIndex: {
        type: Number,
        default: 0,
    },
    defaultNum: {
        default: 1
    },
    sourceType: {
        default: '1'
    }
})
const emit = defineEmits(['update:show', 'selected'])
const currentProductCount = ref(1) //加购数量
const currentIndex = ref(0) //当前选中索引

watch(() => props.show, (val) => {
    currentIndex.value = props.defaultIndex
    currentProductCount.value = props.defaultNum
})
const handleSelect = (index: number) => {
    currentIndex.value = index
    if (props.sourceType != '3') {
        close()
        emit('selected', {
            index,
            num: currentProductCount.value
        })
    }
}
const handleCount = (type: 'increment' | 'decrement') => {
    if (type === 'increment') {
        currentProductCount.value++
    } else if (type === 'decrement' && currentProductCount.value > 1) {
        currentProductCount.value--
    }
}
const close = () => {
    emit('update:show', false)
}

const confirm = () => {
    emit('selected', {
        index: currentIndex.value,
        num: currentProductCount.value
    })
}
</script>

<style scoped lang="scss">
.goodsSpecify {
    border-radius: 28px 28px 0 0;

    .item-active {
        background-color: rgba(90, 164, 255, .1)
    }

    .input-line {
        border: 1px solid #ccc;

        :deep(.van-cell) {
            padding: 5px 10px;
            background: transparent;


            .van-cell__value {
                color: #333;
                font-size: 28px;

            }

            .van-field__control {
                text-align: center;
            }
        }
    }
}

.van-popup {
    background: transparent;
}
</style>