<template>
    <div class="pb-20px">
        <van-form ref="formRef" @submit="submit">
            <van-cell-group>
                <van-field required v-model="Data.formatData.userName" label="姓名"
                        :rules="[{ required: true, message: '请输入姓名' }]" label-width="25%"
                        placeholder="请输入姓名" input-align="right" error-message-align="right"></van-field>
                <van-field required v-model="Data.formatData.phone" label="联系方式"
                    :rules="[{ required: true, message: '请输入联系方式' }]" label-width="25%"
                    placeholder="请输入联系方式" input-align="right" error-message-align="right"></van-field>
                <van-field required v-model="Data.formatData.workUnit" label="工作单位"
                    :rules="[{ required: true, message: '请输入工作单位' }]" label-width="25%"
                    placeholder="请输入工作单位" input-align="right" error-message-align="right"></van-field>
                <van-field required label="地区" @click="showAreaSelect"
                            v-model="Data.formatData.areaName" :rules="[{ required: true, message: '请选择' }]"
                            label-width="25%" placeholder="请选择" right-icon="arrow" readonly input-align="right" error-message-align="right"></van-field>
                 <van-field required v-model="Data.formatData.opusName" label="作品名称"
                        :rules="[{ required: true, message: '请填写作品名称' }]" label-width="25%"
                        placeholder="请填写作品名称" input-align="right" error-message-align="right"></van-field>
                <div class="textarea">
                    <van-field required v-model="Data.formatData.opusContent" label="作品介绍"  type="textarea"
                        :rules="[{ required: true, message: '请输入作品介绍' }]" label-width="100%"
                        placeholder="请输入作品介绍"></van-field>
                </div>
               
                <div class="p-28px">
                    <div class="text-32px text-#333 mb-20px"><span class="text-#CC3333 ">*</span>上传作品封面</div>
                    <van-uploader v-model="Data.imgList" reupload max-count="1" accept="image/*" :after-read="afterRead"/>
                </div>
                <div class="p-28px" v-if="activityDetail.voteInfo?.fileType!='none'">
                    <div class="text-32px text-#333 mb-20px"><span class="text-#CC3333 ">*</span>上传作品</div>
                    <van-uploader v-model="Data.workList" reupload :max-count="activityDetail.voteInfo?.fileType=='video'?'1':activityDetail.voteInfo?.fileLimit" :accept="activityDetail.voteInfo?.fileType=='video'?'video/*':'image/*'" :after-read="afterVideoRead"/>
                </div>
            </van-cell-group>
            <van-button  type="primary" block class="btn w-65/100 mt-40px m-auto text-34px text-#fff py-23px text-center border-none" native-type="submit" >立即投稿</van-button>
        </van-form>
        <!-- 弹窗 -->
        <van-popup v-model:show="areaShow" position="bottom">
            <van-picker
                :columns="areaList"
                @confirm="pickerConfirm"
                :columns-field-names="{
                    text: 'dictName',value:'dictName'
                }"
                @cancel="areaShow = false"
            />
            <!-- <van-cascader v-model="cascaderValue" title="请选择所在地区" :options="areaColumns" @close="areaShow = false"
                @finish="onFinish" /> -->
        </van-popup>
    </div>
</template>
<script lang="ts" setup>
import { useRoute } from "vue-router";
import utils from '@/utils/utils';
import { useUserStore } from '@/store/modules/user';
const useStore = useUserStore();
import { voteApply } from '@/api/activity';
import {uploadFile} from '@/api/public';
import { showFailToast, showToast } from 'vant';
import { useDictionary } from "@/store/modules/dictionary";
const dictionary = useDictionary()
const Data=ref({
    formatData:{
        userName:'',
        opusName:'',
        phone:'',
        opusContent:'',
        areaName:'',
        workUnit:''
    },
    imgList:[],
    workList:[]
})
const areaShow = ref(false);
const areaList = ref([]);
const showAreaSelect = () => {
    areaShow.value = true
}
function pickerConfirm({ selectedOptions }) {
    console.log(selectedOptions,areaList.value);
    
  areaShow.value = false;
  Data.value.formatData.areaName  = selectedOptions[0].dictName;
}
// const onFinish = ({ selectedOptions }) => {
//     areaShow.value = false
//     if (!selectedOptions) return
//     Data.value.formatData.areaName = selectedOptions[0]?.text + ' ' + selectedOptions[1]?.text + ' ' + selectedOptions[2]?.text
// }
function afterRead(file) {
    let filedata = {
        operateType: "17", //操作模块类型
        file: file.file,
      };
      uploadFile(filedata).then((res) => {
        if (res.code == 200 && res.data) {
          file.status = "success";
          file.url = res.data[0];
          let arr = [];
          Data.value.imgList.forEach((item) => {
            arr.push(item.url);
          });
          Data.value.formatData.opusCover = arr.join(",");
        } else {
          file.status = "failed";
          showFailToast(res.message);
        }
      });
}
function afterVideoRead(file) {
    let filedata = {
        operateType: "17", //操作模块类型
        file: file.file,
      };
      uploadFile(filedata).then((res) => {
        if (res.code == 200 && res.data) {
          file.status = "success";
          file.url = res.data[0];
          let arr = [];
          Data.value.workList.forEach((item) => {
            arr.push(item.url);
          });
          Data.value.formatData.opusFiles = arr.join(",");
        } else {
          file.status = "failed";
          showFailToast(res.message);
        }
      });
}

//提交
const route = useRoute()
function submit() {
   voteApply(Data.value.formatData).then(res=>{
    if(res.code==200){
        showToast("提交成功,等待审核");
        setTimeout(()=>{
            utils.commonBackFn(route)
        },1000)
    }else{
        showFailToast(res.message)
    }
   })
}
const showUpLoad=ref(false)
// 计算属性
const activityDetail = computed(() => {
  return useStore.activityDetail || {};
});
onMounted(()=>{
    areaList.value=[ ...dictionary.getDictionaryOBJMap?.['regionCode']];
    if(activityDetail.value.activityId){
        Data.value.formatData.activityId= activityDetail.value.activityId;
        if(!activityDetail.value.voteInfo.fileType){
            showUpLoad.value=true
        }else{
            showUpLoad.value=false
        }
    }
})
</script>
<style lang="scss" scoped>
.textarea{
    :deep(.van-field__control){
        border-radius: 8px;
        background: #F6F7F8;
        padding: 20px;
    }
    :deep(.van-field__body){
        margin-top: 15px;
    }
}
:deep(.van-field__label){
        font-size: 32px !important;
    }
:deep(.van-checkbox){
    margin-right: 15px;
    margin-bottom: 20px;
}
:deep(.van-overlay){
    background:rgba(0,0,0,0.5)
}
.btn{
    background: url("@/assets/public/butto.png") no-repeat;
    background-size: 100% 100%;
}
</style>