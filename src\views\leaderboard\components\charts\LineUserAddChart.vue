<template>
  <div class="echart-box" style="width:100%;height:100%">
    <div ref="echartRef" class="w-full h-full"></div>
  </div>
</template>

<script lang="ts" setup>
import { useECharts } from '@/utils/useECharts'
import { Ref, ref, watch, onMounted } from 'vue'
import * as echarts from 'echarts'
import { getVwSize, dataZoomIndexSet } from '../../data'
const props = defineProps({
  dataSource: {
    type: Object,
    default: {
      dataX: [],
      dataY: [],
    },
  },
})

const echartRef = ref<HTMLDivElement | null>(null)
const { setOptions } = useECharts(echartRef as Ref<HTMLDivElement>)

const initChart = () => {
  let data = { x: [], y: [] }
  data.x = props.dataSource?.dataX
  data.y = props.dataSource?.dataY
  const { startValue, endValue } = dataZoomIndexSet(data.x, 5)
  // 绘制图表
  var option = {
    tooltip: {
      show: true,
      trigger: 'axis',
      formatter: `新增：{c}`,
      textStyle: {
        fontSize: getVwSize(22)
      }
    },
    grid: {
      top: '15%',
      left: '0%',
      right: '0%',
      bottom: '0%',
      containLabel: true,
    },
    legend: {
      show: true,
      icon: 'square',
      orient: 'horizontal',
      top: '0%',
      right: '5%',
      width: '100%',
      itemWidth: getVwSize(20),
      itemHeight: getVwSize(20),
      itemGap: getVwSize(30),
      textStyle: {
        color: '#666666',
        fontSize: getVwSize(20),
      },
    },
    xAxis: [
      {
        type: 'category',
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitArea: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        boundaryGap: true,
        axisLabel: {
          show: true,
          interval: 0,
          rotate: 30,
          fontSize: getVwSize(20),
          lineHeight: getVwSize(30),
          color: '#999',
        },
        data: data.x,
      },
    ],
    yAxis: [
      {
        name: '人数(人)',
        nameTextStyle: {
          color: '#999',
          fontSize: getVwSize(20),
          padding: [0, 0, 0, getVwSize(40)]
        },
        type: 'value',
        min: 0,
        minInterval: 1,
        splitLine: {
          show: true,
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#E6E6E6',
          },
        },
        axisLabel: {
          textStyle: {
            color: '#999',
            fontSize: getVwSize(20),
          },
        },
        axisTick: {
          show: false,
        },
      },
    ],
    dataZoom: [
      {
        // 设置滚动条的隐藏与显示
        show: false,
        // 数据窗口范围的起始数值
        startValue,
        // 数据窗口范围的结束数值（一页显示多少条数据）
        endValue,
        handleSize: data.x?.length || 0,
      },
      {
        // 没有下面这块的话，只能拖动滚动条，
        // 鼠标滚轮在区域内不能控制外部滚动条
        type: 'inside',
        // 滚轮是否触发缩放
        zoomOnMouseWheel: false,
        // 鼠标滚轮触发滚动
        moveOnMouseMove: true,
        moveOnMouseWheel: true,
      },
    ],
    series: [
      {
        name: '新增用户',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        showSymbol: false,
        z: 1,
        label: {
          show: false,
        },
        lineStyle: {
          width: getVwSize(4),
          shadowColor: 'rgba(48,224,189,0.3)',
          shadowBlur: getVwSize(4),
          shadowOffsetY: getVwSize(6)
        },
        itemStyle: {
          normal: {
            color: '#4C99F7',
          },
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [{
            offset: 0,
            color: '#fff'
          },
          {
            offset: 1,
            color: '#B4DFFB'
          }
          ])
        },
        data: data.y,
      },
    ],
  }
  setOptions(option)
}

onMounted(() => {
  initChart()
})

// 监听dataSource
watch(
  () => props.dataSource,
  (newValue, oldValue) => {
    // console.log('watch', newValue, oldValue);
    initChart()
  },
  { deep: true, immediate: true }
)
</script>
<style scoped lang="scss">
.echart-box {}
</style>
