<template>
    <!-- 商品展示 -->
    <div class="goods_cell w-full bg-[#fff] pb-20px box-border overflow-hidden" @click="navTo">
        <div class="goods_img w-full h-297px bg-cover bg-center"
            :style="{ backgroundImage: `url(${judgeStaticUrl(content?.productCoverImg)})` }"></div>
        <div class="goods_info w-full">
            <div class="goods_name text-28px font-weight box-border
            m-20px overflow-hidden text-ellipsis whitespace-nowrap">
                {{ content?.productName }}
            </div>
            <div class="goods_price relative mx-13px">
                <img loading="lazy" src="@/assets/integralMall/price_bg.png" class="w-full">
                <div class="price text-[#FF4344] absolute left-18px top-50% -translate-y-50%">
                    <!-- 金额 -->
                    <span v-if="content?.consumeType === 'mix'">
                        <span class="text-18px">￥</span>
                        <span class="text-30px font-medium">{{
                            sourceType === '1' ? content?.singlePriceInfo?.nowPrice :
                                sourceType === '2' ? content?.singlePriceInfo?.discountPrice : '' }}</span>
                    </span>
                    <!-- 积分 -->
                    <span>
                        <span>
                            <span class="text-25px font-medium" v-if="content?.consumeType === 'mix'">+</span>
                            <span class="text-30px font-medium">{{
                                sourceType === '1' ? content?.singlePriceInfo?.nowIntegral :
                                    sourceType === '2' ? content?.singlePriceInfo?.discountIntegral : '' }}</span>
                        </span>
                        <span class="unit text-20px text-[#252525]">积分</span>
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { judgeStaticUrl } from '@/utils/utils';
const props = defineProps({
    content: {
        type: Object,
        default: () => ({}),
        required: true,
    },
    sourceType: {
        type: String,
        default: '1',//1:积分商城，2：生日专区
    },
})
const router = useRouter()

// 商品详情跳转
const navTo = () => {
    router.push({
        path: `/integralMall/product/detail`,
        query: {
            productId: props.content?.productId,
            sourceType: props.sourceType,
        }
    });
}
</script>

<style lang="scss" scoped>
.goods_cell {
    border-radius: 0px 0px 16px 16px;

    .goods_img {
        border-radius: 16px 16px 0px 0;
    }

    box-shadow: 0px 3px 11px 0px rgba(0, 45, 92, 0.1);

    .goods_info {
        .goods_price {
            word-break: break-all;
        }
    }
}
</style>