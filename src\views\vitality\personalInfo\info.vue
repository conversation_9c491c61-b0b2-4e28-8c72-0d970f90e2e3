<template>
  <div class="" :class="{ 'bg-#f5f5f5 min-h-100vh': Data.status === '3' }">
    <!-- :style="{ backgroundImage: `url(${dealStatus('banner')})` }" bg-no-repeat bg-cover bg-center-->
    <div class="h-327px w-100% flex flex-col justify-center pl-60px pb-50px box-border relative">
      <img loading="lazy" :src="dealStatus('banner')" alt="" class="h-100% w-100% absolute left-0 top-0" />
      <div class="relative">
        <div v-if="Data.status === '1'">
          <div class="big_title">信息审核中</div>
          <div class="text-28px text-#fff">请耐心等待~</div>
        </div>
        <div v-else-if="Data.status === '2'">
          <div class="big_title">信息认证失败</div>
          <div class="text-28px text-#fff">{{ Data.info.reson }}</div>
        </div>
        <div v-else-if="Data.status === '3'" class="flex items-center pb-30px box-border">
          <img loading="lazy" src="@/assets/vitality/personalInfo/pic.png" alt="" class="w-96px h-96px mr-22px" />
          <div class="text-#fff">
            <div class="text-34px font-medium">{{ Data.info.name }}</div>
            <div class="text-26px">工作单位：{{ Data.info.workUnit }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="px-34px box-border">
      <div class="relative" :class="Data.status === '3'
          ? 'bg-#fff rounded-30px mt-[-100px] p-34px box-border'
          : 'bg-#fff rounded-30px'
        ">
        <Title :title="Data.status === '3' ? '我的资料' : '个人信息'" />
        <div>
          <template v-for="(item, index) in formPrams" :key="item.key">
            <div v-if="
              Data.status == '3'
                ? item.key !== 'name' && item.key !== 'workUnit'
                : true
            " class="flex items-center text-30px mt-40px">
              <div class="text-#666">{{ item.name }}:</div>
              <div class="text-#333 ml-26px">{{ Data.info[item.key] }}</div>
            </div>
          </template>
          <div class="flex items-center text-30px mt-40px" v-if="Data.status === '1' || Data.status === '2'">
            <div class="text-#666">提交时间:</div>
            <div class="text-#333 ml-26px">2024-10-10 10:00:00</div>
          </div>
          <div class="flex items-center text-30px mt-40px" v-if="Data.status === '2'">
            <div class="text-#666">审核时间:</div>
            <div class="text-#333 ml-26px">2024-10-12 10:00:00</div>
          </div>
          <div
            class="w-380px h-66px bg-#fff rounded-33px text-#666  text-28px border-1px border-solid border-#BFBFBF flex items-center justify-center mx-auto mt-100px mb-50px"
            v-if="Data.status === '3'" @click="toJump('/vitality/personalInfo')">
            编辑资料
          </div>
          <div v-if="Data.status === '2'">
            <Button name="重新提交" class=" mt-80px m-auto w-450px h-78px" @click="toJump('/vitality/personalInfo')" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import Title from "@/components/Title/vitalityTitle.vue/";
import formPrams from "./formParams.ts";
import shz from "@/assets/vitality/personalInfo/shz.jpg";
import shsb from "@/assets/vitality/personalInfo/shsb.jpg";
import shtg from "@/assets/vitality/personalInfo/shtg.png";
import router from "@/router";
import Button from '@/components/Button/button.vue'
const Data = ref({
  status: "2", //1-审核中  2-审核失败  3-审核成功
  info: {
    name: "刘一菲",
    idNum: "510***********4158",
    sex: "女",
    date: "1996-09-08",
    phone: "185****9845",
    workUnit: "南充市总工会",
    area: "南充市顺庆区金泉路",
    address: "68号502",
    reson: "请输入正确的手机号码", //失败原因
  },
});
function dealStatus(type: string) {
  switch (Data.value.status) {
    case "1":
      return type === "banner" ? shz : "";
    case "2":
      return type === "banner" ? shsb : "";
    case "3":
      return type === "banner" ? shtg : "";
  }
}
function toJump(url: string) {
  router.push({ path: url });
}
function repeatSubmit() {
  Data.value.status = "1";
}
</script>
<style lang="scss" scoped>
.big_title {
  font-weight: bold;
  font-size: 46px;
  color: #ffffff;
  line-height: 98px;
  text-stroke: 3px #3183ed;
  -webkit-text-stroke: 3px #3183ed;
}
</style>