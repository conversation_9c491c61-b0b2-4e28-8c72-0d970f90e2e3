<template>
    <div class="order-detail-cell">
        <div class="flex px-24px py-34px goods-cell" v-for="info, info_index in content?.priceListInfo"
            :key="info_index">
            <div class="left-cover">
                <img :src="judgeStaticUrl(info?.productSubImg)" v-previewImg="judgeStaticUrl(info?.productSubImg)"
                    class="w-140px h-140px rounded-20px object-cover" />
            </div>
            <div class="right-text flex-1 flex flex-col justify-between ml-20px">
                <div>
                    <div class="title text-28px text-#333 text-ellipsis line-clamp-2 whitespace-nowrap text-wrap">
                        {{ content?.productName }}
                    </div>
                    <div class="productSub flex justify-between items-center 
                    text-#999 text-26px w-full mt-16px">
                        <div class="">{{ info?.productSubName }}</div>
                        <div>x{{ info?.currentProductCount }}</div>
                    </div>
                </div>
                <div class="text-#FF4344 text-28px mt-10px flex justify-between">
                    <div class="font-bold">
                        <span class="text-24px">￥</span>{{ info?.nowPrice }}
                        <span v-if="info?.nowIntegral">+{{ info.nowIntegral }}积分</span>
                    </div>
                    <div class="btns" v-if="showBtn">
                        <template v-if="['deliver', 'receive'].includes(orderState)">
                            <div class="refund text-#999 rounded-28px px-14px py-8px leading-none"
                                v-if="content?.productType === 'actual'" @click.stop="refundBtn(info_index)">
                                申请退款
                            </div>
                            <div class="refund text-#999 rounded-28px px-14px py-8px leading-none" v-else>
                                不支持售后
                            </div>
                        </template>
                    </div>
                </div>

            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { judgeStaticUrl } from '@/utils/utils';
const props = defineProps({
    content: {
        type: Object,
        default: () => { }
    },
    showBtn: {
        type: Boolean,
        default: false
    },
    orderState: {
        type: String,
        default: ''
    }
})
const emit = defineEmits(['refund'])

const refundBtn = (priceIndex: any) => {
    emit('refund', priceIndex)
}
</script>
<style scoped lang="scss">
.btns {
    .refund {
        border: 1px solid #999;
    }
}

.goods-cell {
    border-bottom: 1px solid #F2F2F2;

    &:last-child {
        border-bottom: none;
    }
}
</style>