<template>
    <van-popup :show="props.show" position="right" @click-overlay="close">
        <div class="selects h-100vh w-60vw overflow-scroll text-#333 p-30px box-border flex flex-col">
            <div class="flex-1 overflow-scroll">
                <div class="filter-types mt-30px" v-if="typeArry && typeArry.length">
                    <div class="text-32px font-medium">分类</div>
                    <div class="flex flex-wrap">
                        <div class="bg-[#F2F0F1] text-28px leading-none whitespace-nowrap
                        px-22px py-12px rounded-20px mt-20px mr-20px"
                            :class="{ 'active-type': userSearchType === item?.value }" @click="handleType(item.value)"
                            v-for="item, index in typeArry" :key="index">
                            {{ item?.label }}
                        </div>
                    </div>
                </div>
                <slot :form="selfForm"></slot>
            </div>
            <div class="controll-end flex justify-around mt-20px mb-120px">
                <div @click="comfirm"
                    class="text-28px leading-none whitespace-nowrap w-40% text-center py-12px rounded-20px active-type">
                    确认</div>
                <div @click="reset"
                    class="text-28px leading-none whitespace-nowrap w-40% text-center py-12px rounded-20px bg-[#F2F0F1] ">
                    重置
                </div>
            </div>
        </div>
    </van-popup>
</template>
<script lang="ts" setup>

const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    filterSearch: {
        type: String,
        default() {
            return ''
        },
    },
    typeArry: {
        type: Array,
        default() {
            return []
        }
    },
    defaultForm: {
        type: Object,
        default() {
            return {}
        }
    }
})
watch(() => props.show, (val) => {
    if (!val) return
    userSearchType.value = props.filterSearch || ''
    selfForm.value = { ...props.defaultForm }
})
const emit = defineEmits(['update:show', 'confirm'])
const userSearchType = ref<any>('')
const selfForm = ref<any>({}) // 自定义筛选表单

const handleType = (value: boolean) => {
    userSearchType.value = value
}
const close = () => {
    emit('update:show', false)
}
const reset = () => {
    if (props.typeArry?.length) {
        emit('update:show', false)
        userSearchType.value = ''
        emit('confirm', userSearchType.value)
    }
    else {
        // 自定义表单重置
        Object.keys(selfForm.value).forEach((key) => {
            selfForm.value[key] = ''
        })
        emit('confirm', selfForm.value)
    }

}
const comfirm = () => {
    if (props.typeArry?.length) {
        emit('update:show', false)
        emit('confirm', userSearchType.value)
    }
    // 自定义表单
    else emit('confirm', selfForm.value)
}

</script>
<style lang="scss" scoped>
.selects {
    .active-type {
        border: 2px solid #FF4344;
        color: #FF4344;
        background-color: #fff;
    }
}

.exchange-values {
    :deep(.van-cell__value) {
        font-size: 28px;
    }
}
</style>