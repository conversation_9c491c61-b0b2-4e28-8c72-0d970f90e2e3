<template>
    <div class="bg-#f2f2f2 activityDetail" v-cloak>
        <div class="mb-10px bg-#FFF " v-if="activityDetail?.activityType">
            <img loading="lazy" :src="judgeStaticUrl(activityDetail.appCover) || defaultImg" alt="" srcset=""
                class="w-full h-291px">
            <div class="px-29px pb-30px">
                <div class="font-500 text-34px text-#333 leading-44px py-28px">
                    {{ activityDetail?.activityName }}
                    <span class="rounded-10px bg-#eaf1f9 py-4px px-19px w-fit text-#5aa4ff text-26px">{{
                        dictionary.getDictionaryMap?.[`friendshipActivityType_${activityDetail.activityType}`]?.dictName
                        }}</span>
                </div>
                <div class="text-28px font-500 text-#999 pb-23px flex items-center justify-between">
                    浏览量：{{ activityDetail?.readCount }}
                    <span class="h49px flex items-center px26px text-#fff text-30px font-500 rounded-30px "
                        :class="statusObj[activityDetail?.progressMsg]">{{ activityDetail?.progressMsg }}</span>
                </div>
                <div class="mb-35px flex items-center" v-if="activityDetail?.signUpInfo">
                    <div class="font-500 text-32px text-#333 flex items-center">
                        <img loading="lazy" :src="iconTime" alt="" srcset="" class="w-40px h-auto mr-18px">
                        报名日期：
                    </div>
                    <div class="font-400 text-30px text-#666">
                        {{ dayjs(activityDetail?.signUpInfo.signUpStartTime).format('YYYY-MM-DD') }} 至
                        {{ dayjs(activityDetail?.signUpInfo.signUpEndTime).format('YYYY-MM-DD') }}
                    </div>
                </div>
                <div class="mb-35px flex items-center" v-if="activityDetail?.openingStartTime">
                    <div class="font-500 text-32px text-#333 flex items-center">
                        <img loading="lazy" :src="iconTime" alt="" srcset="" class="w-40px h-auto mr-18px">
                        每日开放时间：
                    </div>
                    <div class="font-400 text-30px text-#666">
                        {{ formatTimeWithoutSeconds(activityDetail?.openingStartTime) }} 至
                        {{ formatTimeWithoutSeconds(activityDetail?.openingEndTime) }}
                    </div>
                </div>
                <div class="mb-35px flex items-center" v-if="activityDetail?.customerType">
                    <div class="font-500 text-32px text-#333 flex items-center">
                        <img loading="lazy" :src="iconPerson" alt="" srcset="" class="w-40px h-auto mr-18px">
                        参与对象：
                    </div>
                    <div class="font-400 text-30px text-#666">
                        {{ matchCustomerType(activityDetail?.areaCode, activityDetail?.customerType) }}
                    </div>
                </div>
                <div class="inline leading-44px" v-if="activityDetail?.contacts">
                    <span class="font-500 text-32px text-#333 inline relative">
                        <img loading="lazy" :src="iconPerson" alt="" srcset="" class="w-40px h-auto 
                        mr-18px absolute top-50% -translate-y-50%">
                        <span class="ml-60px">联系人：</span>
                    </span>
                    <span class="font-400 text-30px text-#666 ml-10px  break-all inline">
                        {{ activityDetail?.contacts }}:{{ activityDetail?.contactPhone }}
                    </span>
                </div>
            </div>
        </div>
        <van-empty :image="emptyImg" description="活动已下架或活动信息不存在。" :image-size="['60%', 'auto']"
            v-if="activityDetail?.activityType === ''">
        </van-empty>
        <div class="bg-#fff py36px px39px flex-1 pb-100px self-style" v-if="activityDetail?.activityType">
            <div class="mb-65px">
                <template v-if="activityDetail?.activityContent">
                    <div class="font-500 text-32px text-#333 flex items-center mb-27px">
                        <img loading="lazy" :src="iconIntro" alt="" srcset="" class="w-40px h-auto mr-18px mt-10px">
                        活动介绍
                    </div>
                    <div class="font-400 text-30px text-#333 leading-44px rich_text intro"
                        v-html="activityDetail.activityContent">
                    </div>
                </template>
                <div class="mt-20px" v-if="activityDetail?.activityRules">
                    <div class="font-500 text-32px text-#333 flex items-center mb-27px">
                        <img loading="lazy" :src="iconIntro" alt="" srcset="" class="w-40px h-auto mr-18px mt-10px">
                        活动规则
                    </div>
                    <div class="font-400 text-30px text-#333 leading-44px rich_text"
                        v-html="activityDetail.activityRules">
                    </div>
                </div>
                <div class="mt-20px" v-if="activityDetail?.participationMode">
                    <div class="font-500 text-32px text-#333 flex items-center mb-27px">
                        <img loading="lazy" :src="iconIntro" alt="" srcset="" class="w-40px h-auto mr-18px mt-10px">
                        参与方式
                    </div>
                    <div class="text-31px text-[#333] leading-44px mb-40px rich_text"
                        v-html="activityDetail.participationMode">
                    </div>
                </div>
                <div class="mt-20px" v-if="activityDetail?.activityAddress">
                    <div class="font-500 text-32px text-#333 flex items-center mb-27px">
                        <img loading="lazy" :src="iconIntro" alt="" srcset="" class="w-40px h-auto mr-18px mt-10px">
                        活动地址
                    </div>
                    <div class="font-400 text-30px text-#333 leading-44px">
                        {{ activityDetail.activityAddress }}
                    </div>
                </div>
                <div class="mt-20px" v-if="activityDetail?.activityRemark">

                    <div class="font-500 text-32px text-#333 flex items-center mb-27px">
                        <img loading="lazy" :src="iconIntro" alt="" srcset="" class="w-40px h-auto mr-18px mt-10px">
                        活动备注
                    </div>
                    <div class="text-31px text-[#333] leading-44px mb-40px rich_text"
                        v-html="activityDetail.activityRemark">
                    </div>
                </div>
            </div>
            <div class="bottom_btn" v-if="activityDetail?.signUpInfo && route.query?.singleStatus == 'n'"
                :class="statusBtnObj[activityDetail.status]?.class"
                @click="toPage(statusBtnObj[activityDetail.status], { activityId: activityDetail.activityId })">
                {{ statusBtnObj[activityDetail.status]?.name }}
            </div>

        </div>
        <tipsPopup :showPop="showTipPop" @close-popup="showTipPop = false" @to-write="toWrite"
            v-if="activityDetail.activityType"></tipsPopup>
    </div>
</template>

<script lang="ts" setup>

import iconIntro from "@/assets/friendship/icon_introduce.png"
import iconTime from "@/assets/friendship/icon_time.png"
import iconPerson from "@/assets/friendship/icon_person.png"
import defaultImg from "@/assets/friendship/home_banner.png"
import { useUserStore } from "@/store/modules/user";
import { useDictionary } from "@/store/modules/dictionary";
import { applicationRecord } from '@/api/activity.ts';
import { showToast } from "vant";
import emptyImg from '@/assets/friendShip/empty_img.png'
import tipsPopup from './components/tipsPopup.vue';
import { computed } from "vue";
import { activityValidator } from "@/hooks/useValidator";
import { activityTimeValidator, qualificationValidator } from '@/utils/actRulesValidator.js';
import { judgeStaticUrl } from "@/utils/utils";
import dayjs from "dayjs";

const dictionary = useDictionary()
const router = useRouter();
const route = useRoute();
const useStore = useUserStore();
const signUpRecord = ref(null)
const showTipPop = ref(false)

const customerList = computed(() => {
    return [
        {
            label: '注册用户',
            value: 1,
        }, {
            label: '认证会员',
            value: 2,
        }, {
            label: '工会干部',
            value: 3,
        },
        {
            label: '兴趣小组成员',
            value: 'interestGroup',
        }, {
            label: '志愿者',
            value: 'volunteer',
        }, {
            label: '单身联谊会员',
            value: 'friendship',
        }
    ]
})

// 参与对象匹配 denglanlan
const matchCustomerType = (areaCode, customerType) => {
    const typeName = customerList.value.find((t: any) => t.value === customerType)?.label || ''
    return areaCode + typeName
}

const statusObj = ref({
    '未开始': 'bg-blue',
    '进行中': 'bg-#fe779a',
    '已结束': 'bg-gray',
})

const statusBtnObj = ref(
    {
        '0': {
            name: '未到达报名条件',
            class: 'btn_gray',
        },
        '1': {
            name: '我要报名',
            class: 'btn_blue',
            path: '/activityHome/friendship/activity/apply'
        },
        '2': {
            name: '我的报名',
            class: 'btn_pink',
            path: '/friendship/activity/applyDetail'

        },
        '3': {
            name: '已结束',
            class: 'btn_gray',
        },
    }
)

// 计算属性
const activityDetail: any = computed(() => {
    return useStore.activityDetail || { activityType: '' };
});


onMounted(() => {
    if (activityDetail.value.activityId && useStore.userInfo) {
        getSignUpRecord()
    }
})
watch(activityDetail, (newVal) => {
    if (newVal && useStore.userInfo) {
        getSignUpRecord()
    }
})


function toWrite() {
    router.push({
        path: '/friendship/personFillIn',
    })
    showTipPop.value = false
}


// 获取报名记录
const getSignUpRecord = async () => {
    const { progress } = activityDetail.value
    try {
        if (route.query.isSingle == 'false' && activityDetail.value.customerType == 'friendship') {
            activityDetail.value.status = 0
            return
        }
        const { code, data } = await applicationRecord({
            activityId: unref(activityDetail).activityId,
        })
        if (code === 200) {
            signUpRecord.value = data
        }
        if (signUpRecord.value) {
            activityDetail.value.status = 2

        } else {
            activityDetail.value.status = 1

        }
        if (progress == 3 && !signUpRecord.value) {
            activityDetail.value.status = 3
        }

    } catch (error) {
        activityDetail.value.status = 1

    }
}


function toPage(item: any, query: any) {
    if (activityDetail.value?.status == 3) return
    activityDetail.value.activityStartTime = activityDetail.value?.signUpInfo?.signUpStartTime ?? activityDetail.value.activityStartTime
    activityDetail.value.activityEndTime = activityDetail.value?.signUpInfo?.signUpEndTime ?? activityDetail.value.activityEndTime

    if (!item.path) {
        showTipPop.value = true
        return
    }
    if (activityDetail.value.status === 1 && !activityValidator(activityDetail.value)) {
        return
    }

    if (activityDetail.value.signUpInfo?.signUpCount == activityDetail.value.signUpInfo?.maxCount && item.name == '我要报名') {
        showToast('当前活动人数已满')
        return
    }
    router.push({ path: item.path, query });
}

function formatTimeWithoutSeconds(timeStr: string) {
    // 正则表达式匹配 "时:分:秒" 并将秒替换为空字符串
    return timeStr?.replace(/(\d{2}):(\d{2}):(\d{2})$/, '$1:$2');
}
</script>

<style lang="scss" scoped>
.activityDetail {
    display: flex;
    flex-direction: column;

    .van-empty {
        height: 100vh;
    }

    .bottom_btn {
        width: 431px;
        height: 83px;
        border-radius: 41px;
        margin: 0 auto;
        // margin-bottom: 54px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 500;
        font-size: 36px;
        color: #FFFFFF;
        position: fixed;
        left: 50%;
        bottom: 54px;
        transform: translatex(-50%);
    }

    .btn_gray {
        background: linear-gradient(93deg, #B6B6B6 0%, #E9E9E9 100%);
    }

    .btn_blue {
        background: linear-gradient(100deg, #3F95FF 0%, #93CFFD 100%);
    }

    .btn_pink {
        background: linear-gradient(93deg, #FE779A 0%, #FA9CAB 100%);
    }
}

.self-style {
    background-image: url('@/assets/activity/bg-top.png'), url('@/assets/activity/bg-bottom.png');
    background-repeat: no-repeat, no-repeat;
    background-position: top, bottom;
    background-size: 100% 284px, 100% 424px;
    min-height: calc(100vh - 866px);
    box-sizing: border-box;

    .intro {

        img,
        image {
            width: 100%;
            height: 100%;
            max-width: 100%;
        }
    }

}
</style>
