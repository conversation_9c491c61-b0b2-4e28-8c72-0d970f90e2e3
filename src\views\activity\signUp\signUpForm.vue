<template>
  <div class="signUpActivity-form w-full min-h-full p-28px flex flex-col justify-between items-center bg-[#f7f8fa]">
    <van-form ref="formDataRef" class="!rounded-20px overflow-hidden mb-60px w-full">
      <template v-for="item in topicInfoList" :key="item.topicInfoId">
        <!-- 单行输入 -->
        <van-field v-model="formData[item.topicInfoId]" :name="item.topicContent" :label="item.topicContent"
          :placeholder="`请输入${item.topicContent}`" :required="item.ifMust === 'y'"
          :rules="[{ required: item.ifMust === 'y', message: `请输入${item.topicContent}` }]" input-align="right"
          error-message-align="right" v-if="item.optionType === 'input' || item.optionType === 'single-text'" />
        <!-- 多行输入 -->
        <van-field v-model="formData[item.topicInfoId]" :name="item.topicContent" :label="item.topicContent"
          :placeholder="`请输入${item.topicContent}`" :required="item.ifMust === 'y'"
          :rules="[{ required: item.ifMust === 'y', message: `请输入${item.topicContent}` }]" input-align="left"
          error-message-align="left" v-if="item.optionType === 'textarea' || item.optionType === 'many-text'"
          label-align="top" type="textarea" rows="2" autosize />
        <!-- 下拉单选 -->
        <van-field v-model="formData[item.topicInfoId]" is-link readonly :label="item.topicContent"
          :placeholder="`请选择${item.topicContent}`" :required="item.ifMust === 'y'"
          :rules="[{ required: item.ifMust === 'y', message: `请选择${item.topicContent}` }]" input-align="right"
          error-message-align="right" @click="onSelect(item)" v-if="item.optionType === 'select'" />
        <!-- 多选框 -->
        <van-field :label="item.topicContent" :placeholder="`请选择${item.topicContent}`"
          :rules="[{ required: item.ifMust === 'y', message: `请选择${item.topicContent}` }]" input-align="right"
          error-message-align="right" v-if="item.optionType === 'checkbox'" :required="item.ifMust === 'y'">
          <template #input>
            <van-checkbox-group v-model="formData[item.topicInfoId]" direction="horizontal">
              <van-checkbox :name="i.optionContent" shape="square" v-for="i in item.options" :key="i.topicOptionId"
                class="mb-12px text-28px flex items-center">
                {{ i.optionContent }}
              </van-checkbox>
            </van-checkbox-group>
          </template>
        </van-field>
        <!-- 单选框 -->
        <van-field :label="item.topicContent" :placeholder="`请选择${item.topicContent}`"
          :rules="[{ required: item.ifMust === 'y', message: `请选择${item.topicContent}` }]" input-align="right"
          error-message-align="right" v-if="item.optionType === 'radio'" :required="item.ifMust === 'y'">
          <template #input>
            <van-radio-group v-model="formData[item.topicInfoId]" direction="horizontal">
              <van-radio :name="i.optionContent" v-for="i in item.options" :key="i.topicOptionId">{{ i.optionContent
              }}</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <!-- 日期时间选择 -->
        <van-field v-model="formData[item.topicInfoId]" is-link readonly :label="item.topicContent"
          :placeholder="`请选择${item.topicContent}`"
          :rules="[{ required: item.ifMust === 'y', message: `请选择${item.topicContent}` }]" input-align="right"
          error-message-align="right" @click="onSelect(item)" v-if="item.optionType === 'datePicker'"
          :required="item.ifMust === 'y'" />
        <!-- 地区选择 -->
        <!--        <van-field v-model="formData[item.topicInfoId]" is-link readonly label="所在地区" placeholder="请选择所在地区"-->
        <!--          :rules="[{ required: item.ifMust === 'y', message: '请选择所在地区' }]" input-align="right" error-message-align="right"-->
        <!--          @click="onSelect(item)" v-if="item.optionType === 'areaPicker'" :required="item.ifMust === 'y'" />-->
        <!-- 文件上传 -->
        <!--        <van-field name="uploader" label="文件上传" v-if="item.optionType === 'uploader'"-->
        <!--          :rules="[{ required: item.ifMust === 'y', message: `请上传${item.topicContent}` }]"-->
        <!--          :required="item.ifMust === 'y'">-->
        <!--          <template #input>-->
        <!--            <Uploader :file-lists="formData[item.topicInfoId]" :multiple="false" accept-type="image/jpeg,image/png"-->
        <!--              :visitPrefix="$store.state.visitPrefix" :max-size="5" :operateType="2"-->
        <!--              @success="res => onUploadSuccess(res, item)" @delete="name => onDelete(name, item)">-->
        <!--            </Uploader>-->
        <!--          </template>-->
        <!--        </van-field>-->
        <!-- 日历 -->
        <van-field v-model="formData[item.topicInfoId]" is-link readonly :label="item.topicContent"
          :placeholder="`请选择${item.topicContent}`"
          :rules="[{ required: item.ifMust === 'y', message: `请选择${item.topicContent}` }]" input-align="right"
          error-message-align="right" @click="onSelectCalendar(item)" v-if="item.optionType === 'calendar'"
          :required="item.ifMust === 'y'" />
      </template>
    </van-form>
    <Button name="提交" class="!w-[470px] !h-[70px]" @click="onSubmit" />
    <van-popup v-model:show="show" round position="bottom">
      <van-picker :columns="columns" @cancel="show = false" @confirm="onConfirm" :columns-field-names="customFieldName"
        v-if="currentTopic.optionType === 'select'" />
      <van-date-picker v-model="currentDate" @confirm="onConfirmDate" @cancel="show = false"
        v-if="currentTopic.optionType === 'datePicker'" />
    </van-popup>
    <van-calendar v-model:show="showCalendar" @confirm="onCalendarConfirm" />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { showToast, showConfirmDialog } from 'vant'
import { useUserStore } from '@/store/modules/user';
import { useRouter } from 'vue-router';
import { applicationRecord, signUp } from '@/api/activity';
import Button from '@/components/Button/button.vue';
import { commonBackFn } from '@/utils/utils'

// 自定义字段映射
const customFieldName = {
  text: 'optionContent',
  value: 'topicOptionId',
}

const useStore = useUserStore()
const router = useRouter()
const route = useRoute()
// Reactive state variables
const formDataRef = ref(null)
const formData = ref({})
const topicInfoList = ref([])
const show = ref(false)
const showCalendar = ref(false)
const columns = ref([])
const currentTopic = ref({})
const currentDate = ref([new Date().getFullYear(), new Date().getMonth() + 1, new Date().getDate()])



// 计算属性
const activityDetail = computed(() => useStore.activityDetail || {});

// Watchers
watch(activityDetail, (newVal) => {
  if (newVal) {
    topicInfoList.value = newVal?.signUpInfo?.topicInfoList || []
  }
})

// Mounted hook
onMounted(async () => {
  if (activityDetail.value) {
    topicInfoList.value = activityDetail.value?.signUpInfo?.topicInfoList || []
    //修改报名信息
    if (route.query.autoId) {
      const { data } = await applicationRecord({
        activityId: activityDetail.value.activityId,
      });
      data?.answerRecord?.forEach(t => {
        const { topicInfoId, content } = t
        formData.value[topicInfoId] = content
      })
    }
  }
})

const onConfirm = ({ selectedOptions }) => {
  formData.value[currentTopic.value.topicInfoId] = selectedOptions?.[0].optionContent
  show.value = false
}

const onConfirmDate = ({ selectedValues }) => {
  formData.value[currentTopic.value.topicInfoId] = selectedValues?.join('-')
  show.value = false
}

// Methods
const onSelect = (item) => {
  currentTopic.value = item
  columns.value = item.options
  show.value = true
}

const onSelectCalendar = (item) => {
  currentTopic.value = item
  showCalendar.value = true
}

const onCalendarConfirm = (date) => {
  showCalendar.value = false
  formData.value[currentTopic.value.topicInfoId] = new Date(date).format('yyyy-MM-dd')
}

const onSubmit = async () => {
  try {
    await formDataRef.value.validate()

    await showConfirmDialog({
      title: '温馨提示',
      message: '请确认以上信息是否填写无误?',
    })

    // on confirm
    const form = JSON.parse(JSON.stringify(formData.value))
    const topicAnswers = {}

    topicInfoList.value.forEach(t => {
      const { topicInfoId, optionType } = t
      if (optionType === 'checkbox') {
        topicAnswers[topicInfoId] = form[topicInfoId].join(',')
      } else if (optionType === 'uploader') {
        topicAnswers[topicInfoId] = form[topicInfoId].map(item => item.url)?.[0]
      } else {
        topicAnswers[topicInfoId] = form[topicInfoId]
      }
    })

    const platform = sessionStorage.getItem('platform')
    const { code, message } = await signUp({
      autoId: route.query.autoId ?? undefined,
      activityId: activityDetail.value.activityId,
      platform,
      topicAnswers,
    })

    if (code === 200) {
      await showConfirmDialog({
        title: '温馨提示',
        message: '报名信息提交成功,感谢您的参与~',
        showConfirmButton: true,
        showCancelButton: activityDetail.value.luckDraw === 'y',
        confirmButtonText: activityDetail.value.luckDraw === 'y' ? '去抽奖' : '我知道了',
        cancelButtonText: activityDetail.value.luckDraw === 'y' ? '返回' : ''
      })
      // on confirm
      if (activityDetail.value.luckDraw === 'y') {
        router.replace('/activityHome/lottery')
      } else {
        // router.go(-1)
        backFn()
      }
    } else {
      showToast(message)
    }
  } catch (error) {
    console.log(error)
    showToast({
      message: '请完善报名信息',
    })
  }
}
const backFn = () => {
  // 通过外链打开,需要调用app返回方法
  commonBackFn(route)
} 
</script>

<style lang="scss" scoped>
.signUpActivity-form {
  :deep(.van-checkbox-group) {
    display: flex;
    justify-content: flex-end;
  }

  // :deep(.van-checkbox__icon),
  // :deep(.van-icon:before),
  :deep(.van-checkbox__icon .van-icon),
  :deep(.van-radio__icon .van-icon),
  :deep(.van-radio__icon) {
    display: flex;
    align-items: center;
    width: 30px;
    height: 30px;
    line-height: 30px;
  }

  :deep(.van-checkbox__icon) {
    display: flex;
    align-items: center;
  }

  .submit {
    background-image: url('@/assets/activity/apply_btn.png');
    font-size: 33px;
    background-size: 100% 100%;
    color: #FFFFFF;
  }
}
</style>
