import { showFailToast } from 'vant';

export function checkStatus(status: number, msg: string): void {
  let errMessage = '';

  switch (status) {
    case 400:
      errMessage = `${msg}`;
      break;
    case 401:
    case 9000:
    case 9001:
    case 9002:
    case 9003:
    case 9004:
    case 9005:
      errMessage = msg || '登录信息失效！请重新登录。';
      break;

    case 403:
      errMessage = '用户得到授权，但是访问是被禁止的!';
      break;
    // 404请求不存在
    case 404:
      errMessage = '网络请求错误,未找到该资源!';
      break;
      break;
    case 500:
      errMessage = '服务器错误,请联系管理员!';
      break;
    case 502:
      errMessage = '网络错误!';
      break;
    default:
  }

  if (errMessage) {
    showFailToast(errMessage);
  }
}
