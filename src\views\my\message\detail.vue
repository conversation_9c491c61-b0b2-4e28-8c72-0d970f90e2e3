<template>
    <div class="message-detail min-h-100vh w-full box-border flex flex-col pt-40px pb-80px">
        <div class="message-detail__title text-#333 text-32px px-30px text-justify">
            {{ infos?.mesTitle }}
        </div>
        <div class="message-detail__time text-#999 text-24px mt-27px px-30px">{{ infos?.choiceSendTime }}</div>
        <div class="message-detail__content mt-37px flex-1 box-border relative z-10 overflow-scroll">
            <div class="relative z-10 px-85px mt-38px text-28px text-#333 overflow-scroll leading-60px 
                text-justify indent-8 text-content tracking-wide" v-html="infos?.sysContent">
            </div>
            <div class="expand_fold absolute left-0 right-0 bottom-0px z-1 flex items-cend">
                <img loading="lazy" src="@/assets/my/message/letter-border-3.png" class="w-full -mb-30px" />
                <div class="absolute z-100 left-50% bottom-110px -translate-x-50%" @click="lookDetails"
                    v-if="infos?.routing !== '--'">
                    <div class="relative px-135px h-80px flex items-center justify-center bg-#fff rounded-40px">
                        <div class="text-#5AA4FF text-34px whitespace-nowrap">立即查看</div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</template>
<script setup lang="ts">
import { useUserStore } from '@/store/modules/user';
import { showToast } from 'vant';
import utils from '@/utils/utils';
const userStore = useUserStore();
const infos = computed(() => {
    let info: any = userStore.getCurrentMessageInfo || {}
    if (JSON.stringify(info) === '{}') {
        showToast('获取信息失败,请返回上级界面重试')
    }
    return info;
});
// 业务详情-未对接
const lookDetails = () => {
    let path = '', query = {};
    // 做判断
    let reqParams: any = utils.isJSON(infos.value?.reqParams) ? JSON.parse(infos.value?.reqParams) : ''
    console.log('reqParams', infos.value)
    switch (infos.value?.routing) {
        case 'venuePositionWait':
            // 阵地待审核
            path = '/my/positionAdmin'
            query = {
                active: 'review'
            }
            break;
        case 'venuePositionRes':
            // 阵地
            path = '/reserveDetail'
            query = {
                recordId: reqParams?.recordId
            }
            break;

        case 'shortVideo':
            // 短视频
            path = '/my/manuscript'
            query = {
                status: reqParams?.status
            }
            break;
        case 'model':
            // 劳模认证
            path = '/workerCraftsman/auth/form'
            query = {
                type: reqParams?.type
            }
            break;
        // 单身联谊
        case 'singleWait':
            path = '/my/friendship'
            query = {
                active: 0,
                status: 0
            }
            break;
        case 'singleRes':
            path = '/friendship/personFillIn'
            query = {
                status: reqParams?.status
            }
            break;

        case 'psychologica':
            // 跳转咨询列表
            path = '/psychologicalCounseling'
            query = {
                active: 'consult'
            }
            break;
        default:
            return
    }
    toPage({
        path,
        query
    });
};
const router = useRouter();
const toPage = (item: any) => {
    router.push({
        path: item.path,
        query: item.query,
    })
};
</script>
<style scoped lang="scss">
.message-detail {
    background-image: url('@/assets/my/message/bg.png');
    background-size: 100% 100%;

    .message-detail__content {
        line-height: 50px;
        background-image: url('@/assets/my/message/letter-border-1.png'), url('@/assets/my/message/letter-border-2.png');
        background-position: 0% 0%, 0% 330px;
        background-repeat: no-repeat, repeat-y;
        background-size: 100% auto, 100% 189px;
        overflow: hidden;

        .text-content {
            height: calc(100% - 400px);
        }
    }

    .expand_fold {
        animation: expandShowAnimation .6s ease;
    }
}

// 展开动画
@keyframes expandShowAnimation {
    0% {
        transform: translateY(-100%);
        opacity: 0;
    }

    100% {
        transform: translateY(0px);
        opacity: 1;
    }
}
</style>