<template>
    <div class="address_view bg-[#F9F9F9] min-h-full flex flex-col">
        <div class="flex-1 p-26px">
            <refreshList ref="loadMoreRef" :immeCheck="true" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore"
                class="min-h-full">
                <div class="address_cell py-30px px-25px bg-#fff rounded-20px mt-22px" v-for="item, index in list"
                    :key="index" @click="selectAddress(item)">
                    <div class="text-[#333] text-30px font-medium">
                        <span>收货人：{{ item.receiverName }}</span>
                        <span class="ml-42px">{{ item.receiverPhone }}</span>
                    </div>
                    <div class="text-[#767676] text-28px mt-30px pb-27px address_info">{{ item.detailArea }}
                        {{ item.detailAddress }}</div>

                    <div class="bottom flex items-center justify-between mt-28px">
                        <div class="left flex items-center">
                            <img loading="lazy" src="@/assets/public/checked.png" class="w-26px"
                                v-if="item.defaultFlag === 'y'" />
                            <img loading="lazy" src="@/assets/public/check.png" class="w-26px" v-else />
                            <span class="ml-8px text-28px text-[#999] leading-none"
                                :class="{ '!text-[#418DEA]': item.defaultFlag === 'y' }">默认</span>
                        </div>
                        <div class="right flex items-center">
                            <div class="text-[#999] flex items-center" @click.stop="deleteHandle(item.autoId)">
                                <img loading="lazy" src="@/assets/public/delete_icon.png" class="w-26px" />
                                <span class="ml-8px text-28px leading-none">删除</span>
                            </div>
                            <div class="text-[#999] ml-45px flex items-center" @click.stop="toPage(item)">
                                <img loading="lazy" src="@/assets/public/edit_icon_g.png" class="w-26px" />
                                <span class="ml-8px text-28px leading-none">编辑</span>
                            </div>
                        </div>
                    </div>
                </div>
            </refreshList>
        </div>

        <div class="bottom_btn h-140px bg-[#fff] w-full flex items-center justify-center relative safe_area_bottom" @click="addAddress">
            <img loading="lazy" src="@/assets/public/button.png" class="w-75%" />
            <div class="text-#fff absolute left-50% top-51% -translate-50% 
            flex items-center justify-center leading-none">
                <span class="text-60px leading-none mr-16px">+</span>
                <span class="leading-none text-32px ">新增收货地址</span>
            </div>
        </div>
        <confirmPopop ref="popRef" tips="您确认要删除该地址吗" v-model:show="showTips" @confirm="confirmDelete" />
    </div>
</template>

<script lang="ts" setup>
import refreshList from "@/components/refreshList/index.vue";
import { getAddressList, deleteAddress } from "@/api/address.ts";
import confirmPopop from "@/components/Popup/confirmPopop.vue";
import { useMallStore } from '@/store/modules/mall';
import { useRoute, useRouter } from 'vue-router';
const route = useRoute();
// 列表
const list = ref<any>([])
const pageNum = ref(1)
const loadMoreRef: any = ref(null)
const onRefreshList = () => {
    pageNum.value = 1
    loadMoreData()
}
// 加载更多
const onLoadMore = () => {
    pageNum.value++
    loadMoreData()
}
const loadMoreData = () => {
    getAddressList({
        pageNum: pageNum.value,
        pageSize: 10,
    }).then((res: any) => {
        if (res.code == 200) {
            if (pageNum.value === 1) list.value = res.data
            else list.value = [...list.value, ...res.data]
        }
        //重置刷新状态及 判断是否加载完成
        if (loadMoreRef.value) {
            loadMoreRef.value.onLoadSuc(list.value.length, res.total)
        }
    });
}
// 删除数据
const showTips = ref(false)
const deleteId = ref('')
const deleteHandle = (autoId: string) => {
    showTips.value = true
    deleteId.value = autoId
}
const confirmDelete = () => {
    showTips.value = false
    const todoValueList = [deleteId.value]
    deleteAddress(todoValueList).then((res: any) => {
        if (res.code == 200) {
            deleteId.value = ''
            onRefreshList()
        }
    })
}
const router = useRouter()
const toPage = (item: any) => {
    router.push({
        path: "/address/form",
        query: {
            autoId: item.autoId,
        }
    })
}
const addAddress = () => {
    router.push({
        path: "/address/form",
    })
}
// 选中地址
const mallStore = useMallStore()
const selectAddress = (item: any) => {
    if (route.query?.noBack) return
    item.id = route.query?.autoId
    mallStore.setAddressInfo(item) //store存储
    router.back()
}
</script>

<style scoped lang="scss">
.address_info {
    border-bottom: 1px solid #EFEFEF;
}
</style>