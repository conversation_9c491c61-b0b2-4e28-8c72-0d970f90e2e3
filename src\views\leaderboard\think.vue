<template>
    <div class="think-main relative">
        <img loading="lazy" src="@/assets/leaderBoard/think/banner.png" class="w-full banner absolute top-0 z-1" />
        <div class="relative z-2 mt-158px px-30px content flex flex-col pb-50px border-box">
            <dateSelect :dateTypeArr="dateTypeArr" :defaultType="currentIndex" v-model:default-time="currentDate"
                :columnsType="columnsType" @changeType="changeType">
            </dateSelect>
            <div class="echarts liner-grident py-32px px-30px rounded-20px mt-24px w-full relative z-1">
                <div class="header flex items-center justify-between text-#090A0B">
                    <div class="flex items-center">
                        <img loading="lazy" src="@/assets/leaderBoard/icon_lou.png" class="w-36px h-36px" />
                        <div class="ml-14px">思想引领统计</div>
                    </div>
                    <div class="select z-2 w-fit py-6px px-15px rounded-30px text-#6EAEFC"
                        @click="showPickerFn('region')">
                        <span class="mr-10px text-26px">{{ company?.companyName }}</span>
                        <van-icon name="arrow-down"></van-icon>
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-28px mt-20px w-full">
                    <div v-for="(item, index) in newsSatatics" :key="index"
                        class="bg-#fff rounded-20px flex items-center justify-center h-124px box-shadow">
                        <img loading="lazy" :src="item.icon" class="w-62px h-62px mt-10px" />
                        <div class="ml-20px">
                            <div class="text-30px font-bold" :style="{ color: item.color }">
                                {{ ideaObj?.[item.key] }}
                                <span class="text-26px">{{ item.unit }}</span>
                            </div>
                            <div class="text-26px text-[#999]">{{ item.title }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="echarts liner-grident py-32px px-30px rounded-20px mt-24px w-full relative z-1">
                <div class="header flex items-center justify-between text-#090A0B">
                    <div class="flex items-center">
                        <img loading="lazy" src="@/assets/leaderBoard/think/icon_user.png" class="w-36px h-36px" />
                        <div class="ml-14px">区域会员行为趋势</div>
                    </div>
                    <div class="select z-2 w-fit py-6px px-15px rounded-30px text-#6EAEFC"
                        @click="showPickerFn('behaviorType')">
                        <span class="mr-10px text-26px">{{ behavior?.behaviorTypeName }}</span>
                        <van-icon name="arrow-down"></van-icon>
                    </div>
                </div>
                <div class="mt-20px w-full h-500px">
                    <LineUserChart :behaviorName="behavior?.behaviorTypeName" :dataSource="behaviorObj" />
                </div>
            </div>
            <div class="regions py-32px px-30px rounded-20px mt-24px w-full liner-grident">
                <div class="header flex items-center justify-between text-#090A0B">
                    <div class="flex items-center">
                        <img loading="lazy" src="@/assets/leaderBoard/think/icon_addr.png" class="w-36px h-36px" />
                        <div class="ml-14px">区域会员阅读量统计(次)</div>
                    </div>
                    <div class="select z-2 w-fit py-6px px-12px rounded-30px text-#6EAEFC"
                        @click="showPickerFn('user')">
                        <span class="mr-20px text-26px">{{ customer?.customerTypeName }}</span>
                        <van-icon name="arrow-down"></van-icon>
                    </div>
                </div>
                <template v-if="readList?.length">
                    <Progress :list="readList" :type="1" />
                </template>
                <Empty v-else />
                
            </div>
        </div>
        <van-popup v-model:show="selectShow" position="bottom">
            <van-picker title="选择" :columns="columns" @cancel="selectShow = false" @confirm="onConfirmSelect" />
        </van-popup>
    </div>
</template>
<script setup lang="ts">
import { getIdeasLeadingData, getRegionMemberBehaviorTrend, getRegionMemberReadingStatistics } from '@/api/leaderboard/news'
import Empty from '@/components/Empty/index.vue'
import Progress from './components/charts/Progress.vue';
import newsSend from '@/assets/leaderBoard/think/icon_send.png'
import newsRead from '@/assets/leaderBoard/think/icon_read.png'
import newsLike from '@/assets/leaderBoard/think/icon_like.png'
import newsCollect from '@/assets/leaderBoard/think/icon_collect.png'
import newsShare from '@/assets/leaderBoard/think/icon_share.png'
import newsComment from '@/assets/leaderBoard/think/icon_comment.png'
import LineUserChart from './components/charts/LineUserChart.vue'
import { useDictionary } from '@/store/modules/dictionary';
const dictionaryStore = useDictionary()
import dateSelect from './components/dateSelect.vue'
import { useUserStore } from '@/store/modules/user';
const useStore = useUserStore();
// 日期选择
const dateTypeArr = ref([
    {
        label: '年度',
        value: 'year'
    },
    {
        label: '月度',
        value: 'month'
    },
])
const currentMonth = new Date().getMonth() + 1 < 10 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1;
const currentDate = ref([new Date().getFullYear(), currentMonth]);
// 默认年度
const currentIndex = ref(1);
const columnsType = ref(['year', 'month'])
const changeType = (val: any) => {
    currentIndex.value = val
    if (val === 0) {
        columnsType.value = ['year']
        currentDate.value = [new Date().getFullYear()]
    }
    else {
        columnsType.value = ['year', 'month']
        currentDate.value = [new Date().getFullYear(), currentMonth]
    }
}
// 日期发生变化
watch(currentDate, () => {
    // 选项发生改变
    getIdeasLeadingDataFn()
    getRegionMemberBehaviorTrendFn()
    getRegionReadNumFn()
})
// end



const newsSatatics = ref([
    {
        icon: newsSend,
        title: '新闻历史发布量',
        key: 'releaseVolume',
        color: '#40E076',
        unit: '(条)'
    },
    {
        icon: newsRead,
        title: '新闻历史阅读量',
        key: 'readingQuantity',
        color: '#0AABB3',
        unit: '(次)'
    },
    {
        icon: newsLike,
        title: '新闻历史点赞量',
        key: 'likeTotal',
        color: '#FFB91E',
        unit: '(次)'
    },
    {
        icon: newsCollect,
        title: '新闻历史收藏量',
        key: 'collectTotal',
        color: '#40E076',
        unit: '(次)'
    },
    {
        icon: newsShare,
        title: '新闻历史分享量',
        key: 'shareTotal',
        color: '#8E8DFF',
        unit: '(次)'
    },
    {
        icon: newsComment,
        title: '新闻历史评论量',
        key: 'commentTotal',
        color: '#5279F0',
        unit: '(条)'
    }
])
// 区县下拉
const regionList = computed(() => dictionaryStore.dictionaryOBJmap?.['regionCode']?.map((t: any) => {
    const { dictName, remark } = t
    return { value: remark, text: dictName }
}))
// 区县信息
const company = ref({
    companyId: '',
    companyName: ''
})
const currentUserCode = computed(() => useStore.leaderCode)
// 思想引领
const ideaObj = ref<any>({})
const getIdeasLeadingDataFn = async () => {
    const res = await getIdeasLeadingData({
        // 总工会
        companyId: company.value?.companyId,
        timeType: dateTypeArr.value[currentIndex.value].value,
        queryTime: currentDate.value.join('')
    })
    if (res.code === 200 && res.data) {
        ideaObj.value = res.data
        // 转千分位
        for (const key in ideaObj.value) {
            if (ideaObj.value[key]) {
                ideaObj.value[key] = ideaObj.value[key].toLocaleString()
            }
        }
    }
}

// 会员行为分析
const behaviorObj = ref<any>({})
const behavior = ref<any>({
    behaviorType: '',
    behaviorTypeName: ''
})
const behaviorList = computed(() => {
    return dictionaryStore.getDictionaryOpt?.['behaviorType'].map((t: any) => {
        const { label, value } = t
        return { text: label, value }
    })
})
const getRegionMemberBehaviorTrendFn = async () => {
    const res = await getRegionMemberBehaviorTrend({
        behaviorType: behavior.value.behaviorType,
        timeType: dateTypeArr.value[currentIndex.value].value,
        queryTime: currentDate.value.join('')
    })
    if (res.code === 200 && res.data) {
        behaviorObj.value = res.data
    }
}
// end

// 阅读量统计
const customer = ref({
    customerType: '',
    customerTypeName: ''
})
const customerList = computed(() => {
    return dictionaryStore.getDictionaryOpt?.['statelessCustomerType'].map((t: any) => {
        const { label, value } = t
        return { text: label, value }
    })
})
const readList = ref<any>([])
const getRegionReadNumFn = async () => {
    const res = await getRegionMemberReadingStatistics({
        customerType: customer.value.customerType,
        timeType: dateTypeArr.value[currentIndex.value].value,
        queryTime: currentDate.value.join('')
    })
    if (res.code === 200 && res.data) {
        readList.value = res.data.map((t: any, index: number) => {
            return {
                rank: index + 1,
                title: t.areaName,
                percentage: t.percentage ? parseFloat(t.percentage) * 100 > 100?100:parseFloat(t.percentage) * 100 : 0,
                num: t.number.toLocaleString()
            }
        })
    }
}
// end

const selectShow = ref(false)
const selectType = ref('')//下拉选择类型
const columns = ref<any>([])
const showPickerFn = (type: string) => {
    selectType.value = type
    switch (type) {
        case 'region':
            columns.value = regionList.value
            break
        case 'behaviorType':
            columns.value = behaviorList.value
            break
        case 'user':
            columns.value = customerList.value
            break
    }
    selectShow.value = !selectShow.value
}
const onConfirmSelect = ({ selectedOptions }) => {
    const text = selectedOptions[0].text
    const value = selectedOptions[0].value

    switch (selectType.value) {
        case 'region':
            company.value.companyId = value
            company.value.companyName = text
            getIdeasLeadingDataFn()
            break
        case 'behaviorType':
            behavior.value.behaviorType = value
            behavior.value.behaviorTypeName = text
            getRegionMemberBehaviorTrendFn()
            break
        case 'user':
            customer.value.customerType = value
            customer.value.customerTypeName = text
            getRegionReadNumFn()
            break
    }
    selectShow.value = false
}
onMounted(() => {
    if (currentUserCode.value) {
        company.value.companyId = currentUserCode.value as string
        company.value.companyName = regionList.value.find((t: any) => t.value === currentUserCode.value)?.text || ''
    } else {
        company.value.companyId = regionList.value[0].value
        company.value.companyName = regionList.value[0].text
    }

    behavior.value.behaviorType = behaviorList.value[0].value
    behavior.value.behaviorTypeName = behaviorList.value[0].text
    customer.value.customerType = customerList.value[0].value
    customer.value.customerTypeName = customerList.value[0].text

    getIdeasLeadingDataFn()
    // 用户行为趋势
    getRegionMemberBehaviorTrendFn()
    // 阅读数排行
    getRegionReadNumFn()
})
</script>
<style lang="scss" scoped>
.think-main {
    width: 100%;
    min-height: 100vh;
    background: linear-gradient(to bottom, #F6FBFF 20%, #F6FBFF 60%, #DCEEFB 100%);
    box-sizing: border-box;
    position: relative;

    .content {
        height: calc(100% - 158px);
    }

    .box-shadow {
        box-shadow: 0px 0px 10px 0px rgba(65, 137, 202, 0.09);
    }

    .liner-grident {
        background: linear-gradient(0deg, #FFFFFF 54%, #E5F3FF 100%);
    }

    .select {
        border: 1px solid #76B7FB;
    }

}
</style>