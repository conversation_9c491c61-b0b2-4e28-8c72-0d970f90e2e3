
import { h5Http } from '@/utils/http/axios';
//获取直播列表
export const findVoListLive = (params) => {
    return h5Http.get({
        url: '/curriculumCatalogue/findVoListLive',
        params
    });
}

//获取课堂列表
export const curriculumInfoFindVoListH5 = (params) => {
    return h5Http.get({
        url: '/curriculumInfo/findVoListH5',
        params
    });
}

//获取课堂详情
export const curriculumInfoGetVoByDtoH5 = (params) => {
    return h5Http.get({
        url: '/curriculumInfo/getVoByDtoH5',
        params
    });
}

// 加入课堂
export const curriculumInfoSaveOrUpdateByDTO = (params) => {
    return h5Http.post({
        url: '/curriculumUserInfo/saveOrUpdateByDTO',
        params
    });
}

// 删除课堂
export const curriculumUserInfoDelete = (curriculumBizId) => {
    return h5Http.delete({
        url: '/curriculumUserInfo?curriculumBizId='+curriculumBizId
    });
}

//获取课程列表
export const curriculumCatalogueFindVoListH5 = (params) => {
    return h5Http.get({
        url: '/curriculumCatalogue/findVoListH5',
        params
    });
}

//获取课程详情
export const curriculumCatalogueGetVoByDtoH5 = (params) => {
    return h5Http.get({
        url: '/curriculumCatalogue/getVoByDtoH5',
        params
    });
}


//获取我加入的课堂
export const curriculumUserInfoFindVoListH5 = (params) => {
    return h5Http.get({
        url: '/curriculumInfo/myClassroomListH5',
        params
    });
}


//获取我收藏的课程
export const curriculumInfoMyCollect = (params) => {
    return h5Http.get({
        url: '/curriculumInfo/myCollect',
        params
    });
}

//查询心得分享提交记录
export const  shareFindAuditListH5= (params) => {
    return h5Http.get({
        url: '/experienceSharing/findAuditListH5',
        params
    });
}

// 审核心得分享
export const  shareAuditH5= (params) => {
    return h5Http.post({
        url: '/experienceSharing/auditH5',
        params
    });
}

//查询小组加入的申请记录
export const groupFindAuditListH5 = (params) => {
    return h5Http.get({
        url: '/groupsInsertRecord/findAuditInfoH5',
        params
    });
}


//审核小组加入的提交记录
export const auditInsertGroups = (params) => {
    return h5Http.post({
        url: '/groupsInsertRecord/auditInsertGroups',
        params
    });
}







