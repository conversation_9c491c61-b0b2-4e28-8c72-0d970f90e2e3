import { unref, nextTick, watch, computed, ref } from 'vue'
import type { Ref } from 'vue'
import { tryOnUnmounted } from '@vueuse/core'
import { useDebounceFn } from '@vueuse/core'
import type { EChartsOption } from 'echarts'
import echarts from './echarts'
import { useTimeoutFn } from './hooks/useTimeout'
import { useEventListener } from './hooks/useEventListener'
// import { useBreakpoint } from '/@/hooks/event/useBreakpoint'
// import { useRootSetting } from '/@/hooks/setting/useRootSetting'

export function useECharts(
  elRef: Ref<HTMLDivElement>,
  theme: 'light' | 'dark' | 'default' = 'default',
) {
  // const { getDarkMode: getSysDarkMode } = useRootSetting()

  // const getDarkMode = computed(() => {
  //   return theme === 'default' ? getSysDarkMode.value : theme
  // })
  let chartInstance: echarts.ECharts | null = null
  let resizeFn: ()=>any = resize
  const cacheOptions = ref({}) as Ref<EChartsOption>
  let removeResizeFn:()=>any= () => { }

  resizeFn = useDebounceFn(resize, 200)

  const getOptions = computed(() => {
    // if (getDarkMode.value !== 'dark') {
    //   return cacheOptions.value as EChartsOption
    // }
    return {
      // backgroundColor: 'transparent',
      ...cacheOptions.value,
    } as EChartsOption
  })

  function initCharts(t = theme) {
    const el = unref(elRef)
    if (!el || !unref(el)) {
      return
    }

    chartInstance = echarts.init(el, t, { renderer: 'svg' })
    const { removeEvent } = useEventListener({
      el: window,
      name: 'resize',
      listener: resizeFn,
    })
    removeResizeFn = removeEvent
    // const { widthRef, screenEnum } = useBreakpoint()
    // if (unref(widthRef) <= screenEnum.MD || el.offsetHeight === 0) {
    //   useTimeoutFn(() => {
    //     resizeFn()
    //   }, 30)
    // }
  }

  function setOptions(options: EChartsOption, clear = true) {
    cacheOptions.value = options
    if (unref(elRef)?.offsetHeight === 0) {
      useTimeoutFn(() => {
        setOptions(unref(getOptions))
      }, 30)
      return
    }
    nextTick(() => {
      useTimeoutFn(() => {
        if (!chartInstance) {
          initCharts('default')

          if (!chartInstance) return
        }
        clear && chartInstance?.clear()

        chartInstance?.setOption(unref(getOptions))
        chartInstance.resize()
      }, 30)
    })
  }

  function resize() {
    chartInstance?.resize()
  }

  // watch(
  //   () => getDarkMode.value,
  //   theme => {
  //     if (chartInstance) {
  //       chartInstance.dispose()
  //       initCharts(theme as 'default')
  //       setOptions(cacheOptions.value)
  //     }
  //   },
  // )

  tryOnUnmounted(() => {
    if (!chartInstance) return
    removeResizeFn()
    chartInstance.dispose()
    chartInstance = null
  })

  function getInstance(): echarts.ECharts | null {
    if (!chartInstance) {
      initCharts('default')
    }
    return chartInstance
  }

  function onDataZoom(data:any) {
    const zoomSize = 6
    getInstance()?.on('click', function (params) {
      getInstance()?.dispatchAction({
        type: 'dataZoom',
        startValue: data[Math.max(params.dataIndex - zoomSize / 2, 0)],
        endValue: data[Math.min(params.dataIndex + zoomSize / 2, data.length - 1)],
      })
    })
  }

  return {
    setOptions,
    resize,
    echarts,
    getInstance,
    onDataZoom,
  }
}
