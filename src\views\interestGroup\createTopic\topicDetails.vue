<template>
  <div class="bg-[#f6f7f8] min-h-[100vh] flex flex-col relative">
    <div class="px-[18px] box-border">
      <div class="bg-[#fff] px-[18px] pb-[18px] pt-[40px] box-border">
        <topicList :data="Data.topicList" type="examine" />
      </div>
    </div>

    <div class="bg-[#fff] px-30px box-border my-[20px]">
      <interestGroupList :data="Data.groupList" :showBtn="true" @details="toDetail">
        <template #showBtn="{ item }">
          <div class="w-90px py-6px bg-#5AA4FF rounded-19px text-#fff text-center text-24px" @click="joinGroup">
            加入
          </div>
        </template>
      </interestGroupList>
    </div>
    <div class="bg-[#fff] box-border flex-1 pb-122px">
      <div class="text-[#333] text-30px px-30px py-34px box-border pinglun-title">
        评论 1
      </div>
      <div class="px-30px  box-border">
        <div v-for="(item, index) in Data.evaluateList" :key="index"
          class="flex mb-20px pt-30px pb-15px box-border border-b-1px border-b-solid border-b-#E5E5E5">
          <img loading="lazy" src="@/assets/interest/banner.png" alt="" class="w-76px h-76px rounded-[50%]" />
          <div class="topic-right pl-33px flex-1">
            <div class="flex items-center">
              <div class="flex-1">惟**磬音</div>
              <slot name="score"></slot>
            </div>
            <div class="text-#333 text-28px mt-35px mb-24px">
              拍摄的好好看！
            </div>
            <div class="bg-[#F6F7F8] rounder-4px px-24px pt-20px box-border pb-5px">
              <div class="flex items-center text-24px text-#666 mb-15px">
                <div class="text-#5AA4FF">吃瓜罗布特</div>
                ：谢谢
              </div>
              <div class="flex items-center text-24px text-#666 mb-15px">
                <div class="text-#5AA4FF">张三</div>
                ：你好
              </div>
            </div>
            <div class="text-#999999 text-20px mt-16px flex items-center">
              <div class="flex-1">2024-10-24 9:00</div>
              <div class="flex items-center">
                <div class="flex items-center mr-30px">
                  <van-icon name="chat-o" size="20" class="mr-10px" /> 6
                </div>
                <div class="flex items-center">
                  <van-icon name="good-job-o" size="20" class="mr-10px" /> 5
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
    <div class="flex bg-#fff text-#333 text-25px h-[122px] w-100% bottom-box justify-around">
      <div class="flex items-center justify-center">
        <img loading="lazy" src="@/assets/interest/icon_comment.png" alt="" class="w-40px  mr-20px">
        <div>评论</div>
      </div>
      <div class="flex items-center"><van-icon name="eye-o" size="30" color="#999999" class="mr-20px" />158</div>
      <div class="flex items-center"><van-icon name="good-job-o" size="30" color="#999999" class="mr-20px" />158</div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import topicList from "@/components/List/topicList.vue";
import interestGroupList from "@/components/List/interestGroupList.vue";
import detailImg from "@/assets/interest/banner.png";
const Data = ref({
  topicList: [
    {
      title:
        "推荐#旅游攻略#，目的地绝对是最重要的一环，只有选择好旅游的目的地，我们才能更好的进行下一步动作。 在有目的地之后，出行工具的",
      time: "2024-10-24  9:00",
      src: [{ img: detailImg }, { img: detailImg }, { img: detailImg }],
    },
  ],
  groupList: [
    {
      title: "中华户外南充公益联盟",
      src: detailImg,
      activityNum: 1,
      totalMember: 120,
      tags: ["户外", "运动", "公益"],
    },
  ],
  evaluateList: [{ pic: detailImg }, { pic: detailImg }],
});
</script>
<style lang="scss" scoped>
.pinglun-title {
  position: relative;
  border-bottom: 1px solid #e5e5e5;

  &::before {
    content: "";
    width: 36px;
    height: 6px;
    background: linear-gradient(0deg, #a1cbff 0%, #5aa4ff 100%);
    border-radius: 3px;
    position: absolute;
    left: 0;
    transform: translateX(100%);
    bottom: 10px;
  }
}

.bottom-box {
  position: fixed;
  bottom: 0;
  box-shadow: 11px 3px 17px 0px rgba(194, 194, 194, 0.6);
}
</style>