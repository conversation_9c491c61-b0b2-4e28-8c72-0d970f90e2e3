<template>
    <div class="topic mb-25px" v-for="(item, index) of data" :key="index" @click="toPage(item)">
        <div class="flex mb-20px">
            <img loading="lazy" src="@/assets/public/head_default.png" alt="" class="w-76px h-76px topic-icon">
            <div class="topic-right pl-33px">
                <div class="text-28px">{{ item.userName }}</div>
                <div class="text-#999999 text-24px mt-16px">{{ item.createTime }}</div>
            </div>
        </div>
        <span class="bg-#F2F8FF px-20px py-10px text-24px text-#5AA4FF "
            v-if="props.type != 'examine' && item.groupName">{{ item.groupName }}</span>
        <van-rate v-if="item.score" :readonly="true" v-model="item.score" :size="20" color="#ffd21e" void-icon="star"
            void-color="#BFBFBF" class="pt-20px" />
        <div class="text-30px mt-20px"><span class="text-#5AA4FF" v-if="item.topicTitle">#{{ item.topicTitle
        }}#</span>{{
                    item.content }}</div>
        <div class="overflow-hidden">
            <img loading="lazy" :src="judgeStaticUrl(src)" alt="" @click.stop="toPreview(item?.images?.split(','), i)"
                v-for="(src, i) of item?.images?.split(',')" :key="i"
                class="w-200px h-200px mr-13px mt-20px rounded-15px object-cover">
        </div>
        <div class="flex mt-20px justify-between" v-if="props.type != 'examine'">
            <input type="text" class="w-40/100 bg-#F6F7F8 rounded-18px border-0 h-40px text-20px pl-15px"
                placeholder="说点什么" v-model="item.val" @blur.stop="comments(item)">
            <div class="text-#999999 text-28px flex justify-around w-50/100 my-icons">
                <div class="flex items-center">
                    <!-- size="20" -->
                    <van-icon name="eye-o" v-if="type != 'studyshare'" />{{
                        item.readCount }}
                </div>
                <div class="flex items-center">
                    <van-icon name="chat-o" />{{ item.replyCount }}
                </div>
                <div class="flex items-center" @click.stop="like(item)">
                    <van-icon :name="item.likeState ? 'good-job' : 'good-job-o'"
                        :color="item.likeState ? '#5AA4FF' : '#333'" />{{ item.likeCount }}
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { CommentsLikeOperate, interestGroupComments } from '@/api/interestGroup'
import { showFailToast, showSuccessToast, showImagePreview } from 'vant';
import router from '@/router';
import { judgeStaticUrl } from '@/utils/utils';
const props = defineProps({
    data: {
        type: Array,
        default: [],
    },
    type: {
        type: String,
        default: '',//examine-审核信息，不展示标签及数据部分
    },
    source: {
        type: String,
        default: '',
    },
    customClick: { //是否自定义点赞、评论、详情事件
        type: Boolean,
        default: false,
    }

})
const val = ref(null)
const emit = defineEmits(["like", "comment", 'detail']);
//图片预览
function toPreview(img, index) {
    showImagePreview({ images: img.map(t => judgeStaticUrl(t)), startPosition: index })
}
//点赞
function like(item) {
    if (props.customClick) {
        emit('like', item)
        return
    }
    CommentsLikeOperate({
        commentsId: item.commentsId
    }).then(res => {
        if (res.code == 200) {
            if (item.likeState) {
                item.likeState = false;
                item.likeCount--;
                showFailToast('取消点赞')
            } else {
                item.likeState = true;
                item.likeCount++;
                showSuccessToast('点赞成功')
            }

        }
    })
}
//评论
function comments(item) {
    if (props.customClick) {
        emit('comment', item)
        return
    }
    let params = {
        content: item.val,
        groupId: item.groupId,
        dataSources: "group", //评论来源 : group:小组  activity：活动
        commentType: props.source, //评论类型 （groupComment：小组评价，dynamic：小组动态，activityComment：活动评论，activityDynamic：活动评价 ）
        pid: props.source == 'reply' ? item.commentsId : undefined
    }
    interestGroupComments(params).then(res => {
        if (res.code == 200) {
            item.val = ""
            showSuccessToast("提交成功,等待审核");
        }
    })
}
//详情
function toPage(item) {
    if (props.customClick) {
        emit('detail', item)
        return
    }
    router.push({
        path: '/dynamicDetail',
        query: {
            commentsId: item.commentsId
        }
    })
}
</script>
<style lang="scss" scoped>
.topic {
    .topic-icon {
        border-radius: 50%;
    }
}

.my-icons {}
</style>
