<script setup lang="ts"></script>
<template>
  <div class="home-page w-full">
    <div class="banner-top h-300px w-full relative">
      <img loading="lazy" src="@/assets/home/<USER>" alt="" class="h-full w-full z-9999" />
      <div class="background_three waveWrapper waveAnimation w-full absolute top-0 left-0 h-full">
        <div class="wave waveTop"></div>
        <div class="wave waveMiddle waveMiddle_one"></div>
        <!-- <div class="wave waveMiddle waveMiddle_two"></div> -->
        <div class="wave waveBottom"></div>
      </div>
      <div class="total_pop" @click="toShowTotal()">
        <van-swipe :vertical="true" :show-indicators="false" class="notic_swip" :autoplay="3200">
          <van-swipe-item v-for="(item, index) in Data.tradeList" :key="index">
            <div class="flex items-center justify-center">
              <img loading="lazy" :src="item.src" alt="" class="w-57px h-57px" />
              <div class="flex flex-col justify-center w-full h-full total_text ">
                <div class="leading-none text-20px mt-5px">{{ item.titName }}</div>
                <div class="infoNum mt-5px">{{ item.allNumb }}</div>
              </div>
            </div>
          </van-swipe-item>
        </van-swipe>
      </div>
    </div>
    <div class="nav-box h-160px flex justify-around text-28px text-#333">
      <div v-for="(item, index) of Data.extrance" :key="index"
        class="flex flex-col justify-center items-center tracking-wide" @click.stop="toPage(item)">
        <img loading="lazy" :src="item.src" alt="" class="w-80px h-80px mb-8px" />
        {{ item.name }}
      </div>
    </div>
    <div class="p-20px pt-0">
      <div class="relative">
        <van-swipe class="my-swipe " :autoplay="3000" indicator-color="white" v-if="Data.speicalList?.length">
          <van-swipe-item v-for="(item, index) of Data.speicalList" class="w-full h-240px rounded-15px"
            @click="toSubject(item)">
            <img loading="lazy" :src="item.appBannerPath ? useStore.getPrefix + item.appBannerPath : bannerImg" alt=""
              class="w-full h-full rounded-15px" />
          </van-swipe-item>

        </van-swipe>
        <img loading="lazy" :src="bannerImg" alt="" v-else class="w-full h-240px rounded-15px">
        <img loading="lazy" src="@/assets/home/<USER>" alt="" class="w-15% absolute top-0 right-0 z-1000"
          @click="toPage({ path: '/subject' })">
      </div>

      <div class="flex justify-between h-300px w-full mt-15px">
        <div class="w-34% h-full">
          <div class="w-full h-full relative" @click="toPage({ path: '/integralTree' })">
            <img loading="lazy" :src="Data.isRecive ? change : recive" alt="" class="w-full h-full" />
            <img loading="lazy" src="@/assets/home/<USER>" alt="" v-if="!Data.isRecive"
              class="w-60px absolute top-46% left-19% annimation" />
            <img loading="lazy" src="@/assets/home/<USER>" alt="" v-if="!Data.isRecive"
              class="w-60px absolute top-55% left-29% annimation" />
          </div>
        </div>
        <div class="w-64% h-full video-bg p-20px box-border">
          <Title :titleImg="'video.png'" :icon="'4.png'" :title="'video'" @lookMore="lookMore"></Title>
          <div class="h-90% w-full pt-15px pb-5px box-border">
            <Swiper :modules="modules" v-if="Data.swiperList.length" :loop="true" :slides-per-view="3"
              :initial-slide="1" :centered-slides="true" @swiper="onSwiper" ref="videoSwiper" key="videoSwiper"
              :autoplay="{ delay: 2500, disableOnInteraction: false }" class="w-full h-full">
              <swiper-slide v-for="(item, index) in Data.swiperList" :key="index" class="swiper-slide"
                @click="toVideo(item, index)">
                <div class="h-full relative">
                  <div class="w-139px h-100% bg-cover bg-center slideImg rounded-10px">
                    <img loading="lazy" :src="utils.judgeStaticUrl(item.pictures || item.cover)"
                      class="rounded-10px w-full h-full object-cover">
                  </div>
                  <img loading="lazy" src="@/assets/public/icon_play.png" alt=""
                    class="w-42px h-42px absolute left-1/2 top-[50%] -translate-1/2" />
                </div>
              </swiper-slide>
            </Swiper>
          </div>
        </div>
      </div>
      <div class="py-20px pb-200px">
        <div class="tabs h-78px flex items-center justify-center sticky top-0 z-99 bg-#fff">
          <div class="w-85% h-full"
            :class="Data.activetab === 0 ? 'start-tab' : Data.activetab === Data.tabs.length - 1 ? 'end-tab' : 'mid-tab'">
            <van-sticky>
              <van-tabs v-model:active="Data.activetab" background="transparent" title-inactive-color="#666666"
                title-active-color="#333333" @change="onClickTab" :swipe-threshold="Data.swipeThreshold">
                <van-tab class="flex-1" v-for="(item, index) in Data.tabs" :id="item.categoryId"
                  :title="item.categoryName" :key="index">
                </van-tab>
              </van-tabs>
            </van-sticky>

          </div>
          <div class="w-15% flex justify-center items-center" @click="toPage({ path: '/searchList' })">
            <div
              class="border border-1px border-solid border-#5AA4FF w-77px h-40px rounded-20px flex items-center justify-center mt-10px">
              <img loading="lazy" src="@/assets/public/icon_s.png" alt="" class="w-20px h-20px mr-5px" />
              <span class="text-20px text-#999">搜</span>
            </div>
          </div>
        </div>

        <div class="min-h-30vh">
          <van-tabs class="tabs-children" v-model:active="Data.tabsActive" :line-height="0" :line-width="0"
            @change="onClickChildren" v-if="Data.tabs[Data.activetab]?.hasChildren">
            <van-tab :title="item.categoryName" v-for="(item, index) in Data.tabsChildren">
              <template #title>
                <div
                  class="bg-#FFFFFF border-1px border-#E5E5E5 border-solid px-22px py-5px rounded-50px text-#9B9B9B min-w-160px text-center"
                  :class="{
                    '!bg-#EEF6FF !text-#5AA4FF !border-1px  !border-solid  !border-#EEF6FF': Data.tabsActive == index,
                  }">
                  {{ item.categoryName }}
                </div>
              </template>
            </van-tab>
          </van-tabs>
          <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
            <newsCell v-for="(item, index) in Data.list" :key="index" :content="item"></newsCell>
          </refreshList>
        </div>
      </div>
    </div>
    <div>
      <PopupLeft :showPop="Data.show" @closePopup="closePopup" :data="Data.moreData" />
      <PopupTotal :showTotalPop="Data.showTotalPop" :data="Data.tradeList" @closeTotal="closeTotalPop()" />
    </div>
    <!-- 首页推送 -->
    <launchPopup :isShow="Data.isShow" :imgList="Data.imgList" @closeLaunch="closeLaunch" v-if="Data.isShow" />
    <!-- 一岁一礼生日弹窗 -->
    <yearGiftPopup v-model:show="yearGiftShow" :content="yearContent" v-if="yearGiftShow"></yearGiftPopup>
    <!-- 加载页 -->
    <!-- <div class="loading_bg fixed left-0 right-0 top-0 bottom-0 z-10000" v-if="showLoading"> -->
    <!-- <div class="relative h-full w-full z-1"> -->
    <!-- <div class="top absolute w-100% top-0 z-2"></div> -->
    <!-- <img loading="lazy" :src="loadingBg" class="w-full loading_gif block relative z-1" /> -->
    <!-- <div class="bottom absolute w-100% bottom-0 z-2"></div> -->

    <!-- <img loading="lazy" :src="loadingBg" class="w-full loading_gif block relative z-1" /> -->
    <!-- </div> -->
    <!-- </div> -->
  </div>
</template>
<script lang="ts" setup>
// import loadingBg from '@/assets/public/page_loading.gif'
// import loadingBg from '@/assets/public/loading_bg1.jpg'
import { judgeSendBirthCard, getUserBirthdayCard } from "@/api/yearGift.ts";
import yearGiftPopup from "@/components/yearGift/popup.vue";
import { getSubordinateColumn, getNewsList,h5SpecialList,encryptUserInfo } from '@/api/news/index';
import { showConfirmDialog  } from "vant";
import utils from '@/utils/utils';
import bannerImg from '@/assets/home/<USER>/subject.jpg'
import Title from '@/components/Title/index.vue';
import { useUserStore } from '@/store/modules/user';
import sign from '@/assets/home/<USER>';
import single from '@/assets/home/<USER>';
import interest from '@/assets/home/<USER>';
import more from '@/assets/home/<USER>';
import low from '@/assets/home/<USER>';
import scan from '@/assets/home/<USER>';
import video from '@/assets/home/<USER>';
import gj from '@/assets/home/<USER>';
import hl from '@/assets/home/<USER>';
import help from '@/assets/home/<USER>';
import xt from '@/assets/home/<USER>';
import zd from '@/assets/home/<USER>';
import js from '@/assets/home/<USER>';
import gy from '@/assets/home/<USER>';
import nc from '@/assets/home/<USER>';
import yz from '@/assets/home/<USER>';
import zgsw from '@/assets/home/<USER>';
import change from '@/assets/home/<USER>';
import recive from '@/assets/home/<USER>';
import banner from '@/assets/home/<USER>';
import banner1 from '@/assets/interest/banner.png';
import special from '@/assets/home/<USER>';
import turn from '@/assets/home/<USER>';
import phIcon from "@/assets/home/<USER>";
import ghIcon from "@/assets/home/<USER>";
// 异步组件加载 在需要的时候再加载
const PopupLeft = defineAsyncComponent(() => import('@/components/Popup/popupLeft.vue'));
const PopupTotal = defineAsyncComponent(() => import('@/components/Popup/popupTotal.vue'))

import newsCell from '@/components/Cell/newsCell.vue';
import refreshList from '@/components/refreshList/index.vue';
import launchPopup from '@/components/Popup/launchPopup.vue';
import { showToast } from 'vant';
import Empty from '@/components/Empty/index.vue';
import { getShortPublicVideos } from '@/api/video/index';;
import {currentUnionUserCount,queryCurrentUserLaunchConfig } from '@/api/public'
import { queryMyDetail } from "@/api/friendship/index"//判断单身联谊是否认证
const router = useRouter();
import { useVideoStore } from '@/store/modules/video';
const videoStore = useVideoStore();
import { Swiper, SwiperSlide } from 'swiper/vue';
//用到哪些功能自行添加
import { Scrollbar, A11y,EffectCoverflow,Autoplay } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/scrollbar';
const useStore = useUserStore();
import iconRz from "@/assets/home/<USER>";
import iconZc from "@/assets/home/<USER>";
import iconHy from "@/assets/home/<USER>";
import iconGb from "@/assets/home/<USER>";
import iconZz from "@/assets/home/<USER>";
import ys from "@/assets/home/<USER>";
import { personalTreeInfo,treeAcDetail } from '@/api/integralTree';

// 字典
import { useDictionary } from '@/store/modules/dictionary';
const dictionary = useDictionary();
// end


const modules= [Scrollbar, A11y,EffectCoverflow,Autoplay];
defineOptions({
  name:'home'
})
// const showLoading = computed(() => !useStore?.hasShowLoading)

const Data = ref({
  isShow:false,
  isRecive: false,
  nav: [
    { name: '积分签到', src: sign, path: '/integralMall' },
    { name: '单身联谊', src: single,path: '/friendship/activity' },
    { name: '数字阵地', src: zd, path: '/position' },
    { name: '兴趣社区', src: interest, path: '/interest' },
    // { name: '更多服务', src: more, id: 'more' },
    { name: '活力南充', src: hl, path: '/vitality' },
    { name: '南充工匠', src: gj, path: '/workerCraftsman' },
    { name: '视频专区', src: video,path:'/video' },
    { name: '困难帮扶', src: help,record:{"MenusName":"困难帮扶","name":"困难帮扶","MenusPath":"widget://html/service/difficulty/difficulty.html","HarmonyPath":"/pagesServer/peasant/help","MenusIfrmaeName":"difficulty","MenusSans":1,"UserInfo":null,"shar_integral":null,} },
    { name: '职工学院', src: xt, path: '/digitalSchool' },
    { name: '法律援助', src: low,record:{"MenusName":"法律援助","name":"法律维权","MenusPath":"widget://html/service/statute/statute.html","HarmonyPath":"/pagesServer/legaAid/legaAid","MenusIfrmaeName":"statute","MenusSans":1,"UserInfo":null,"shar_integral":null,} },
    { name: '扫码入会', src: scan, path: '/joinHistory' },
    { name: '我要转会', src: turn, toastText:'请到省级服务大厅进行转会'},
    { name: '职工书屋', src: zgsw,record:{"MenusName":"职工书屋","name":"职工书屋","MenusPath":"https://open.dzzgsw.com/weixin/index.html","HarmonyPath":"https://open.dzzgsw.com/weixin/index.html","MenusIfrmaeName":"职工书屋","MenusSans":0,"UserInfo":null,"shar_integral":null,} },
    { name: '工友之声', src: gy, path: '/workersVoice' },
    { name: '南充竞赛', src: nc, path: '/competition' },
    { name: '趣味竞赛', src: js, path: '/competition/fun' },
    { name: '心灵驿站', src: yz, path: '/soulStation' },
    { name: '一岁一礼', src: ys, path: '/yearGift' },
  ],
  extrance: [
    { name: '积分签到', src: sign, path: '/integralMall' },
    { name:'普惠服务',src:phIcon,path:'/inclusive'},
    { name:'工会活动',src:ghIcon,path:'/activity'},
    { name:'兴趣社区',src:interest,path:'/interest'},
    { name: '更多服务', src: more, id: 'more' },
  ],
  swipeThreshold:5,//scroll-threshold 滑动阈值

  activetab: 2,
  tabsActive: 0,
  moreData: [],
  speicalList: [],
  show: false,
  tabs: [],
  list: [],
  tabsChildren:[],
  swiperList: [],
  pageNum: 1,
  tradeList:[ {
          titName: '认证用户',
          allNumb: '--',
          src: iconRz,
        },
        {
          titName: '工会组织',
          allNumb: '--',
          src: iconZz,
        },
        {
          titName: '工会会员',
          allNumb: '--',
          src:iconHy
        },
        {
          titName: '工会干部',
          allNumb: '--',
          src: iconGb,
        },
        {
          titName: '注册用户',
          allNumb: '--',
          src:iconZc,
        }, {
          titName: '活跃用户',
          allNumb: '--',
          src:iconZc,
        },],
        showTotalPop:false,
        imgList:[]
});
const popBoxSty =ref(null)
function getUserCount() {
  currentUnionUserCount({}).then(res=>{
    if(res.code==200){
      Data.value.tradeList[0].allNumb=res.data.checkedUserCount||'--';
      Data.value.tradeList[4].allNumb=res.data.registerCount||'--';
      Data.value.tradeList[1].allNumb=res.data.unionCount||'--';
      Data.value.tradeList[3].allNumb=res.data.cadreCount||'--';
      Data.value.tradeList[2].allNumb=res.data.userCount||'--';
      Data.value.tradeList[5].allNumb=res.data.activetyCount||'--';
    }
  })
}
//显示数据统计
function toShowTotal() {
  Data.value.showTotalPop=true;
}
// 视频siwper
const videoSiwper = ref(null);
const onSwiper = swiper => {
  videoSiwper.value = swiper;
};


//视频列表
async function getVideos() {
  let res = await getShortPublicVideos({
      pageNum:1,
      pageSize:5,
  });
  Data.value.swiperList=res.data;
}
function toVideo(item,index) {
  let userId = useStore.getUserInfo?.userId;
  let entertype=1;
  videoStore.setVideoList(Data.value.swiperList, entertype, userId);
  router.push({
    path: '/video-playback',
    query: { entertype, userId, listnumber:index },
  });
}
//专题列表
async function getSpecicalLists() {
  let res = await h5SpecialList({
        platformType: 30,
        pageNum:1,
        pageSize:8,
        whetherInquireNews:false,
  });
  Data.value.speicalList=res.data;
}
//跳转专题
function toSubject(item) {
  // "specialType": "custom", //专题类型(自定义专题--custom,系统默认专题--systemDefault)
  if (item.specialType == "custom") {
        if (item.pageType == "defaultTemplate") {
            router.push({
                path: "/subjectList",
                query: {
                    specialId: item.specialId,
                },
            });
            return;
        }else if (item.pageType == "customSinglePage") {
            //跳转方式:builtIn-->> 内置 external-->> 外部,内置项目中打开,外部新开窗口打开
            if (item.skipType == "builtIn") {
                router.push({
                    path: item.appPageLink,
                });
            } else {
                if(item.whetherPrompt == "y" ){ 
                    showConfirmDialog ({
                        title: "",
                        message: "是否跳转外部链接",
                    }).then(() => {
                        getUserInfo(item);
                    }).catch((res)=>{});
                }
                else {
                    getUserInfo(item);
                }

            }
        }
    }
}
//用户信息加密
function getUserInfo(item) {
    console.log('用户信息加密');
    //是否加密
    if(item.whetherEncryption){
        //是否过期
        if(item.whetherExpire){
            showToast(("授权已过期！"));
        }else {
            getEncryption(item);//获取登录返回的用户信息
        }
    }else {
        if(utils.isApp()){
          utils.citySercive(item.appPageLink,item.specialName,'open',item.specialId)
        }else {
            window.location.href = item.appPageLink;
        }

    }
}
function getEncryption(item) {
    if(!useStore.getUserInfo?.userId){
        showToast("请先登录！")
    }else{
        encryptUserInfo({
            recordId: item.specialId,
            userInfo: useStore.getUserInfo?.userId,
        }).then(res=>{
            if (res.code == 200 && res.data) {
                let url = item.appPageLink; //返回的url
                url += item.appPageLink.indexOf("?") == -1 ? "?" : "&"; //判断是否有问号
                if(utils.isApp()){
                    utils.citySercive(url + "token=" + res.data,item.specialName,'open',item.specialId)
                }else{
                    window.location.href=url + "token=" + res.data
                }
            } else {
                if(utils.isApp()){
                    utils.citySercive(item.appPageLink,item.specialName,'open',item.specialId)
                }else{
                    window.location.href=item.appPageLink
                }
                
            }
        })
    }
}
//页面跳转
async function toPage(item) {
  if(item.name === '单身联谊'){
    const res = await queryMyDetail()
    if(res.data) {
      return router.push({
        path:'/friendship',
      })
    }else{
      return router.push({
        path:'/friendship/activity',
      })
    }
    return
  }

  if (item.id == 'more') {
    Data.value.show = true;
    Data.value.moreData = Data.value.nav.filter(item => item.name !== '更多服务');
    return;
  }
  if (item.path) {
    router.push(item.path);
    Data.value.show=false;
  }
}
//查看更多
function lookMore() {
  router.push({
    path:'/video'
  })
}
function onClickChildren(val) {
  Data.value.tabsActive = val;
  Data.value.pageNum = 1;
  if (loadMoreRef.value) loadMoreRef.value.resetStatus();
    onRefreshList();
}
function onClickTab(val) {
  Data.value.activetab = val;
  Data.value.pageNum = 1;
  Data.value.tabsActive = 0;
  if(Data.value.tabs[Data.value.activetab]?.hasChildren){
    getSubordinateColumn({
      categoryCode: Data.value.tabs[Data.value.activetab]?.categoryCode,
      platformType: 30,
    }).then(res => {
      Data.value.tabsChildren = res.data;
      getLists();
    });
  }else{
    if (loadMoreRef.value) loadMoreRef.value.resetStatus();
    onRefreshList();
  }
  
}
//关闭弹窗
function closePopup(val) {
  Data.value.show = val;
}
function closeTotalPop() {
  Data.value.showTotalPop = false
}
//获取栏目
async function getColumn() {
  getSubordinateColumn({
    categoryCode: 'pin_dao_shou_ye',
    platformType: 30,
  }).then(async res => {
    Data.value.tabs = res.data;
    if(Data.value.activetab != 0){
        getSubordinateColumn({
        categoryCode: Data.value.tabs[Data.value.activetab]?.categoryCode,
        platformType: 30,
      }).then(async res1 => {
        Data.value.tabsChildren = res1.data;
        await getLists();
        nextTick(() => {
          Data.value.swipeThreshold = 3;
        })
      });
    }else{
      await getLists();
      nextTick(() => {
        Data.value.swipeThreshold = 3;
      })
    }
  });
}
//获取新闻列表
const loadMoreRef = ref(null);
// 刷新
const onRefreshList = () => {
  Data.value.pageNum = 1;
  getLists();
};
// 加载更多
const onLoadMore = () => {
  Data.value.pageNum++;
  getLists();
};
async function getLists() {
  let res = await getNewsList({
    categoryCode:Data.value.tabs[Data.value.activetab]?.hasChildren? Data.value.tabsChildren[Data.value.tabsActive]?.categoryCode: Data.value.tabs[Data.value.activetab]?.categoryCode,
    platformType: 30,
    pageNum: Data.value.pageNum,
    pageSize: 5,
  });
  if (Data.value.pageNum === 1) Data.value.list = [];
  Data.value.list = Data.value.list.concat(res.data);
  //重置刷新状态及 判断是否加载完成
  if (loadMoreRef.value) {
    loadMoreRef.value.onLoadSuc(Data.value.list.length, res.total);
  }
}
// 活动内容
const getActivtiDetail = async () => {
    const { code, data } = await treeAcDetail()
    // 活动存在并且用户信息存在
    if (code === 200 && data && useStore.getUserInfo?.userId) {
        getPersonalTreeInfo(data?.activityId)
    }
}

// 活动内容请求
const getPersonalTreeInfo = async (id) => {
    const { code, data } = await personalTreeInfo(id)
    if (code === 200) {
      if(data){
        Data.value.isRecive = true;
      }else{
        Data.value.isRecive = false;
      }
        
    }
}
//获取首页配置
function getLaunch() {
  queryCurrentUserLaunchConfig({firstIntoHome:'y',companyIdH5:'6650f8e054af46e7a415be50597a99d5'}).then(res=>{
    if(res.code==200){
      if(res.data?.length){
        Data.value.isShow=true;
        Data.value.imgList=res.data;
      }
     
    }
  })
}
function closeLaunch(show) {
  Data.value.isShow=false;
}
const yearGiftShow = ref(false);
const yearContent = ref({})
const getjudgeBirthCard = async () => {
  const { data, code } = await judgeSendBirthCard()
  if (code === 200 && data.hasCard) {
    getUserBirthdayCard(data.autoId).then((res: any) => {
      yearContent.value = res.data
      // 卡片详情
      yearGiftShow.value = true
    })
  }else{
    getLaunch()
  }
}
onMounted( () => {
  // 延迟请求
  setTimeout(() => {
    getjudgeBirthCard();
    getUserCount();
    getActivtiDetail();
    utils.getPosition('home',(code,content) =>{
      if(code==200){
        useStore.setLocationInfo(JSON.parse(content));
      }
    })
  }, 1000);


  // if(showLoading.value){
  //   setTimeout(() => {
  //     useStore.setLoadingPageShow(true)
  //   },1000)      
  // }
});
onBeforeMount(async () => {
  getColumn();
  getSpecicalLists();
  getVideos()
})

// 局部刷新
const changePage = () => {
  // 解决轮播切换不自动播放
  if(videoSiwper.value?.autoplay.running){
    videoSiwper.value?.autoplay.resume()
  }else{
     videoSiwper.value?.autoplay.start()
  }
}

const route = useRoute()
watch(route,(to)=>{
  if(to.path ==='/home'){
    changePage();
  }else{
    videoSiwper.value?.autoplay.pause()
  }
})
</script>
<style scoped lang="scss">
// 更改tabs 样式
:deep(.van-tabs__nav--line.van-tabs__nav--complete) {
  padding-left: 0;
  padding-right: 0;
}

:deep(.van-tab--grow) {
  padding-left: 0
}

// end

.home-page {
  .nav-box {
    background: url('@/assets/home/<USER>') no-repeat;
    background-size: 100% 100%;
  }

  .video-bg {
    background: url('@/assets/home/<USER>') no-repeat;
    background-size: 100% 100%;
  }

  .tabs {
    background-image: url('@/assets/home/<USER>');
    background-size: 100% 100%;
    background-position: top center;
    background-repeat: no-repeat;

    :deep(.van-tabs) {
      height: 78px;
      padding: 0 !important;
      border: none;
    }

    :deep(.van-tabs--line .van-tabs__wrap) {
      height: 100%;
    }

    :deep(.van-tab) {
      height: 100%;
      padding-bottom: 0;
    }

    :deep(.van-tabs__nav--line) {
      padding: 0;
    }

    :deep(.van-tabs__line) {
      width: 0px;
      height: 0px;
      display: none;
    }

    :deep(.van-tab__text) {
      font-size: 32px;
      margin-top: 10px;
    }

    :deep(.van-tab--active) {
      .van-tab__text {
        font-size: 32px !important;
        color: #3A98FA;
      }

      background-size: 100% auto;
      background-position:center bottom;
      background-repeat: no-repeat;
    }

    .start-tab {
      :deep(.van-tab--active) {
        width: 216px;
        background-image: url('@/assets/home/<USER>');
      }
    }

    .mid-tab {
      padding-left: 12px;

      :deep(.van-tab--active) {
        width: 240px;
        background-image: url('@/assets/home/<USER>');
        padding-left: 12px;
      }
    }

    .end-tab {
      padding-left: 12px;

      :deep(.van-tab--active) {
        width: 216px;
        background-image: url('@/assets/home/<USER>');
        padding-left: 50px;
      }
    }
  }

  .tabs-children {
    :deep(.van-tab) {
      justify-content: flex-start;
      flex: none;
      margin-right: 10px;
    }

    :deep(.van-tabs__line) {
      width: 0px;
      height: 0px;
      display: none;
    }
  }

  .swiper {
    width: 100%;
    height: 100%;

    .swiper-slide {
      display: flex;
      justify-content: center;
      align-items: center;
      transition: 300ms;
      transform: scale(0.9);
      filter: blur(1px);
      opacity: 0.9;
    }

    .swiper-slide-active,
    .swiper-slide-duplicate-active {
      transform: scale(1);
      z-index: 999;
      filter: blur(0px);
      opacity: 1;

      .slideImg {
        width: 160px;
      }
    }
  }

  /* 定义放大缩小动画 */
  @keyframes zoomInOut {

    0%,
    100% {
      transform: scale(1);
    }

    50% {
      transform: scale(1.2);
      /* 放大1.5倍 */
    }
  }

  .total_pop {
    min-width: 150px;
    height: 67px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 32px 0px 0px 32px;
    position: absolute;
    right: 0;
    top: 45px;
    font-size: 25px;
    color: #418CE9;
    padding: 0 12px;
    padding-left: 10px;

    .notic_swip {
      height: 100%;
    }

    :deep(.van-swipe-item) {
      >div {
        height: 100%;
      }

    }

    .total_text {
      overflow: hidden;
      padding-left: 10px;
    }

    .infoNum {
      font-size: 26px;
      color: #5AA4FF;
      text-align: center;
    }
  }

  /* 应用动画到元素 */
  .annimation {
    animation: zoomInOut 1.8s infinite;
    /* 动画名称，每次动画持续2秒，并且无限循环 */
  }

  :deep(.van-swipe__indicator) {
    width: 14px;
    height: 4px;
    background: #f5f5f5;
  }

  @keyframes move_wave {
    0% {
      transform: translateX(0) translateZ(0) scaleY(1);
    }

    50% {
      transform: translateX(-25%) translateZ(0) scaleY(0.55);
    }

    100% {
      transform: translateX(-50%) translateZ(0) scaleY(1);
    }
  }

  @keyframes move-wave {
    0% {
      transform: translateX(0) translateZ(0) scaleY(1) rotateY(180deg);
    }

    50% {
      transform: translateX(-25%) translateZ(0) scaleY(0.55) rotateY(180deg);
    }

    100% {
      transform: translateX(-50%) translateZ(0) scaleY(1) rotateY(180deg);
    }
  }

  .waveWrapper {
    overflow: hidden;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    margin: auto;
  }

  .wave {
    position: absolute;
    left: 0;
    width: 200%;
    height: 100%;
    background-repeat: repeat no-repeat;
    background-position: 0 bottom;
    transform-origin: center bottom;
  }

  .waveTop {
    background: url('@/assets/home/<USER>') center bottom no-repeat;
    // background-size: 100% 30%;
    // z-index: 4;
    transform: rotateY(180deg);
  }

  .waveAnimation .waveTop {
    animation: move-wave 10s linear infinite;
    -webkit-animation: move-wave 10s linear infinite;
    -webkit-animation-delay: 1s;
    animation-delay: 1s;
  }

  .waveMiddle_one {
    background: url('@/assets/home/<USER>') center bottom no-repeat;
    // background-size: 100% 25%;
    // z-index: 2;
  }

  .waveMiddle_two {
    width: 250%;
    background: url('@/assets/home/<USER>') center bottom no-repeat;
    background-size: 100% 30%;
    // z-index: 3;
  }

  .waveAnimation .waveMiddle_one {
    animation: move_wave 8s linear infinite;
  }

  .waveAnimation .waveMiddle_two {
    animation: move_wave 12s linear infinite;
  }

  .waveBottom {
    width: 200%;
    background: url('@/assets/home/<USER>') bottom bottom no-repeat;
    background-size: 100% 18%;
    // z-index: 1;
  }

  .waveAnimation .waveBottom {
    animation: move_wave 12s linear infinite;
  }
}

.loading_bg {
  background: linear-gradient(to bottom, #f3f2fa, #f3fafa);
  height: 100%;

  .top {
    height: 50%;
    background: #f3fafa;
    animation: topLoadingAnimation 2s linear forwards;
  }

  .bottom {
    height: 50%;
    background: #f3fafa;
    animation: topLoadingAnimation 2s linear forwards;
  }

  .loading_gif {
    // opacity: 0;
    // animation-delay: 1s;
    animation: imgLoadingAnimation .5s linear forwards;
  }
}

// 图片扭动
@keyframes imgLoadingAnimation {
  0% {
    opacity: 0;

  }

  100% {
    opacity: 1;
  }
}

// 百叶窗动画效果

@keyframes topLoadingAnimation {
  0% {
    height: 50%;
    background-size: 100% 14%;
    background-repeat: repeat-y;
    background: linear-gradient(0deg, #f3fafa 14%,
        transparent 14%, transparent 15%, #f3fafa 15%,
        #f3fafa 29%, transparent 29%, transparent 30%,
        #f3fafa 30%, #f3fafa 44%, transparent 44%, transparent 45%,
        #f3fafa 45%, #f3fafa 59%, transparent 59%,
        transparent 60%, #f3fafa 60%,
        #f3fafa 74%, transparent 74%, transparent 75%,
        #f3fafa 75%, #f3fafa 89%, transparent 89%, transparent 90%,
        #f3fafa 90%, #f3fafa 100%);
  }

  10% {
    background: linear-gradient(0deg, #f3fafa 14%,
        transparent 14%, transparent 15%, #f3fafa 15%,
        #f3fafa 29%, transparent 29%, transparent 30%,
        #f3fafa 30%, #f3fafa 44%, transparent 44%, transparent 45%,
        #f3fafa 45%, #f3fafa 59%, transparent 59%,
        transparent 60%, #f3fafa 60%,
        #f3fafa 74%, transparent 74%, transparent 75%,
        #f3fafa 75%, #f3fafa 89%, transparent 89%, transparent 90%,
        #f3fafa 90%, #f3fafa 100%);
  }

  20% {
    background: linear-gradient(0deg, #f3fafa 12%,
        transparent 12%, transparent 15%, #f3fafa 15%,
        #f3fafa 27%, transparent 27%, transparent 30%,
        #f3fafa 30%, #f3fafa 40%, transparent 40%, transparent 45%,
        #f3fafa 45%, #f3fafa 57%, transparent 57%,
        transparent 60%, #f3fafa 60%,
        #f3fafa 72%, transparent 72%, transparent 75%,
        #f3fafa 75%, #f3fafa 87%, transparent 87%, transparent 90%,
        #f3fafa 90%, #f3fafa 100%);
  }

  30% {
    background: linear-gradient(0deg, #f3fafa 10%,
        transparent 10%, transparent 15%, #f3fafa 15%,
        #f3fafa 25%, transparent 25%, transparent 30%,
        #f3fafa 30%, #f3fafa 38%, transparent 38%, transparent 45%,
        #f3fafa 45%, #f3fafa 55%, transparent 55%,
        transparent 60%, #f3fafa 60%,
        #f3fafa 70%, transparent 70%, transparent 75%,
        #f3fafa 75%, #f3fafa 85%, transparent 85%, transparent 90%,
        #f3fafa 90%, #f3fafa 100%);
  }

  40% {
    background: linear-gradient(0deg, #f3fafa 8%,
        transparent 8%, transparent 15%, #f3fafa 15%,
        #f3fafa 23%, transparent 23%, transparent 30%,
        #f3fafa 30%, #f3fafa 36%, transparent 36%, transparent 45%,
        #f3fafa 45%, #f3fafa 53%, transparent 53%,
        transparent 60%, #f3fafa 60%,
        #f3fafa 68%, transparent 68%, transparent 75%,
        #f3fafa 75%, #f3fafa 83%, transparent 83%, transparent 90%,
        #f3fafa 90%, #f3fafa 98%, transparent 98%, transparent 100%);
  }

  50% {
    background: linear-gradient(0deg, #f3fafa 6%,
        transparent 6%, transparent 15%, #f3fafa 15%,
        #f3fafa 21%, transparent 21%, transparent 30%,
        #f3fafa 30%, #f3fafa 34%, transparent 34%, transparent 45%,
        #f3fafa 45%, #f3fafa 51%, transparent 51%,
        transparent 60%, #f3fafa 60%,
        #f3fafa 66%, transparent 66%, transparent 75%,
        #f3fafa 75%, #f3fafa 81%, transparent 81%, transparent 90%,
        #f3fafa 90%, #f3fafa 96%, transparent 96%, transparent 100%);
  }

  60% {
    background: linear-gradient(0deg, #f3fafa 4%,
        transparent 4%, transparent 15%, #f3fafa 15%,
        #f3fafa 19%, transparent 19%, transparent 30%,
        #f3fafa 30%, #f3fafa 34%, transparent 34%, transparent 45%,
        #f3fafa 45%, #f3fafa 49%, transparent 49%,
        transparent 60%, #f3fafa 60%,
        #f3fafa 64%, transparent 64%, transparent 75%,
        #f3fafa 75%, #f3fafa 79%, transparent 79%, transparent 90%,
        #f3fafa 90%, #f3fafa 94%, transparent 94%, transparent 100%);
  }

  70% {
    background: linear-gradient(0deg, #f3fafa 3%,
        transparent 3%, transparent 15%, #f3fafa 15%,
        #f3fafa 18%, transparent 18%, transparent 30%,
        #f3fafa 30%, #f3fafa 33%, transparent 33%, transparent 45%,
        #f3fafa 45%, #f3fafa 48%, transparent 48%,
        transparent 60%, #f3fafa 60%,
        #f3fafa 63%, transparent 63%, transparent 75%,
        #f3fafa 75%, #f3fafa 78%, transparent 78%, transparent 90%,
        #f3fafa 90%, #f3fafa 93%, transparent 93%, transparent 100%);
  }

  80% {
    background: linear-gradient(0deg, #f3fafa 2%,
        transparent 2%, transparent 15%, #f3fafa 15%,
        #f3fafa 17%, transparent 17%, transparent 30%,
        #f3fafa 30%, #f3fafa 32%, transparent 32%, transparent 45%,
        #f3fafa 45%, #f3fafa 47%, transparent 47%,
        transparent 60%, #f3fafa 60%,
        #f3fafa 62%, transparent 62%, transparent 75%,
        #f3fafa 75%, #f3fafa 77%, transparent 77%, transparent 90%,
        #f3fafa 90%, #f3fafa 92%, transparent 92%, transparent 100%);
  }

  90% {
    background: linear-gradient(0deg, #f3fafa 1%,
        transparent 1%, transparent 15%, #f3fafa 15%,
        #f3fafa 16%, transparent 16%, transparent 30%,
        #f3fafa 30%, #f3fafa 31%, transparent 31%, transparent 45%,
        #f3fafa 45%, #f3fafa 46%, transparent 46%,
        transparent 60%, #f3fafa 60%,
        #f3fafa 61%, transparent 61%, transparent 75%,
        #f3fafa 75%, #f3fafa 76%, transparent 76%, transparent 90%,
        #f3fafa 90%, #f3fafa 91%, transparent 91%, transparent 100%);
  }

  100% {
    // background: linear-gradient(0deg, #f3fafa 0%, transparent 100%,)
    height: 0%;
  }
}
</style>
