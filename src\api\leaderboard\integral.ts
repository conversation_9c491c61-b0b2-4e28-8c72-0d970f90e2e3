// 积分数据统计接口
import { dataCenterHttp } from '@/utils/http/axios';

//积分统计
export function integralStatistics() {
  return dataCenterHttp.get({
    url:'/customIntegral/dataSummary',
  })
}
// 区域会员积分统计
export function integralAreaStatistics() {
  return dataCenterHttp.get({
    url:'/customIntegral/dataSummaryByAreaName',
  })
}

// 会员积分分段统计

export function integralSegmentStatistics() {
  return dataCenterHttp.get({
    url:'/customIntegral/integralSegmentationCount',
  })
}

// 近年半积分增长消耗统计
export function integralGrowthStatistics() {
  return dataCenterHttp.get({
    url:'/customIntegral/halfYearSummary',
  })
}
