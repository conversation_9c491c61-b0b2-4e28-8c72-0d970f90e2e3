<template>
    <div class="worker_detail px-30px pt-52px pb-84px box-border w-full">
        <div class="worker_content bg-[#fff] w-full min-h-full box-border rounded-[24px] px-35px py-44px">
            <div class="worker_title text-24px font-bold mb-30px w-357px h-76px mx-auto box-border relative">
                <img loading="lazy" src="@/assets/workerCraftsman/woker_intro_title.png" class="w-full" />
                <span class="absolute top-[50%] left-[50%] -translate-y-[50%] leading-none  
                -translate-x-[50%] whitespace-nowrap text-[#FFF957] text-32px">{{ route.query?.type ===
                    '0' ? '劳模简介' : '工匠简介' }}</span>
            </div>
            <div class="worker_info">
                <div class="worker_info_avator rounded-16px text-center">
                    <img loading="lazy" :src="details?.avatar" class="max-w-full">
                </div>
                <div class="worker_info_tag mt-39px text-center text-[#C91113] text-[32px]">
                    {{ details?.whenModelWorker ? details?.whenModelWorker + '-' : '' }}{{ details?.userName }}</div>
                <div class="worker_info_unit mt-19px text-center text-[#333333] text-[28px] font-bold"
                    v-if="details?.workUnitName">{{
                        details?.workUnitName }}
                </div>
                <!--  text-[#333333] text-[28px] indent-8 leading-6 text-justify -->
                <div class="worker_info_content rich_text mt-40px" v-html="details?.personalProfile">
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { getwokerCraftDetail } from "@/api/workerCraftsman";
import { judgeStaticUrl } from "@/utils/utils";
import { showFailToast } from "vant";
const route = useRoute();
//type 0劳模类型1工匠类型
onMounted(() => {
    getDetails();
});
const details = ref<any>({});
const getDetails = () => {
    if (!route.query.id) return showFailToast("获取详情失败");
    getwokerCraftDetail(route.query?.id).then((res: Recordable) => {
        details.value = res.data;
        details.value.avatar = judgeStaticUrl(details.value.avatar);
    });
}

</script>

<style scoped lang="scss">
.worker_detail {
    width: 100%;
    height: 100vh;
    overflow-y: scroll;
    background-image: url('@/assets/workerCraftsman/woker_introduce.jpg');
    background-size: cover;
    background-position: bottom center;
    background-repeat: no-repeat;
}
</style>