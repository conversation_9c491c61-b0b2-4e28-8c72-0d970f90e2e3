<template>
    <div>
        <van-popup :show="showPop" class="flex items-center justify-center" position="center"
            :style="{ width: '85vw', height: '70vh' }">
            <div class="w-[calc(100%-20px)] h-[calc(100%-20px)] p26px bg-#fff rounded-30px flex flex-col">
                <div class="flex-1 overflow-scroll">
                    <div class="bg-[#eaf6ff] text-24px text-#3f95ff rounded-20px p20px leading-35px">
                        单身联谊是南充市总工会专门为全市单身职工搭建的一个婚恋交友平台。请各入驻会员本着诚信友善原则，共同维护好平台。若发现存在不当言行，市总工会将与入驻会员所在单位工会进行对接、反映情况。
                    </div>
                    <div class="mt60px flex flex-col items-center">
                        <div class="w-184px h-46px rounded-24px flex items-center justify-center
                            text-#fff bg-#3f95ff mb44px leading-none text-28px">
                            单身告知书
                        </div>
                        <div class="px10px text-#333 text-26px leading-38px text-justify">
                            {{ notice }}
                        </div>
                    </div>
                    <div class="mt60px flex flex-col items-center">
                        <div class="w-184px h-46px rounded-24px flex items-center justify-center 
                            text-#fff bg-#3f95ff mb44px leading-none text-28px">
                            单身承诺书
                        </div>
                        <div class="px10px text-#333 text-26px leading-38px text-justify">
                            {{ letter }}
                        </div>
                    </div>
                </div>

                <div class="confirm_btn flex items-center justify-center 
                my-30px mx-auto text-32px leading-none" @click="close">
                    我已阅读并同意签订
                </div>
            </div>
        </van-popup>
    </div>

</template>
<script lang="ts" setup>
import { getAgreement } from '@/api/friendship/profile'
const emit = defineEmits(['closePopup']);
const letter = ref('')
const notice = ref('')
onMounted(() => {
    getAgreement().then((res: any) => {
        letter.value = res.data?.letter
        notice.value = res.data?.notice
    })
})
const show = ref(false)
const props = defineProps({
    showPop: {
        type: Boolean,
        default: false
    },
})
function close() {
    show.value = false;
    emit('closePopup', false)
}

</script>
<style lang="scss" scoped>
.van-overlay {
    background: rgba(0, 0, 0, .4);
}

.van-popup {
    border-radius: 40px;
    background-image: linear-gradient(120deg, #b0deff 0%, #D3D2F8 100%);

    .confirm_btn {
        width: 431px;
        height: 83px;
        background: linear-gradient(0deg, #3F95FF 0%, #93CFFD 100%);
        border-radius: 41px;
        font-weight: 500;
        font-size: 32px;
        color: #FFFFFF;
        text-shadow: 0px 1px 4px rgba(67, 152, 255, 0.61);
    }
}
</style>