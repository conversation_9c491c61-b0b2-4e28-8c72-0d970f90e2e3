<template>
  <div class="group-info-page bg-[#f6f7f8] min-h-[100vh] pb-[84px] box-border">

    <div class=" px-[29px] py-[44px] box-border">

      <div class="bg-[#fff] rounded-[20px] px-[21px] pt-[39px] pb-[1px] box-border">
        <div v-for="(item, index) in Data.infoList" :key="index">
          <div class="flex items-center justify-between text-[28px] mb-[49px]" v-if="item.type === 'text'">
            <div class="text-[#666]">{{ item.name }}</div>
            <div class="text-[#333]">{{ item.value }}</div>
          </div>
          <div class="flex flex-col text-[28px]" v-else-if="item.type === 'textarea'">
            <div class="text-[#666]">{{ item.name }}</div>
            <div class="text-[#333] bg-[#F6F7F8] p-[28px] box-border mt-[35px] mb-[25px] rounded-[10px]">{{ item.value
            }}
            </div>
          </div>
          <div class="flex flex-col text-[28px]" v-else-if="item.type === 'img'">
            <div class="text-[#666]">{{ item.name }}</div>
            <div class="flex items-center mb-[25px] flex-wrap">
              <img loading="lazy" :src="e.src" v-for="e, i in item.value" :key="i"
                class="w-[160px] h-[160px] rounded-[4px] mr-[20px] mt-[25px]" alt="">
            </div>
          </div>
        </div>
      </div>
      <div class="bg-[#fff] rounded-[20px] px-[21px] pt-[26px] pb-[1px] box-border mt-[20px]">
        <div v-for="(item, index) in Data.applyInfoList" :key="index">
          <div class="flex items-center  text-[28px] mb-[25px]" v-if="item.type === 'text'">
            <div class="text-[#666]">{{ item.name }}</div>
            <div class="text-[#333] pl-12px">
              {{ item.value == 'wait' ? '待审核' : item.value == 'pass' ? '审核通过' : item.value == 'refuse' ? '审核不通过' :
                item.value ? item.value : '--' }}
            </div>
          </div>
          <div class="flex flex-col text-[28px]" v-else-if="item.type === 'textarea'">
            <div class="text-[#666]">{{ item.name }}</div>
            <div class="text-[#333] bg-[#F6F7F8] p-[28px] box-border mt-[35px] rounded-[10px]">{{ item.value }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="flex items-center justify-between w-[100%] px-[90px] box-border " v-if="route.query.isExamine === '1'">
      <van-button @click.stop="onRefuse(Data.infoList[0], 'refuse')" block
        class="w-[260px] h-[76px] rounded-[39px] justify-center flex items-center text-[30px] text-[#5AA4FF] border-[1px] border-[#5AA4FF] border-solid mr-[30px]">
        拒绝
      </van-button>
      <van-button block @click.stop="onPass(Data.infoList[0], 'pass')"
        class="w-[260px] h-[78px] rounded-[39px] justify-center flex items-center text-[30px] text-[#fff]"
        style="background: linear-gradient(0deg, #a1cbff 0%, #5aa4ff 100%)">
        通过
      </van-button>
    </div>
    <Popup :show="Data.showPop" :titleName="Data.titleName" :placeHolder="'请输入' + Data.titleName"
      @submit-content="submitContent" @close-popup="closePopup" />
  </div>
</template>
<script lang="ts" setup>
import { useRoute } from "vue-router";
import { getAuditDetails, groupAudit } from "@/api/interestGroup";
import { judgeStaticUrl } from "@/utils/utils";
import Popup from "@/components/Popup/popup.vue";
import { showToast } from "vant";
const route = useRoute();
const Data = ref({
  infoList: [
    { name: '小组名称', value: '', type: 'text' },
    { name: '成员数', value: '', type: 'text' },
    { name: '加入要求', value: '', type: 'textarea' },
    { name: '标签', value: '', type: 'textarea' },
    { name: '封面图片', value: [], type: 'img' }
  ],
  applyInfoList: [
    // 未通过和已通过  加对应审核参数即可
    { name: '申请时间', value: '', type: 'text' },
    { name: '审核状态', value: '', type: 'text' },
    { name: '审核意见', value: '', type: 'text' },
  ],
  showPop: false,
  titleName: '',
  objInfo: {},

});
//详情
function getGroupDetail() {
  getAuditDetails({
    autoId: route.query.autoId
  }).then(res => {
    if (res.code == 200) {
      Data.value.infoList[0].value = res.data.groupName;
      Data.value.infoList[1].value = res.data.memberMax;
      Data.value.infoList[2].value = res.data.requirement;
      Data.value.infoList[4].value = [{ src: judgeStaticUrl(res.data.logo) }];
      Data.value.infoList[3].value = res.data.labels.map((item: any) => item.labelName).join(',');
      Data.value.applyInfoList[0].value = res.data.createTime;
      Data.value.applyInfoList[1].value = res.data.state;
      Data.value.applyInfoList[2].value = res.data.auditOpinion;
    }
  })
}
/**
 * 点击拒绝  处理函数
 *
 * @param item 请求项，可以是任意类型的值
 */
function onRefuse(item: any, type) {
  Data.value.showPop = true;
  item.type = type;
  Data.value.titleName = '失败原因'
  Data.value.objInfo = item;

}
//通过
function onPass(item, type) {
  item.type = type;
  Data.value.objInfo = item;
  Data.value.titleName = '通过原因'
  Data.value.showPop = true;
}
//确认提交  拒绝
function submitContent(val: string) {
  // if (!val&&Data.value.objInfo.type=='refuse') {
  //   showToast({
  //     message: "请输入拒绝原因",
  //     icon: "none",
  //   });
  //   return;
  // }
  groupAudit({
    autoId: route.query.autoId,
    state: Data.value.objInfo.type,
    auditOpinion: val
  }).then(res => {
    if (res.code == 200) {
      showToast("审核成功");
      getGroupDetail()
      Data.value.showPop = false;
    }
  })

}
//关闭拒绝弹窗
function closePopup() {
  Data.value.showPop = false;
}
onMounted(() => {
  getGroupDetail()
})
</script>
<style lang="scss" scoped>
.group-info-page {}
</style>
