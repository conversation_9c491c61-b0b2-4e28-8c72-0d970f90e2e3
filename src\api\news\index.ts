
import { h5Http,fileHttp,cloudManageHttp } from '@/utils/http/axios';
import { BasicResponse } from '.';
//获取栏目
export const getSubordinateColumn = (params) => {
    return h5Http.get({
        url: '/h5CategoryInfo/getSubordinateColumn',
        params,
    });
};
//栏目下级新闻
export const getNewsList = (params) => {
    return h5Http.get({
        url: '/h5NewsInfo/getNewsList',
        params,
    });
};
//新闻详情
export const getNewsDetails = (params) => {
    return h5Http.get({
        url: '/h5NewsInfo/getNewsDetails',
        params,
    });
};
//预览新闻详情
export const previewGetNewsDetails = (params) => {
    return h5Http.get({
        url: '/h5NewsInfo/previewGetNewsDetails',
        params,
    });
};

//新闻推荐
export const getRecommendNewsList = (params) => {
    return h5Http.get({
        url: '/h5NewsInfo/getRecommendNewsList',
        params,
    });
};
//新闻推荐刷新
export const getRecommendNewsListByKeywords = (params) => {
    return h5Http.get({
        url: '/h5NewsInfo/getRecommendNewsListByKeywords',
        params,
    });
};
//获取评论
export const getUserCommentList = (params) => {
    return h5Http.get({
        url: '/H5UserComment/getUserCommentList',
        params,
    });
};
//新增评论
export const addUserComment = (params) => {
    return h5Http.post({
        url: '/H5UserComment',
        params,
    });
};
//获取本级栏目
export const getCategoryInfo = (params) => {
    return h5Http.get({
        url: '/h5CategoryInfo/getCategoryInfo',
        params,
    });
};
// 专题新闻列表
export const h5SpecialList = (params) => {
    return h5Http.get({
        url: '/h5SpecialInfo/h5findList',
        params,
    });
};
//根据专题业务id(specialId)获取专题信息+关联栏目
export const getCategoryInfoListBySpecialId = (params) => {
    return h5Http.get({
        url: '/h5SpecialInfo/getCategoryInfoListBySpecialId',
        params,
    });
};
//专题加密
export const encryptUserInfo = (params) => {
    return h5Http.post({
        url: '/encryptionCommon/encryptUserInfo',
        params,
    });
};
//区县新闻
export const h5CategoryInfo = (params) => {
    return h5Http.get({
        url: '/h5CategoryInfo/appGetCategoryInfoByCompanyId',
        params,
    });
};
//区县banner
export const findH5XQBannerList = (params) => {
    return cloudManageHttp.get({
        url: '/bannerPic/findH5XQBannerList',
        params,
    });
};
//订阅区县
export const saveSubscribeRecord = (params) => {
    return cloudManageHttp.post({
        url: '/bannerPic/saveSubscribeRecord',
        params,
    });
};
//取消订阅
export const deleteSubscribeRecord = (params) => {
    return cloudManageHttp.post({
        url: '/bannerPic/deleteSubscribeRecord',
        params,
    });
};
//搜索
export const homePageSearchGetNewsList = (params) => {
    return h5Http.get({
        url: '/h5NewsInfo/homePageSearchGetNewsList',
        params,
    });
};
//热门搜索排行
export const getPopularSearches = (params) => {
    return h5Http.get({
        url: '/homePageSearch/getPopularSearches',
        params,
    });
};
//热门搜索排行增加点击量
export const addPopularity = (params) => {
    return h5Http.post({
        url: '/homePageSearch/addPopularity',
        params,
    });
};

// 我的新闻收藏列表
export const getUserCollectionList = (params:any) => {
    return h5Http.get({
        url: '/h5NewsInfo/getCurrentCollectList',
        params,
    });
};
