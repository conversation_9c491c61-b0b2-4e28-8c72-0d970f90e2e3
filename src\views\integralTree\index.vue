<template>
    <!-- 积分树 -->
    <div class="integral-tree relative overflow-hidden flex flex-col z-100">
        <!-- 顶部 -->
        <div class="top flex items-end justify-between ml-1.5vh mr-3.2vh">
            <div class="my_integral_show h-6.1vh relative">
                <img loading="lazy" src="@/assets/tree/integral_bg.png" class="h-full">
                <div class="context absolute left-5.2vh top-0 bottom-1vh 
                bg-#dbf3ff flex flex-col justify-center pr-1vh" style="border-radius: 0 3vh 3vh 0">
                    <div class="text-[#445E2C] text-1.6vh">我的积分</div>
                    <div class="integral text-2vh text-[#3F9B0A] font-500 leading-none mt-0.2vh">
                        {{ userIntegral ? userIntegral : 0 }}
                    </div>
                </div>
            </div>
            <div class="right_nav flex">
                <!-- 积分商城 -->
                <div class="mall h-7.2vh" @click="pageTo('/integralMall')">
                    <img loading="lazy" src="@/assets/tree/mall_icon.png" class="h-full" />
                </div>
                <!-- 积分规则 -->
                <div class="rule h-7.2vh ml-1.4vh" @click="pageTo('/integralTree/rule')">
                    <img loading="lazy" src="@/assets/tree/exchange_rule.png" class="h-full" />
                </div>
            </div>
        </div>
        <!-- 风车 -->
        <div class="windmill1 absolute z-1 top-20% -left-4% flex items-center flex-col">
            <img loading="lazy" src="@/assets/tree/fan.png" class="w-138px relative z-2 fan-animation-delay">
            <img loading="lazy" src="@/assets/tree/column.png" class="w-16px relative z-1 -mt-72px ml-5px">
        </div>
        <div class="windmill2 absolute z-2 top-22% -right-5% flex items-center flex-col">
            <img loading="lazy" src="@/assets/tree/fan.png" class="w-226px relative z-2 fan-animation">
            <img loading="lazy" src="@/assets/tree/column.png" class="w-26px relative z-1 -mt-112px ml-7px">
        </div>

        <div class="windmill3 absolute z-1 top-21% -right-6% flex items-center flex-col">
            <img loading="lazy" src="@/assets/tree/fan.png" class="w-138px relative z-2 fan-animation">
            <img loading="lazy" src="@/assets/tree/column.png" class="w-16px relative z-1 -mt-72px ml-7px">
        </div>

        <!-- 中间内容 -->
        <div class="middle text-center mt-4vh flex flex-col relative z-10">
            <div class="header h-10vh relative z-10">
                <img loading="lazy" src="@/assets/tree/subject_title.png" class="h-full" />
            </div>
            <div class="tree_view relative z-10 px-2.5vh flex-1 flex flex-col items-center 
            justify-center mt-0.6vh mb-1.2vh box-border">
                <div class="tree_show w-full relative">
                    <div class="w-full h-full flex items-end relative">
                        <div class="mx-auto w-full flex flex-col items-center relative z-4" @click="handleClick"
                            :class="{ 'tree_shake': hashandleGrow }">
                            <!-- 无活动 -->
                            <div class="progress_0 relative z-2 h-24vh mb-0.8vh" v-if="!infos && treeInfo === ''">
                                <img loading="lazy" src="@/assets/tree/no_tree_activity.png" class="h-full" />
                            </div>
                            <template v-else>
                                <!-- 未领取-树苗  -->
                                <div class="progress_0 relative z-2 h-24vh mb-0.8vh" v-if="!treeInfo && treeInfo !== ''"
                                    @click="handleShowSapling">
                                    <img loading="lazy" src="@/assets/tree/sapling_tips.png" class="h-full" />
                                    <img loading="lazy" src="@/assets/tree/tips_hand.png"
                                        class="w-5vh absolute top-9vh -right-3.2vh scale_zoom_handle">
                                </div>
                                <!-- end -->

                                <!-- 小树苗  -->
                                <div class="progress_1 relative z-2 h-16vh" v-else-if="mathchStage === 1">
                                    <img loading="lazy" src="@/assets/tree/tree_1.png" class="h-full" />
                                </div>
                                <!-- end -->
                                <!-- 小树 -->
                                <div class="progress_2 relative z-2 h-34vh" v-else-if="mathchStage === 2">
                                    <img loading="lazy" src="@/assets/tree/tree_2.png" class="h-full" />
                                </div>
                                <!-- end -->

                                <!-- 大树 -->
                                <div class="progress_3 relative z-2 h-44vh" v-else-if="mathchStage === 3">
                                    <img loading="lazy" src="@/assets/tree/tree_3.png" class="h-full" />
                                </div>

                                <!-- 开花 -->
                                <div class="progress_4 relative z-2 h-44vh" v-else-if="mathchStage === 4">
                                    <img loading="lazy" src="@/assets/tree/tree_4.png" class="h-full" />
                                </div>

                                <!-- 结果 -->
                                <div class="progress_5 relative z-2 h-44vh" v-else-if="mathchStage === 5">
                                    <img loading="lazy" src="@/assets/tree/tree_5.png" class="h-full" />
                                </div>
                                <!-- 成熟 -->
                                <div class="progress_6 relative z-2 h-44vh" v-else-if="mathchStage === 6">
                                    <img loading="lazy" src="@/assets/tree/tree_6.png" class="h-full" />
                                    <!-- 果树上的兑换奖品或优惠券 -->
                                    <div
                                        class="absolute left-5% right-5% top-0 bottom-0 pt-15% flex justify-center flex-wrap">
                                        <div v-for="item, index in integralTreeConfig?.prizeInfos" :key="index"
                                            @click.stop="exchangeReult(item)" class="w-22% h-fit"
                                            :class="{ 'mt-1.2vh ml-1vh': (index + 1) % 2 === 0 }">
                                            <img loading="lazy" src="@/assets/tree/ex_coupons_icon.png" class="w-full"
                                                v-if="item?.prizeType === '7'" />
                                            <img loading="lazy" src="@/assets/tree/ex_goods_icon.png" class="w-full"
                                                v-else-if="item?.prizeType === '3'" />
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>

                        <!-- 养分 领取树后才显示-->
                        <div class="soil_point absolute left-20% top-35% z-5 right-50%" @click="handleSoil"
                            v-if="treeInfo">
                            <div class="relative w-full h-full">
                                <!-- 养分不足提示 -->
                                <div class="absolute top-20% -left-60% z-3">
                                    <div class="messagetips1 text-1.2vh py-0.5vh px-1vh rounded-10px text-[#174C00]"
                                        v-show="showMessage1">
                                        养分不足<br>请兑换肥料
                                    </div>
                                </div>
                                <div class="relative">
                                    <div class="relative soil_ball" :class="{ 'soil_ball_active': showDebance }">
                                        <img loading="lazy" src="@/assets/tree/nutrient.png" class="w-full h-full" />
                                        <div class="text-[#fff] text-2.1vh absolute
                                        left-28% right-28% top-24% text-center">
                                            <!-- 用户剩余肥料 / 用户用积分兑换的肥料数 -->
                                            {{ (treeInfo?.fertilizerNum / integralTreeConfig?.fertilizerUseIntegral) *
                                                100
                                            }}%
                                        </div>
                                    </div>
                                    <!-- <img loading="lazy" src="@/assets/tree/tips_hand.png"
                                        class="w-90px absolute top-110px -right-10px scale_zoom_handle"> -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <img loading="lazy" src="@/assets/tree/soil.png" class="
                     absolute z-1 soil h-25% left-50% -translate-x-50% bottom-0" />
                </div>

                <div class="current_progress w-full mt-1.2vh min-h-6vh">
                    <!-- 领取树后才显示 -->
                    <div v-if="treeInfo">
                        <div v-if="mathchStage < 6">
                            <div class="progress_line w-63% h-1.8vh bg-[#AC5109] mx-auto border-box">
                                <div class="num w-0%  h-100%" :style="{ 'width': progress.progressNum + '%' }"></div>
                            </div>
                            <div class="text text-[#174C00] text-1.5vh mt-1vh">
                                <!-- fertilizerUseIntegral兑换积分数 useFertilizerOnce每次养分减少百分比 -->
                                <!-- 再浇{{ progress.currentGap }}g水或使用{{ progress.currentGap *
                                    integralTreeConfig?.fertilizerUseIntegral/integralTreeConfig?.useFertilizerOnce }}次养分就能{{ progress.progressText
                                }}哦~ -->

                                再获取{{ progress.currentGap / progress.totalGap * 100 }}%的成长值就能{{ progress.progressText
                                }}哦~
                            </div>
                        </div>
                        <div v-else>
                            <div class="progress_line w-63% h-1.8vh bg-[#AC5109] mx-auto border-box">
                                <div class="num w-100% h-100%"></div>
                            </div>
                            <div class="text text-[#174C00] text-1.5vh mt-1vh">
                                果实成熟啦！快去兑换奖励吧~
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部 -->
        <div class="bottom flex justify-between px-3.2vh">
            <!-- 兑水滴 -->
            <div class="h-8.5vh" @click="handleExchange('water')">
                <img loading="lazy" src="@/assets/tree/water_icon.png" class="h-full" />
            </div>
            <!-- 兑肥料 -->
            <div class="h-8.5vh" @click="handleExchange('fertilizer')">
                <img loading="lazy" src="@/assets/tree/fertilizer_icon.png" class="h-full" />
            </div>
            <!-- 我的收获 -->
            <div class="h-8.6vh" @click="toRecord()" :class="{ 'active_harvest': haveNewReceive }">
                <img loading="lazy" src="@/assets/tree/harvest_icon.png" class="h-full" />
            </div>

            <!-- 水壶 -->
            <div class="relative z-99 h-8.6vh" @click="handleWater">
                <!-- 水滴不足提示 -->
                <div class="absolute -top-75% left-0 right-0 z-3" v-show="showMessage2">
                    <div class="messagetips2 text-1.5vh py-0.5vh px-0.5vh rounded-0.8vh text-[#174C00] ">
                        水滴不足<br>请兑换水滴
                    </div>
                </div>

                <!-- 水壶 + 动画 -->
                <div class="relative z-99" :class="{ 'kettle_icon': ishandleWater }">
                    <!-- 水滴 -->
                    <div class="absolute water_icons">
                        <img loading="lazy" src="@/assets/tree/water1.png" class="w-19px absolute water1"
                            :class="{ 'water_icons_an': showWateran }">
                        <img loading="lazy" src="@/assets/tree/water1.png" class="w-20px absolute water2"
                            :class="{ 'water_icons_an': showWateran }">
                        <img loading="lazy" src="@/assets/tree/water1.png" class="w-18px absolute water3"
                            :class="{ 'water_icons_an': showWateran }">
                        <img loading="lazy" src="@/assets/tree/water1.png" class="w-15px absolute water4"
                            :class="{ 'water_icons_an': showWateran }">
                        <img loading="lazy" src="@/assets/tree/water1.png" class="w-14px absolute water5"
                            :class="{ 'water_icons_an': showWateran }">
                        <img loading="lazy" src="@/assets/tree/water1.png" class="w-13px absolute water6"
                            :class="{ 'water_icons_an': showWateran }">
                    </div>
                    <img loading="lazy" src="@/assets/tree/kettle_icon.png" class="h-6vh" />
                </div>
                <!-- 水壶底座 + 参数 -->
                <div class="absolute left-0 right-0  -bottom-1vh z-2 ">
                    <div class="relative mx-auto text-center">
                        <img loading="lazy" src="@/assets/tree/kettle_bttom.png"
                            class="h-4vh absolute z-1 bottom-1vh left-50% -translate-x-47%">
                        <div class="water_num px-1.8vh py-0.3vh 
                    rounded-0.9vh border-box inline-block
                    text-1.8vh font-medium text-[#fff] relative z-2">{{
                        treeInfo?.waterDropletNum ? treeInfo?.waterDropletNum : 0 }}g</div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 领取树苗弹窗 -->
        <saplingPopup v-model:show="saplingShow" :activityId="integralTreeConfig?.activityId" @refresh="refreshInfo"
            :integralNum="integralTreeConfig?.seedIntegral">
        </saplingPopup>
        <!-- 兑换水滴弹窗 -->
        <waterPopup v-model:show="waterShow" :activityId="integralTreeConfig?.activityId"
            :currentNum="treeInfo?.todayWaterExCount" :waterDropletNum="integralTreeConfig?.waterDropletNum"
            :waterUseIntegral="integralTreeConfig?.waterUseIntegral"
            :waterDailyCount="integralTreeConfig?.waterDailyCount" @refresh="refreshInfo">
        </waterPopup>
        <!-- 兑换肥料弹窗 -->
        <fertilizerPopup v-model:show="fertilizerShow" :activityId="integralTreeConfig?.activityId"
            :currentNum="treeInfo?.todayFertilizerExCount" :fertilizerNum="integralTreeConfig?.fertilizerNum"
            :fertilizerUseIntegral="integralTreeConfig?.fertilizerUseIntegral"
            :fertilizerDailyCount="integralTreeConfig?.fertilizerDailyCount" @refresh="refreshInfo">
        </fertilizerPopup>
        <!-- 提示弹窗 -->
        <tipsPopup v-model:show="tipsShow" :tipsContent="tipsContent"></tipsPopup>
        <!-- 兑换商品/优惠券 -->
        <resultPopup v-model:show="resuletShow" :resultInfo="resultInfo" :activityId="integralTreeConfig?.activityId"
            @refresh="refreshInfo">
        </resultPopup>

        <!-- 成长弹窗 -->
        <growPopup v-model:show="growPopupShow" :confirmText="growPopupSet.confirmText"
            :popupType="growPopupSet.popupType" @confirm="growPopupShow = false">
            <template #right_btn>
                <div v-if="growPopupSet.popupType === '1'">
                    <img loading="lazy" src="@/assets/tree/water1.png" class="w-27px ml-8px">
                </div>
            </template>
        </growPopup>
    </div>
</template>
<script lang="ts" setup>
import growPopup from './components/growPopup.vue'; //树成长弹窗
import tipsPopup from './components/tipsPopup.vue'; //提示弹窗
import saplingPopup from './components/saplingPopup.vue'; //树苗领取弹窗
import waterPopup from './components/waterPopup.vue'; //兑换水滴弹窗
import fertilizerPopup from './components/fertilizerPopup.vue'; //兑换肥料弹窗
import resultPopup from './components/resultPopup.vue'; //兑换商品弹窗
import {
    treeAcDetail, personalTreeInfo, dailyWaterFertilizer, personIntegral
} from '@/api/integralTree'
import { activityTimeValidator, qualificationValidator } from '@/utils/actRulesValidator.js';
import { useUserStore } from '@/store/modules/user';
import { showToast } from 'vant';
const router = useRouter()
const useStore = useUserStore()
const userInfo = computed(() => useStore.userInfo)

const waterShow = ref(false)//兑换水弹窗
const fertilizerShow = ref(false)//兑换肥料
const resuletShow = ref(false)//成熟兑换商品弹窗

const hashandleGrow = ref(false) //是否对树做了操作
const ishandleWater = ref(false) //水壶是否正在使用
const kettleTimer: any = ref(null) //水壶定时器
const showWateran = ref(false) //浇水动画

const haveNewReceive = ref(false) //是否有新收获


// 成长弹窗
const growPopupShow = ref(false)
const growPopupSet = ref({
    popupType: '',
    confirmText: ''
})
// 提示弹窗
const tipsShow = ref(false)
const tipsContent = ref('树苗陆续发放中，敬请期待！')

// 施肥动画
const showDebance = ref(false) //施肥动画

// 消息展示两秒后消失
const showMessage1 = ref(false) //养分提示
const showMessage2 = ref(false) //浇水提示
watch(showMessage2, (val) => {
    if (val) {
        setTimeout(() => {
            showMessage2.value = false
        }, 2000);
    }
}, { immediate: true })
watch(showMessage1, (val) => {
    if (val) {
        setTimeout(() => {
            showMessage1.value = false
        }, 2000);
    }
}, { immediate: true })

// 点击树
const handleClick = () => {
    hashandleGrow.value = true
}
watch(hashandleGrow, (val) => {
    if (val) setTimeout(() => {
        hashandleGrow.value = false
    }, 2000);
})
onMounted(() => {
    initalPage()
})
// 初始化
const initalPage = async () => {
    await getActivtiDetail()
    await getPersonIntegral()
    await getPersonalTreeInfo()
}

// 活动内容
const infos = ref<any>()//活动内容
const integralTreeConfig = ref<any>()//活动树配置信息
// 活动内容请求
const getActivtiDetail = async () => {
    const { code, data } = await treeAcDetail()
    if (code === 200) {
        infos.value = data
        integralTreeConfig.value = data?.integralTreeConfig
    }
}
// 个人树信息请求
const treeInfo = ref<any>('')
const getPersonalTreeInfo = async () => {
    const { code, data } = await personalTreeInfo(integralTreeConfig.value?.activityId)
    if (code === 200) treeInfo.value = data
}

// 个人积分
const userIntegral = ref(0)
const getPersonIntegral = async () => {
    const { code, data } = await personIntegral(userInfo.value?.userId)
    if (code === 200) {
        userIntegral.value = data?.userIntegral
    }
}

// 领取树苗
const saplingShow = ref(false)//树苗弹窗
const handleShowSapling = async () => {
    // 用户类型及区域
    const { state, message } = await qualificationValidator(infos.value, userInfo.value)
    if (!state) {
        tipsContent.value = message
        tipsShow.value = true
        return
    }
    // 开放时间校验
    const { state: result, message: msg } = await activityTimeValidator(infos.value)
    if (!result) {
        tipsContent.value = msg
        tipsShow.value = true
        return
    }

    // 积分限制
    if (!userIntegral.value || userIntegral.value < integralTreeConfig.value?.seedIntegral) {
        tipsContent.value = `您当前积分不足,领取树苗需消耗 ${integralTreeConfig.value.seedIntegral} 个积分`
        tipsShow.value = true
        return
    }
    // 树苗数量 0
    if (!integralTreeConfig.value?.seedCount) {
        tipsContent.value = '没有更多的树苗了，感谢您的支持。'
        tipsShow.value = true
        return
    }
    // 展示树苗弹窗
    saplingShow.value = true
}


// 每日兑换
const handleExchange = async (type: string) => {
    if (!infos.value && treeInfo.value === '') {
        tipsContent.value = `活动暂未开启,敬请期待~`
        tipsShow.value = true
        return
    }
    if (!treeInfo.value) {
        tipsContent.value = `请先领取树苗后再进行兑换`
        tipsShow.value = true
        return
    }
    // 用户类型及区域
    const { state, message } = await activityTimeValidator(infos.value)
    if (!state) {
        tipsContent.value = message
        tipsShow.value = true
        return
    }
    // 开放时间校验
    const { state: result, message: msg } = await activityTimeValidator(infos.value)
    if (!result) {
        tipsContent.value = msg
        tipsShow.value = true
        return
    }

    if (type === 'water') {
        if (treeInfo.value?.todayWaterExCount >= integralTreeConfig.value?.waterDailyCount) {
            tipsContent.value = `您今天兑换的水滴已达上限 ${integralTreeConfig.value?.waterDailyCount} 次,请明日再来`
            tipsShow.value = true
            return
        }
        if (!userIntegral.value || userIntegral.value < integralTreeConfig.value?.waterUseIntegral) {
            tipsContent.value = `您当前积分不足,兑换水滴至少需要: ${integralTreeConfig.value?.waterUseIntegral} 个积分`
            tipsShow.value = true
            return
        }
        waterShow.value = true
    }
    else if (type === 'fertilizer') {
        if (treeInfo.value?.fertilizerNum) {
            tipsContent.value = `请将养分使用完后,再兑换肥料`
            tipsShow.value = true
            return
        }

        if (treeInfo.value?.todayFertilizerExCount >= integralTreeConfig.value?.fertilizerDailyCount) {
            tipsContent.value = `您今天兑换的肥料已达上限 ${integralTreeConfig.value?.fertilizerDailyCount} 次,请明日再来`
            tipsShow.value = true
            return
        }
        if (!userIntegral.value || userIntegral.value < integralTreeConfig.value?.fertilizerUseIntegral) {
            tipsContent.value = `您当前积分不足,兑换肥料至少需要: ${integralTreeConfig.value?.fertilizerUseIntegral} 个积分`
            tipsShow.value = true
            return
        }
        fertilizerShow.value = true
    }
}

// 兑换商品
const resultInfo = ref<any>()
const exchangeReult = (item: any) => {
    resultInfo.value = item
    resuletShow.value = true
}

// 浇水
const currentTime = ref(0) //计时
let waitingTime = false //接口请求
const handleWater = async () => {
    if (!infos.value && treeInfo.value === '') {
        tipsContent.value = `活动暂未开启,敬请期待~`
        tipsShow.value = true
        return
    }

    if (waitingTime) return
    // 水分不足提示
    if (!treeInfo.value?.waterDropletNum) {
        showMessage2.value = true
        return
    }
    // 如果当前正在进行操作，则不能进行浇水
    if (ishandleWater.value || showDebance.value || hashandleGrow.value) {
        showToast('亲~，给树一点吸收时间，请稍后浇水')
        return
    }
    waitingTime = true
    const flag = await dailyTask('water')
    waitingTime = false
    if (!flag) return

    ishandleWater.value = true //开始浇水标识符
    if (kettleTimer.value) clearInterval(kettleTimer.value)
    kettleTimer.value = setInterval(() => {
        hashandleGrow.value = false
        currentTime.value += 1
        // 1s显示浇水动画
        if (currentTime.value === 1) {
            showWateran.value = true
        }
        // 4s隐藏浇水动画
        else if (currentTime.value === 4) {
            showWateran.value = false
        }
        // 5s结束
        if (currentTime.value === 5) {
            ishandleWater.value = false
            hashandleGrow.value = true // 开始树生长标识符
        }
        // 6s结束
        if (currentTime.value === 6) {
            currentTime.value = 0
            clearInterval(kettleTimer.value)
        }
    }, 1000)
}

// 施肥
const handleSoil = async () => {
    if (waitingTime) return
    if (!treeInfo.value?.fertilizerNum) {
        showMessage1.value = true
        return
    }
    // 如果当前正在进行操作，则不能施肥
    if (ishandleWater.value || showDebance.value || hashandleGrow.value) {
        showToast('亲~，给树一点吸收时间，请稍后施肥')
        return
    }
    waitingTime = true
    const flag = await dailyTask('fertilizer')
    waitingTime = false
    if (!flag) return

    showDebance.value = true
    hashandleGrow.value = false
    setTimeout(() => {
        showDebance.value = false
        hashandleGrow.value = true
    }, 1000)
}

// 每日浇水施肥接口
const dailyTask = (exchangeType: string) => {
    return new Promise(async (resolve) => {
        const { code, data, message } = await dailyWaterFertilizer({
            platform: 'app',
            activityId: integralTreeConfig.value?.activityId,
            exchangeType,
        })
        if (code === 200) {
            refreshTreeInfo(data)
            resolve(true)
        }
        else {
            tipsContent.value = message
            tipsShow.value = true
            resolve(false)
        }
    })
}


// 刷新树信息
const refreshTreeInfo = (data: any) => {
    // 水滴 肥料 树成长值
    const { waterDropletNum, fertilizerNum, nutrientNum } = data
    treeInfo.value.waterDropletNum = waterDropletNum
    treeInfo.value.fertilizerNum = fertilizerNum
    treeInfo.value.nutrientNum = nutrientNum
}
// 成长进度条
const progress = computed(() => {

    const currentGap = integralTreeConfig.value['stage' + (mathchStage.value + 1)] - treeInfo.value?.nutrientNum //当前成长值与下一阶段成长值的差值
    const totalGap = integralTreeConfig.value['stage' + (mathchStage.value + 1)] - integralTreeConfig.value['stage' + mathchStage.value]//阶段成长值
    const progressNum = (totalGap - currentGap) / totalGap * 100
    let progressText = ''
    switch (mathchStage.value) {
        case 1:
        case 2:
            progressText = `长大`
            break;
        case 3:
            progressText = `开花`
            break;
        case 4:
            progressText = `成熟`
            break;
        case 5:
            progressText = `结果`
            break;
    }
    return {
        progressNum,
        currentGap,
        totalGap,
        progressText
    }
})
// 阶段匹配
const mathchStage = computed(() => {
    if (treeInfo.value && treeInfo.value.nutrientNum < integralTreeConfig.value?.stage2) {
        return 1
    }
    else if (treeInfo.value?.nutrientNum >= integralTreeConfig.value?.stage2 && treeInfo.value?.nutrientNum < integralTreeConfig.value?.stage3) {
        return 2
    }
    else if (treeInfo.value?.nutrientNum >= integralTreeConfig.value?.stage3 && treeInfo.value?.nutrientNum < integralTreeConfig.value?.stage4) {
        return 3
    }
    else if (treeInfo.value?.nutrientNum >= integralTreeConfig.value?.stage4 && treeInfo.value?.nutrientNum < integralTreeConfig.value?.stage5) {
        return 4
    }
    else if (treeInfo.value?.nutrientNum >= integralTreeConfig.value?.stage5 && treeInfo.value?.nutrientNum < integralTreeConfig.value?.stage6) {
        return 5
    }
    else if (treeInfo.value?.nutrientNum >= integralTreeConfig.value?.stage6) {
        return 6
    } else {
        return 0
    }
})
// 监听成长值判断到达阶段展示对应弹窗
watch(() => treeInfo.value?.nutrientNum, (val, oldval) => {
    // 初次加载不执行显示
    if (!oldval) return
    switch (val) {
        case integralTreeConfig.value?.stage2:
            growPopupSet.value.popupType = '2' // 小树
            growPopupSet.value.confirmText = `我知道了`
            growPopupShow.value = true
            break;
        case integralTreeConfig.value?.stage3:
            growPopupSet.value.popupType = '3' // 大树
            growPopupSet.value.confirmText = `我知道了`
            growPopupShow.value = true
            break;
        case integralTreeConfig.value?.stage4:
            growPopupSet.value.popupType = '4' // 开花
            growPopupSet.value.confirmText = `我知道了`
            growPopupShow.value = true
            break;
        case integralTreeConfig.value?.stage5:
            growPopupSet.value.popupType = '5' // 结果
            growPopupSet.value.confirmText = `我知道了`
            growPopupShow.value = true
            break;
        case integralTreeConfig.value?.stage6:
            growPopupSet.value.popupType = '6' // 成熟
            growPopupSet.value.confirmText = `去兑换奖励`
            growPopupShow.value = true
            break;
    }

}, { deep: true })

// 子组件刷新内容
const refreshInfo = (val: any) => {
    switch (val.type) {
        case 'sapling':
            growPopupSet.value.popupType = '1' // 树苗奖励
            growPopupSet.value.confirmText = `领取奖励:${integralTreeConfig.value?.firstActive}g水滴`
            growPopupShow.value = true
            initalPage()
            break;
        case 'exchange':
            refreshTreeInfo(val.data)
            getPersonIntegral()
            break;
        case 'exchangeResult':
            initalPage()
            haveNewReceive.value = true
            break
        default:
            initalPage()
            break
    }
}
// 页面跳转
const pageTo = (path: string) => {
    router.push({ path })
}
const toRecord = () => {
    if (!infos.value && treeInfo.value === '') {
        tipsContent.value = `活动暂未开启,敬请期待~`
        tipsShow.value = true
        return
    }
    router.push({
        path: '/activityHome/lotteryRecord',
        query: {
            prizeTypes: '3,7',
            activityId: infos.value.activityId,
        }
    })
}
</script>


<style lang="scss" scoped>
// 动画时长相关配置
$scaleZoom: 1s; //手指缩放时长
$ballUpDown: 1s; //养分上下移动时长
$waterMove: 5s; //水壶移动时长
$waterShow: 1s; //每个水滴显示时长
$treeShake: 1s; //树抖动时长
$messageShow: 2s; //消息框显示时长
$ballDebounce: 1s; //球显示时长
$fanRotate: 4s; //风扇旋转时长
$fanRotate1: 3s; //风扇旋转时长

// 效果
.scale_zoom_handle {
    animation: scal_zoom_animation $scaleZoom infinite linear;
}

.fan-animation {
    transform-origin: 50% 50%;
    animation: self-fan-animation $fanRotate1 infinite linear;
}

.fan-animation-delay {
    transform-origin: 50% 50%;
    animation: self-fan-animation $fanRotate infinite linear;
    animation-delay: 1s;
}

// 缩放动画
@keyframes scal_zoom_animation {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.2);
    }

    100% {
        transform: scale(1);
    }
}

@keyframes up_down_animation {

    0%,
    100% {
        transform: translateY(0);
    }

    50% {
        transform: translateY(-5px);
    }
}

// 水壶移动动画
@keyframes kettle_move_animation {

    0%,
    100% {
        transform: translateX(0);
    }

    30% {
        transform: translate(-32%, -30vh);
    }

    50% {
        transform: translate(-32%, -30vh) rotate(-60deg);
    }

    60% {
        transform: translate(-32%, -30vh) rotate(-30deg);
    }

    70% {
        transform: translate(-32%, -30vh) rotate(-60deg);
    }

    80% {
        transform: translate(-32%, -30vh) rotate(-30deg);
    }
}

// 显示动画
@keyframes fadeIn {

    0%,
    100% {
        opacity: 0;
    }

    50%,
    80% {
        opacity: 1;
    }
}

// 树抖动动画
@keyframes updown_animation {

    0%,
    100% {
        transform: scaleY(1);
    }

    50% {
        transform: scaleY(1.1);
    }
}

// 弹动动画
@keyframes debunce_animation {

    0%,
    100% {
        transform: translate(0) scaleX(1) scaleY(1);
        height: auto;
    }

    20% {
        transform: translate(0px, 10px) scaleX(1.1);
    }

    50%,
    70% {
        transform: translate(0px, -10px) scaleX(1) scaleY(1.1);
    }

    80% {
        transform: translate(0px, 10px) scaleX(1.1);
    }
}

@keyframes self-fan-animation {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}


.integral-tree {
    width: 100%;
    height: 100vh;
    // bj_.png bg.png
    background: url('@/assets/tree/bj_.png'), linear-gradient(0deg, #519534 0%, #519534 100%);
    background-repeat: no-repeat, repeat;
    background-position: 0px 0px, left 1480px;
    background-size: 100% auto, 100% 100%;

    .top {
        flex: 0.1
    }

    .middle {
        flex: 0.75;

        .tree_view {
            .tree_show {
                flex: 0.8;
            }

            .progress_line {
                border: 10px solid #e36f22;
                border-radius: 30px;

                .num {
                    border-radius: 30px;
                    background-image: linear-gradient(135deg, #ff8e18 25%, #fec31d 25%, #fec31d 50%,
                            #ff8e18 50%, #ff8e18 75%, #fec31d 75%, #fec31d);
                    background-size: 40px 40px;
                    background-repeat: repeat-x;
                }
            }

        }

        // 树抖动动画
        .tree_shake {
            transform-origin: bottom center;
            animation: updown_animation $treeShake ease forwards;
        }

        .soil_ball {
            width: 12vh;
            height: 12vh;
            animation: up_down_animation $ballUpDown infinite linear;
        }

        .soil_ball_active {
            animation: debunce_animation $ballDebounce ease-in infinite;
        }
    }

    .bottom {
        flex: 0.13;

        .water_num {
            background: linear-gradient(-90deg, #ffbf47 0%, #ff9932 100%);
            border: 0.1vh solid #FFFFFF;
        }

        // 水壶
        .kettle_icon {
            animation: kettle_move_animation $waterMove ease-in-out;
        }

        .water_icons {
            img {
                opacity: 0;
            }

            // 水滴
            .water1 {
                top: -10px;
                left: 0px;
                transform: rotate(90deg);
            }

            .water2 {
                top: 10px;
                left: -15px;
                transform: rotate(50deg);
                animation-delay: .1s;
            }

            .water3 {
                top: -5px;
                left: -35px;
                transform: rotate(45deg);
                animation-delay: .2s;
            }

            .water4 {
                top: 30px;
                left: -35px;
                transform: rotate(35deg);
                animation-delay: .3s;
            }

            .water5 {
                top: 15px;
                left: -47px;
                transform: rotate(30deg);
                animation-delay: .4s;
            }

            .water6 {
                top: 40px;
                left: -55px;
                transform: rotate(30deg);
                animation-delay: .5s;
            }
        }

        .water_icons_an {
            animation: fadeIn $waterShow ease-in-out infinite;
        }
    }

    // 消息框
    .messagetips1 {
        text-align: center;
        background-color: rgba($color: #fff, $alpha: 0.4);
        position: relative;
        animation: fadeIn $messageShow ease-in-out forwards;
    }

    .messagetips1::after {
        display: block;
        content: '';
        width: 0;
        height: 0;
        border-left: 0.8vh solid transparent;
        border-right: 0.8vh solid transparent;
        border-top: 1.6vh solid rgba($color: #fff, $alpha: 0.4);
        position: absolute;
        right: -18%;
        top: 50%;
        transform: translateY(-50%) rotate(-90deg);
    }

    .messagetips2 {
        text-align: center;
        background-color: rgba($color: #fff, $alpha: 0.4);
        position: relative;
        z-index: 2;
        animation: fadeIn $messageShow ease-in-out forwards;
    }

    .messagetips2::after {
        display: block;
        content: '';
        width: 0;
        height: 0;
        border-left: 0.8vh solid transparent;
        border-right: 0.8vh solid transparent;
        border-top: 1.6vh solid rgba($color: #fff, $alpha: 0.3);
        position: absolute;
        z-index: 1;
        bottom: -32%;
        left: 50%;
        transform: translateX(-50%);
    }

    // end

    .active_harvest {
        position: relative;
        z-index: 1;
    }

    .active_harvest::after {
        content: ' ';
        display: block;
        width: 18px;
        height: 18px;
        background-color: #FE181F;
        border-radius: 50%;
        position: absolute;
        right: 10px;
        top: 10px;
        z-index: 2;
    }
}
</style>
