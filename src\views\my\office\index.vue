<template>
    <div class="office h-100vh px-50px py-44px box-border">
        <div v-for="(item, index) of Data.listData" @click="toPage(item)" :key="index">
            <div v-if="item.show" :style="`background-image: url(${item.bgImg})`"
                class="rounded-25px h-315px w-full mb-60px px-40px pt-100px box-border bg-no-repeat bg-[length:100%,100%]">
                <img loading="lazy" :src="item.titleImg" alt="" class="w-60% mb-10px">
                <div class="text-28px " :style="`color:${item.color}`">{{ item.tips }}</div>
                <div class=" relative h-100px flex items-center">
                    <template v-if="item.code !== 'liu_cheng_ban_gong'">
                        <span v-for="(num, i) of splitAndShowDigits(item.num, item)" :key="i"
                            class="px-12px py-2px rounded-10px mr-15px"
                            :style="`color:${item.color};border:1px solid ${item.color}`">{{ num }}
                        </span>
                    </template>
                    <span :style="`color:${item.color}`"
                        class="px-29px py-10px bg-#fff rounded-31px absolute right-0 top-1/2 -translate-y-1/2 text-32px font-500">{{
                            item.button }} <span v-if="item.num > 0" class="text-28px">({{ item.num }})</span></span>
                </div>
            </div>
        </div>

    </div>
</template>
<script lang="ts" setup>
import oaBg from "@/assets/my/office/oa_bg.png";
import singleBg from "@/assets/my/office/single_bg.png";
import auditBg from "@/assets/my/office/audit_bg.png";
import oaTitle from "@/assets/my/office/oa_title.png";
import singleTitle from "@/assets/my/office/single_title.png";
import auditTitle from "@/assets/my/office/audit_title.png";
import router from "@/router";
import { countWaitNum } from "@/api/joinUnion";
import { queryTableCount } from "@/api/friendship/audit";
import { useUserStore } from '@/store/modules/user';
import { useRoute } from "vue-router";
import utils from "@/utils/utils";
import { staticEnum } from "@/enums/configEnum";
const route = useRoute()
const useStore = useUserStore();
const Data = ref({
    listData: [
        { bgImg: oaBg, titleImg: oaTitle, tips: '智能管理,提升效能', button: '立即办公', num: 0, color: '#009FCC', path: '', code: 'liu_cheng_ban_gong' },
        { bgImg: auditBg, titleImg: auditTitle, tips: '待审核人数', button: '立即审核', num: 0, color: '#3271E7', path: '/handAudit', code: 'sao_ma_ru_hui' },
        { bgImg: singleBg, titleImg: singleTitle, tips: '会员档案待审核人数', button: '立即审核', num: 0, color: '#F74575', path: '/my/friendship', code: 'dan_shen_lian_yi' },
    ],
    permissionLabel: null
})
function splitAndShowDigits(number, item) {
    // 将数字转换为字符串
    const numberStr = number.toString();
    item.arr = []
    // 遍历字符串中的每个字符，并单独显示
    for (let i = 0; i < numberStr.length; i++) {
        const digit = numberStr.charAt(i); // 获取当前位置的数字字符
        item.arr.push(digit)
    }
    return item.arr
}
function toPage(item: any) {
    // oa智慧办公跳转外部链接
    if (item.code == 'liu_cheng_ban_gong') {
        const url = staticEnum.OA_URL + '?token=' + useStore.getToken
        if (utils.isApp()) {
            utils.citySercive(
                url,
                'OA智慧办公',
                'open',
                'liu_cheng_ban_gong_'+Date.now()
            )
        } else {
            window.location.href = url
        }
        return
    }
    // end

    if (item.path) {
        router.push(item.path)
    }

}
//获取
function getPermission(index) {
    queryTableCount({
        nextLevelFlag: true
    }).then(res => {
        if (res.code == 200) {
            Data.value.listData[index].num = res.data.single.wait || 0
        }
    })
}
//待审核条数
function getCountWaitNum(index) {
    countWaitNum({}).then(res => {
        if (res.code == 200) {
            Data.value.listData[index].num = res.data || 0
        }
    })
}
onMounted(() => {
    Data.value.permissionLabel = useStore.officePermission || {};
    let label = Object.keys(Data.value.permissionLabel);
    Data.value.listData.forEach((item, index) => {
        label.forEach((i, index) => {
            if (item.code == i) {
                item.show = true
            }
        });
    });
    Data.value.listData.map((el, index) => {
        if (el.code == 'sao_ma_ru_hui') {
            getCountWaitNum(index)
        } else if (el.code == 'dan_shen_lian_yi') {
            getPermission(index)
        }
    })
    // getCountWaitNum()
})
</script>
<style lang="scss" scoped>
.office {
    background: url("@/assets/my/office/office_bg.png") no-repeat;
    background-size: 100% 100%;
}
</style>