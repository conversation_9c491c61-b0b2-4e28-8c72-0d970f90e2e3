export default [
    {
        path:'/integralTree',
        component:()=>import('@/views/integralTree/index.vue'),
        name:'integralTree',
        meta: {
            title: "积分种树",
            isShowTabBar: false,
            isBack:true,
        }
    },
    {
        path:'/integralTree/rule',
        component:()=>import('@/views/integralTree/rule.vue'),
        name:'integralTreeRule',
        meta: {
            title: "积分种树规则",
            isShowTabBar: false,
            isBack:true,
            keepAlive:true
        }
    }
]