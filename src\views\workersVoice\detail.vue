<template>
  <div class="bg-[#fff] w-full">
    <div class="p-5">
      <div class="flex items-center my-2 justify-between">
        <div class="flex items-center">
          <img loading="lazy" :src="item.icon" class="w-[40px] h-[40px] mr-2" />
          <div class="text-[#333333] text-[24px]">
            <div>{{ item.userName || '-' }}</div>
            <div>{{ item.createTime || '-' }}</div>
          </div>
        </div>
        <div class="flex items-center text-[24px] text-[#333333]">
          <img loading="lazy" :src="iconViewXs" class="w-[28px] h-[17px] mr-1" />
          <span>{{ item.clickNum || '' }}</span>
        </div>
      </div>
      <div class="text-[#333333] text-[32px] font-semibold">{{ item.describes || '-' }}</div>
      <div class="flex flex-wrap" v-if="item.img">
        <img loading="lazy" :src="v" class="w-[30%] h-220px mr-16px rounded-15px mt-20px object-cover"
          @click="previewImg(v)" v-for="(v, i) in item.img" :key="i" />
      </div>
    </div>
    <div class="bg-[#F6F7F8] h-[20px]"></div>
    <div class="p-5">
      <div class="flex items-center justify-between my-2 mb-3">
        <div class="flex items-center">
          <img loading="lazy" :src="ghIcon" class="w-[40px] h-[40px] mr-1" />
          <div class="text-[#5AA4FF]"> 工会回复：</div>
        </div>
        <div class="flex items-center text-[24px] text-[#333333]">
          <img loading="lazy" :src="iconDzXs" class="w-[28px] h-[28px] mr-1" />
          <span>{{ item.employeeMessageReplyVO?.likeNumber || '' }}</span>
        </div>
      </div>
      <div>{{ item.employeeMessageReplyVO?.content || '-' }}</div>
    </div>
    <div class="w-full flex justify-center mt-10" v-if="item?.employeeMessageReplyVO?.employeeMessageReplyId">
      <img loading="lazy" :src="dzUnclick" @click="like" :class="`${dzShow ? 'puff-out-center' : 'puff-in-center'}`"
        v-if="!dzShow" />
      <img loading="lazy" :src="dzClick" :class="`${dzShow ? 'puff-in-center' : 'puff-out-center'}`" @click="like"
        v-else />
    </div>
    <!-- 气泡样式 -->
    <div class="absolute left-50% top-50% -translate-50% z-99">
      <waterIntergral v-model:show="showBubble" :score="scoreNum" bigSize="132px" midSize="52px" smallSize="40px"
        scorefontSize="40px"></waterIntergral>
    </div>
  </div>
</template>

<script lang="ts" setup>
import ghIcon from '@/assets/workers-voice/32.png';
import iconDzXs from '@//assets/workers-voice/icon-dz-xs.png';
import iconViewXs from '@//assets/workers-voice/icon-view-xs.png';
import dzUnclick from '@/assets/workers-voice/dz-unclick.png';
import dzClick from '@/assets/workers-voice/dz-click.png';
import { view } from '@/api/workerVoice';
import boy from '@/assets/workers-voice/boy.png';
import girl from '@/assets/workers-voice/girl.png';
import { likeOperate } from '@/api/public';
import utils from '@/utils/utils';
const waterIntergral = defineAsyncComponent(() => import('@/components/Bubble/waterIntergral.vue'))
const route = useRoute();

const item = ref<Recordable>({});

const dzShow = ref<boolean>(false);
// 气泡提示框参数设置
const showBubble = ref(false);
const scoreNum = ref(0);
let isReq = false
function like() {
  if (isReq) return
  isReq = true;
  // dzShow.value = !unref(dzShow);
  likeOperate({ sourceId: unref(item)?.employeeMessageReplyVO?.employeeMessageReplyId }).then(res => {
    isReq = false
    const { code, data: { statefulFlowState, score } } = res;
    if (code === 200) {
      if (statefulFlowState) dzShow.value = true; // 点赞成功
      else dzShow.value = false; // 取消点赞成功
      if (score) {
        showBubble.value = true;
      }
    }
  })
    .catch(() => {
      isReq = false
    })
}
const previewImg = (url: string) => {
  utils.imagePreview([url]);
}
onMounted(async () => {
  const record = await view({ employeeMessageId: route.query.id });
  item.value = { ...(record || {}), icon: record?.genderCn === '男' ? boy : girl };
  dzShow.value = !!unref(item)?.employeeMessageReplyVO?.weatherLike;
  if (item.value.img) {
    item.value.img = item.value.img.split(',').map((v: any) => utils.judgeStaticUrl(v));
  }
})
</script>
