<template>
  <div class="echart-box" style="width:100%;height:100%">
    <div ref="echartRef" class="w-full h-full"></div>
  </div>
</template>

<script lang="ts" setup>
// 饼图
import { useECharts } from '@/utils/useECharts'
import { Ref, ref, watch, onMounted } from 'vue'
import { getVwSize } from '../../data'

const props = defineProps({
  dataSource: {
    type: Object,
    default: {
      columnList: [
        '本市级',
        '顺庆区',
        '高坪区',
        '嘉陵区',
        '阆中市',
        '南部县',
        '西充县',
        '仪陇县',
        '营山县',
        '蓬安县',
      ],
      publishCountList: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      assignCountList: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      usedCountList: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    },
  }, //color
})

const echartRef = ref<HTMLDivElement | null>(null)
const { setOptions } = useECharts(echartRef as Ref<HTMLDivElement>)

const initChart = () => {
  let maxNumPub = 0, maxNumAss = 0, maxNumUsed = 0
  let isFormated = false

  let data = { x: [], y1: <any[]>[], y2: <any[]>[], y3: <any[]>[] }
  data.x = props.dataSource?.columnList
  if (props.dataSource?.publishCountList) {
    data.y1 = props.dataSource?.publishCountList
    maxNumPub = Math.max(...data.y1)
  }
  if (props.dataSource?.assignCountList) {
    data.y2 = props.dataSource?.assignCountList
    maxNumAss = Math.max(...data.y2)
  }
  if (props.dataSource?.usedCountList) {
    data.y3 = props.dataSource?.usedCountList
    maxNumUsed = Math.max(...data.y3)
  }

  if ([maxNumPub, maxNumAss, maxNumUsed].some(item => item > 1000)) {
    isFormated = true
    data.y1 = data.y1.map((item: any) => parseFloat((item / 1000).toFixed(2)))
    data.y2 = data.y2.map((item: any) => parseFloat((item / 1000).toFixed(2)))
    data.y3 = data.y3.map((item: any) => parseFloat((item / 1000).toFixed(2)))
  }


  // 绘制图表
  var option = {
    tooltip: {
      show: true,
      trigger: 'axis',
      formatter: function (params: any) {
        let text = ''
        params.forEach((v: any) => {
          let value = v.value;
          if (v.seriesIndex === 0) {
            value = props.dataSource.publishCountList[v.dataIndex]
          }
          else if (v.seriesIndex === 1) {
            value = props.dataSource.assignCountList[v.dataIndex]
          }
          else if (v.seriesIndex === 2) {
            value = props.dataSource.usedCountList[v.dataIndex]
          }
          let name = v.seriesName.slice(1, 3) + '量';
          text += `${name}: ${value}张<br/>`
        })
        return text;
      },
      textStyle:{
        fontSize:getVwSize(20)
      }
    },
    grid: {
      top: '20%',
      left: '0%',
      right: '3%',
      bottom: '0%',
      containLabel: true,
    },
    legend: {
      show: true,
      icon: 'circle',
      orient: 'horizontal',
      top: '0%',
      right: '0%',
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 30,
      textStyle: {
        color: '#666666',
        fontSize: getVwSize(20),
      },
    },
    xAxis: [
      {
        type: 'category',
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitArea: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        boundaryGap: true,
        axisLabel: {
          show: true,
          interval: 0,
          rotate: 30,
          fontSize: getVwSize(22),
          lineHeight: getVwSize(30),
          margin: getVwSize(20),
          color: '#999',
        },
        data: data.x,
      },
    ],
    yAxis: [
      {
        name: `单位:${isFormated ? '千' : ''}张`,
        nameTextStyle: {
          color: '#999',
          fontSize: getVwSize(20),
          padding: [0, 0, 0, getVwSize(50)]
        },
        type: 'value',
        min: 0,
        minInterval: 1,
        splitLine: {
          show: true,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#E6E6E6',
          },
        },
        axisLabel: {
          textStyle: {
            color: '#999',
          },
        },
        axisTick: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: '已发放',
        type: 'line',
        symbol: 'circle',
        symbolSize: getVwSize(10),
        z: 1,
        label: {
          show: false,
        },
        lineStyle: {
          width: getVwSize(4),
          shadowColor: 'rgba(66, 163, 255, 0.1)',
          shadowBlur: getVwSize(6),
          shadowOffsetY: getVwSize(6)
        },
        itemStyle: {
          normal: {
            color: '#2CB99D',
          },
        },
        data: data.y1,
      },
      {
        name: '已领取',
        type: 'line',
        symbol: 'circle',
        symbolSize: getVwSize(10),
        z: 3,
        label: {
          show: false,
          position: 'top',
        },

        lineStyle: {
          width: getVwSize(4),
          shadowColor: 'rgba(98, 42, 220, 0.1)',
          shadowBlur: getVwSize(6),
          shadowOffsetY: getVwSize(6)
        },
        itemStyle: {
          normal: {
            color: '#B290FA',
          },
        },
        data: data.y2,
      },
      {
        name: '已核销',
        type: 'line',
        symbol: 'circle',
        symbolSize: getVwSize(10),
        z: 3,
        label: {
          show: false,
          position: 'top',
        },

        lineStyle: {
          width: getVwSize(4),
          shadowColor: 'rgba(98, 42, 220, 0.1)',
          shadowBlur: getVwSize(6),
          shadowOffsetY: getVwSize(6)
        },
        itemStyle: {
          normal: {
            color: '#42A3FF',
          },
        },
        data: data.y3,
      },
    ],
  }

  setOptions(option)
}

onMounted(() => {
  initChart()
})

// 监听dataSource
watch(
  () => props.dataSource,
  (newValue, oldValue) => {
    // console.log('watch', newValue, oldValue);
    initChart()
  },
  { deep: true, immediate: true }
)
</script>
<style scoped lang="scss">
.echart-box {}
</style>
