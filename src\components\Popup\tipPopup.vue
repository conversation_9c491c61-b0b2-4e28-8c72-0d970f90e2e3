<template>
    <van-popup :show="showPop" round class="px-80px box-border pt-30px pb-70px flex flex-col items-center"
        @click-overlay="onClick">
        <img loading="lazy" :src="icon" alt="" class="w-276px h-auto">
        <div class="tip text-#333 text-32px my-60px">{{ tip }}</div>
        <Button :name="btnTitle" class="w-400px h-78px" @click="onClick" />
    </van-popup>
</template>
<script lang="ts" setup>
import Button from '@/components/Button/button.vue'
const props = defineProps({
    showPop: {
        type: Boolean,
        default: false
    },
    icon: {
        type: String,
        default: ''
    },
    tip: {
        type: String,
        default: '请输入'
    },
    btnTitle: {
        type: String,
        default: '请输入'
    }
})
const emit = defineEmits(['closePop'])
function onClick() {
    emit('closePop')
}
</script>