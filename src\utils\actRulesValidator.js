/* rulesValidator 是 vant表单验证规则 */

//手机号码验证
import {useDictionary} from '@/store/modules/dictionary';

const telRules = [
  {
    required: true,
    message: '手机号不能为空',
    trigger: 'onBlur',
  },
  {
    // 自定义校验规则
    validator: value => {
      // return /^(0|86|17951)?(13[0-9]|15[012356789]|166|17[3678]|18[0-9]|14[57])[0-9]{8}$/.test(value);
      return /^(0|86|17951)?(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/.test(
        value,
      )
    },
    message: '请输入正确格式的手机号',
    trigger: 'onBlur',
  },
]

//身份证验证
const idRules = [
  {
    required: true,
    message: '身份证号码不能为空',
    trigger: 'onBlur',
  },
  {
    // 自定义校验规则
    validator: value => {
      return /^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}([0-9]|x|X)$/.test(
        value,
      )
    },
    message: '请输入正确格式的身份证',
    trigger: 'onBlur',
  },
]


//投票征集日期校验
const voteUploadWordsTimeValidator = activityDetail => {
  const {
    openingStartTime,
    openingEndTime,
    voteInfo: { signStartDate, signEndDate,voteStartDate },
  } = activityDetail
  // 当前日期
  const now = new Date()
  const currentDate = now.format('yyyy-MM-dd')
  const currentTime = now.format('hh:mm:ss')
  //校验日期
  if (currentDate < signStartDate) {
    return {
      state: false,
      message: `投稿活动日期：${signStartDate}至${signEndDate}`,
    }
  }
  // if(signEndDate>voteStartDate){
  //   return { state: false }
  // }
  // if( currentDate == signEndDate){
  //   return { state: true }
  // }
  // 校验时间
  if (currentTime < openingStartTime || currentTime > openingEndTime) {
    return {
      state: false,
      message: `活动开启时间：${/\d{1,2}:\d{1,2}/g.exec(openingStartTime)[0]} - ${/\d{1,2}:\d{1,2}/g.exec(openingEndTime)[0]
        }`,
    }
  }
  
  return { state: true }
}
//投票日期校验
const voteTimeValidator = activityDetail => {
  const {
    openingStartTime,
    openingEndTime,
    voteInfo: { voteStartDate, voteEndDate,signEndDate },
  } = activityDetail
  // 当前日期
  const now = new Date()
  const currentDate = now.format('yyyy-MM-dd')
  const currentTime = now.format('hh:mm:ss')
  //校验日期
  // console.log(currentDate > signEndDate);
  
  if (currentDate < voteStartDate ||currentDate > voteEndDate ) {
    return {
      state: false,
      message: `投票活动日期：${voteStartDate}至${voteEndDate}`,
    }
  }
  // 校验时间
  if (currentTime < openingStartTime || currentTime > openingEndTime) {
    return {
      state: false,
      message: `活动开启时间：${/\d{1,2}:\d{1,2}/g.exec(openingStartTime)[0]} - ${/\d{1,2}:\d{1,2}/g.exec(openingEndTime)[0]
        }`,
    }
  }
  return { state: true }
}
//活动时间校验
const activityTimeValidator = activityDetail => {
  let activityStartTime = activityDetail.activityStartTime.replace(/-/g, '/')
  let activityEndTime = activityDetail.activityEndTime.replace(/-/g, '/')
  // 活动日期
  let startDate = new Date(activityStartTime).format('yyyy-MM-dd')
  let endDate = new Date(activityEndTime).format('yyyy-MM-dd')
  // 活动时间
  let startTime = activityDetail.openingStartTime
  let endTime = activityDetail.openingEndTime
  // 当前日期
  let currentDate = new Date()
  if (
    currentDate.format('yyyy-MM-dd') >= startDate &&
    currentDate.format('yyyy-MM-dd') <= endDate
  ) {
    // 日期满足 判断每日时间
    if (startTime && endTime) {
      let currentTime = currentDate.format('hh:mm:ss')
      if (currentTime >= startTime && currentTime <= endTime) {
        // 活动时间校验通过
        return { state: true }
      } else {
        // 时间不满足
        return {
          state: false,
          message: `活动开启时间：${/\d{1,2}:\d{1,2}/g.exec(startTime)[0]} - ${/\d{1,2}:\d{1,2}/g.exec(endTime)[0]
            }`,
        }
      }
    } else {
      // 活动时间校验通过
      return { state: true }
    }
  } else {
    // 日期不满足
    return {
      state: false,
      message: `活动日期：${startDate}至${endDate}`,
    }
  }
}
// 活动地区及资格校验
const qualificationValidator = (activityDetail, userInfo) => {
  const { areaCode, customerType, companyId } = activityDetail
  const { companyClassicIds, areaName, nickname } = userInfo
  if (customerType === '2') {
    if(!companyClassicIds){
      return { state: false, message: `尊敬的${nickname}会员,本次活动仅限【${areaCode}】认证会员参与,感谢您的支持。` }
    }
    if(areaCode === '四川省'){
      return {state:true}
    }
    const dictionary = useDictionary();
    const checkUnion = dictionary.getDictionaryOBJMap?.['regionCode']
      ?.filter((t) => areaCode?.split(",")?.includes(t.dictName))
      .some((t) => companyClassicIds.includes(t.remark));
    if(!checkUnion){
      return { state: false, message: `尊敬的${nickname}会员,本次活动仅限【${areaCode}】认证会员参与,感谢您的支持。` }
    }
    
    return { state: true }
  }
  if ((customerType === '1' && areaName.includes(areaCode) === false) || (!areaName && customerType === '1')) {
    return { state: false, message: `尊敬的${nickname}会员,本次活动仅限${areaCode}注册用户参与,感谢您的支持。` }
  }
  return { state: true }
}
export {
  telRules,
  idRules,
  activityTimeValidator, //活动时间校验
  qualificationValidator, //活动资格校验
  voteUploadWordsTimeValidator, //投票征集日期校验
  voteTimeValidator,
}
