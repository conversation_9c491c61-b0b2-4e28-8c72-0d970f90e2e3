<template>
  <div class="voteList w-full min-h-full p-28px pt-60px">
    <div class="w-full flex justify-center text-[#fff] text-40px px-50px text-center mb-30px leading-50px">
      {{ activityDetail?.activityName || '投票活动' }}
    </div>
    <div class="w-full vote-time flex justify-center mb-20px items-center">
      距离投票结束还有：
      <div>
        <template v-if="time !== null && time !== -1">
          <van-count-down v-if="time > 0" :time="time">
            <template #default="timeData">
              <div class="timer flex">
                <span class="block">{{
                  timeData.days > 9 ? timeData.days : '0' + timeData.days
                }}</span>
                <span class="colon">天</span>
                <span class="block">{{
                  timeData.hours > 9 ? timeData.hours : '0' + timeData.hours
                }}</span>
                <span class="colon">时</span>
                <span class="block">{{
                  timeData.minutes > 9 ? timeData.minutes : '0' + timeData.minutes
                }}</span>
                <span class="colon">分</span>
                <span class="block">{{
                  timeData.seconds > 9 ? timeData.seconds : '0' + timeData.seconds
                }}</span>
                <span class="colon">秒</span>
              </div>
            </template>
          </van-count-down>
          <p v-else>已结束</p>
        </template>
        <p v-else-if="time === -1" class="end">未开始</p>
        <p v-else class="end">--</p>
      </div>
    </div>
    <div class="w-full flex justify-between vote-data">
      <div>
        <div class="flex items-center justify-center mb-5px">
          <img loading="lazy" src="@/assets/activity/icon01.png" alt="" class="w-31px h-35px" />
          候选人数
        </div>
        <div class="flex justify-center num">
          {{ reportInfo.opusCount || '-' }}
          <!-- <IOdometer class="iOdometer" :value="reportInfo.opusCount || 0" /> -->
          <!-- <van-rolling-text :start-num="0" :target-num="reportInfo.opusCount || 0" direction="up" class="num" /> -->
        </div>
      </div>
      <div>
        <div class="flex items-center justify-center mb-5px">
          <img loading="lazy" src="@/assets/activity/icon02.png" alt="" class="w-35px h-28px" />
          累计投票
        </div>
        <div class="flex justify-center num">
          {{ reportInfo.voteCount || '-' }}
          <!-- <IOdometer class="iOdometer" :value="reportInfo.voteCount || 0" /> -->
          <!-- <van-rolling-text :start-num="0" :target-num="reportInfo.voteCount || 0" direction="up" class="num" /> -->
        </div>
      </div>
      <div>
        <div class="flex items-center justify-center mb-5px">
          <img loading="lazy" src="@/assets/activity/icon03.png" alt="" class="w-30px h-29px" />
          访问量
        </div>
        <div class="flex justify-center num">
          {{ reportInfo.readCount || '-' }}
          <!-- <IOdometer class="iOdometer" :value="reportInfo.readCount || 0" /> -->
          <!-- <van-rolling-text :start-num="0" :target-num="99898" direction="up" class="num" /> -->
        </div>
      </div>
    </div>
    <div class="search w-full mb-10px">
      <van-search v-model="searchValue" placeholder="请输入姓名或编号" @search="onSearch" show-action :left-icon="null"
        @clear="onClear" clear-trigger="always" class="flex-1 flex items-center !p-0" input-align="center">
        <template #action>
          <div @click.stop="onSearch" class="search-btn"> 搜索 </div>
        </template>
      </van-search>
    </div>
    <div class="list w-full mb-30px">
      <van-list v-model:loading="loading" v-model:error="error" error-text="获取失败，点击重新加载" @load="getWorkList"
        finished-text="没有更多了" :finished="finished" v-if="workList && workList.length">
        <div class="flex flex-wrap justify-between w-full">
          <div
            class="card w-1/2 overflow-hidden flex flex-col items-center justify-center flex-shrink-0 mb-70px px-15px"
            v-for="item in workList" :key="item.opusInfoId" @click.stop="toDetail(item)">
            <div class="card-name flex justify-center items-center mb-5px">{{
              item.opusNo + ' ' + item.opusName
            }}</div>
            <div class="card-body h-400px rounded-14px overflow-hidden w-full relative">
              <img loading="lazy" alt="加载失败" class="w-full"
                v-lazy="visitPrefix && item.opusCover ? visitPrefix + item.opusCover : ''" />
              <!--  :src="visitPrefix + item.opusCover" -->
              <div class="absolute bottom-0 left-0 vote-num w-full">
                <div>{{ item.votesNum }}票</div>
                <div @click.stop="onThumbsUp(item)" class="relative">
                  <template v-if="item.likeState">
                    <!-- <div class="absolute flex justify-center items-end bottom-15px transform -translate-x-25px">
                    </div>
                    <div class="pb-3px opacity-0">投票成功</div> -->
                    <img loading="lazy" src="@/assets/activity/Like-btn.png" alt="" class="w-42px h-35px mr-10px" />
                    <div class="pb-3px">投票成功</div>
                  </template>
                  <template v-else>
                    <img loading="lazy" src="@/assets/activity/icon-vote.png" alt="" class="w-42px h-35px mr-10px" />
                    <div class="pb-3px text-[#999999]">投TA一票</div>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>
      </van-list>
      <van-empty description="暂无数据~" v-else class="w-full" />
    </div>

  </div>
</template>

<script>
import validatorMixins from '@/views/activity/mixins/validator'
import { Lazyload } from 'vant'
import loadingImg from '@/assets/activity/public/loading.gif'
import { getCurrentInstance } from 'vue';
import { showToast, showDialog } from 'vant';
// import IOdometer from 'vue-odometer'
// import 'odometer/themes/odometer-theme-default.css'
export default {
  mixins: [validatorMixins], //注入验证方法
  components: {
    // IOdometer,
  },
  data() {
    return {
      time: 0,
      searchValue: '',
      workList: [],
      pageSize: 20,
      pageNum: 1,
      loading: false,
      error: false,
      finished: false,
      //统计数据
      reportInfo: {},
      // 频繁投票
      clickState: false,
      // 今日已投票次数
      thumbsUpNum: 0,
      // 每日可投票次数
      defaultThumbsUpNum: 0,
    }
  },
  computed: {
    visitPrefix() {
      return this.$store.state.visitPrefix
    },
  },
  watch: {
    activityDetail() {
      if (this.activityDetail) {
        this.initActivityTime()
        this.getWorkList()
        this.getStatistics()
        if (this.isLogin) {
          this.getThumbsUpNum()
        }
      }
    },
  },
  created() {
    // 注册懒加载组件
    const app = getCurrentInstance().appContext.app;
    app.use(Lazyload, {
      loading: loadingImg,
    })
  },
  mounted() {
    if (this.activityDetail) {
      this.initActivityTime()
      this.getWorkList()
    }
  },
  activated() {
    if (this.activityDetail) {
      this.getStatistics()
      if (this.isLogin) {
        this.getThumbsUpNum()
      }
    }
    const workInfo = JSON.parse(sessionStorage.getItem('workInfo'))
    if (workInfo) {
      const workItem = this.workList.find(item => workInfo.opusInfoId === item.opusInfoId)
      if (workItem) {
        workItem.votesNum = workInfo.votesNum
        workItem.mainRank = workInfo.mainRank
      }
    }
  },
  methods: {
    // 初始化倒计时
    initActivityTime() {
      const { activityStartTime, activityEndTime } = this.activityDetail || {}
      const startTime = activityStartTime.replace(/-/g, '/')
      const endTime = activityEndTime.replace(/-/g, '/')
      if (new Date().getTime() >= new Date(startTime).getTime()) {
        if (new Date(endTime).getTime() - new Date().getTime() > 0) {
          this.time = new Date(endTime).getTime() - new Date().getTime()
        } else {
          this.time = 0
        }
      } else {
        this.time = -1
      }
    },
    // 获取统计数据
    async getStatistics() {
      const res = await this.$api.common.getStatistics({
        activityId: this.$store.state.activityId,
      })
      if (res.code === 200) {
        this.reportInfo = res.data
      }
    },
    // 作品投票
    async onThumbsUp(item) {
      if (!this.validator()) return
      if (this.clickState) {
        showToast({
          message: '投票太频繁啦,请稍后再试',
          duration: 2000,
          forbidClick: true,
        })
        return
      }
      if (this.thumbsUpNum >= this.defaultThumbsUpNum) {
        showDialog({
          title: '温馨提示',
          message: `每位用户每日最多可投${this.defaultThumbsUpNum || 0}票,明日再来吧~`,
          confirmButtonText: '我知道了',
        })
        return
      }
      const { dailySingleCount } = this.activityDetail?.voteInfo || { dailySingleCount: 0 }
      if (item.todayCount >= dailySingleCount) {
        showDialog({
          title: '温馨提示',
          message: `每个作品每天只能投${dailySingleCount || 0}票,继续选择其他作品投票吧~`,
          confirmButtonText: '我知道了',
        })
        return
      }
      this.clickState = true
      const platform = sessionStorage.getItem('platform')
      const { code, data } = await this.$api.common.giveThumbsUp({
        activityId: this.$store.state.activityId,
        opusType: item.opusType,
        opusInfoId: item.opusInfoId,
        platform,
      })
      if (code === 200) {
        this.thumbsUpNum++
        item.votesNum++
        item.todayCount++
        item.likeState = true
        this.reportInfo.voteCount++
        showToast({
          message: '投票成功',
          duration: 1500,
          type: 'success',
          forbidClick: true,
        })
      }
      setTimeout(() => {
        this.clickState = false
      }, 1000)
    },
    // 获取已点赞次数
    async getThumbsUpNum() {
      let { code, data } = await this.$api.common.thumbsUpNum({
        activityId: this.$store.state.activityId,
      })
      if (code === 200) {
        const { dailyMax } = this.activityDetail?.voteInfo || { dailyMax: 0 }
        this.defaultThumbsUpNum = dailyMax
        this.thumbsUpNum = data || 0
      }
    },
    onSearch() {
      this.pageNum = 1
      this.workList = []
      this.finished = false
      this.getWorkList()
    },
    onClear() { },
    // 作品列表
    async getWorkList() {
      this.loading = true
      const { code, hasNextPage, data } = await this.$api.common.worksList({
        activityId: this.$store.state.activityId,
        searchNoOrTitle: this.searchValue,
        pageSize: this.pageSize,
        pageNum: this.pageNum,
      })
      this.loading = false
      if (code === 200) {
        // this.workList = data ? data : []
        if (this.pageNum === 1) {
          this.workList = data ? data : []
        } else {
          this.workList = this.workList.concat(data)
        }
        if (hasNextPage) {
          this.finished = false
          this.pageNum++
        } else {
          this.finished = true
        }
      } else {
        this.error = true
      }
    },
    // 作品详情
    toDetail(item) {
      this.$router.push({
        path: '/voteActivity/workDetail',
        query: {
          opusInfoId: item.opusInfoId,
        },
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.voteList {
  background-color: #feb23a;
  background-size: 100%;
  background-repeat: no-repeat;

  .vote-time {
    background: linear-gradient(0deg, #fef0d8 0%, #ffffff 100%);
    border-radius: 14px;
    font-size: 31px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #0382f2;
    padding: 20px 0;

    .timer {
      color: #eaf1ff;

      .colon {
        font-size: 31px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #0382f2;
      }

      .block {
        padding: 0 5px;
        display: inline-block;
        margin-left: 4px;
        margin-right: 4px;
        color: #fba536;
        -webkit-text-stroke: 1px #ff5000;
        text-stroke: 1px #ff5000;
      }
    }
  }

  .vote-data {
    background: linear-gradient(0deg, #fef0d8 0%, #ffffff 100%);
    border-radius: 14px;
    padding: 30px;
    font-size: 28px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #4c4c4c;

    >div {
      width: 33%;
      border-right: 1px solid #6eb7f8;

      &:last-child {
        border: none;
      }
    }

    img {
      margin-right: 10px;
    }

    .num {
      font-size: 28px;
      font-family: Source Han Sans CN;
      font-weight: 550;
      color: #0382f2;
    }
  }

  .search {
    padding: 20px 0px;

    .search-btn {
      font-size: 29px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #4c4c4c;
      line-height: 42px;
      padding: 0 20px;
      border-left: 1px solid #ffedd5;
    }

    .van-search__action:active {
      background: transparent;
    }

    :deep(.van-search) {
      width: 100%;
      height: 70px;
      background: #fff;
      border: 2px solid #ffedd5;
      border-radius: 35px;

      .van-search__content {
        border-radius: 35px;
        background-color: transparent;

        .van-field__control {
          color: #4c4c4c !important;
        }
      }

      .van-cell {
        padding: 0;
        height: 76px;
        line-height: 76px;
      }

      .van-search__action {
        padding-right: 0;
      }

      .van-field__left-icon {
        display: flex;
        align-items: center;

        img {
          width: 32px;
          height: 32px;
        }
      }
    }
  }

  .list {
    background-color: #fff;
    padding: 20px 10px;
    padding-top: 33px;
    border-radius: 14px;

    .card {
      .card-name {
        font-size: 28px;
        font-family: OPPOSans;
        font-weight: normal;
        color: #4c4c4c;
        width: 70%;
        padding: 10px 5px;
        border-radius: 30px;
        border: 1px solid #ff7600;
      }

      .card-body {
        .vote-num {
          >div {
            display: flex;
            justify-content: center;
            align-items: center;

            &:first-child {
              font-size: 25px;
              font-family: Arial;
              font-weight: 400;
              color: #000000;
              background-color: rgba(255, 255, 255, 0.5);
              padding: 6px 0;
            }

            &:last-child {
              padding: 8px 0;
              font-size: 31px;
              font-family: Source Han Sans CN;
              font-weight: 400;
              color: #ff7200;
              background: #f7eed0;
            }
          }
        }
      }
    }
  }
}
</style>
