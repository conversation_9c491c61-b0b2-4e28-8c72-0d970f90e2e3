<template>
  <div class="echart-box" style="width:100%;height:100%">
    <div ref="echartRef" class="w-full h-full"></div>
  </div>
</template>

<script lang="ts" setup>
// 饼图
import { useECharts } from '@/utils/useECharts'
import { Ref, ref, watch, onMounted } from 'vue'
import * as echarts from 'echarts'
import { getVwSize } from '../../data'
const props = defineProps({
  dataSource: {
    type: Object,
    default: {
      dataX: [
        '顺庆区',
        '高坪区',
        '嘉陵区',
        '阆中市',
        '南部县',
        '西充县',
        '仪陇县',
        '营山县',
        '蓬安县',
      ],
      register: [],
      unionUser: []
    },
  }, //color
})

const echartRef = ref<HTMLDivElement | null>(null)
const { setOptions } = useECharts(echartRef as Ref<HTMLDivElement>)

const initChart = () => {
  let data = { x: [], y1: <any[]>[], y2: <any[]>[] }
  let maxNumReg = 0, maxNumUnion = 0
  let isFormated = false
  data.x = props.dataSource?.dataX
  data.y1 = props.dataSource?.register
  data.y2 = props.dataSource?.unionUser

  if (props.dataSource?.register) {
    maxNumReg = Math.max(...props.dataSource?.register)
    data.y1 = props.dataSource?.register
  }
  if (props.dataSource?.unionUser) {
    maxNumUnion = Math.max(...props.dataSource?.unionUser)
    data.y2 = props.dataSource?.unionUser
  }
  if ([maxNumReg, maxNumUnion].some(item => item >= 10000)) {
    isFormated = true
    data.y1 = data.y1.map((item: any) => parseFloat((item / 10000).toFixed(2)))
    data.y2 = data.y2.map((item: any) => parseFloat((item / 10000).toFixed(2)))
  }
  // 绘制图表
  var option = {
    tooltip: {
      show: true,
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      textStyle: {
        fontSize: getVwSize(22)
      },
      formatter: function (params: any) {
        let result = ''
        if (params?.length) {
          result = params[0].name + '<br/>'
          params.forEach((item: any) => {
            let value = item.value
            if (item.seriesIndex === 0) value = props.dataSource?.register[item.dataIndex];
            else if (item.seriesIndex === 1) value = props.dataSource?.unionUser[item.dataIndex];
            result += item.marker + item.seriesName + ': ' + value + '<br/>';
          })
          return result;
        }
        return result
      },
    },
    grid: {
      top: '20%',
      left: '0%',
      right: '0%',
      bottom: '0%',
      containLabel: true,
    },
    legend: {
      show: true,
      icon: 'square',
      orient: 'horizontal',
      top: '0%',
      right: '0%',
      itemWidth: getVwSize(20),
      itemHeight: getVwSize(20),
      itemGap: getVwSize(60),
      textStyle: {
        color: '#666666',
        fontSize: getVwSize(20),
      },
    },
    xAxis: [
      {
        type: 'category',
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitArea: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        boundaryGap: true,
        axisLabel: {
          show: true,
          interval: 0,
          rotate: 35,
          fontSize: getVwSize(20),
          lineHeight: getVwSize(30),
          margin: getVwSize(20),
          color: '#999',
        },
        data: data.x,
      },
    ],
    yAxis: [
      {
        name: `人数${isFormated ? '(万)' : ''}`,
        nameTextStyle: {
          color: '#999',
          fontSize: getVwSize(20),
          padding: [0, 0, 0, getVwSize(40)]
        },
        type: 'value',
        min: 0,
        minInterval: 1,
        splitLine: {
          show: true,
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#E6E6E6',
          },
        },
        axisLabel: {
          textStyle: {
            color: '#999',
            padding: [0, 0, 0, getVwSize(40)],

          },
        },
        axisTick: {
          show: false,
        },
      },
    ],
    series: [
      {

        name: '注册会员',
        type: 'bar',
        barWidth: getVwSize(20),
        label: {
          show: false,
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [{
            offset: 0,
            color: '#92E4DC'
          },
          {
            offset: 1,
            color: '#0AABB3'
          }
          ]),
          borderRadius: getVwSize(16)
        },
        data: data.y1,
      },
      {
        name: '工会会员',
        type: 'bar',
        barWidth: getVwSize(20),
        label: {
          show: false,
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [{
            offset: 0,
            color: 'rgba(55, 131, 250, 1)'

          },
          {
            offset: 1,
            color: 'rgba(66, 201, 250, 1)'
          }
          ]),
          borderRadius: getVwSize(16)
        },
        data: data.y2,
      },
    ],
  }

  setOptions(option)
}

onMounted(() => {
  initChart()
})

// 监听dataSource
watch(
  () => props.dataSource,
  (newValue, oldValue) => {
    initChart()
  },
  { deep: true, immediate: true }
)
</script>
<style scoped lang="scss">
.echart-box {}
</style>
