<template>
    <div class="main">
        <img class="banner" src="@/assets/leaderBoard/points/banner.png" alt="" srcset="">
        <div class="box p-28px ">
            <div class="info !mt-0px">
                <div class="title flex items-center">
                    <img class="icons mr-10px" src="@/assets/leaderBoard/points/icon_tj.png" alt="" srcset="">
                    <span class="font-600">积分统计</span>
                </div>
                <div class="content ph-activit mt-10px flex">
                    <div class="label">
                        <div class="text-[26px] text-[#666]">累计积分{{ integralObj?.total >= 1000000 ? '(百万分)' : '' }}
                        </div>
                        <div class="text-[36px] text-[#333] mt-20px font-600" v-formatNum="1000000">{{
                            integralObj?.total || 0 }}</div>
                    </div>
                    <div class="label">
                        <div class="text-[26px] text-[#666]">可用积分{{ integralObj?.usable >= 10000 ? '(万分)' : '' }}</div>
                        <div class="text-[36px] text-[#333] mt-20px font-600" v-formatNum="10000">{{ integralObj?.usable
                            || 0 }}</div>
                    </div>
                    <div class="label">
                        <div class="text-[26px] text-[#666]">消耗积分{{ integralObj?.used >= 10000 ? '(万分)' : '' }}</div>
                        <div class="text-[36px] text-[#333] mt-20px font-600" v-formatNum="10000">{{ integralObj?.used
                            || 0 }}</div>
                    </div>
                </div>
            </div>
            <div class="info ">
                <div class="title flex items-center">
                    <img class="icons mr-10px" src="@/assets/leaderBoard/points/icon_area.png" alt="" srcset="">
                    <span>区域会员积分统计</span>
                </div>
                <div class="content chart-box mt-10px">
                    <AreaPointChart :dataSource="areaUserIntegral" />
                </div>
            </div>
            <div class="info ">
                <div class="title flex items-center">
                    <img class="icons mr-10px" src="@/assets/leaderBoard/points/icon_people.png" alt="" srcset="">
                    <span>会员积分分段统计</span>
                </div>
                <div class="content chart-box mt-10px">
                    <StageChart :dataSource="integralSegment" />
                </div>
            </div>
            <div class="info ">
                <div class="title flex items-center">
                    <img class="icons mr-10px" src="@/assets/leaderBoard/points/icon_xh.png" alt="" srcset="">
                    <span>会员近半年新增/消耗积分趋势</span>
                </div>
                <div class="content w-full h-450px mt-10px ">
                    <LinePointsChart :dataSource="integralGrowth" />
                </div>
            </div>
        </div>
        <img class="banner bottom" src="@/assets/leaderBoard/ph/bottom_bg.png" alt="" srcset="">
    </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import AreaPointChart from './components/charts/AreaPointChart.vue';
import StageChart from './components/charts/StageChart.vue';
import LinePointsChart from './components/charts/LinePointsChart.vue';
import { integralStatistics, integralAreaStatistics, integralSegmentStatistics, integralGrowthStatistics } from '@/api/leaderboard/integral'
import { showLoadingToast, closeToast } from 'vant'
const integralObj = ref<any>({})
// 积分统计
const getIntegralStatistics = async () => {
    showLoadingToast({ message: '数据统计中，请稍等...', forbidClick: true })
    const res = await integralStatistics()
    closeToast()
    if (res.code === 200) {
        integralObj.value = res.data
    }
}
// 区域会员积分统计
const areaUserIntegral = ref<any>({})
const getAreaUserIntegral = async () => {
    showLoadingToast({ message: '数据统计中，请稍等...', forbidClick: true })
    const res = await integralAreaStatistics()
    closeToast()
    if (res.code === 200) {
        areaUserIntegral.value = res.data
    }
}
// 会员积分分段统计
const integralSegment = ref<any>({})
const getIntegralSegment = async () => {
    showLoadingToast({ message: '数据统计中，请稍等...', forbidClick: true })
    const res = await integralSegmentStatistics()
    closeToast()
    if (res.code === 200) {
        integralSegment.value = res.data
    }
}
// 会员近半年新增/消耗积分趋势
const integralGrowth = ref<any>({})
const getIntegralGrowth = async () => {
    showLoadingToast({ message: '数据统计中，请稍等...', forbidClick: true })
    const res = await integralGrowthStatistics()
    closeToast()
    if (res.code === 200) {
        integralGrowth.value = res.data
    }
}


onBeforeMount(() => {
    getIntegralStatistics()
})
onMounted(() => {
    getAreaUserIntegral()
    getIntegralSegment()
    getIntegralGrowth()
})
</script>
<style lang="scss" scoped>
.main {
    width: 100%;
    min-height: 100vh;
    background: linear-gradient(to bottom, #D8E9FE 0%, #F6FBFF 20%, #F6FBFF 80%, #DCEEFB 100%);
    box-sizing: border-box;
    position: relative;

    .border {
        border: 1px solid red;
    }

    .banner {
        width: 100%;
        position: absolute;
        z-index: 1;
    }

    .bottom {
        position: absolute;
        bottom: 0;
    }

    .center {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .title {
        font-weight: 500;
    }

    .box {
        width: 100%;
        box-sizing: border-box;
        margin-top: 130px;
        position: relative;
        z-index: 5;

        .tab-box {
            margin-bottom: 40px;
        }

        .tab-item {
            width: 30%;
            height: 50px;
            font-size: 28px;
            color: #4898FB;
            border-radius: 23px;
            border: 1px solid #76B7FB;

            &.actived {
                background: #4898FB;
                color: white;
            }
        }

        .time {
            width: 30%;
            height: 50px;
            font-size: 28px;
            color: #4898FB;
            border-radius: 23px;
            border: 1px solid #76B7FB;

            &.gray {
                color: rgba(118, 183, 251, 0.5);
            }
        }

        .info {
            width: 100%;
            padding: 20px;
            margin: 25px 0;
            background: linear-gradient(0deg, #FFFFFF 54%, #E5F3FF 100%);
            border-radius: 20px;
            border: 2px solid #FFFFFF;
            box-sizing: border-box;

            .icons {
                width: 40px;
                height: 40px;
                object-fit: contain;
                position: relative;
                top: 2px;
            }

            .title {
                color: #333;
                font-weight: 500;
                font-size: 32px;
            }

            .ph-activit {
                height: 155px;
                background: linear-gradient(to bottom, #F5FAFF 60%, #FFFFFF 100%, );
                border: 1px solid #fff;
                border-radius: 35px;
                justify-content: space-around;
                align-items: center;

                .label {
                    text-align: center;
                }
            }

            .chart-box {
                width: 100%;
                height: 500px;
            }
        }
    }

}
</style>