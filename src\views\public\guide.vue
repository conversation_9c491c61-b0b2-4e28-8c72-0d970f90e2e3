<template>
     <div class="guide h-100vh">
      <div class="logo">
        <img loading="lazy"
          src="@/assets/public/logo.png"
          alt=""
        />
      </div>
      <div class="text-box">
        <div class="name">川工之家APP</div>
        <div class="text">开启<span style="color: #387cff">南充频道</span>升级之旅</div>
      </div>
      <div class="phone-box">
        <img loading="lazy"
          class="phone"
          src="@/assets/public/phone_bg.png"
          alt=""
        />
        <img loading="lazy"
          class="phone_page"
          src="@/assets/public/phone.png"
          alt=""
        />
      </div>
      <div
        class="download"
        @click="toPage('http://app.appurl.me/79815334476')"
      >
        立即下载
      </div>
    </div> 
</template>
<script lang="ts" setup>
  function toPage(url) {
    location.href=url
  }
</script>
<style>
.guide {
  /* width: 100%; */
  /* height: 100%; */
  background: url('@/assets/public/bg.png') no-repeat;
  background-size: 101% 100%;
  background-position: center center;
  display: flex;
  flex-flow: column;
  align-items: center;
  box-sizing: border-box;
  padding-top: 6vh;
  overflow: hidden;
}

.logo {
  width: 13vw;
  height: 13vw;
}

.logo > img {
  width: 100%;
  height: 100%;
}

.text-box {
  display: flex;
  flex-flow: column;
  align-items: center;
  margin-top: 4vh;
}

.text-box .name {
  font-weight: 500;
  font-size: 5.98vw;
  line-height: 10.5vw;
  color: #333333;
}

.text-box .text {
  font-size: 4.68vw;

  color: #666666;
}

.phone {
  width: 100%;
}
.download {
  width: 65.6vw;
  height: 11.6vw;
  background: linear-gradient(0deg, #a1cbff 0%, #5aa4ff 100%);
  border-radius: 6vw;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: 5.5vw;
  line-height: 0;
  color: #ffffff;
  position: fixed;
  bottom: 10vh;
}
.phone-box {
  width: 100%;
  position: relative;
}
.phone_page {
  position: absolute;
  height: 30vh;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>

