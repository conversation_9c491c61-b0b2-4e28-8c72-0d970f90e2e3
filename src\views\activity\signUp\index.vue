<template>
  <div class="vieAnswerActivity w-full h-100vh relative"
    :style="{ backgroundImage: `url(${activityDetail.appDetailsCover ? useStore.getPrefix + activityDetail?.appDetailsCover : backgroundImg})` }">

    <!--    <template v-else>-->
    <!--      <div class="absolute  top-30px left-30px flex items-center text-35px text-[#fff]">-->
    <!--        <img loading="lazy" src="@/assets/activity/logo.png" alt="" class="w-60px mr-10px">-->
    <!--        {{ activityDetail?.companyName || '' }}-->
    <!--      </div>-->
    <!--      <img loading="lazy" src="@/assets/activity/background.jpg" alt="" class="w-full h-full">-->
    <!--    </template>-->
    <div class="absolute top-950px w-full flex flex-col items-center">
      <div class="w-325px h-80px btn flex justify-center items-center text-[#fff] text-40px mb-40px rounded-40px"
        @click="toSignUp">
        立即报名
      </div>
      <div class="flex btns w-70/100 justify-around">
        <div class="" @click="toDetail"> <img loading="lazy" src="@/assets/activity/icon_hdxq.png" alt="">活动详情</div>
        <div @click="toLotteryRecord" v-if="activityDetail.luckDraw === 'y'"> <img loading="lazy"
            src="@/assets/activity/icon_zjjl.png" alt="">中奖记录</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { showDialog } from 'vant'
import { activityValidator, defaultValidator, activityDetailValidator } from '@/hooks/useValidator.js'
import { useUserStore } from '@/store/modules/user';
import { useRouter } from 'vue-router';
import { applicationRecord } from '@/api/activity';
import backgroundImg from '@/assets/activity/background.jpg';

const useStore = useUserStore();
const router = useRouter();
// 响应式数据
const signUpRecord = ref(null)
const activityDetail = computed(() => useStore.activityDetail || {});

// 获取报名记录
const getSignUpRecord = async () => {
  try {
    const { code, data } = await applicationRecord({
      activityId: unref(activityDetail).activityId,
    })
    if (code === 200) {
      signUpRecord.value = data
    }
  } catch (error) {
    console.error('获取报名记录失败:', error)
  }
}

// 跳转到报名页面
const toSignUp = () => {
  if (!activityValidator()) return
  if (signUpRecord.value) {
    showDialog({
      title: "温馨提示",
      message: "您已提交过报名信息，请勿重复报名。",
      confirmButtonText: "我知道了",
    })
  } else {
    router.push('/activityHome/signUpActivity/form')
  }
}

// 跳转到活动详情页面
const toDetail = () => {
  if (!activityDetailValidator()) return
  router.push('/activityDetail')
}

// 跳转到中奖记录页面
const toLotteryRecord = () => {
  if (!defaultValidator()) return
  router.push('/lotteryRecord')
}

// 在组件挂载时获取报名记录
onMounted(() => {
  getSignUpRecord()
})
</script>


<style lang="scss" scoped>
.vieAnswerActivity {
  background-color: #9bbefe;
  background-size: 100% 100%;

  .btn {
    // background-image: url('@/assets/activity/stater_button.png');
    background: linear-gradient(0deg, #FF4902, #FF9672);
    box-shadow: 0px 5px 22px 0px rgba(158, 6, 6, 0.54);
    border: 3px solid #FFF0C9;
  }

  .btns {
    >div {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 208px;
      height: 68px;
      font-size: 29px;
      color: #BF1A05;
      background: #FFFFFF;
      border-radius: 34px;
      border: 1px solid #FFFFFF;
      font-weight: 550;

      img {
        width: 30px;
        margin-right: 5px;
      }
    }
  }
}
</style>
