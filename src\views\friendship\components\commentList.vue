<template>
    <div class="comment-list">
        <div class="comment-item" v-for="item, index in list" :key="index">
            <div class="text-#333 flex">
                <div class="w-56px h-56px rounded-50% bg-cover center mr-10px"
                    :style="{ backgroundImage: `url(${item.avatar})` }" v-if="commentType === 'detail'">
                </div>
                <div class="flex-1 mt-5px">
                    <span class="name">
                        <span class="replyer" v-if="parentName">
                            <span class="text-30px leading-none">{{ item.nickname }}</span>
                            <span class="text-30px text-#666 leading-none"> 回复 </span>
                            <span class="text-30px leading-none">{{ parentName }}：</span>
                        </span>
                        <span class="publisher text-30px" v-else>{{ item.nickname }}：</span>
                    </span>
                    <span class="text-28px text-#666">{{ item.content }}</span>
                </div>
                <!-- 发布动态者和评论发布者可以删除 -->
                <div class="text-24px text-#999 flex mt-5px mr-10px"
                    v-if="commentType === 'detail' && (item.userId === currrentUserId || currrentUserId === postUserId)"
                    @click="deleteFn(item, index)">
                    <img loading="lazy" src="@/assets/friendShip/del.png" class="w-23px h-23px" />
                    <span class="ml-10px leading-none">删除</span>
                </div>
            </div>
            <!-- 操作按钮 -->
            <div class="reply mt-10px mb-20px"
                :class="{ 'border-b-1 pb-20px': commentType === 'detail' && !item.children }">
                <span class="mr-10px text-#B2B2B2 text-26px">发布于{{ item.releaseTimeChinese }}</span>
                <span class="text-24px text-#ADBED0" @click="replay(item)"
                    v-if="item.userId !== currrentUserId && commentType === 'detail'">回复</span>
            </div>

            <template v-if="item.children && item.children.length > 0">
                <commentList :contentArry="item.children" :parentName="item.nickname" :commentType="commentType"
                    @replyed="replay">
                </commentList>
            </template>
        </div>
    </div>

</template>
<script lang="ts" setup>
import { showConfirmDialog } from 'vant';
import commentList from './commentList.vue';
import { deleteComment } from '@/api/friendShip';
import { judgeStaticUrl } from '@/utils/utils';
const props = defineProps({
    contentArry: {
        type: Array,
        required: true,
        default: () => [],
    },
    parentName: {
        type: String,
        default: '',
    },
    commentType: {
        type: String,
        default: 'simple',
    },
    postUserId: {
        type: String,
        default: '',
        required: true,
    },
    currrentUserId: {
        type: String,
        default: '',
        required: true,
    },
})
const emit = defineEmits(['replyed'])
const list = computed(() => formatterArray(props.contentArry));
// 格式化数组
const formatterArray = (arr: any) => {
    return arr.map((item: any) => {
        item.avatar = judgeStaticUrl(item.avatar, true);
        if (item.children) item.children = formatterArray(item.children)
        return item;
    });
}
const replay = (item: any) => {
    emit('replyed', item);
}
const deleteFn = async (item: any, index: number) => {
    showConfirmDialog({
        title: '确定删除该评论吗？',
    }).then(async () => {
        const { code } = await deleteComment(item.autoId);
        if (code === 200) {
            props.contentArry.splice(index, 1);
        }
    });
}
</script>
<style scoped lang="scss">
.comment-item {
    margin-bottom: 30px;

    &:nth-last-child(1) {
        margin-bottom: 0;
    }
}

.border-b-1 {
    border-bottom: 1px solid #f2f2f2;
}
</style>