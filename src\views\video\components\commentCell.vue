<template>
  <div class="flex items-top w-full border-b-solid border-b-1 border-slate-100 py-3 justify-between relative">
    <img loading="lazy" :src="avatar" class="avatar w-60px h-60px rounded-[50%] mt-1 object-cover"
      @click="onUserClick(content)" />
    <div class="w-[calc(100%-200px)] text-#999 text-24px">
      <div class="truncate text-28px text-#333 mb-1">
        <span @click="onUserClick(content)"> {{ content.userName }}</span>
      </div>
      <span>{{ dayjs(content.createTime).format('MM-DD') }}</span>
      <div class="text-32px text-#333 mt-1 messagebutton leading-6">
        <span class="mr-1">{{ content.content }} </span>
        <van-button round size="small" @click="emit('reply', content)">
          回复
        </van-button>
        <van-button round size="small" @click="deleteComment(content)" v-if="content.whetherDelete">
          删除
        </van-button>
        <van-button round size="small" :class="content.whetherLike ? 'text-#5CA4FF' : 'text-#999'"
          @click="likeClick(content)">
          <van-icon name="good-job-o" />{{ content.likeVolume ? content.likeVolume : '' }}
        </van-button>
      </div>
      <div class="flex items-top w-full mt-1" v-for="item in content.childCommentsList">
        <img loading="lazy" src="@/assets/public/female.png" class="avatar w-30px h-30px rounded-[50%]" />
        <div class="ml-2 text-28px text-#333">
          <div class="text-24px leading-4 text-#999 mb-1">{{ item.userName }}</div>
          <div class="leading-[35px]">
            回复
            <span class="mx-1 text-#999">{{ item.replyUserName }} </span>
            {{ item.content }}
          </div>
          <div class="flex items-center messagebutton mt-1">
            <span class="text-24px text-#999 mr-2">{{
              dayjs(item.createTime).format('MM-DD')
            }}</span>
            <van-button round size="small" @click="deleteComment(item, content.childCommentsList)"
              v-if="item.whetherDelete">
              删除
            </van-button>
          </div>
        </div>
      </div>
    </div>
    <img loading="lazy" :src="judgeStaticUrl(content.pictures)"
      class="avatar w-100px h-100px rounded-md object-cover" />
    <!-- 气泡样式 -->
    <div class="absolute left-50% top-50% -translate-50% z-100">
      <waterIntergral v-model:show="showBubble" :score="scoreNum"></waterIntergral>
    </div>
  </div>
</template>

<script setup lang="ts">
import { deleteFunComment } from '@/api/video';
import { likeOperate } from '@/api/public';
import { showToast, showConfirmDialog } from 'vant';
import dayjs from 'dayjs';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/store/modules/user';
const waterIntergral = defineAsyncComponent(() => import('@/components/Bubble/waterIntergral.vue'))
const useStore = useUserStore();
import defaultAvatar from '@/assets/public/head_default.png';
import female from '@/assets/public/female.png';
import male from '@/assets/public/male.png';
import { judgeStaticUrl } from '@/utils/utils';
const router = useRouter();
const props = defineProps({
  content: {
    type: Object,
    default: () => { },
  },
});
const avatar = computed(() => {
  if (props.content?.userId && props.content?.userId === useStore.getUserInfo?.userId) {
    if (useStore.getUserInfo?.avatar) return judgeStaticUrl(useStore.getUserInfo?.avatar)
    if (useStore.getUserInfo?.gender === '男') return male
    if (useStore.getUserInfo?.gender === '女') return female
    return defaultAvatar
  }
  else return defaultAvatar;
})

const emit = defineEmits(['reply', 'deleteContent']);
function onUserClick(item: any) {
  router.push({
    path: '/video-personalCenter',
    query: { userId: item.userId, nickname: item.userName },
  });
}
// 气泡提示框参数设置
const showBubble = ref(false);
const scoreNum = ref(0);
let isReq = false
/**
 * 点赞|取消点赞
 */
const likeClick = (item: any) => {
  if (isReq) return
  isReq = true;
  likeOperate({ sourceId: item.shortVideoCommentsId }).then(res => {
    isReq = false
    if (res.code == 200) {
      if (!res.data?.statefulFlowState) {
        showToast('已取消点赞');
        item.likeVolume -= 1;
      } else {
        item.likeVolume += 1;
        showToast({ type: 'success', message: '点赞成功', duration: 1500 })
      }
      item.whetherLike = res.data;
      if (res.data?.score) {
        scoreNum.value = res.data.score
        // 气泡展示
        setTimeout(() => { showBubble.value = true }, 1500)
      }
    }
  })
    .catch(() => {
      isReq = false
    })
};

/**
 * 删除我的评论
 */
function deleteComment(comment: any, list: any = []) {
  showConfirmDialog({
    title: '提示',
    message: '是否要删除评论内容？',
  })
    .then(async () => {
      let index = list.findIndex(
        (item: any) => item.shortVideoCommentsId === comment.shortVideoCommentsId
      );
      let { code, message } = await deleteFunComment({
        shortVideoCommentsId: comment.shortVideoCommentsId,
      });
      if (code == 200) {
        if (list.length) list.splice(index, 1);
        else emit('deleteContent', comment);
        showToast('删除成功');
      } else {
        showToast('删除失败！' + message);
      }
    })
    .catch(() => {
      showToast('已取消删除');
    });
}
</script>

<style lang="scss" scoped>
.messagebutton {
  --van-button-small-font-size: 24px;
  --van-button-small-height: 45px;
  --van-button-default-background: #f6f7f8;
  --van-button-border-width: 0px;
  --van-button-default-line-height: 45px;

  .van-button {
    margin-right: 10px;
  }
}
</style>
