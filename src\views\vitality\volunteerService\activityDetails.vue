<template>
  <div class="bg-#f5f5f5 h-100vh overflow-hidden">
    <div class="h-full flex flex-col scorll-box overflow-scroll"
      :class="!!activityDetail.signUpInfo ? 'pb-160px' : '20px'">
      <div class="bg-#fff">
        <div class="w-full p-28px border-b-[#f5f7f9] border-b-19px border-b-style-solid">
          <template v-if="activityDetail.appCover">
            <div class="w-full h-280px mb-20px rounded-14px">
              <img loading="lazy" :src="useStore.getPrefix + activityDetail.appCover" alt=""
                class="w-full h-full rounded-14px">
            </div>
          </template>
          <template v-else>
            <div class="w-full h-280px mb-15px rounded-14px flex justify-center items-center text-[#fff] text-50px"
              :style="{ backgroundImage: `url(${defaultCover})`, backgroundSize: '100% 100%' }">
            </div>
          </template>
          <div class="flex justify-between w-full items-center">
            <div class="text-28px text-[#999] flex items-center">
              <img loading="lazy" src="@/assets/activity/icon-readCount.png" alt="" class="w-38px mr-10px">
              {{ activityDetail?.readCount }}
            </div>
            <div
              class="w-125px h-40px rounded-20px border-1px border-[#BF1A05] b-solid text-28px text-[#BF1A05] flex justify-center items-center">
              {{ activityDetail?.progressMsg || '-' }}
            </div>
          </div>
        </div>
        <div class="px-30px box-border pt-32px pb-38px">
          <div class="text-[#333] text-[34px] font-500 leading-60px">{{ activityDetail.activityName }}</div>
          <div
            class="w-100% px-30px box-border h-87px mt-32px flex items-center text-#fff text-26px bg-no-repeat bg-cover bg-center"
            :style="{ backgroundImage: `url(${djs_bg})` }" v-if="Data.time > 0">
            活动倒计时：
            <van-count-down :time="Data.time">
              <template #default="timeData">
                <div class="flex items-center text-#fff text-26px">
                  <div class="flex items-center">
                    <div
                      class="mr-20px w-47px h-47px bg-#fff rounded-8px text-30px text-#333 flex items-center justify-center">
                      {{
                        timeData.days < 10 ? "0" + timeData.days : timeData.days }} </div>
                        天
                    </div>
                    <div class="flex items-center">
                      <div
                        class="mx-20px w-47px h-47px bg-#fff rounded-8px text-30px text-#333 flex items-center justify-center">
                        {{
                          timeData.hours < 10 ? "0" + timeData.hours : timeData.hours }} </div>
                          时
                      </div>
                      <div class="flex items-center">
                        <div
                          class="mx-20px w-47px h-47px bg-#fff rounded-8px text-30px text-#333 flex items-center justify-center">
                          {{
                            timeData.minutes < 10 ? "0" + timeData.minutes : timeData.minutes }} </div>
                            分
                        </div>
                        <div class="flex items-center">
                          <div
                            class="mx-20px w-47px h-47px bg-#fff rounded-8px text-30px text-#333 flex items-center justify-center">
                            {{
                              timeData.seconds < 10 ? "0" + timeData.seconds : timeData.seconds }} </div>
                              秒
                          </div>
                        </div>
              </template>
            </van-count-down>
          </div>
        </div>
      </div>
      <div class="bg-#fff px-32px py-28px box-border my-16px">
        <div class="flex items-center text-26px mb-28px">
          <img loading="lazy" src="@/assets/vitality/volunteerService/icon_fbzz.png" alt="" class="w-28px" />
          <div class="text-#999 ml-22px mr-34px">发布组织</div>
          <div class="text-#333 flex-1">{{ activityDetail.companyName }}</div>
        </div>
        <div class="flex items-center text-26px mb-28px">
          <img loading="lazy" src="@/assets/vitality/volunteerService/icon_bmsj.png" alt="" class="w-28px" />
          <div class="text-#999 ml-22px mr-34px">报名时间</div>
          <div class="text-#333 flex-1">
            {{
              dayjs(activityDetail.signUpInfo?.signUpStartTime).format(
                "YYYY-MM-DD HH:MM"
              )
            }}
            至
            {{
              dayjs(activityDetail.signUpInfo?.signUpEndTime).format(
                "YYYY-MM-DD HH:MM"
              )
            }}
          </div>
        </div>
        <div class="flex items-center text-26px mb-28px">
          <img loading="lazy" src="@/assets/vitality/volunteerService/icon_bmsj.png" alt="" class="w-28px" />
          <div class="text-#999 ml-22px mr-34px">服务时间</div>
          <div class="text-#333 flex-1">
            {{ dayjs(activityDetail.activityStartTime).format("YYYY-MM-DD HH:MM") }}
            至 {{ dayjs(activityDetail.activityEndTime).format("YYYY-MM-DD HH:MM") }}
          </div>
        </div>
        <div class="flex items-center text-26px mb-28px">
          <img loading="lazy" src="@/assets/vitality/volunteerService/icon_fwdz.png" alt="" class="w-28px" />
          <div class="text-#999 ml-22px mr-34px">服务地址</div>
          <div class="text-#333 flex-1">
            {{ activityDetail.activityAddress || "暂无地址" }}
          </div>
        </div>
        <div class="flex items-center text-26px mb-28px">
          <img loading="lazy" src="@/assets/vitality/volunteerService/icon_jf.png" alt="" class="w-28px" />
          <div class="text-#999 ml-22px mr-34px">奖励积分</div>
          <div class="text-#FF6F18 flex-1">
            {{ activityDetail.signUpInfo?.enrollmentScore || 0 }}积分
          </div>
        </div>
        <div class="flex items-center text-26px">
          <img loading="lazy" src="@/assets/vitality/volunteerService/icon_bmrs.png" alt="" class="w-28px" />
          <div class="text-#999 ml-22px mr-34px">报名人数</div>
          <div class="text-#333 flex-1 flex items-center">
            <span class="text-#FF6F18 mr-8px">{{
              activityDetail.signUpInfo?.signUpCount || 0
            }}</span>
            / {{ activityDetail.signUpInfo?.maxCount || 0 }}
            <div class="flex-1 ml-35px">
              <van-progress :percentage="Data.percentageVal" stroke-width="9px" color="#FF8F44" track-color="#FFCAA7"
                pivot-text="" />
            </div>
          </div>
        </div>
      </div>
      <div class="bg-#fff p-30px box-border flex-1">
        <Title title="活动详情" />
        <div class="text-#4D4D4D text-28px mt-26px mb-48px rich_text" v-html="activityDetail.activityContent || '暂无详情'"></div>
        <Title title="报名要求" />
        <div class="text-#4D4D4D text-28px mt-26px mb-48px rich_text" v-html="activityDetail.activityRules || '暂无要求'"></div>
      </div>
    </div>
    <FixedBottom v-if="!!activityDetail.signUpInfo" :name="dealBtn('name')" :disable="dealBtn('disable')"
      @click="toSignUp" />
  </div>
</template>
<script lang="ts" setup>
import djs_bg from "@/assets/vitality/volunteerService/djs_bg.png";
import Title from "@/components/Title/vitalityTitle.vue/";
import FixedBottom from "@/components/Button/fixedBottom.vue";
import dayjs from "dayjs";
import defaultCover from '@/assets/activity/default_cover.jpg';

import { useUserStore } from "@/store/modules/user";
import { computed, ref } from "vue";
import { useRouter } from "vue-router";
import { applicationRecord, signUp } from "@/api/activity";
import utils from "@/utils/utils";
import { activityValidator, checkIntegral } from "@/hooks/useValidator";
import { showConfirmDialog, } from "vant";

const router = useRouter();
const useStore = useUserStore();
const signUpRecord = ref(null)
const Data = ref({
  time: 0,
  btnStatus: "2", //1-可报名  2-不可报名  3-报名了（当日暂未签到） 4-报名了（当日已签到）
  showPop: false,
  info: {},
  percentageVal: 0,
});
const activityDetail = computed(() => useStore.activityDetail || {});

// 获取报名记录
const getSignUpRecord = async () => {
  const { code, data } = await applicationRecord({
    activityId: unref(activityDetail).activityId,
  })
  if (code === 200) {
    signUpRecord.value = data
  }
}
watch(activityDetail, () => {
  if (activityDetail.value.activityId) {
    getSignUpRecord()
  }
})
onMounted(() => {
  //获取定位信息
  utils.getPosition('scan', (code: any, content: any) => {
    if (code == 200) {
      useStore.setLocationInfo(JSON.parse(content));
    }
  })
  if (activityDetail.value.activityId) {
    getSignUpRecord()
  }

})

const toSignUp = async () => {
  if (dealBtn('disable')) {
    return
  }
  if (!activityValidator()) {
    return
  }

  //已报名
  if (signUpRecord.value) {
    if (activityDetail.value.signUpInfo.registerFlag === 'y' && signUpRecord.value.signInFlag === 'n' && signUpRecord.value.state === 'pass') {
      // if(activityDetail.value?.coordinate){
      //  let coordinate =  activityDetail.value?.coordinate?.split(",");
      //   // utils.isPointInCircle({longitude: useStore.getLocationInfo?.longitude, latitude: useStore.getLocationInfo?.latitude},{longitude: coordinate[0], latitude: coordinate[1]},500)
      //   return
      // }
      utils.scan('USER', [{ Name: 'scan' }]);
      // todo 扫码签到
      return
    }
    //我的报名记录
    router.push('/activityHome/signUpActivity/record')

  }

  if (! await checkIntegral()) {
    return;
  }
  if (activityDetail.value.signUpInfo.writeFlag !== 'y') {
    const platform = sessionStorage.getItem('platform')
    const { code } = await signUp({
      activityId: activityDetail.value.activityId,
      platform,
    })
    if (code === 200) {
      await getSignUpRecord()
      await showConfirmDialog({
        title: '温馨提示',
        message: '报名信息提交成功,感谢您的参与~',
        confirmButtonText: '我知道了',
      })
    }
    return
  }
  router.push('/activityHome/signUpActivity/form')
}

function dealBtn(type: string) {
  if (activityDetail.value.progress === '1') {
    return type === "name" ? "未开始" : true;
  }
  if (activityDetail.value.progress === '3') {
    return type === "name" ? "已结束" : true;
  }
  if (!signUpRecord.value) {
    if (activityDetail.value.signUpInfo?.signUpCount === activityDetail.value.signUpInfo?.maxCount) {
      return type === "name" ? "报名人数已达上线" : true;
    }
    return type === "name" ? "立即报名" : false;
  }
  if (activityDetail.value.signUpInfo.registerFlag === 'y' && signUpRecord.value.signInFlag === 'n' && signUpRecord.value.state === 'pass') {
    return type === "name" ? "扫码签到" : false;
  }
  return type === "name" ? "我的报名" : false;
}
</script>
<style lang="scss" scoped>
.scorll-box {
  // height: calc(100% - 140px);
}

.button {
  box-shadow: 0px -2px 10px 0px rgba(183, 192, 204, 0.2);
}

:deep(.van-steps--vertical) {
  padding-left: 0;
}
</style>
