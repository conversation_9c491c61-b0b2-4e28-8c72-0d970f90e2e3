<template>
    <div class="friendship_black pt56px px28px">
        <van-field v-model="userName" :center="true" :clearable="true" placeholder="关键字搜索"
            class="!py-[6px] w-5/6 mb13px">
            <template #button>
                <van-button size="small" class="w-[115px]" @click="search">
                    搜索
                </van-button>
            </template>
            <template #left-icon>
                <img loading="lazy" :src="iconSearch" class="w-[27px] h-[27px] mt-[10px]" />
            </template>
        </van-field>
        <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
            <div class="flex py30px items-center border-b-solid border-b-[#ebebeb] border-b-1px"
                v-for="(item, index) in list" :key="index" @click="clickRemove(item)">
                <div class="flex-1">
                    <div class="text-28px mb-5px">所属阵地：{{ item.positionName }}</div>
                    <div class="text-28px mb-5px">场所名称：{{ item.venueName }}</div>
                    {{ item.userName }} <span class="text-#5BA5FF">{{ item.phone }}</span>
                </div>
                <div class="delete_btn flex items-center justify-center">
                    移除
                </div>
            </div>
        </refreshList>
        <van-popup :show="showPop" position="bottom" :style="{ width: '100%' }">
            <div class="w-full h-full flex flex-col items-center bg-#f5f5f5 text-32px font-400 font-400 ">
                <div class="pt55px pb60px w-full bg-#fff text-center text-28px text-#444">
                    确定移除黑名单
                </div>
                <div class="text-center text-32px text-#333  bg-#fff w-full pt27px pb37px border-t-solid border-t-1px border-t-#eeee "
                    @click="cancelBlack">
                    确定
                </div>
                <div class="text-center text-32px text-#333  bg-#fff w-full pt50px pb80px mt12px"
                    @click="showPop = false">
                    取消
                </div>
            </div>
        </van-popup>
    </div>
</template>

<script lang="ts" setup>
import iconSearch from '@/assets/public/icon_search_blue.png';
import head from '@/assets/public/head_default.png';
import refreshList from '@/components/refreshList/index.vue';
import { showFailToast, showSuccessToast, showToast } from 'vant';
import { useUserStore } from '@/store/modules/user';
import { removeVenueBlacklistById, findVenueBlacklistList } from '@/api/position';
const userStore = useUserStore()
const userName = ref("")
const list = ref([])
const pageNum = ref(1)
const loadMoreRef = ref(null)
const showPop = ref(false)
const popupType = ref("first")
const clickItem = ref(null)
onMounted(() => {
    getList()
})

function clickRemove(item) {
    popupType.value = "first"
    clickItem.value = item;
    showPop.value = true
}

function getList() {
    findVenueBlacklistList({
        phone: userStore.getUserInfo?.phone,
        userName: userName.value
    }).then(res => {
        if (res.code == 200) {
            if (pageNum.value === 1) {
                list.value = [];
            }
            list.value = list.value.concat(res.data);
            //重置刷新状态及 判断是否加载完成
            if (loadMoreRef.value) {
                loadMoreRef.value.onLoadSuc(list.value.length, res.total);
            }
        }

    })
}
//移除黑名单
function cancelBlack() {
    removeVenueBlacklistById(clickItem.value?.autoId).then(res => {
        if (res.code == 200) {
            showPop.value = false;
            showToast("已移除黑名单")
            onRefreshList()
        } else {
            showFailToast(res.message)
        }
    })
}

// 刷新
const onRefreshList = () => {
    pageNum.value = 1;
    getList();
};
const search = () => {
    pageNum.value = 1;
    getList();
}
// 加载更多
const onLoadMore = () => {
    pageNum.value++;
    getList();
};


</script>

<style lang="scss" scoped>
.friendship_black {
    .van-field {
        background: #FFFFFF;
        border-radius: 10px;
        border: 1px solid #5AA4FF;
        width: 100%;

        .van-button {
            border: unset !important;
            color: #5aa4ff;
            background-color: rgba(90, 164, 255, 0.1);
            border-radius: 40px;
            font-size: 24px;
        }
    }

    .delete_btn {
        width: 130px;
        height: 50px;
        background: #FFFFFF;
        border-radius: 23px;
        border: 2px solid #FB2A2A;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 24px;
        color: #FB2A2A;
    }

    .van-popup {
        border-radius: 36px 36px 0px 0px;
    }
}
</style>