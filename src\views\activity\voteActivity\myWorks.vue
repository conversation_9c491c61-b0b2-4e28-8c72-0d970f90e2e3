<template>
    <div class="my-works">
        <div class="headers h-110px bg-#fff  sticky top-0 z-99">
            <van-tabs v-model:active="tabActive" title-active-color="#5AA4FF" @change="tabChange" v-if="tabs.length"
                title-inactive-color="#333">
                <van-tab :title="item.label" v-for="item in tabs" :key="item.value" title-class="tab-title">
                </van-tab>
            </van-tabs>
            <div class="list p-32px">
                <refreshList key="relist" @onRefreshList="onRefreshList" @onLoadMore="onLoadMore" ref="loadMoreRef">
                    <div class="flex items-center bg-#FFFFFF rounded-20px mb-22px" v-for="(item, index) of list"
                        :key="index" @click="toPage(item)">
                        <div class="w-45% rounded-20px h-170px">
                            <img loading="lazy" :src="utils.judgeStaticUrl(item.opusCover)" alt=""
                                class="rounded-20px w-full h-full object-cover">
                        </div>
                        <div class="p-26px w-55% box-border">
                            <div class="text-30px text-#333">{{ item.opusName }}</div>
                            <div class="text-25px text-#999 py-10px">上传时间：{{
                                utils.formatTimeWithoutSeconds(item.createTime) }}</div>
                            <div class="text-25px text-#999">审核状态：<span class=""
                                    :class="item.state == 'wait' ? '!text-#5AA4FF' : item.state == 'refuse' ? '!text-#D11111' : 'text-#41AD50'">{{
                                        item.state == 'pass' ? '已通过' : item.state=='wait'?'待审核':'审核驳回'}}</span></div>
                        </div>
                    </div>
                </refreshList>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
defineOptions({
    name: 'activeMyWorks',
})
import useRefreshFun from '@/hooks/app.ts'
import router from '@/router';
import { myApplyList } from '@/api/activity';
import utils from '@/utils/utils';
import refreshList from '@/components/refreshList/index.vue';
const tabActive = ref(0);
import { useDictionary } from "@/store/modules/dictionary";
const dictionary = useDictionary()
const tabs = ref([]);
import { useUserStore } from '@/store/modules/user';
const useStore = useUserStore();
// 计算属性
const activityDetail = computed(() => {
    return useStore.activityDetail || {};
});
const tabChange = (index) => {
    Data.value.tabIndex = index
    getApplyList();
}
const list = ref([])
function toPage(item) {
    router.push({
        path: '/activityHome/myWorks/detail',
        query: {
            opusInfoId: item.opusInfoId,
            status: tabs.value[Data.value.tabIndex]?.value,
            info: JSON.stringify(item)
        }
    })
}
const Data = ref({
    pageNum: 1,
    tabIndex: 0
})
//获取活动列表
const loadMoreRef = ref(null)
function getApplyList() {
    myApplyList({
        pageSize: 10,
        pageNum: unref(Data).pageNum,
        state: tabs.value[Data.value.tabIndex]?.value,
        activityId: activityDetail.value.activityId
    }).then(res => {
        if (res.code == 200) {
            if (Data.value.pageNum === 1) {
                list.value = [];
            }
            list.value = list.value.concat(res.data);
            //重置刷新状态及 判断是否加载完成
            if (loadMoreRef.value) {
                loadMoreRef.value.onLoadSuc(list.value.length, res.total);
            }
        }

    })
}
// 刷新
const onRefreshList = () => {
    Data.value.pageNum = 1;
    getApplyList();
};
// 加载更多
const onLoadMore = () => {
    Data.value.pageNum++;
    getApplyList();
};
const changePage = () => {
    onRefreshList()
}
const { refresh } = useRefreshFun()
refresh([
    { name: 'changePage', funsName: [changePage]}
])

onMounted(() => {
    const activityVerifyStatus = dictionary.getDictionaryOpt?.['activityVerifyStatus']
    tabs.value = activityVerifyStatus
    getApplyList()
})
</script>
<style lang="scss" scoped>
.my-works {
    background: #F6F7F8;
    min-height: 100vh;

    .headers {
        :deep(.van-cell) {
            background-color: transparent;
            padding: 0;
        }

        :deep(.van-tabs--line .van-tabs__wrap) {
            height: 110px;
        }

        :deep(.van-tab) {
            line-height: 1;
            height: 100%;
        }

        :deep(.van-tabs__nav--line) {
            height: 100%;
            padding-bottom: 15px;
        }

        :deep(.van-tabs__line) {
            width: 36px;
            height: 6px;
            background: linear-gradient(0deg, #A1CBFF 0%, #5AA4FF 100%);
            border-radius: 3px;
        }

        :deep(.van-tab__text) {
            font-size: 32px;
        }

        :deep(.van-field__body) {
            height: 100%;
            font-size: 24px;
        }
    }
}
</style>