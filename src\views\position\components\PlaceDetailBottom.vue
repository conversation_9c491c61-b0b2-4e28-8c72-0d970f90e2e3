<template>
  <div class="w-full h-[140px] fixed bottom-0 bg-[#fff] flex z-1 justify-evenly items-center"
    :class="$style['place-detail-bottom']">
    <div
      class="active:shadow-[0_0_10px_0_#5ba5ff] active:rounded-[36px] text-center py-2 border border-solid px-[54px] rounded-[36px]"
      :class="`${placeRecord?.reserveState === 'n' ? 'w-full mx-7' : 'w-[230px]'}`" v-if="ifVr" @click="handleClick">
      VR查看
    </div>

    <div
      class="detail-btn  text-center text-[#fff] rounded-[36px] py-2 active:shadow-[0_0_10px_0_#5ba5ff] active:rounded-[36px]"
      :class="`${ifVr ? 'w-[230px]' : 'w-full mx-7'}`" v-if="placeRecord?.reserveState === 'y'" @click="handleReserve">
      我要预约
    </div>
  </div>
</template>

<script lang="ts" setup>
import {isLoginDialog} from '@/utils/utils'
import { useUserStore } from '@/store/modules/user';
import { checkReservation } from '../utils';

const router = useRouter();

const userStore = useUserStore();

const placeRecord = inject<Recordable>('placeRecord', {});

const ifVr = computed(() => unref(placeRecord)?.panoramaUrl);

function handleClick() {
  router.push({ path: '/vrShow', query: {} });
}

// 预约
function handleReserve() {
  isLoginDialog((isLogin:boolean) => {
    if (!isLogin) return;
    checkReservation(
      {
        venueInfoId: unref(placeRecord)?.venueInfoId,
        userId: userStore.getUserInfo.userId,
      },
      unref(placeRecord)?.autoId,
      unref(placeRecord)?.recordId,
      router
    );    
  })

}
</script>

<style lang="less" module>
.place-detail-bottom {
  :global {
    box-shadow: 0 0 7px 0 #d3d3d3;

    .detail-btn {
      background: linear-gradient(to right, #5BA5FF, #5CB6FF);
    }
  }
}
</style>
