<template>
  <div class="personal flex pl-50px pt-50px pb-30px min-h-180px"
    :style="'background:url(' + personlBg + ') no-repeat center/100% 100%;'">
    <div class="left w-88px h-88px" @click="previewImage">
      <!-- 如果是当前用户就判断类型 -->
      <img loading="lazy" :src="avatar" class="w-full h-full rounded-50% object-cover" />
    </div>
    <div class="right mt-20px ml-40px">
      <div class="user_name text-36px text-[#333]">{{ nickname ?? '--' }}</div>
      <div class="products mt-41px flex">
        <div class="ml-7px text-center">
          <div class="text-[#333] text-28px">{{ personData.count ?? '--' }}</div>
          <div class="tips text-[#666] text-26px">作品</div>
        </div>
        <div class="ml-60px text-center">
          <div class="text-[#333] text-28px">{{ personData.likeVolume ?? '--' }}</div>
          <div class="tips text-[#666] text-26px">点赞</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { getMyVideoStatistics } from '@/api/video/index';
import personlBg from '@/assets/video/person_bg.png';
import defaultAvatar from '@/assets/public/head_default.png';
import female from '@/assets/public/female.png';
import male from '@/assets/public/male.png';
import { useUserStore } from '@/store/modules/user';
import utils from '@/utils/utils';
const useStore = useUserStore();
const personData = ref({});
const props = defineProps({
  userId: {
    type: String,
    default: '',
  },
  nickname: {
    type: String,
    default: '',
  },
});

/**
 * 获取个人数据统计
 */
const getPersonData = async () => {
  const { code, data } = await getMyVideoStatistics({ userId: props?.userId });
  if (code === 200) {
    personData.value = data;
  }
};

watch(
  () => props.userId,
  val => {
    if (val) getPersonData();
  },
  { immediate: true }
);
const avatar = computed(() => {
  if (props.userId && props.userId === useStore.getUserInfo?.userId) {
    if (useStore.getUserInfo?.avatar) return utils.judgeStaticUrl(useStore.getUserInfo?.avatar)
    if (useStore.getUserInfo?.gender === '男') return male
    if (useStore.getUserInfo?.gender === '女') return female
    return defaultAvatar
  } else {
    return defaultAvatar
  }
})
const previewImage = () => {
  utils.imagePreview([avatar.value])
}
</script>
