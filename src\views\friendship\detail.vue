<script lang="ts" setup>
import malew from '@/assets/friendShip/male_icon.png'
import femalew from '@/assets/friendShip/female_icon.png'
import follow from '@/assets/friendShip/follow_icon_w.png'
import followed from '@/assets/friendShip/file_followeach_a.png'
import defaultBanner from '@/assets/friendShip/defualt_banner.png'
import noAccountbg from '@/assets/friendShip/no_account_bg.jpg'
import emptyImg from '@/assets/friendShip/empty_img.png'
import contactPopup from './components/contactPopup.vue'
import contactPop from './components/contactPop.vue'
import dynamicsCell from './components/dynamicsCell.vue'
import dayjs from 'dayjs'
import utils from '@/utils/utils'
import refreshList from '@/components/refreshList/index.vue'
import { queryOthersDetailApi, followUserApi, queryMyDetail, getDynamicsList, addOrCancelBlack } from '@/api/friendship/index';
import {
    useRouter,
    useRoute
} from "vue-router";
import { showConfirmDialog, showToast } from 'vant'
import { useFriendShipStore } from '@/store/modules/friendShip.ts'
import useRefreshFun from '@/hooks/app.ts'
const friendShipStore = useFriendShipStore()
import { useDictionary } from '@/store/modules/dictionary';
import Empty from '@/components/Empty/index.vue';
// 局部刷新
const refreshPart = (index: number, refresh: boolean = false) => {
    let currenPageNum = Math.floor(index / 10) + 1
    if (!refresh) return friendShipStore.setdynamicPageNum(currenPageNum)
    refreshHandle(currenPageNum)
}
const refreshHandle = (currenPageNum: number) => {
    if (currenPageNum > 1) {
        let endIndex = ((currenPageNum - 1) * 10) - 1
        list.value = list.value.slice(0, endIndex)
    }
    pageNum.value = currenPageNum
    getActiveList()
}
// 其他页面触发刷新执行事件
const savePageNum = computed(() => friendShipStore.getDynamicsPageNum)
const changePage = () => {
    refreshHandle(savePageNum.value)
}
const { refresh } = useRefreshFun()
refresh([
    { name: 'changePage', funsName: [changePage] }
])
const dictionary = useDictionary();
// 根据数据字典查询
const getDictionaryLabel = (dictionaryType: any, value: any) => {
    let label
    try {
        let c = dictionary.getDictionaryOpt?.[dictionaryType].find(t => { return t.value == value })
        label = c.label
    } catch {
        label = value
    }
    return label
}
const router = useRouter();
const route = useRoute();
const btnClick = ref(false)
const images = ref<any>([]);
const singleUserId = ref<any>('')
const detailObj = ref<any>({})
const errorMessage = ref<any>('用户已注销')

// 他人详情
const getDetail = async () => {
    const { code, data, message } = await queryOthersDetailApi({
        singleUserId: singleUserId.value
    })
    if (code == 200) {
        if(!data){
            errorMessage.value = message
            detailObj.value = null

        }else{
            if (data?.album) {
                images.value = data.album.split(',').map((item: any) => { return utils.judgeStaticUrl(item) })
            }
            images.value.unshift(utils.judgeStaticUrl(data.avatar, true))
            detailObj.value = data
            checked.value = data.blackFlagMy //我拉黑状态
            if (!images.value.length) images.value.push(defaultBanner)            
        }

    }else{
        errorMessage.value = message
        detailObj.value = null
    }
}

const pageNum = ref(1);
const loadMoreRef = ref()

const list = ref<any>([]);
const onLoadMore = () => {
    pageNum.value++;
    getActiveList()
}
const onRefreshList = () => {
    pageNum.value = 1;
    getActiveList();
};
const getActiveList = async () => {
    const { data, code, total } = await getDynamicsList({
        userId: singleUserId.value,
        queryType: 'all',
        pageNum: pageNum.value,
        pageSize: 10
    })
    if (code === 200) {
        if (pageNum.value === 1) list.value = data;
        else list.value = [...list.value, ...data];
    }
    if (loadMoreRef.value) {
        loadMoreRef.value.onLoadSuc(list.value.length, total);
    }
}
// 获取个人资料
const myInfo = ref({
    labelList: []
})
const popNumshow = ref(false)
// 关闭联系方式弹窗
const closeApplyPopup = (res) => {
    popNumshow.value = res
}
const getMyDetail = () => {
    queryMyDetail().then(res => {
        if (res.code == 200) {
            myInfo.value = res.data
            myInfo.value.labelList = res.data.singleLabelList ? res.data.singleLabelList.map((item: any) => item.labelId) : []
        }
    })
}
// 关注方法
const folowFn = () => {
    if (btnClick.value) return
    btnClick.value = true
    followUserApi({
        singleUserId: singleUserId.value
    }).then(res => {
        detailObj.value.likeFlag ? showToast('取关成功') : showToast('关注成功')
        getDetail()
        btnClick.value = false

    })
}
// 获联系方式
const showContact = ref(false)
const getContacts = () => {
    if (detailObj.value.friendFlag) {
        popNumshow.value = true
    } else {
        showContact.value = true

    }
}
const active = ref(0)
const checked = ref(false)
const showPopChoose = ref(false)
const onUpdateValue = (val: any) => {
    showConfirmDialog({
        title: '温馨提示',
        message: `确认${checked.value ? '取消' : '拉入'}黑名单吗？`,
    }).then(async () => {
        const { code } = await addOrCancelBlack({
            blackUserId: singleUserId.value,
        })
        if (code == 200) {
            router.back()
        }
    });
}
const toDetail = (item: any, index: number) => {
    // 存储当前动态
    refreshPart(index)

    router.push({
        path: '/friendship/dynamicDetail',
        query: {
            postId: item.postId
        }
    })
}
// 图片预览
const previewImages = (index: number) => {
    utils.imagePreview(images.value, index)
}
onMounted(
    async () => {
        singleUserId.value = route.query.singleUserId
        getDetail()
        onRefreshList()
        getMyDetail()
    }
)

const offset = ref({ x: -window.innerWidth, y: window.innerHeight - 280 });
</script>
<template>
    <div class="friendship-detail" @click="showPopChoose = false">
        <div v-if="detailObj">
            <div class="swiper-box w-full h-520px">
                <van-swipe class="w-full h-full" :autoplay="3000" lazy-render>
                    <van-swipe-item v-for="image, index in images" :key="index" @click="previewImages(index)">
                        <img loading="lazy" style="object-fit: cover;" :src="image" />
                    </van-swipe-item>
                    <template #indicator="{ active, total }">
                        <div class="custom-indicator">{{ active + 1 }}/{{ total }}</div>
                    </template>
                </van-swipe>
                <div class="setting-box" style="filter: drop-shadow(rgb(215, 106, 172) 2px 4px 6px);"
                    @click.stop="showPopChoose = true"><img loading="lazy" @click.stop="showPopChoose = !showPopChoose"
                        src="@/assets/friendShip/more_icon.png" alt="">
                    <div class="pop-choose" v-show="showPopChoose">加入黑名单
                        <van-switch class="ml-15px" active-color="#5AA4FF" :model-value="checked"
                            @update:model-value="onUpdateValue" size="16px" />
                    </div>
                </div>
            </div>
            <div class="info-box min-h-30vh">
                <div class="name-box">
                    <div class="name flex items-center">{{ detailObj.nickname }}<img loading="lazy"
                            class="w-32px ml-13px" :src="detailObj.gender == 'male' ? malew : femalew" alt=""></div>

                    <span class="follow-state" v-if="myInfo.singleStatus != 'y'" @click="folowFn()"
                        :class="detailObj.likeFlag ? 'followed' : 'follow'">
                        <img loading="lazy" class="w-25px mr-13px"
                            v-show="!detailObj.likeFlag || (detailObj.otherLikeFlag && detailObj.likeFlag)"
                            :src="detailObj.likeFlag ? followed : follow" alt="">
                        {{ !detailObj.likeFlag ? '关注' : detailObj.otherLikeFlag ? '已互关' : '已关注' }}
                    </span>
                </div>
                <!-- 不被拉黑才展示 -->
                <div v-if="!detailObj.blackFlagOther && !checked">
                    <div class="info-text">
                        {{ detailObj.age }}岁·{{ detailObj.height }}cm·{{ getDictionaryLabel('modelEducation',
                            detailObj.education) }}
                    </div>
                    <div class="place">{{ detailObj.companyName }}</div>
                    <div class="match-box">
                        <span class="leading-none flex items-center">
                            与您匹配度
                            <span class="num leading-none">{{ detailObj.score }}%
                            </span>
                        </span>
                    </div>
                    <div class="info-title">
                        <span class="flex items-center">
                            <img loading="lazy" class="w-42px" src="@/assets/friendShip/brief_icon.png"
                                alt="">个人简介</span>
                    </div>
                    <div class="contentAMe">{{ detailObj.biography ? detailObj.biography : '这个人很懒，什么都没留下' }}</div>
                </div>

            </div>

            <div v-if="!detailObj.blackFlagOther && !checked">
                <div class="h-75px sticky top-0 z-999">
                    <van-tabs v-model:active="active">
                        <van-tab title="个人信息"></van-tab>

                        <van-tab title="Ta的动态"></van-tab>
                    </van-tabs>
                </div>
                <div class="live-box" v-show="active == 1">
                    <div class="live-list">
                        <refreshList ref="loadMoreRef" key="relist" @onRefreshList="onRefreshList" :immeCheck="false"
                            @onLoadMore="onLoadMore" :slotEmpty="true">
                            <dynamicsCell @handleRefresh="refreshPart(index, true)" :detailObj="item"
                                v-for="item, index in list" :key="index" @click="toDetail(item, index)">
                            </dynamicsCell>
                            <template #noData>
                                <van-empty :image="emptyImg" description="暂时没有内容哦" :image-size="['60%', 'auto']">
                                </van-empty>
                            </template>
                        </refreshList>

                    </div>
                </div>
                <!-- 个人资料 -->
                <div class="person-box" v-show="active == 0">
                    <div class="card">
                        <div class="info-title">
                            <span class="flex items-center"><img loading="lazy" class="w-33px h-32px"
                                    src="@/assets/friendShip/base_info_icon.png" alt="">基本信息</span>
                        </div>
                        <div class="row-list leading-56px">
                            <div class="row text-#333333 text-30px"><span class="text-#999">单身情况<span>：</span></span>{{
                                getDictionaryLabel('maritalStatus', detailObj.maritalStatus) }}
                            </div>
                            <div class="row text-#333333 text-30px"><span class="text-#999">年龄<span>：</span></span>{{
                                detailObj.age }}岁
                            </div>
                            <div class="row text-#333333 text-30px"><span class="text-#999">学历<span>：</span></span>{{
                                getDictionaryLabel('modelEducation', detailObj.education) }}
                            </div>
                            <div class="row text-#333333 text-30px"><span class="text-#999">手机号码<span>：</span></span>{{
                                detailObj.phone
                            }}
                            </div>
                            <div class="row text-#333333 text-30px"><span class="text-#999">收入<span>：</span></span>{{
                                getDictionaryLabel('incomeMonth', detailObj.incomeMonth) }}</div>
                            <div class="row text-#333333 text-30px"><span class="text-#999">购房情况<span>：</span></span>{{
                                detailObj.houseStatus == 'y' ? '已购房' : '未购房' }}</div>
                            <div class="row text-#333333 text-30px"><span class="text-#999">购车情况<span>：</span></span>{{
                                detailObj.carStatus == 'y' ? '已购车' : '未购车' }}</div>
                            <div class="row text-#333333 text-30px"><span class="text-#999">户籍<span>：</span></span>{{
                                detailObj.residence }}
                            </div>
                            <div class="row text-#333333 text-30px"><span class="text-#999">所属工会<span>：</span></span>{{
                                detailObj.companyName }}</div>
                            <div class="row text-#333333 text-30px"><span class="text-#999">注册时间<span>：</span></span>
                                {{ dayjs(detailObj.createTime).format('YYYY-MM-DD') }}
                            </div>
                        </div>
                    </div>
                    <div class="card">
                        <div class="info-title flex items-center">
                            <span class="inline-flex items-center"><img loading="lazy" class="w-33px h-32px"
                                    src="@/assets/friendShip/hobby_icon.png" alt="">兴趣爱好</span>
                            <span class="text-24px text-#999 ml-17px">相同爱好会被点亮</span>
                        </div>
                        <div class="tag-box">
                            <span class="tag"
                                :class="{ '!bg-#5AA4FF !text-#fff': myInfo.labelList && myInfo.labelList.includes(i.labelId) }"
                                v-for="(i, j) in detailObj.singleLabelList" :key="j">
                                {{ i.labelName }}
                            </span>
                        </div>
                    </div>
                    <div class="card">
                        <div class="info-title">
                            <span class="flex items-center"><img loading="lazy" class="w-33px h-32px"
                                    src="@/assets/friendShip/want_icon.png" alt="">择偶标准</span>
                        </div>
                        <div class="row-list leading-56px">
                            <div class="row text-#333333 text-30px"><span class="text-#999">年龄<span>：</span></span>{{
                                detailObj.singleMateChoose?.lowerAge }}岁-{{ detailObj.singleMateChoose?.upperAge }}岁
                            </div>
                            <div class="row text-#333333 text-30px"><span class="text-#999">身高<span>：</span></span>{{
                                detailObj.singleMateChoose?.lowerHeight }}cm-{{ detailObj.singleMateChoose?.upperHeight
                                }}cm
                            </div>
                            <div class="row text-#333333 text-30px"><span class="text-#999">学历<span>：</span></span>{{
                                getDictionaryLabel('modelEducation', detailObj.singleMateChoose?.education ?
                                    detailObj.singleMateChoose?.education : '不限') }}
                            </div>
                            <div class="row text-#333333 text-30px"><span class="text-#999">收入<span>：</span></span>{{
                                getDictionaryLabel('incomeMonth',
                                    detailObj.singleMateChoose?.incomeMonth ? detailObj.singleMateChoose?.incomeMonth :
                                        '不限') }}
                            </div>
                            <div class="row text-#333333 text-30px"><span class="text-#999">购房情况<span>：</span></span>{{
                                detailObj.singleMateChoose?.houseStatus == 'none' ? '不限' : '有房' }}
                            </div>
                            <div class="row text-#333333 text-30px"><span class="text-#999">购车情况<span>：</span></span>{{
                                detailObj.singleMateChoose?.carStatus == 'none' ? '不限' : '有车' }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <!-- 账户信息未查询到 -->
        <div v-else>
            <div class="empty-views mt-20%">
                <img loading="lazy" :src="noAccountbg" class="w-full" />
                <div class="no-account flex items-center justify-center h-70px w-85%
                mx-auto rounded-30px mt-23px text-center
                text-#A99FA3 text-32px">{{errorMessage}}</div>
            </div>
        </div>

        <!-- 获取联系方式 不被拉黑才展示 -->
        <van-floating-bubble v-model:offset="offset" axis="xy" magnetic="x" :gap="5" class="move_single_btn_class"
            v-if="detailObj">
            <div class="phone-bar" @click="getContacts" v-if="!detailObj.blackFlagOther && myInfo.singleStatus != 'y'">
                <div class="relative">
                    <img loading="lazy" src="@/assets/friendShip/phone_bg.png" class="w-250px" />
                    <div class="text-24px text-#fff absolute left-40% right-20% top-16% bottom-10%">
                        希望获得联系方式
                    </div>
                </div>
            </div>
        </van-floating-bubble>

        <!-- 获取联系方式弹窗 -->
        <template v-if="detailObj">
            <contactPopup v-model:show="showContact" :aimUserId="detailObj?.singleUserId"></contactPopup>
            <contactPop :showPop="popNumshow" :detailObj="detailObj" @closePopup="closeApplyPopup"></contactPop>
        </template>
        

    </div>
</template>
<style lang="scss" scoped>
.friendship-detail {
    width: 100%;

    .tag-box {
        display: flex;
        flex-flow: row wrap;
        margin-top: 20px;
        margin-bottom: -15px;

        .tag {
            background: #EAF1F9;
            border-radius: 12px;
            padding: 12px 14px;
            font-weight: 400;
            font-size: 28px;
            color: #95B3D8;
            margin-right: 17px;
            margin-bottom: 15px;

            &.active {
                background: #5AA4FF;
                color: #FFFFFF;
            }
        }
    }

    :deep(.van-tabs) {
        .van-tabs__nav {
            background: url('@/assets/friendShip/bg_detail.png');
            height: 75px;
        }

        .van-tabs__wrap {
            height: 75px;
        }

        .van-tab {
            font-size: 26px;
            color: #333333;
            line-height: 1;
            height: 100%;
        }

        .van-tab__text {
            font-size: 28px;
        }

        .van-tab--active {
            font-weight: 500;
            font-size: 32px;
            color: #333333;
        }

        .van-tabs__line {
            width: 40px;
            height: 6px;
            background: linear-gradient(86deg, #C7E0FF 0%, #5AA4FF 100%);
            border-radius: 3px;
            bottom: 30px;
        }
    }

    .swiper-box {
        .van-swipe-item {
            >img {
                width: 100%;
                height: 100%;
            }
        }

        .setting-box {
            position: absolute;
            top: 24px;
            right: 29px;

            .pop-choose {
                width: 233px;
                height: 60px;
                background: #FFFFFF;
                box-shadow: 0px 1px 25px 2px rgba(88, 88, 88, 0.21);
                border-radius: 16px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 400;
                font-size: 26px;
                color: #333333;
                position: absolute;
                transform: translate(-80%, 0);
            }

            >img {
                width: 33px;
                height: 37px;
            }
        }

        .custom-indicator {
            position: absolute;
            right: 30px;
            bottom: 13px;
            padding: 5px 20px;
            font-size: 28px;
            color: #FFFFFF;
            border-radius: 18px;
            background: rgba(0, 0, 0, 0.2);
        }
    }


    .info-title {
        font-weight: 500;
        font-size: 30px;
        color: #333333;
        margin-bottom: 10px;

        img {
            margin-right: 21px;
        }
    }

    .live-box {
        box-sizing: border-box;
        padding: 20px 31px;

        .live-list {
            .live-item {
                .comment-box {
                    width: 100%;
                    background: #F5F5F5;
                    border-radius: 10px;
                    padding: 30px;
                    box-sizing: border-box;

                    .comment-item {
                        margin-bottom: 30px;

                        &:nth-last-child(1) {
                            margin-bottom: 0;
                        }
                    }

                    .comment-content {
                        font-weight: 400;
                        font-size: 28px;
                        color: #333333;
                        line-height: 38px;

                        .name {
                            font-weight: 550;
                            font-size: 30px;
                            color: #333333;
                        }
                    }
                }

                .content-box {
                    font-weight: 400;
                    font-size: 28px;
                    color: #333333;
                    line-height: 38px;
                }

                .img-box {
                    display: flex;
                    flex-flow: row wrap;
                    margin-bottom: 12px;

                    &.one {
                        >img {
                            width: 100%;
                            height: 226px;
                            border-radius: 15px;
                        }
                    }

                    &.two {
                        justify-content: space-between;

                        >img {
                            width: calc((100% - 17px) / 2);
                            // margin-right: 11px;
                            height: 226px;

                            border-radius: 15px;

                        }

                    }

                    &.three {
                        >img {
                            width: calc((100% - 28px)/3);
                            margin-right: 14px;
                            height: 226px;
                            border-radius: 15px;
                            margin-bottom: 16px;

                            &:nth-child(3n) {
                                margin-right: 0;
                            }
                        }
                    }
                }

                .btn-box {
                    >div {
                        display: flex;
                        align-items: center;
                        margin-left: 51px;
                        font-weight: 400;
                        font-size: 24px;
                        color: #999999;

                        >img {
                            width: 27px;
                            margin-right: 13px;
                        }
                    }
                }

                .live-info {
                    width: 100%;

                    .info-left {

                        .info-text {
                            display: flex;
                            flex-flow: column;

                            .sex-time {
                                .sex {
                                    font-weight: 400;
                                    font-size: 24px;
                                    color: #FFFFFF;
                                    width: 85px;
                                    height: 36px;

                                    border-radius: 18px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;

                                    >img {
                                        width: 19px;
                                        margin-right: 7px;
                                    }

                                    &.female {
                                        background: linear-gradient(90deg, #FAA8AB, #FF7097);
                                    }

                                    &.male {
                                        background: linear-gradient(90deg, #9FC9FF, #4196FF);
                                    }
                                }
                            }
                        }
                    }

                }
            }
        }
    }

    .person-box {
        .card {
            box-sizing: border-box;
            padding: 27px 30px;
            border-bottom: 20px solid #F5F5F5;

        }
    }

    .info-box {
        box-sizing: border-box;
        padding: 27px 30px;
        background: url('@/assets/friendship/bj.png') no-repeat;
        background-position: center top;
        background-size: 100% auto;

        .name-box {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;

            .name {
                font-weight: 500;
                font-size: 40px;
                color: #333333;

                >img {
                    width: 32px;
                    margin-left: 13px;
                }
            }

            .follow-state {
                width: 162px;
                height: 48px;
                border-radius: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 400;
                font-size: 24px;

                &.follow {
                    background: #F4698D;
                    color: #FFF;
                }

                &.followed {
                    background: #FFE1E6;
                    border: 2px solid #F4698D;
                    color: #F4698D;
                }
            }
        }

        .info-text {
            font-weight: 400;
            font-size: 26px;
            color: #999999;
            margin-bottom: 6px;
        }

        .place {
            font-weight: 400;
            font-size: 28px;
            color: #666666;
        }

        .match-box {
            width: 651px;
            height: 99px;
            background: #FFFFFF;
            border-radius: 20px;
            border: 1px solid #5AA4FF;
            margin: 31px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 400;
            font-size: 30px;
            color: #333333;

            .num {
                font-weight: bold;
                font-size: 40px;
                color: #0C79F4;
                margin-left: 30px;

                i {
                    font-size: 30px;
                }
            }
        }

        .contentAMe {
            font-size: 24px;
            color: #444;
            line-height: 40px;
        }
    }
}
</style>
<style lang="less">
.move_single_btn_class {
    background-color: transparent !important;
    border-radius: 0 !important;
    overflow: visible;
    width: 250px;
}
</style>